package com.tqhit.battery.one.ads.core;

import com.tqhit.adlib.sdk.analytics.AnalyticsTracker;
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ApplovinAppOpenAdManager_Factory implements Factory<ApplovinAppOpenAdManager> {
  private final Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider;

  private final Provider<AnalyticsTracker> analyticsTrackerProvider;

  public ApplovinAppOpenAdManager_Factory(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<AnalyticsTracker> analyticsTrackerProvider) {
    this.remoteConfigHelperProvider = remoteConfigHelperProvider;
    this.analyticsTrackerProvider = analyticsTrackerProvider;
  }

  @Override
  public ApplovinAppOpenAdManager get() {
    return newInstance(remoteConfigHelperProvider.get(), analyticsTrackerProvider.get());
  }

  public static ApplovinAppOpenAdManager_Factory create(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<AnalyticsTracker> analyticsTrackerProvider) {
    return new ApplovinAppOpenAdManager_Factory(remoteConfigHelperProvider, analyticsTrackerProvider);
  }

  public static ApplovinAppOpenAdManager newInstance(FirebaseRemoteConfigHelper remoteConfigHelper,
      AnalyticsTracker analyticsTracker) {
    return new ApplovinAppOpenAdManager(remoteConfigHelper, analyticsTracker);
  }
}
