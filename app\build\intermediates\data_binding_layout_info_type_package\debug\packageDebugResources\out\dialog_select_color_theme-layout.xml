<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_select_color_theme" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\dialog_select_color_theme.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/dialog_select_color_theme_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="356" endOffset="16"/></Target><Target id="@+id/textView20" view="TextView"><Expressions/><location startLine="23" startOffset="16" endLine="40" endOffset="59"/></Target><Target id="@+id/strelka" view="RelativeLayout"><Expressions/><location startLine="41" startOffset="16" endLine="73" endOffset="32"/></Target><Target id="@+id/exit_color" view="Button"><Expressions/><location startLine="48" startOffset="20" endLine="59" endOffset="74"/></Target><Target id="@+id/blue_btn" view="Button"><Expressions/><location startLine="90" startOffset="20" endLine="101" endOffset="74"/></Target><Target id="@+id/green_btn" view="Button"><Expressions/><location startLine="102" startOffset="20" endLine="112" endOffset="74"/></Target><Target id="@+id/orange_btn" view="Button"><Expressions/><location startLine="113" startOffset="20" endLine="123" endOffset="74"/></Target><Target id="@+id/light_blue_btn" view="Button"><Expressions/><location startLine="124" startOffset="20" endLine="136" endOffset="74"/></Target><Target id="@+id/red_btn" view="Button"><Expressions/><location startLine="137" startOffset="20" endLine="146" endOffset="74"/></Target><Target id="@+id/pink_btn" view="Button"><Expressions/><location startLine="157" startOffset="20" endLine="168" endOffset="74"/></Target><Target id="@+id/light_green_btn" view="Button"><Expressions/><location startLine="169" startOffset="20" endLine="179" endOffset="74"/></Target><Target id="@+id/telo_btn" view="Button"><Expressions/><location startLine="180" startOffset="20" endLine="190" endOffset="74"/></Target><Target id="@+id/gold_btn" view="Button"><Expressions/><location startLine="191" startOffset="20" endLine="203" endOffset="74"/></Target><Target id="@+id/night_blue_btn" view="Button"><Expressions/><location startLine="204" startOffset="20" endLine="213" endOffset="74"/></Target><Target id="@+id/table1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="215" startOffset="16" endLine="283" endOffset="67"/></Target><Target id="@+id/color_btn_1" view="Button"><Expressions/><location startLine="226" startOffset="20" endLine="237" endOffset="74"/></Target><Target id="@+id/color_btn_2" view="Button"><Expressions/><location startLine="238" startOffset="20" endLine="248" endOffset="74"/></Target><Target id="@+id/color_btn_3" view="Button"><Expressions/><location startLine="249" startOffset="20" endLine="259" endOffset="74"/></Target><Target id="@+id/color_btn_4" view="Button"><Expressions/><location startLine="260" startOffset="20" endLine="272" endOffset="74"/></Target><Target id="@+id/color_btn_5" view="Button"><Expressions/><location startLine="273" startOffset="20" endLine="282" endOffset="74"/></Target><Target id="@+id/table2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="284" startOffset="16" endLine="352" endOffset="67"/></Target><Target id="@+id/color_btn_6" view="Button"><Expressions/><location startLine="295" startOffset="20" endLine="306" endOffset="74"/></Target><Target id="@+id/color_btn_7" view="Button"><Expressions/><location startLine="307" startOffset="20" endLine="317" endOffset="74"/></Target><Target id="@+id/color_btn_8" view="Button"><Expressions/><location startLine="318" startOffset="20" endLine="328" endOffset="74"/></Target><Target id="@+id/color_btn_9" view="Button"><Expressions/><location startLine="329" startOffset="20" endLine="341" endOffset="74"/></Target><Target id="@+id/color_btn_10" view="Button"><Expressions/><location startLine="342" startOffset="20" endLine="351" endOffset="74"/></Target></Targets></Layout>