<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_animation_grid" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_animation_grid.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_animation_grid_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="74" endOffset="51"/></Target><Target id="@+id/scrollView" view="ScrollView"><Expressions/><location startLine="7" startOffset="4" endLine="72" endOffset="16"/></Target><Target id="@+id/animation_title" view="TextView"><Expressions/><location startLine="33" startOffset="16" endLine="41" endOffset="53"/></Target><Target id="@+id/animation_info" view="ImageView"><Expressions/><location startLine="42" startOffset="16" endLine="49" endOffset="53"/></Target><Target id="@+id/categoryRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="52" startOffset="12" endLine="60" endOffset="86"/></Target><Target id="@+id/animationRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="62" startOffset="12" endLine="70" endOffset="34"/></Target></Targets></Layout>