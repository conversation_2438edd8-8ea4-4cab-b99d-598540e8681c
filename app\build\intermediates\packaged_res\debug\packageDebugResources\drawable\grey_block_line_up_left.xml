<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android" android:exitFadeDuration="250">
    <item android:state_pressed="true">
        <shape android:tint="?attr/grey_pressed">
            <size
                android:height="10dp"
                android:width="10dp"/>
            <corners
                android:topLeftRadius="10dp"
                android:topRightRadius="4dp"
                android:bottomLeftRadius="4dp"
                android:bottomRightRadius="4dp"/>
        </shape>
    </item>
    <item>
        <shape android:tint="?attr/grey">
            <size
                android:height="10dp"
                android:width="10dp"/>
            <corners
                android:topLeftRadius="10dp"
                android:topRightRadius="4dp"
                android:bottomLeftRadius="4dp"
                android:bottomRightRadius="4dp"/>
        </shape>
    </item>
</selector>
