// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.media3.ui.PlayerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityChargingOverlayBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView batteryPercent;

  @NonNull
  public final LinearLayout dateTimeContainer;

  @NonNull
  public final PlayerView playerView;

  @NonNull
  public final TextView textDate;

  @NonNull
  public final TextView textTime;

  private ActivityChargingOverlayBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView batteryPercent, @NonNull LinearLayout dateTimeContainer,
      @NonNull PlayerView playerView, @NonNull TextView textDate, @NonNull TextView textTime) {
    this.rootView = rootView;
    this.batteryPercent = batteryPercent;
    this.dateTimeContainer = dateTimeContainer;
    this.playerView = playerView;
    this.textDate = textDate;
    this.textTime = textTime;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityChargingOverlayBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityChargingOverlayBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_charging_overlay, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityChargingOverlayBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.battery_percent;
      TextView batteryPercent = ViewBindings.findChildViewById(rootView, id);
      if (batteryPercent == null) {
        break missingId;
      }

      id = R.id.date_time_container;
      LinearLayout dateTimeContainer = ViewBindings.findChildViewById(rootView, id);
      if (dateTimeContainer == null) {
        break missingId;
      }

      id = R.id.playerView;
      PlayerView playerView = ViewBindings.findChildViewById(rootView, id);
      if (playerView == null) {
        break missingId;
      }

      id = R.id.text_date;
      TextView textDate = ViewBindings.findChildViewById(rootView, id);
      if (textDate == null) {
        break missingId;
      }

      id = R.id.text_time;
      TextView textTime = ViewBindings.findChildViewById(rootView, id);
      if (textTime == null) {
        break missingId;
      }

      return new ActivityChargingOverlayBinding((ConstraintLayout) rootView, batteryPercent,
          dateTimeContainer, playerView, textDate, textTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
