<?xml version="1.0" encoding="utf-8"?>


<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/display"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
    android:background="?attr/grey"
    android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
    <androidx.viewpager.widget.ViewPager
        android:id="@+id/slide_pager"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
    <com.tbuonomo.viewpagerdotsindicator.SpringDotsIndicator
        android:id="@+id/spring_dots_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        app:dampingRatio="0.5"
        app:dotsColor="?attr/black"
        app:dotsCornerRadius="10dp"
        app:dotsSize="9dp"
        app:dotsSpacing="2dp"
        app:dotsStrokeColor="?attr/black"
        app:dotsStrokeWidth="1.2dp"
        app:stiffness="300"/>
</RelativeLayout>


    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/nativeAd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/grey" />


</LinearLayout>