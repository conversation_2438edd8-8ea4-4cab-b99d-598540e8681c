// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAppPowerConsumptionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnGrantPermission;

  @NonNull
  public final ImageView ivCloseDialog;

  @NonNull
  public final LinearLayout llPermissionRequest;

  @NonNull
  public final ProgressBar progressLoading;

  @NonNull
  public final RecyclerView rvAppsList;

  @NonNull
  public final TextView tvDialogTitle;

  @NonNull
  public final TextView tvErrorMessage;

  @NonNull
  public final TextView tvNoDataMessage;

  @NonNull
  public final TextView tvPermissionStatus;

  @NonNull
  public final TextView tvSessionDuration;

  @NonNull
  public final TextView tvSkipPermission;

  @NonNull
  public final TextView tvTotalConsumption;

  private DialogAppPowerConsumptionBinding(@NonNull LinearLayout rootView,
      @NonNull Button btnGrantPermission, @NonNull ImageView ivCloseDialog,
      @NonNull LinearLayout llPermissionRequest, @NonNull ProgressBar progressLoading,
      @NonNull RecyclerView rvAppsList, @NonNull TextView tvDialogTitle,
      @NonNull TextView tvErrorMessage, @NonNull TextView tvNoDataMessage,
      @NonNull TextView tvPermissionStatus, @NonNull TextView tvSessionDuration,
      @NonNull TextView tvSkipPermission, @NonNull TextView tvTotalConsumption) {
    this.rootView = rootView;
    this.btnGrantPermission = btnGrantPermission;
    this.ivCloseDialog = ivCloseDialog;
    this.llPermissionRequest = llPermissionRequest;
    this.progressLoading = progressLoading;
    this.rvAppsList = rvAppsList;
    this.tvDialogTitle = tvDialogTitle;
    this.tvErrorMessage = tvErrorMessage;
    this.tvNoDataMessage = tvNoDataMessage;
    this.tvPermissionStatus = tvPermissionStatus;
    this.tvSessionDuration = tvSessionDuration;
    this.tvSkipPermission = tvSkipPermission;
    this.tvTotalConsumption = tvTotalConsumption;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAppPowerConsumptionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAppPowerConsumptionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_app_power_consumption, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAppPowerConsumptionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_grant_permission;
      Button btnGrantPermission = ViewBindings.findChildViewById(rootView, id);
      if (btnGrantPermission == null) {
        break missingId;
      }

      id = R.id.iv_close_dialog;
      ImageView ivCloseDialog = ViewBindings.findChildViewById(rootView, id);
      if (ivCloseDialog == null) {
        break missingId;
      }

      id = R.id.ll_permission_request;
      LinearLayout llPermissionRequest = ViewBindings.findChildViewById(rootView, id);
      if (llPermissionRequest == null) {
        break missingId;
      }

      id = R.id.progress_loading;
      ProgressBar progressLoading = ViewBindings.findChildViewById(rootView, id);
      if (progressLoading == null) {
        break missingId;
      }

      id = R.id.rv_apps_list;
      RecyclerView rvAppsList = ViewBindings.findChildViewById(rootView, id);
      if (rvAppsList == null) {
        break missingId;
      }

      id = R.id.tv_dialog_title;
      TextView tvDialogTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvDialogTitle == null) {
        break missingId;
      }

      id = R.id.tv_error_message;
      TextView tvErrorMessage = ViewBindings.findChildViewById(rootView, id);
      if (tvErrorMessage == null) {
        break missingId;
      }

      id = R.id.tv_no_data_message;
      TextView tvNoDataMessage = ViewBindings.findChildViewById(rootView, id);
      if (tvNoDataMessage == null) {
        break missingId;
      }

      id = R.id.tv_permission_status;
      TextView tvPermissionStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvPermissionStatus == null) {
        break missingId;
      }

      id = R.id.tv_session_duration;
      TextView tvSessionDuration = ViewBindings.findChildViewById(rootView, id);
      if (tvSessionDuration == null) {
        break missingId;
      }

      id = R.id.tv_skip_permission;
      TextView tvSkipPermission = ViewBindings.findChildViewById(rootView, id);
      if (tvSkipPermission == null) {
        break missingId;
      }

      id = R.id.tv_total_consumption;
      TextView tvTotalConsumption = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalConsumption == null) {
        break missingId;
      }

      return new DialogAppPowerConsumptionBinding((LinearLayout) rootView, btnGrantPermission,
          ivCloseDialog, llPermissionRequest, progressLoading, rvAppsList, tvDialogTitle,
          tvErrorMessage, tvNoDataMessage, tvPermissionStatus, tvSessionDuration, tvSkipPermission,
          tvTotalConsumption);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
