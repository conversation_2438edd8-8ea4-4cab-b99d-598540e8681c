package com.tqhit.battery.one.features.navigation;

import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppNavigator_Factory implements Factory<AppNavigator> {
  private final Provider<DynamicNavigationManager> dynamicNavigationManagerProvider;

  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  public AppNavigator_Factory(Provider<DynamicNavigationManager> dynamicNavigationManagerProvider,
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider) {
    this.dynamicNavigationManagerProvider = dynamicNavigationManagerProvider;
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
  }

  @Override
  public AppNavigator get() {
    return newInstance(dynamicNavigationManagerProvider.get(), coreBatteryStatsProvider.get());
  }

  public static AppNavigator_Factory create(
      Provider<DynamicNavigationManager> dynamicNavigationManagerProvider,
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider) {
    return new AppNavigator_Factory(dynamicNavigationManagerProvider, coreBatteryStatsProvider);
  }

  public static AppNavigator newInstance(DynamicNavigationManager dynamicNavigationManager,
      CoreBatteryStatsProvider coreBatteryStatsProvider) {
    return new AppNavigator(dynamicNavigationManager, coreBatteryStatsProvider);
  }
}
