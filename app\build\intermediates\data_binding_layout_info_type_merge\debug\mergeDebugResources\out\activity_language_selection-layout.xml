<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_language_selection" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\activity_language_selection.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_language_selection_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="344" endOffset="14"/></Target><Target id="@+id/titleText" view="TextView"><Expressions/><location startLine="20" startOffset="4" endLine="33" endOffset="9"/></Target><Target id="@+id/subtitleText" view="TextView"><Expressions/><location startLine="36" startOffset="4" endLine="49" endOffset="51"/></Target><Target id="@+id/next_button" view="TextView"><Expressions/><location startLine="52" startOffset="4" endLine="68" endOffset="69"/></Target><Target id="@+id/de" view="TextView"><Expressions/><location startLine="85" startOffset="12" endLine="97" endOffset="49"/></Target><Target id="@+id/nl" view="TextView"><Expressions/><location startLine="99" startOffset="12" endLine="114" endOffset="49"/></Target><Target id="@+id/en" view="TextView"><Expressions/><location startLine="116" startOffset="12" endLine="131" endOffset="49"/></Target><Target id="@+id/es" view="TextView"><Expressions/><location startLine="133" startOffset="12" endLine="148" endOffset="49"/></Target><Target id="@+id/fr" view="TextView"><Expressions/><location startLine="150" startOffset="12" endLine="165" endOffset="49"/></Target><Target id="@+id/it" view="TextView"><Expressions/><location startLine="167" startOffset="12" endLine="182" endOffset="49"/></Target><Target id="@+id/hu" view="TextView"><Expressions/><location startLine="184" startOffset="12" endLine="199" endOffset="49"/></Target><Target id="@+id/pl" view="TextView"><Expressions/><location startLine="201" startOffset="12" endLine="216" endOffset="49"/></Target><Target id="@+id/pt" view="TextView"><Expressions/><location startLine="218" startOffset="12" endLine="233" endOffset="49"/></Target><Target id="@+id/ro" view="TextView"><Expressions/><location startLine="235" startOffset="12" endLine="250" endOffset="49"/></Target><Target id="@+id/tr" view="TextView"><Expressions/><location startLine="252" startOffset="12" endLine="267" endOffset="49"/></Target><Target id="@+id/ru" view="TextView"><Expressions/><location startLine="269" startOffset="12" endLine="284" endOffset="49"/></Target><Target id="@+id/ua" view="TextView"><Expressions/><location startLine="286" startOffset="12" endLine="301" endOffset="49"/></Target><Target id="@+id/ar" view="TextView"><Expressions/><location startLine="303" startOffset="12" endLine="318" endOffset="49"/></Target><Target id="@+id/zh" view="TextView"><Expressions/><location startLine="320" startOffset="12" endLine="334" endOffset="49"/></Target><Target id="@+id/nativeAd" view="com.facebook.shimmer.ShimmerFrameLayout"><Expressions/><location startLine="339" startOffset="4" endLine="343" endOffset="41"/></Target></Targets></Layout>