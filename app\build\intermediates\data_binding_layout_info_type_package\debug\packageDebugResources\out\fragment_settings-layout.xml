<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_settings" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView" rootNodeViewId="@+id/scroll_view"><Targets><Target id="@+id/scroll_view" tag="layout/fragment_settings_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="1389" endOffset="39"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="5" startOffset="4" endLine="1388" endOffset="18"/></Target><Target id="@+id/include_back_navigation" tag="binding_1" include="layout_back_navigation"><Expressions/><location startLine="11" startOffset="8" endLine="13" endOffset="53"/></Target><Target id="@+id/p6" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="27" startOffset="12" endLine="69" endOffset="63"/></Target><Target id="@+id/p_12334" view="TextView"><Expressions/><location startLine="35" startOffset="16" endLine="53" endOffset="62"/></Target><Target id="@+id/switch_vibration" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="54" startOffset="16" endLine="68" endOffset="55"/></Target><Target id="@+id/p5" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="70" startOffset="12" endLine="113" endOffset="63"/></Target><Target id="@+id/p_11" view="TextView"><Expressions/><location startLine="79" startOffset="16" endLine="97" endOffset="62"/></Target><Target id="@+id/switch_info" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="98" startOffset="16" endLine="112" endOffset="55"/></Target><Target id="@+id/p_t" view="TextView"><Expressions/><location startLine="114" startOffset="12" endLine="123" endOffset="58"/></Target><Target id="@+id/change_theme" view="TextView"><Expressions/><location startLine="124" startOffset="12" endLine="142" endOffset="63"/></Target><Target id="@+id/change_icon" view="TextView"><Expressions/><location startLine="143" startOffset="12" endLine="162" endOffset="85"/></Target><Target id="@+id/change_second_color_theme" view="TextView"><Expressions/><location startLine="163" startOffset="12" endLine="181" endOffset="72"/></Target><Target id="@+id/change_lang" view="TextView"><Expressions/><location startLine="182" startOffset="12" endLine="200" endOffset="71"/></Target><Target id="@+id/change_temp" view="TextView"><Expressions/><location startLine="201" startOffset="12" endLine="220" endOffset="71"/></Target><Target id="@+id/n_t" view="TextView"><Expressions/><location startLine="235" startOffset="12" endLine="244" endOffset="58"/></Target><Target id="@+id/n_1.1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="245" startOffset="12" endLine="287" endOffset="63"/></Target><Target id="@+id/n1.1" view="TextView"><Expressions/><location startLine="254" startOffset="16" endLine="274" endOffset="62"/></Target><Target id="@+id/switch_is_charge_notify" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="275" startOffset="16" endLine="286" endOffset="81"/></Target><Target id="@+id/n_1.2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="288" startOffset="12" endLine="330" endOffset="63"/></Target><Target id="@+id/n1.2" view="TextView"><Expressions/><location startLine="297" startOffset="16" endLine="317" endOffset="62"/></Target><Target id="@+id/switch_is_discharge_notify" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="318" startOffset="16" endLine="329" endOffset="81"/></Target><Target id="@+id/n_2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="331" startOffset="12" endLine="373" endOffset="63"/></Target><Target id="@+id/n2" view="TextView"><Expressions/><location startLine="340" startOffset="16" endLine="360" endOffset="62"/></Target><Target id="@+id/switch_is_showed_on_lockscreen" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="361" startOffset="16" endLine="372" endOffset="81"/></Target><Target id="@+id/n_3" view="TextView"><Expressions/><location startLine="374" startOffset="12" endLine="393" endOffset="63"/></Target><Target id="@+id/button_change_notify" view="TextView"><Expressions/><location startLine="394" startOffset="12" endLine="415" endOffset="63"/></Target><Target id="@+id/button_change_frequency" view="TextView"><Expressions/><location startLine="416" startOffset="12" endLine="437" endOffset="80"/></Target><Target id="@+id/button_change_notify_icon" view="TextView"><Expressions/><location startLine="438" startOffset="12" endLine="460" endOffset="83"/></Target><Target id="@+id/button_settings_notify" view="TextView"><Expressions/><location startLine="461" startOffset="12" endLine="481" endOffset="85"/></Target><Target id="@+id/animation_title_block" view="TextView"><Expressions/><location startLine="496" startOffset="12" endLine="505" endOffset="58"/></Target><Target id="@+id/switch_animation_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="506" startOffset="12" endLine="548" endOffset="63"/></Target><Target id="@+id/switch_animation_title" view="TextView"><Expressions/><location startLine="515" startOffset="16" endLine="535" endOffset="62"/></Target><Target id="@+id/switch_enable_animation" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="536" startOffset="16" endLine="547" endOffset="81"/></Target><Target id="@+id/switch_animation_time_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="549" startOffset="12" endLine="591" endOffset="63"/></Target><Target id="@+id/switch_animation_time_title" view="TextView"><Expressions/><location startLine="558" startOffset="16" endLine="578" endOffset="62"/></Target><Target id="@+id/switch_enable_animation_time" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="579" startOffset="16" endLine="590" endOffset="81"/></Target><Target id="@+id/anti_thief_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="593" startOffset="8" endLine="713" endOffset="59"/></Target><Target id="@+id/anti_thief_title_block" view="TextView"><Expressions/><location startLine="607" startOffset="12" endLine="616" endOffset="58"/></Target><Target id="@+id/anti_thief_info" view="ImageView"><Expressions/><location startLine="617" startOffset="12" endLine="626" endOffset="58"/></Target><Target id="@+id/switch_anti_thief_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="627" startOffset="12" endLine="669" endOffset="63"/></Target><Target id="@+id/switch_anti_thief_title" view="TextView"><Expressions/><location startLine="636" startOffset="16" endLine="656" endOffset="62"/></Target><Target id="@+id/switch_enable_anti_thief" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="657" startOffset="16" endLine="668" endOffset="81"/></Target><Target id="@+id/switch_anti_thief_sound_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="670" startOffset="12" endLine="712" endOffset="63"/></Target><Target id="@+id/switch_anti_thief_sound_title" view="TextView"><Expressions/><location startLine="679" startOffset="16" endLine="699" endOffset="62"/></Target><Target id="@+id/switch_enable_anti_thief_sound" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="700" startOffset="16" endLine="711" endOffset="81"/></Target><Target id="@+id/change_BLM_button" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="727" startOffset="12" endLine="770" endOffset="63"/></Target><Target id="@+id/switch_BLM" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="758" startOffset="16" endLine="769" endOffset="81"/></Target><Target id="@+id/hand_reset_button" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="771" startOffset="12" endLine="813" endOffset="63"/></Target><Target id="@+id/switch_hand_reset_session" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="801" startOffset="16" endLine="812" endOffset="81"/></Target><Target id="@+id/import_database" view="TextView"><Expressions/><location startLine="814" startOffset="12" endLine="834" endOffset="74"/></Target><Target id="@+id/export_database" view="TextView"><Expressions/><location startLine="835" startOffset="12" endLine="855" endOffset="75"/></Target><Target id="@+id/change_dual_battery" view="TextView"><Expressions/><location startLine="856" startOffset="12" endLine="873" endOffset="75"/></Target><Target id="@+id/d1" view="TextView"><Expressions/><location startLine="874" startOffset="12" endLine="883" endOffset="58"/></Target><Target id="@+id/current_session_amperage_button" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="884" startOffset="12" endLine="926" endOffset="63"/></Target><Target id="@+id/current_session_amperage_session" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="914" startOffset="16" endLine="925" endOffset="81"/></Target><Target id="@+id/heand_stab_database" view="TextView"><Expressions/><location startLine="927" startOffset="12" endLine="947" endOffset="91"/></Target><Target id="@+id/auto_stab_database" view="TextView"><Expressions/><location startLine="948" startOffset="12" endLine="968" endOffset="79"/></Target><Target id="@+id/clear_database" view="TextView"><Expressions/><location startLine="969" startOffset="12" endLine="989" endOffset="78"/></Target><Target id="@+id/change_capacity" view="TextView"><Expressions/><location startLine="990" startOffset="12" endLine="1007" endOffset="79"/></Target><Target id="@+id/indent_down" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1009" startOffset="8" endLine="1338" endOffset="59"/></Target><Target id="@+id/battery_info" view="TextView"><Expressions/><location startLine="1024" startOffset="12" endLine="1043" endOffset="63"/></Target><Target id="@+id/privacy_setting_button" view="TextView"><Expressions/><location startLine="1044" startOffset="12" endLine="1062" endOffset="84"/></Target><Target id="@+id/privacy_button" view="TextView"><Expressions/><location startLine="1063" startOffset="12" endLine="1081" endOffset="82"/></Target><Target id="@+id/debugger_button" view="TextView"><Expressions/><location startLine="1082" startOffset="12" endLine="1101" endOffset="74"/></Target><Target id="@+id/mediation_debugger_button" view="TextView"><Expressions/><location startLine="1103" startOffset="12" endLine="1122" endOffset="75"/></Target><Target id="@+id/about_translations" view="TextView"><Expressions/><location startLine="1123" startOffset="12" endLine="1142" endOffset="68"/></Target><Target id="@+id/write_me" view="TextView"><Expressions/><location startLine="1143" startOffset="12" endLine="1162" endOffset="77"/></Target><Target id="@+id/a_t" view="TextView"><Expressions/><location startLine="1164" startOffset="12" endLine="1176" endOffset="59"/></Target><Target id="@+id/remove_ads" view="TextView"><Expressions/><location startLine="1178" startOffset="12" endLine="1198" endOffset="72"/></Target><Target id="@+id/buy_advance_access" view="TextView"><Expressions/><location startLine="1199" startOffset="12" endLine="1218" endOffset="70"/></Target><Target id="@+id/work_in_backgound_button" view="TextView"><Expressions/><location startLine="1219" startOffset="12" endLine="1237" endOffset="78"/></Target><Target id="@+id/reset_purchases_button" view="TextView"><Expressions/><location startLine="1238" startOffset="12" endLine="1257" endOffset="74"/></Target><Target id="@+id/rate_button" view="TextView"><Expressions/><location startLine="1258" startOffset="12" endLine="1277" endOffset="82"/></Target><Target id="@+id/support_me_button" view="TextView"><Expressions/><location startLine="1278" startOffset="12" endLine="1297" endOffset="71"/></Target><Target id="@+id/version_app_button" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1298" startOffset="12" endLine="1337" endOffset="63"/></Target><Target id="@+id/ertretr" view="LinearLayout"><Expressions/><location startLine="1310" startOffset="16" endLine="1336" endOffset="30"/></Target><Target id="@+id/version_app" view="TextView"><Expressions/><location startLine="1329" startOffset="20" endLine="1335" endOffset="52"/></Target><Target id="@+id/debug_title" view="TextView"><Expressions/><location startLine="1355" startOffset="12" endLine="1364" endOffset="58"/></Target><Target id="@+id/test_new_discharge" view="TextView"><Expressions/><location startLine="1366" startOffset="12" endLine="1384" endOffset="71"/></Target></Targets></Layout>