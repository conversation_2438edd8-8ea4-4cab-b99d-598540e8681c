<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_test" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\activity_test.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_test_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="69" endOffset="14"/></Target><Target id="@+id/fragment_container" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="7" startOffset="4" endLine="11" endOffset="35"/></Target><Target id="@+id/test_controls_layout" view="LinearLayout"><Expressions/><location startLine="14" startOffset="4" endLine="68" endOffset="18"/></Target><Target id="@+id/service_status_text" view="TextView"><Expressions/><location startLine="30" startOffset="8" endLine="36" endOffset="46"/></Target><Target id="@+id/start_service_button" view="Button"><Expressions/><location startLine="43" startOffset="12" endLine="49" endOffset="47"/></Target><Target id="@+id/stop_service_button" view="Button"><Expressions/><location startLine="51" startOffset="12" endLine="58" endOffset="47"/></Target><Target id="@+id/check_service_button" view="Button"><Expressions/><location startLine="60" startOffset="12" endLine="66" endOffset="49"/></Target></Targets></Layout>