// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.github.mikephil.charting.charts.LineChart;
import com.tqhit.battery.one.R;
import com.tqhit.battery.one.component.progress.VerticalProgressBar;
import eightbitlab.com.blurview.BlurView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentHealthBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final BlurView blurViewDeadTop;

  @NonNull
  public final TextView btn0;

  @NonNull
  public final TextView btn0T;

  @NonNull
  public final TextView btn1;

  @NonNull
  public final TextView btn1T;

  @NonNull
  public final TextView btn2;

  @NonNull
  public final TextView btn2T;

  @NonNull
  public final TextView btn3;

  @NonNull
  public final TextView btn3T;

  @NonNull
  public final LineChart chart1;

  @NonNull
  public final RelativeLayout chart1L;

  @NonNull
  public final RelativeLayout chart1Percent;

  @NonNull
  public final LineChart chartPercent;

  @NonNull
  public final ConstraintLayout cumulative1;

  @NonNull
  public final TextView cumulative31;

  @NonNull
  public final TextView cumulative32;

  @NonNull
  public final ConstraintLayout cumulativeBtn;

  @NonNull
  public final ConstraintLayout cumulativeCalculated;

  @NonNull
  public final TextView cumulativeCapacityNi;

  @NonNull
  public final ConstraintLayout cumulativeSessionInfo;

  @NonNull
  public final ConstraintLayout d2;

  @NonNull
  public final TextView d21;

  @NonNull
  public final TextView d22;

  @NonNull
  public final TextView dT;

  @NonNull
  public final ProgressBar damageBarPercentCurrent;

  @NonNull
  public final ProgressBar damageBarPercentCurrentSingular;

  @NonNull
  public final ProgressBar damageBarSeekwhite;

  @NonNull
  public final ProgressBar damageBarSeekwhiteSingular;

  @NonNull
  public final TextView day1;

  @NonNull
  public final TextView day1Percent;

  @NonNull
  public final TextView day1Temp;

  @NonNull
  public final TextView day2;

  @NonNull
  public final TextView day2Percent;

  @NonNull
  public final TextView day2Temp;

  @NonNull
  public final TextView day3;

  @NonNull
  public final TextView day3Percent;

  @NonNull
  public final TextView day3Temp;

  @NonNull
  public final TextView day4;

  @NonNull
  public final TextView day4Percent;

  @NonNull
  public final TextView day4Temp;

  @NonNull
  public final TextView day5;

  @NonNull
  public final TextView day5Percent;

  @NonNull
  public final TextView day5Temp;

  @NonNull
  public final TextView day6;

  @NonNull
  public final TextView day6Percent;

  @NonNull
  public final TextView day6Temp;

  @NonNull
  public final TextView day7;

  @NonNull
  public final TextView day7Percent;

  @NonNull
  public final TextView day7Temp;

  @NonNull
  public final TextView deadTimeText;

  @NonNull
  public final ConstraintLayout deadTimeUp;

  @NonNull
  public final LinearLayout deadTimeUpCummulative;

  @NonNull
  public final LinearLayout deadTimeUpSingular;

  @NonNull
  public final LinearLayout degreeOfWear;

  @NonNull
  public final ImageView degreeWearInfo;

  @NonNull
  public final RelativeLayout grahpTempViewgroup;

  @NonNull
  public final RelativeLayout graph;

  @NonNull
  public final RelativeLayout graphPercent;

  @NonNull
  public final RelativeLayout graphTemp;

  @NonNull
  public final LinearLayout healthAccess2;

  @NonNull
  public final TextView healthCheckedBateryCapacityCumulative;

  @NonNull
  public final TextView healthCheckedBateryCapacitySingular;

  @NonNull
  public final TextView healthCountOfSessionsCumulative;

  @NonNull
  public final TextView healthCountOfSessionsSingular;

  @NonNull
  public final ProgressBar healthFirstProgressbarCumulative;

  @NonNull
  public final ProgressBar healthFirstProgressbarSingular;

  @NonNull
  public final TextView healthFullBateryCapacity;

  @NonNull
  public final TextView healthPercentDamageCumulative;

  @NonNull
  public final TextView healthPercentDamageSingular;

  @NonNull
  public final TextView historyDatabase;

  @NonNull
  public final LayoutBackNavigationBinding includeBackNavigation;

  @NonNull
  public final ConstraintLayout indentDown;

  @NonNull
  public final TextView methodText;

  @NonNull
  public final TextView methodTextSingular;

  @NonNull
  public final ShimmerFrameLayout nativeAd;

  @NonNull
  public final TextView percentCumulative;

  @NonNull
  public final LinearLayout percentDamage;

  @NonNull
  public final LinearLayout percentDamageCumulative;

  @NonNull
  public final TextView percentDamageDead;

  @NonNull
  public final TextView percentDamageDeadSingular;

  @NonNull
  public final ConstraintLayout percentGraphChange;

  @NonNull
  public final TextView percentSingular;

  @NonNull
  public final ImageView predictionWearInfo;

  @NonNull
  public final VerticalProgressBar progbar1;

  @NonNull
  public final VerticalProgressBar progbar2;

  @NonNull
  public final VerticalProgressBar progbar3;

  @NonNull
  public final VerticalProgressBar progbar4;

  @NonNull
  public final VerticalProgressBar progbar5;

  @NonNull
  public final VerticalProgressBar progbar6;

  @NonNull
  public final VerticalProgressBar progbar7;

  @NonNull
  public final RelativeLayout proggersBarDamage;

  @NonNull
  public final RelativeLayout proggersBarDamageCumulative;

  @NonNull
  public final NestedScrollView scrollView;

  @NonNull
  public final TextView sdfsd;

  @NonNull
  public final SeekBar seekBar;

  @NonNull
  public final SeekBar seekBarSingular;

  @NonNull
  public final ConstraintLayout singular1;

  @NonNull
  public final TextView singular31;

  @NonNull
  public final TextView singular32;

  @NonNull
  public final ConstraintLayout singularBtn;

  @NonNull
  public final ConstraintLayout singularCalculated;

  @NonNull
  public final TextView singularCapacityNi;

  @NonNull
  public final ConstraintLayout singularSessionInfo;

  @NonNull
  public final TextView t1;

  @NonNull
  public final TextView t1Temp;

  @NonNull
  public final TextView t2;

  @NonNull
  public final TextView t2Temp;

  @NonNull
  public final TextView t3;

  @NonNull
  public final TextView t3Temp;

  @NonNull
  public final TextView t4;

  @NonNull
  public final TextView t4Temp;

  @NonNull
  public final TextView t5;

  @NonNull
  public final TextView t5Temp;

  @NonNull
  public final TextView t6;

  @NonNull
  public final TextView t6Temp;

  @NonNull
  public final TextView t7;

  @NonNull
  public final TextView t7Temp;

  @NonNull
  public final TextView t8;

  @NonNull
  public final TextView t8Temp;

  @NonNull
  public final TextView t9;

  @NonNull
  public final TextView t9Temp;

  @NonNull
  public final TextView textDead;

  @NonNull
  public final TextView textDeadSingular;

  @NonNull
  public final TextView textRemainVarCumulative;

  @NonNull
  public final TextView textRemainVarSingular;

  @NonNull
  public final RelativeLayout textTopDeadTime111;

  @NonNull
  public final RelativeLayout textTopDeadTime111Cumulative;

  @NonNull
  public final ConstraintLayout timeDeadViewgroup;

  @NonNull
  public final ConstraintLayout timeGraphChange;

  @NonNull
  public final LinearLayout underGraph;

  @NonNull
  public final LinearLayout underGraphPercent;

  @NonNull
  public final LinearLayout underGraphTemp;

  @NonNull
  public final LinearLayout updateView;

  @NonNull
  public final TextView updateViewBtn;

  @NonNull
  public final TextView wearRate2;

  @NonNull
  public final TextView wearRate3;

  @NonNull
  public final TextView wearRatePercent;

  @NonNull
  public final LinearLayout whyINeedToDoThis;

  private FragmentHealthBinding(@NonNull NestedScrollView rootView,
      @NonNull BlurView blurViewDeadTop, @NonNull TextView btn0, @NonNull TextView btn0T,
      @NonNull TextView btn1, @NonNull TextView btn1T, @NonNull TextView btn2,
      @NonNull TextView btn2T, @NonNull TextView btn3, @NonNull TextView btn3T,
      @NonNull LineChart chart1, @NonNull RelativeLayout chart1L,
      @NonNull RelativeLayout chart1Percent, @NonNull LineChart chartPercent,
      @NonNull ConstraintLayout cumulative1, @NonNull TextView cumulative31,
      @NonNull TextView cumulative32, @NonNull ConstraintLayout cumulativeBtn,
      @NonNull ConstraintLayout cumulativeCalculated, @NonNull TextView cumulativeCapacityNi,
      @NonNull ConstraintLayout cumulativeSessionInfo, @NonNull ConstraintLayout d2,
      @NonNull TextView d21, @NonNull TextView d22, @NonNull TextView dT,
      @NonNull ProgressBar damageBarPercentCurrent,
      @NonNull ProgressBar damageBarPercentCurrentSingular, @NonNull ProgressBar damageBarSeekwhite,
      @NonNull ProgressBar damageBarSeekwhiteSingular, @NonNull TextView day1,
      @NonNull TextView day1Percent, @NonNull TextView day1Temp, @NonNull TextView day2,
      @NonNull TextView day2Percent, @NonNull TextView day2Temp, @NonNull TextView day3,
      @NonNull TextView day3Percent, @NonNull TextView day3Temp, @NonNull TextView day4,
      @NonNull TextView day4Percent, @NonNull TextView day4Temp, @NonNull TextView day5,
      @NonNull TextView day5Percent, @NonNull TextView day5Temp, @NonNull TextView day6,
      @NonNull TextView day6Percent, @NonNull TextView day6Temp, @NonNull TextView day7,
      @NonNull TextView day7Percent, @NonNull TextView day7Temp, @NonNull TextView deadTimeText,
      @NonNull ConstraintLayout deadTimeUp, @NonNull LinearLayout deadTimeUpCummulative,
      @NonNull LinearLayout deadTimeUpSingular, @NonNull LinearLayout degreeOfWear,
      @NonNull ImageView degreeWearInfo, @NonNull RelativeLayout grahpTempViewgroup,
      @NonNull RelativeLayout graph, @NonNull RelativeLayout graphPercent,
      @NonNull RelativeLayout graphTemp, @NonNull LinearLayout healthAccess2,
      @NonNull TextView healthCheckedBateryCapacityCumulative,
      @NonNull TextView healthCheckedBateryCapacitySingular,
      @NonNull TextView healthCountOfSessionsCumulative,
      @NonNull TextView healthCountOfSessionsSingular,
      @NonNull ProgressBar healthFirstProgressbarCumulative,
      @NonNull ProgressBar healthFirstProgressbarSingular,
      @NonNull TextView healthFullBateryCapacity, @NonNull TextView healthPercentDamageCumulative,
      @NonNull TextView healthPercentDamageSingular, @NonNull TextView historyDatabase,
      @NonNull LayoutBackNavigationBinding includeBackNavigation,
      @NonNull ConstraintLayout indentDown, @NonNull TextView methodText,
      @NonNull TextView methodTextSingular, @NonNull ShimmerFrameLayout nativeAd,
      @NonNull TextView percentCumulative, @NonNull LinearLayout percentDamage,
      @NonNull LinearLayout percentDamageCumulative, @NonNull TextView percentDamageDead,
      @NonNull TextView percentDamageDeadSingular, @NonNull ConstraintLayout percentGraphChange,
      @NonNull TextView percentSingular, @NonNull ImageView predictionWearInfo,
      @NonNull VerticalProgressBar progbar1, @NonNull VerticalProgressBar progbar2,
      @NonNull VerticalProgressBar progbar3, @NonNull VerticalProgressBar progbar4,
      @NonNull VerticalProgressBar progbar5, @NonNull VerticalProgressBar progbar6,
      @NonNull VerticalProgressBar progbar7, @NonNull RelativeLayout proggersBarDamage,
      @NonNull RelativeLayout proggersBarDamageCumulative, @NonNull NestedScrollView scrollView,
      @NonNull TextView sdfsd, @NonNull SeekBar seekBar, @NonNull SeekBar seekBarSingular,
      @NonNull ConstraintLayout singular1, @NonNull TextView singular31,
      @NonNull TextView singular32, @NonNull ConstraintLayout singularBtn,
      @NonNull ConstraintLayout singularCalculated, @NonNull TextView singularCapacityNi,
      @NonNull ConstraintLayout singularSessionInfo, @NonNull TextView t1, @NonNull TextView t1Temp,
      @NonNull TextView t2, @NonNull TextView t2Temp, @NonNull TextView t3,
      @NonNull TextView t3Temp, @NonNull TextView t4, @NonNull TextView t4Temp,
      @NonNull TextView t5, @NonNull TextView t5Temp, @NonNull TextView t6,
      @NonNull TextView t6Temp, @NonNull TextView t7, @NonNull TextView t7Temp,
      @NonNull TextView t8, @NonNull TextView t8Temp, @NonNull TextView t9,
      @NonNull TextView t9Temp, @NonNull TextView textDead, @NonNull TextView textDeadSingular,
      @NonNull TextView textRemainVarCumulative, @NonNull TextView textRemainVarSingular,
      @NonNull RelativeLayout textTopDeadTime111,
      @NonNull RelativeLayout textTopDeadTime111Cumulative,
      @NonNull ConstraintLayout timeDeadViewgroup, @NonNull ConstraintLayout timeGraphChange,
      @NonNull LinearLayout underGraph, @NonNull LinearLayout underGraphPercent,
      @NonNull LinearLayout underGraphTemp, @NonNull LinearLayout updateView,
      @NonNull TextView updateViewBtn, @NonNull TextView wearRate2, @NonNull TextView wearRate3,
      @NonNull TextView wearRatePercent, @NonNull LinearLayout whyINeedToDoThis) {
    this.rootView = rootView;
    this.blurViewDeadTop = blurViewDeadTop;
    this.btn0 = btn0;
    this.btn0T = btn0T;
    this.btn1 = btn1;
    this.btn1T = btn1T;
    this.btn2 = btn2;
    this.btn2T = btn2T;
    this.btn3 = btn3;
    this.btn3T = btn3T;
    this.chart1 = chart1;
    this.chart1L = chart1L;
    this.chart1Percent = chart1Percent;
    this.chartPercent = chartPercent;
    this.cumulative1 = cumulative1;
    this.cumulative31 = cumulative31;
    this.cumulative32 = cumulative32;
    this.cumulativeBtn = cumulativeBtn;
    this.cumulativeCalculated = cumulativeCalculated;
    this.cumulativeCapacityNi = cumulativeCapacityNi;
    this.cumulativeSessionInfo = cumulativeSessionInfo;
    this.d2 = d2;
    this.d21 = d21;
    this.d22 = d22;
    this.dT = dT;
    this.damageBarPercentCurrent = damageBarPercentCurrent;
    this.damageBarPercentCurrentSingular = damageBarPercentCurrentSingular;
    this.damageBarSeekwhite = damageBarSeekwhite;
    this.damageBarSeekwhiteSingular = damageBarSeekwhiteSingular;
    this.day1 = day1;
    this.day1Percent = day1Percent;
    this.day1Temp = day1Temp;
    this.day2 = day2;
    this.day2Percent = day2Percent;
    this.day2Temp = day2Temp;
    this.day3 = day3;
    this.day3Percent = day3Percent;
    this.day3Temp = day3Temp;
    this.day4 = day4;
    this.day4Percent = day4Percent;
    this.day4Temp = day4Temp;
    this.day5 = day5;
    this.day5Percent = day5Percent;
    this.day5Temp = day5Temp;
    this.day6 = day6;
    this.day6Percent = day6Percent;
    this.day6Temp = day6Temp;
    this.day7 = day7;
    this.day7Percent = day7Percent;
    this.day7Temp = day7Temp;
    this.deadTimeText = deadTimeText;
    this.deadTimeUp = deadTimeUp;
    this.deadTimeUpCummulative = deadTimeUpCummulative;
    this.deadTimeUpSingular = deadTimeUpSingular;
    this.degreeOfWear = degreeOfWear;
    this.degreeWearInfo = degreeWearInfo;
    this.grahpTempViewgroup = grahpTempViewgroup;
    this.graph = graph;
    this.graphPercent = graphPercent;
    this.graphTemp = graphTemp;
    this.healthAccess2 = healthAccess2;
    this.healthCheckedBateryCapacityCumulative = healthCheckedBateryCapacityCumulative;
    this.healthCheckedBateryCapacitySingular = healthCheckedBateryCapacitySingular;
    this.healthCountOfSessionsCumulative = healthCountOfSessionsCumulative;
    this.healthCountOfSessionsSingular = healthCountOfSessionsSingular;
    this.healthFirstProgressbarCumulative = healthFirstProgressbarCumulative;
    this.healthFirstProgressbarSingular = healthFirstProgressbarSingular;
    this.healthFullBateryCapacity = healthFullBateryCapacity;
    this.healthPercentDamageCumulative = healthPercentDamageCumulative;
    this.healthPercentDamageSingular = healthPercentDamageSingular;
    this.historyDatabase = historyDatabase;
    this.includeBackNavigation = includeBackNavigation;
    this.indentDown = indentDown;
    this.methodText = methodText;
    this.methodTextSingular = methodTextSingular;
    this.nativeAd = nativeAd;
    this.percentCumulative = percentCumulative;
    this.percentDamage = percentDamage;
    this.percentDamageCumulative = percentDamageCumulative;
    this.percentDamageDead = percentDamageDead;
    this.percentDamageDeadSingular = percentDamageDeadSingular;
    this.percentGraphChange = percentGraphChange;
    this.percentSingular = percentSingular;
    this.predictionWearInfo = predictionWearInfo;
    this.progbar1 = progbar1;
    this.progbar2 = progbar2;
    this.progbar3 = progbar3;
    this.progbar4 = progbar4;
    this.progbar5 = progbar5;
    this.progbar6 = progbar6;
    this.progbar7 = progbar7;
    this.proggersBarDamage = proggersBarDamage;
    this.proggersBarDamageCumulative = proggersBarDamageCumulative;
    this.scrollView = scrollView;
    this.sdfsd = sdfsd;
    this.seekBar = seekBar;
    this.seekBarSingular = seekBarSingular;
    this.singular1 = singular1;
    this.singular31 = singular31;
    this.singular32 = singular32;
    this.singularBtn = singularBtn;
    this.singularCalculated = singularCalculated;
    this.singularCapacityNi = singularCapacityNi;
    this.singularSessionInfo = singularSessionInfo;
    this.t1 = t1;
    this.t1Temp = t1Temp;
    this.t2 = t2;
    this.t2Temp = t2Temp;
    this.t3 = t3;
    this.t3Temp = t3Temp;
    this.t4 = t4;
    this.t4Temp = t4Temp;
    this.t5 = t5;
    this.t5Temp = t5Temp;
    this.t6 = t6;
    this.t6Temp = t6Temp;
    this.t7 = t7;
    this.t7Temp = t7Temp;
    this.t8 = t8;
    this.t8Temp = t8Temp;
    this.t9 = t9;
    this.t9Temp = t9Temp;
    this.textDead = textDead;
    this.textDeadSingular = textDeadSingular;
    this.textRemainVarCumulative = textRemainVarCumulative;
    this.textRemainVarSingular = textRemainVarSingular;
    this.textTopDeadTime111 = textTopDeadTime111;
    this.textTopDeadTime111Cumulative = textTopDeadTime111Cumulative;
    this.timeDeadViewgroup = timeDeadViewgroup;
    this.timeGraphChange = timeGraphChange;
    this.underGraph = underGraph;
    this.underGraphPercent = underGraphPercent;
    this.underGraphTemp = underGraphTemp;
    this.updateView = updateView;
    this.updateViewBtn = updateViewBtn;
    this.wearRate2 = wearRate2;
    this.wearRate3 = wearRate3;
    this.wearRatePercent = wearRatePercent;
    this.whyINeedToDoThis = whyINeedToDoThis;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentHealthBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentHealthBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_health, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentHealthBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.blurView_dead_top;
      BlurView blurViewDeadTop = ViewBindings.findChildViewById(rootView, id);
      if (blurViewDeadTop == null) {
        break missingId;
      }

      id = R.id.btn0;
      TextView btn0 = ViewBindings.findChildViewById(rootView, id);
      if (btn0 == null) {
        break missingId;
      }

      id = R.id.btn0_t;
      TextView btn0T = ViewBindings.findChildViewById(rootView, id);
      if (btn0T == null) {
        break missingId;
      }

      id = R.id.btn1;
      TextView btn1 = ViewBindings.findChildViewById(rootView, id);
      if (btn1 == null) {
        break missingId;
      }

      id = R.id.btn1_t;
      TextView btn1T = ViewBindings.findChildViewById(rootView, id);
      if (btn1T == null) {
        break missingId;
      }

      id = R.id.btn2;
      TextView btn2 = ViewBindings.findChildViewById(rootView, id);
      if (btn2 == null) {
        break missingId;
      }

      id = R.id.btn2_t;
      TextView btn2T = ViewBindings.findChildViewById(rootView, id);
      if (btn2T == null) {
        break missingId;
      }

      id = R.id.btn3;
      TextView btn3 = ViewBindings.findChildViewById(rootView, id);
      if (btn3 == null) {
        break missingId;
      }

      id = R.id.btn3_t;
      TextView btn3T = ViewBindings.findChildViewById(rootView, id);
      if (btn3T == null) {
        break missingId;
      }

      id = R.id.chart1;
      LineChart chart1 = ViewBindings.findChildViewById(rootView, id);
      if (chart1 == null) {
        break missingId;
      }

      id = R.id.chart1_l;
      RelativeLayout chart1L = ViewBindings.findChildViewById(rootView, id);
      if (chart1L == null) {
        break missingId;
      }

      id = R.id.chart1_percent;
      RelativeLayout chart1Percent = ViewBindings.findChildViewById(rootView, id);
      if (chart1Percent == null) {
        break missingId;
      }

      id = R.id.chart_percent;
      LineChart chartPercent = ViewBindings.findChildViewById(rootView, id);
      if (chartPercent == null) {
        break missingId;
      }

      id = R.id.cumulative_1;
      ConstraintLayout cumulative1 = ViewBindings.findChildViewById(rootView, id);
      if (cumulative1 == null) {
        break missingId;
      }

      id = R.id.cumulative_31;
      TextView cumulative31 = ViewBindings.findChildViewById(rootView, id);
      if (cumulative31 == null) {
        break missingId;
      }

      id = R.id.cumulative_32;
      TextView cumulative32 = ViewBindings.findChildViewById(rootView, id);
      if (cumulative32 == null) {
        break missingId;
      }

      id = R.id.cumulative_btn;
      ConstraintLayout cumulativeBtn = ViewBindings.findChildViewById(rootView, id);
      if (cumulativeBtn == null) {
        break missingId;
      }

      id = R.id.cumulative_calculated;
      ConstraintLayout cumulativeCalculated = ViewBindings.findChildViewById(rootView, id);
      if (cumulativeCalculated == null) {
        break missingId;
      }

      id = R.id.cumulative_capacity_ni;
      TextView cumulativeCapacityNi = ViewBindings.findChildViewById(rootView, id);
      if (cumulativeCapacityNi == null) {
        break missingId;
      }

      id = R.id.cumulative_session_info;
      ConstraintLayout cumulativeSessionInfo = ViewBindings.findChildViewById(rootView, id);
      if (cumulativeSessionInfo == null) {
        break missingId;
      }

      id = R.id.d_2;
      ConstraintLayout d2 = ViewBindings.findChildViewById(rootView, id);
      if (d2 == null) {
        break missingId;
      }

      id = R.id.d_21;
      TextView d21 = ViewBindings.findChildViewById(rootView, id);
      if (d21 == null) {
        break missingId;
      }

      id = R.id.d_22;
      TextView d22 = ViewBindings.findChildViewById(rootView, id);
      if (d22 == null) {
        break missingId;
      }

      id = R.id.d_t;
      TextView dT = ViewBindings.findChildViewById(rootView, id);
      if (dT == null) {
        break missingId;
      }

      id = R.id.damage_bar_percent_current;
      ProgressBar damageBarPercentCurrent = ViewBindings.findChildViewById(rootView, id);
      if (damageBarPercentCurrent == null) {
        break missingId;
      }

      id = R.id.damage_bar_percent_current_singular;
      ProgressBar damageBarPercentCurrentSingular = ViewBindings.findChildViewById(rootView, id);
      if (damageBarPercentCurrentSingular == null) {
        break missingId;
      }

      id = R.id.damage_bar_seekwhite;
      ProgressBar damageBarSeekwhite = ViewBindings.findChildViewById(rootView, id);
      if (damageBarSeekwhite == null) {
        break missingId;
      }

      id = R.id.damage_bar_seekwhite_singular;
      ProgressBar damageBarSeekwhiteSingular = ViewBindings.findChildViewById(rootView, id);
      if (damageBarSeekwhiteSingular == null) {
        break missingId;
      }

      id = R.id.day_1;
      TextView day1 = ViewBindings.findChildViewById(rootView, id);
      if (day1 == null) {
        break missingId;
      }

      id = R.id.day_1_percent;
      TextView day1Percent = ViewBindings.findChildViewById(rootView, id);
      if (day1Percent == null) {
        break missingId;
      }

      id = R.id.day_1_temp;
      TextView day1Temp = ViewBindings.findChildViewById(rootView, id);
      if (day1Temp == null) {
        break missingId;
      }

      id = R.id.day_2;
      TextView day2 = ViewBindings.findChildViewById(rootView, id);
      if (day2 == null) {
        break missingId;
      }

      id = R.id.day_2_percent;
      TextView day2Percent = ViewBindings.findChildViewById(rootView, id);
      if (day2Percent == null) {
        break missingId;
      }

      id = R.id.day_2_temp;
      TextView day2Temp = ViewBindings.findChildViewById(rootView, id);
      if (day2Temp == null) {
        break missingId;
      }

      id = R.id.day_3;
      TextView day3 = ViewBindings.findChildViewById(rootView, id);
      if (day3 == null) {
        break missingId;
      }

      id = R.id.day_3_percent;
      TextView day3Percent = ViewBindings.findChildViewById(rootView, id);
      if (day3Percent == null) {
        break missingId;
      }

      id = R.id.day_3_temp;
      TextView day3Temp = ViewBindings.findChildViewById(rootView, id);
      if (day3Temp == null) {
        break missingId;
      }

      id = R.id.day_4;
      TextView day4 = ViewBindings.findChildViewById(rootView, id);
      if (day4 == null) {
        break missingId;
      }

      id = R.id.day_4_percent;
      TextView day4Percent = ViewBindings.findChildViewById(rootView, id);
      if (day4Percent == null) {
        break missingId;
      }

      id = R.id.day_4_temp;
      TextView day4Temp = ViewBindings.findChildViewById(rootView, id);
      if (day4Temp == null) {
        break missingId;
      }

      id = R.id.day_5;
      TextView day5 = ViewBindings.findChildViewById(rootView, id);
      if (day5 == null) {
        break missingId;
      }

      id = R.id.day_5_percent;
      TextView day5Percent = ViewBindings.findChildViewById(rootView, id);
      if (day5Percent == null) {
        break missingId;
      }

      id = R.id.day_5_temp;
      TextView day5Temp = ViewBindings.findChildViewById(rootView, id);
      if (day5Temp == null) {
        break missingId;
      }

      id = R.id.day_6;
      TextView day6 = ViewBindings.findChildViewById(rootView, id);
      if (day6 == null) {
        break missingId;
      }

      id = R.id.day_6_percent;
      TextView day6Percent = ViewBindings.findChildViewById(rootView, id);
      if (day6Percent == null) {
        break missingId;
      }

      id = R.id.day_6_temp;
      TextView day6Temp = ViewBindings.findChildViewById(rootView, id);
      if (day6Temp == null) {
        break missingId;
      }

      id = R.id.day_7;
      TextView day7 = ViewBindings.findChildViewById(rootView, id);
      if (day7 == null) {
        break missingId;
      }

      id = R.id.day_7_percent;
      TextView day7Percent = ViewBindings.findChildViewById(rootView, id);
      if (day7Percent == null) {
        break missingId;
      }

      id = R.id.day_7_temp;
      TextView day7Temp = ViewBindings.findChildViewById(rootView, id);
      if (day7Temp == null) {
        break missingId;
      }

      id = R.id.dead_time_text;
      TextView deadTimeText = ViewBindings.findChildViewById(rootView, id);
      if (deadTimeText == null) {
        break missingId;
      }

      id = R.id.dead_time_up;
      ConstraintLayout deadTimeUp = ViewBindings.findChildViewById(rootView, id);
      if (deadTimeUp == null) {
        break missingId;
      }

      id = R.id.dead_time_up_cummulative;
      LinearLayout deadTimeUpCummulative = ViewBindings.findChildViewById(rootView, id);
      if (deadTimeUpCummulative == null) {
        break missingId;
      }

      id = R.id.dead_time_up_singular;
      LinearLayout deadTimeUpSingular = ViewBindings.findChildViewById(rootView, id);
      if (deadTimeUpSingular == null) {
        break missingId;
      }

      id = R.id.degree_of_wear;
      LinearLayout degreeOfWear = ViewBindings.findChildViewById(rootView, id);
      if (degreeOfWear == null) {
        break missingId;
      }

      id = R.id.degree_wear_info;
      ImageView degreeWearInfo = ViewBindings.findChildViewById(rootView, id);
      if (degreeWearInfo == null) {
        break missingId;
      }

      id = R.id.grahp_temp_viewgroup;
      RelativeLayout grahpTempViewgroup = ViewBindings.findChildViewById(rootView, id);
      if (grahpTempViewgroup == null) {
        break missingId;
      }

      id = R.id.graph;
      RelativeLayout graph = ViewBindings.findChildViewById(rootView, id);
      if (graph == null) {
        break missingId;
      }

      id = R.id.graph_percent;
      RelativeLayout graphPercent = ViewBindings.findChildViewById(rootView, id);
      if (graphPercent == null) {
        break missingId;
      }

      id = R.id.graph_temp;
      RelativeLayout graphTemp = ViewBindings.findChildViewById(rootView, id);
      if (graphTemp == null) {
        break missingId;
      }

      id = R.id.health_access2;
      LinearLayout healthAccess2 = ViewBindings.findChildViewById(rootView, id);
      if (healthAccess2 == null) {
        break missingId;
      }

      id = R.id.health_checked_batery_capacity_cumulative;
      TextView healthCheckedBateryCapacityCumulative = ViewBindings.findChildViewById(rootView, id);
      if (healthCheckedBateryCapacityCumulative == null) {
        break missingId;
      }

      id = R.id.health_checked_batery_capacity_singular;
      TextView healthCheckedBateryCapacitySingular = ViewBindings.findChildViewById(rootView, id);
      if (healthCheckedBateryCapacitySingular == null) {
        break missingId;
      }

      id = R.id.health_count_of_sessions_cumulative;
      TextView healthCountOfSessionsCumulative = ViewBindings.findChildViewById(rootView, id);
      if (healthCountOfSessionsCumulative == null) {
        break missingId;
      }

      id = R.id.health_count_of_sessions_singular;
      TextView healthCountOfSessionsSingular = ViewBindings.findChildViewById(rootView, id);
      if (healthCountOfSessionsSingular == null) {
        break missingId;
      }

      id = R.id.health_first_progressbar_cumulative;
      ProgressBar healthFirstProgressbarCumulative = ViewBindings.findChildViewById(rootView, id);
      if (healthFirstProgressbarCumulative == null) {
        break missingId;
      }

      id = R.id.health_first_progressbar_singular;
      ProgressBar healthFirstProgressbarSingular = ViewBindings.findChildViewById(rootView, id);
      if (healthFirstProgressbarSingular == null) {
        break missingId;
      }

      id = R.id.health_full_batery_capacity;
      TextView healthFullBateryCapacity = ViewBindings.findChildViewById(rootView, id);
      if (healthFullBateryCapacity == null) {
        break missingId;
      }

      id = R.id.health_percent_damage_cumulative;
      TextView healthPercentDamageCumulative = ViewBindings.findChildViewById(rootView, id);
      if (healthPercentDamageCumulative == null) {
        break missingId;
      }

      id = R.id.health_percent_damage_singular;
      TextView healthPercentDamageSingular = ViewBindings.findChildViewById(rootView, id);
      if (healthPercentDamageSingular == null) {
        break missingId;
      }

      id = R.id.history_database;
      TextView historyDatabase = ViewBindings.findChildViewById(rootView, id);
      if (historyDatabase == null) {
        break missingId;
      }

      id = R.id.include_back_navigation;
      View includeBackNavigation = ViewBindings.findChildViewById(rootView, id);
      if (includeBackNavigation == null) {
        break missingId;
      }
      LayoutBackNavigationBinding binding_includeBackNavigation = LayoutBackNavigationBinding.bind(includeBackNavigation);

      id = R.id.indent_down;
      ConstraintLayout indentDown = ViewBindings.findChildViewById(rootView, id);
      if (indentDown == null) {
        break missingId;
      }

      id = R.id.method_text;
      TextView methodText = ViewBindings.findChildViewById(rootView, id);
      if (methodText == null) {
        break missingId;
      }

      id = R.id.method_text_singular;
      TextView methodTextSingular = ViewBindings.findChildViewById(rootView, id);
      if (methodTextSingular == null) {
        break missingId;
      }

      id = R.id.nativeAd;
      ShimmerFrameLayout nativeAd = ViewBindings.findChildViewById(rootView, id);
      if (nativeAd == null) {
        break missingId;
      }

      id = R.id.percent_cumulative;
      TextView percentCumulative = ViewBindings.findChildViewById(rootView, id);
      if (percentCumulative == null) {
        break missingId;
      }

      id = R.id.percent_damage;
      LinearLayout percentDamage = ViewBindings.findChildViewById(rootView, id);
      if (percentDamage == null) {
        break missingId;
      }

      id = R.id.percent_damage_cumulative;
      LinearLayout percentDamageCumulative = ViewBindings.findChildViewById(rootView, id);
      if (percentDamageCumulative == null) {
        break missingId;
      }

      id = R.id.percent_damage_dead;
      TextView percentDamageDead = ViewBindings.findChildViewById(rootView, id);
      if (percentDamageDead == null) {
        break missingId;
      }

      id = R.id.percent_damage_dead_singular;
      TextView percentDamageDeadSingular = ViewBindings.findChildViewById(rootView, id);
      if (percentDamageDeadSingular == null) {
        break missingId;
      }

      id = R.id.percent_graph_change;
      ConstraintLayout percentGraphChange = ViewBindings.findChildViewById(rootView, id);
      if (percentGraphChange == null) {
        break missingId;
      }

      id = R.id.percent_singular;
      TextView percentSingular = ViewBindings.findChildViewById(rootView, id);
      if (percentSingular == null) {
        break missingId;
      }

      id = R.id.prediction_wear_info;
      ImageView predictionWearInfo = ViewBindings.findChildViewById(rootView, id);
      if (predictionWearInfo == null) {
        break missingId;
      }

      id = R.id.progbar_1;
      VerticalProgressBar progbar1 = ViewBindings.findChildViewById(rootView, id);
      if (progbar1 == null) {
        break missingId;
      }

      id = R.id.progbar_2;
      VerticalProgressBar progbar2 = ViewBindings.findChildViewById(rootView, id);
      if (progbar2 == null) {
        break missingId;
      }

      id = R.id.progbar_3;
      VerticalProgressBar progbar3 = ViewBindings.findChildViewById(rootView, id);
      if (progbar3 == null) {
        break missingId;
      }

      id = R.id.progbar_4;
      VerticalProgressBar progbar4 = ViewBindings.findChildViewById(rootView, id);
      if (progbar4 == null) {
        break missingId;
      }

      id = R.id.progbar_5;
      VerticalProgressBar progbar5 = ViewBindings.findChildViewById(rootView, id);
      if (progbar5 == null) {
        break missingId;
      }

      id = R.id.progbar_6;
      VerticalProgressBar progbar6 = ViewBindings.findChildViewById(rootView, id);
      if (progbar6 == null) {
        break missingId;
      }

      id = R.id.progbar_7;
      VerticalProgressBar progbar7 = ViewBindings.findChildViewById(rootView, id);
      if (progbar7 == null) {
        break missingId;
      }

      id = R.id.proggersBarDamage;
      RelativeLayout proggersBarDamage = ViewBindings.findChildViewById(rootView, id);
      if (proggersBarDamage == null) {
        break missingId;
      }

      id = R.id.proggersBarDamage_cumulative;
      RelativeLayout proggersBarDamageCumulative = ViewBindings.findChildViewById(rootView, id);
      if (proggersBarDamageCumulative == null) {
        break missingId;
      }

      NestedScrollView scrollView = (NestedScrollView) rootView;

      id = R.id.sdfsd;
      TextView sdfsd = ViewBindings.findChildViewById(rootView, id);
      if (sdfsd == null) {
        break missingId;
      }

      id = R.id.seekBar;
      SeekBar seekBar = ViewBindings.findChildViewById(rootView, id);
      if (seekBar == null) {
        break missingId;
      }

      id = R.id.seekBar_singular;
      SeekBar seekBarSingular = ViewBindings.findChildViewById(rootView, id);
      if (seekBarSingular == null) {
        break missingId;
      }

      id = R.id.singular_1;
      ConstraintLayout singular1 = ViewBindings.findChildViewById(rootView, id);
      if (singular1 == null) {
        break missingId;
      }

      id = R.id.singular_31;
      TextView singular31 = ViewBindings.findChildViewById(rootView, id);
      if (singular31 == null) {
        break missingId;
      }

      id = R.id.singular_32;
      TextView singular32 = ViewBindings.findChildViewById(rootView, id);
      if (singular32 == null) {
        break missingId;
      }

      id = R.id.singular_btn;
      ConstraintLayout singularBtn = ViewBindings.findChildViewById(rootView, id);
      if (singularBtn == null) {
        break missingId;
      }

      id = R.id.singular_calculated;
      ConstraintLayout singularCalculated = ViewBindings.findChildViewById(rootView, id);
      if (singularCalculated == null) {
        break missingId;
      }

      id = R.id.singular_capacity_ni;
      TextView singularCapacityNi = ViewBindings.findChildViewById(rootView, id);
      if (singularCapacityNi == null) {
        break missingId;
      }

      id = R.id.singular_session_info;
      ConstraintLayout singularSessionInfo = ViewBindings.findChildViewById(rootView, id);
      if (singularSessionInfo == null) {
        break missingId;
      }

      id = R.id.t1;
      TextView t1 = ViewBindings.findChildViewById(rootView, id);
      if (t1 == null) {
        break missingId;
      }

      id = R.id.t1_temp;
      TextView t1Temp = ViewBindings.findChildViewById(rootView, id);
      if (t1Temp == null) {
        break missingId;
      }

      id = R.id.t2;
      TextView t2 = ViewBindings.findChildViewById(rootView, id);
      if (t2 == null) {
        break missingId;
      }

      id = R.id.t2_temp;
      TextView t2Temp = ViewBindings.findChildViewById(rootView, id);
      if (t2Temp == null) {
        break missingId;
      }

      id = R.id.t3;
      TextView t3 = ViewBindings.findChildViewById(rootView, id);
      if (t3 == null) {
        break missingId;
      }

      id = R.id.t3_temp;
      TextView t3Temp = ViewBindings.findChildViewById(rootView, id);
      if (t3Temp == null) {
        break missingId;
      }

      id = R.id.t4;
      TextView t4 = ViewBindings.findChildViewById(rootView, id);
      if (t4 == null) {
        break missingId;
      }

      id = R.id.t4_temp;
      TextView t4Temp = ViewBindings.findChildViewById(rootView, id);
      if (t4Temp == null) {
        break missingId;
      }

      id = R.id.t5;
      TextView t5 = ViewBindings.findChildViewById(rootView, id);
      if (t5 == null) {
        break missingId;
      }

      id = R.id.t5_temp;
      TextView t5Temp = ViewBindings.findChildViewById(rootView, id);
      if (t5Temp == null) {
        break missingId;
      }

      id = R.id.t6;
      TextView t6 = ViewBindings.findChildViewById(rootView, id);
      if (t6 == null) {
        break missingId;
      }

      id = R.id.t6_temp;
      TextView t6Temp = ViewBindings.findChildViewById(rootView, id);
      if (t6Temp == null) {
        break missingId;
      }

      id = R.id.t7;
      TextView t7 = ViewBindings.findChildViewById(rootView, id);
      if (t7 == null) {
        break missingId;
      }

      id = R.id.t7_temp;
      TextView t7Temp = ViewBindings.findChildViewById(rootView, id);
      if (t7Temp == null) {
        break missingId;
      }

      id = R.id.t8;
      TextView t8 = ViewBindings.findChildViewById(rootView, id);
      if (t8 == null) {
        break missingId;
      }

      id = R.id.t8_temp;
      TextView t8Temp = ViewBindings.findChildViewById(rootView, id);
      if (t8Temp == null) {
        break missingId;
      }

      id = R.id.t9;
      TextView t9 = ViewBindings.findChildViewById(rootView, id);
      if (t9 == null) {
        break missingId;
      }

      id = R.id.t9_temp;
      TextView t9Temp = ViewBindings.findChildViewById(rootView, id);
      if (t9Temp == null) {
        break missingId;
      }

      id = R.id.text_dead;
      TextView textDead = ViewBindings.findChildViewById(rootView, id);
      if (textDead == null) {
        break missingId;
      }

      id = R.id.text_dead_singular;
      TextView textDeadSingular = ViewBindings.findChildViewById(rootView, id);
      if (textDeadSingular == null) {
        break missingId;
      }

      id = R.id.text_remain_var_cumulative;
      TextView textRemainVarCumulative = ViewBindings.findChildViewById(rootView, id);
      if (textRemainVarCumulative == null) {
        break missingId;
      }

      id = R.id.text_remain_var_singular;
      TextView textRemainVarSingular = ViewBindings.findChildViewById(rootView, id);
      if (textRemainVarSingular == null) {
        break missingId;
      }

      id = R.id.text_top_dead_time_111;
      RelativeLayout textTopDeadTime111 = ViewBindings.findChildViewById(rootView, id);
      if (textTopDeadTime111 == null) {
        break missingId;
      }

      id = R.id.text_top_dead_time_111_cumulative;
      RelativeLayout textTopDeadTime111Cumulative = ViewBindings.findChildViewById(rootView, id);
      if (textTopDeadTime111Cumulative == null) {
        break missingId;
      }

      id = R.id.time_dead_viewgroup;
      ConstraintLayout timeDeadViewgroup = ViewBindings.findChildViewById(rootView, id);
      if (timeDeadViewgroup == null) {
        break missingId;
      }

      id = R.id.time_graph_change;
      ConstraintLayout timeGraphChange = ViewBindings.findChildViewById(rootView, id);
      if (timeGraphChange == null) {
        break missingId;
      }

      id = R.id.under_graph;
      LinearLayout underGraph = ViewBindings.findChildViewById(rootView, id);
      if (underGraph == null) {
        break missingId;
      }

      id = R.id.under_graph_percent;
      LinearLayout underGraphPercent = ViewBindings.findChildViewById(rootView, id);
      if (underGraphPercent == null) {
        break missingId;
      }

      id = R.id.under_graph_temp;
      LinearLayout underGraphTemp = ViewBindings.findChildViewById(rootView, id);
      if (underGraphTemp == null) {
        break missingId;
      }

      id = R.id.update_view;
      LinearLayout updateView = ViewBindings.findChildViewById(rootView, id);
      if (updateView == null) {
        break missingId;
      }

      id = R.id.update_view_btn;
      TextView updateViewBtn = ViewBindings.findChildViewById(rootView, id);
      if (updateViewBtn == null) {
        break missingId;
      }

      id = R.id.wear_rate2;
      TextView wearRate2 = ViewBindings.findChildViewById(rootView, id);
      if (wearRate2 == null) {
        break missingId;
      }

      id = R.id.wear_rate3;
      TextView wearRate3 = ViewBindings.findChildViewById(rootView, id);
      if (wearRate3 == null) {
        break missingId;
      }

      id = R.id.wear_rate_percent;
      TextView wearRatePercent = ViewBindings.findChildViewById(rootView, id);
      if (wearRatePercent == null) {
        break missingId;
      }

      id = R.id.why_i_need_to_do_this;
      LinearLayout whyINeedToDoThis = ViewBindings.findChildViewById(rootView, id);
      if (whyINeedToDoThis == null) {
        break missingId;
      }

      return new FragmentHealthBinding((NestedScrollView) rootView, blurViewDeadTop, btn0, btn0T,
          btn1, btn1T, btn2, btn2T, btn3, btn3T, chart1, chart1L, chart1Percent, chartPercent,
          cumulative1, cumulative31, cumulative32, cumulativeBtn, cumulativeCalculated,
          cumulativeCapacityNi, cumulativeSessionInfo, d2, d21, d22, dT, damageBarPercentCurrent,
          damageBarPercentCurrentSingular, damageBarSeekwhite, damageBarSeekwhiteSingular, day1,
          day1Percent, day1Temp, day2, day2Percent, day2Temp, day3, day3Percent, day3Temp, day4,
          day4Percent, day4Temp, day5, day5Percent, day5Temp, day6, day6Percent, day6Temp, day7,
          day7Percent, day7Temp, deadTimeText, deadTimeUp, deadTimeUpCummulative,
          deadTimeUpSingular, degreeOfWear, degreeWearInfo, grahpTempViewgroup, graph, graphPercent,
          graphTemp, healthAccess2, healthCheckedBateryCapacityCumulative,
          healthCheckedBateryCapacitySingular, healthCountOfSessionsCumulative,
          healthCountOfSessionsSingular, healthFirstProgressbarCumulative,
          healthFirstProgressbarSingular, healthFullBateryCapacity, healthPercentDamageCumulative,
          healthPercentDamageSingular, historyDatabase, binding_includeBackNavigation, indentDown,
          methodText, methodTextSingular, nativeAd, percentCumulative, percentDamage,
          percentDamageCumulative, percentDamageDead, percentDamageDeadSingular, percentGraphChange,
          percentSingular, predictionWearInfo, progbar1, progbar2, progbar3, progbar4, progbar5,
          progbar6, progbar7, proggersBarDamage, proggersBarDamageCumulative, scrollView, sdfsd,
          seekBar, seekBarSingular, singular1, singular31, singular32, singularBtn,
          singularCalculated, singularCapacityNi, singularSessionInfo, t1, t1Temp, t2, t2Temp, t3,
          t3Temp, t4, t4Temp, t5, t5Temp, t6, t6Temp, t7, t7Temp, t8, t8Temp, t9, t9Temp, textDead,
          textDeadSingular, textRemainVarCumulative, textRemainVarSingular, textTopDeadTime111,
          textTopDeadTime111Cumulative, timeDeadViewgroup, timeGraphChange, underGraph,
          underGraphPercent, underGraphTemp, updateView, updateViewBtn, wearRate2, wearRate3,
          wearRatePercent, whyINeedToDoThis);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
