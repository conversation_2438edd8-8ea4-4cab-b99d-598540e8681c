// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogSelectColorThemeBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final Button blueBtn;

  @NonNull
  public final Button colorBtn1;

  @NonNull
  public final Button colorBtn10;

  @NonNull
  public final Button colorBtn2;

  @NonNull
  public final Button colorBtn3;

  @NonNull
  public final Button colorBtn4;

  @NonNull
  public final Button colorBtn5;

  @NonNull
  public final Button colorBtn6;

  @NonNull
  public final Button colorBtn7;

  @NonNull
  public final Button colorBtn8;

  @NonNull
  public final Button colorBtn9;

  @NonNull
  public final Button exitColor;

  @NonNull
  public final Button goldBtn;

  @NonNull
  public final Button greenBtn;

  @NonNull
  public final Button lightBlueBtn;

  @NonNull
  public final Button lightGreenBtn;

  @NonNull
  public final Button nightBlueBtn;

  @NonNull
  public final Button orangeBtn;

  @NonNull
  public final Button pinkBtn;

  @NonNull
  public final Button redBtn;

  @NonNull
  public final RelativeLayout strelka;

  @NonNull
  public final ConstraintLayout table1;

  @NonNull
  public final ConstraintLayout table2;

  @NonNull
  public final Button teloBtn;

  @NonNull
  public final TextView textView20;

  private DialogSelectColorThemeBinding(@NonNull RelativeLayout rootView, @NonNull Button blueBtn,
      @NonNull Button colorBtn1, @NonNull Button colorBtn10, @NonNull Button colorBtn2,
      @NonNull Button colorBtn3, @NonNull Button colorBtn4, @NonNull Button colorBtn5,
      @NonNull Button colorBtn6, @NonNull Button colorBtn7, @NonNull Button colorBtn8,
      @NonNull Button colorBtn9, @NonNull Button exitColor, @NonNull Button goldBtn,
      @NonNull Button greenBtn, @NonNull Button lightBlueBtn, @NonNull Button lightGreenBtn,
      @NonNull Button nightBlueBtn, @NonNull Button orangeBtn, @NonNull Button pinkBtn,
      @NonNull Button redBtn, @NonNull RelativeLayout strelka, @NonNull ConstraintLayout table1,
      @NonNull ConstraintLayout table2, @NonNull Button teloBtn, @NonNull TextView textView20) {
    this.rootView = rootView;
    this.blueBtn = blueBtn;
    this.colorBtn1 = colorBtn1;
    this.colorBtn10 = colorBtn10;
    this.colorBtn2 = colorBtn2;
    this.colorBtn3 = colorBtn3;
    this.colorBtn4 = colorBtn4;
    this.colorBtn5 = colorBtn5;
    this.colorBtn6 = colorBtn6;
    this.colorBtn7 = colorBtn7;
    this.colorBtn8 = colorBtn8;
    this.colorBtn9 = colorBtn9;
    this.exitColor = exitColor;
    this.goldBtn = goldBtn;
    this.greenBtn = greenBtn;
    this.lightBlueBtn = lightBlueBtn;
    this.lightGreenBtn = lightGreenBtn;
    this.nightBlueBtn = nightBlueBtn;
    this.orangeBtn = orangeBtn;
    this.pinkBtn = pinkBtn;
    this.redBtn = redBtn;
    this.strelka = strelka;
    this.table1 = table1;
    this.table2 = table2;
    this.teloBtn = teloBtn;
    this.textView20 = textView20;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogSelectColorThemeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogSelectColorThemeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_select_color_theme, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogSelectColorThemeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.blue_btn;
      Button blueBtn = ViewBindings.findChildViewById(rootView, id);
      if (blueBtn == null) {
        break missingId;
      }

      id = R.id.color_btn_1;
      Button colorBtn1 = ViewBindings.findChildViewById(rootView, id);
      if (colorBtn1 == null) {
        break missingId;
      }

      id = R.id.color_btn_10;
      Button colorBtn10 = ViewBindings.findChildViewById(rootView, id);
      if (colorBtn10 == null) {
        break missingId;
      }

      id = R.id.color_btn_2;
      Button colorBtn2 = ViewBindings.findChildViewById(rootView, id);
      if (colorBtn2 == null) {
        break missingId;
      }

      id = R.id.color_btn_3;
      Button colorBtn3 = ViewBindings.findChildViewById(rootView, id);
      if (colorBtn3 == null) {
        break missingId;
      }

      id = R.id.color_btn_4;
      Button colorBtn4 = ViewBindings.findChildViewById(rootView, id);
      if (colorBtn4 == null) {
        break missingId;
      }

      id = R.id.color_btn_5;
      Button colorBtn5 = ViewBindings.findChildViewById(rootView, id);
      if (colorBtn5 == null) {
        break missingId;
      }

      id = R.id.color_btn_6;
      Button colorBtn6 = ViewBindings.findChildViewById(rootView, id);
      if (colorBtn6 == null) {
        break missingId;
      }

      id = R.id.color_btn_7;
      Button colorBtn7 = ViewBindings.findChildViewById(rootView, id);
      if (colorBtn7 == null) {
        break missingId;
      }

      id = R.id.color_btn_8;
      Button colorBtn8 = ViewBindings.findChildViewById(rootView, id);
      if (colorBtn8 == null) {
        break missingId;
      }

      id = R.id.color_btn_9;
      Button colorBtn9 = ViewBindings.findChildViewById(rootView, id);
      if (colorBtn9 == null) {
        break missingId;
      }

      id = R.id.exit_color;
      Button exitColor = ViewBindings.findChildViewById(rootView, id);
      if (exitColor == null) {
        break missingId;
      }

      id = R.id.gold_btn;
      Button goldBtn = ViewBindings.findChildViewById(rootView, id);
      if (goldBtn == null) {
        break missingId;
      }

      id = R.id.green_btn;
      Button greenBtn = ViewBindings.findChildViewById(rootView, id);
      if (greenBtn == null) {
        break missingId;
      }

      id = R.id.light_blue_btn;
      Button lightBlueBtn = ViewBindings.findChildViewById(rootView, id);
      if (lightBlueBtn == null) {
        break missingId;
      }

      id = R.id.light_green_btn;
      Button lightGreenBtn = ViewBindings.findChildViewById(rootView, id);
      if (lightGreenBtn == null) {
        break missingId;
      }

      id = R.id.night_blue_btn;
      Button nightBlueBtn = ViewBindings.findChildViewById(rootView, id);
      if (nightBlueBtn == null) {
        break missingId;
      }

      id = R.id.orange_btn;
      Button orangeBtn = ViewBindings.findChildViewById(rootView, id);
      if (orangeBtn == null) {
        break missingId;
      }

      id = R.id.pink_btn;
      Button pinkBtn = ViewBindings.findChildViewById(rootView, id);
      if (pinkBtn == null) {
        break missingId;
      }

      id = R.id.red_btn;
      Button redBtn = ViewBindings.findChildViewById(rootView, id);
      if (redBtn == null) {
        break missingId;
      }

      id = R.id.strelka;
      RelativeLayout strelka = ViewBindings.findChildViewById(rootView, id);
      if (strelka == null) {
        break missingId;
      }

      id = R.id.table1;
      ConstraintLayout table1 = ViewBindings.findChildViewById(rootView, id);
      if (table1 == null) {
        break missingId;
      }

      id = R.id.table2;
      ConstraintLayout table2 = ViewBindings.findChildViewById(rootView, id);
      if (table2 == null) {
        break missingId;
      }

      id = R.id.telo_btn;
      Button teloBtn = ViewBindings.findChildViewById(rootView, id);
      if (teloBtn == null) {
        break missingId;
      }

      id = R.id.textView20;
      TextView textView20 = ViewBindings.findChildViewById(rootView, id);
      if (textView20 == null) {
        break missingId;
      }

      return new DialogSelectColorThemeBinding((RelativeLayout) rootView, blueBtn, colorBtn1,
          colorBtn10, colorBtn2, colorBtn3, colorBtn4, colorBtn5, colorBtn6, colorBtn7, colorBtn8,
          colorBtn9, exitColor, goldBtn, greenBtn, lightBlueBtn, lightGreenBtn, nightBlueBtn,
          orangeBtn, pinkBtn, redBtn, strelka, table1, table2, teloBtn, textView20);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
