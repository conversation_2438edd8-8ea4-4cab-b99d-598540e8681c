package com.tqhit.battery.one.features.navigation;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SharedNavigationViewModel_Factory implements Factory<SharedNavigationViewModel> {
  @Override
  public SharedNavigationViewModel get() {
    return newInstance();
  }

  public static SharedNavigationViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SharedNavigationViewModel newInstance() {
    return new SharedNavigationViewModel();
  }

  private static final class InstanceHolder {
    static final SharedNavigationViewModel_Factory INSTANCE = new SharedNavigationViewModel_Factory();
  }
}
