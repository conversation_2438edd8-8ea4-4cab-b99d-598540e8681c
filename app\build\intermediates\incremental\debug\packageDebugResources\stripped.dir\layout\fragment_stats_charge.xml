<?xml version="1.0" encoding="utf-8"?>
<!--
 * Stats charge fragment layout
 * Lean implementation matching the design patterns of fragment_new_charge.xml
 * Displays charge statistics and estimates with consistent visual styling
 -->
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/stats_charge_scroll_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:animateLayoutChanges="true">

        <!-- Back navigation component -->
        <include
            android:id="@+id/include_back_navigation"
            layout="@layout/layout_back_navigation" />

        <!-- Main display section (percentage and time estimates) -->
        <LinearLayout
            android:id="@+id/stats_charge_main_display_root"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:baselineAligned="false"
            android:orientation="horizontal">

            <!-- Battery percentage with circular progress -->
            <RelativeLayout
                android:id="@+id/percent_layout"
                android:background="@drawable/white_block"
                android:padding="8dp"
                android:layout_width="0dp"
                android:layout_height="235dp"
                android:layout_marginBottom="14dp"
                android:layout_weight="1"
                android:layout_marginStart="9dp"
                android:layout_marginEnd="14dp">
                <RelativeLayout
                    android:id="@+id/percent_inner_layout"
                    android:background="@drawable/grey_block_static"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <TextView
                        android:textSize="50sp"
                        android:textColor="?attr/colorr"
                        android:gravity="center"
                        android:id="@+id/tv_percentage"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                    <com.google.android.material.progressindicator.CircularProgressIndicator
                        android:id="@+id/charge_prog_bar_percent"
                        android:background="@drawable/circle_back"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:max="100"
                        android:progress="0"
                        android:layout_centerInParent="true"
                        app:indicatorColor="?attr/colorr"
                        app:indicatorInset="2dp"
                        app:indicatorSize="160dp"
                        app:trackColor="@color/empty"
                        app:trackCornerRadius="10dp"
                        app:trackThickness="3dp"/>
                </RelativeLayout>
            </RelativeLayout>

            <!-- Time estimates -->
            <LinearLayout
                android:orientation="vertical"
                android:id="@+id/time_estimates_layout"
                android:background="@drawable/white_block"
                android:padding="8dp"
                android:visibility="visible"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginBottom="14dp"
                android:layout_marginEnd="9dp">

                <!-- Time to full charge -->
                <LinearLayout
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:id="@+id/full_charge_block"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="5dp"
                    android:layout_weight="1"
                    android:paddingStart="7dp"
                    android:paddingEnd="7dp">
                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:contentDescription="@string/zero"
                        app:srcCompat="@drawable/ic_day"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:gravity="center"
                        android:id="@+id/tv_time_to_full"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                </LinearLayout>

                <!-- Time to target charge -->
                <LinearLayout
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:id="@+id/target_charge_block"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="3dp"
                    android:layout_marginBottom="3dp"
                    android:layout_weight="1"
                    android:paddingStart="7dp"
                    android:paddingEnd="7dp">
                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:contentDescription="@string/zero"
                        app:srcCompat="@drawable/ic_day_night"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:gravity="center"
                        android:id="@+id/tv_time_to_target"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                </LinearLayout>

                <!-- Charging status -->
                <LinearLayout
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:id="@+id/charging_status_block"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp"
                    android:layout_weight="1"
                    android:paddingStart="7dp"
                    android:paddingEnd="7dp">
                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:contentDescription="@string/zero"
                        app:srcCompat="@drawable/ic_night"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:gravity="center"
                        android:id="@+id/tv_charging_status"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="@string/zero"/>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <com.facebook.shimmer.ShimmerFrameLayout
            android:id="@+id/nativeAd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/grey" />

        <!-- Target percentage section (matching battery wear design) -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/target_percentage_root"
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp"
            android:layout_marginBottom="14dp">

            <!-- Section Title -->
            <TextView
                android:id="@+id/target_percentage_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="Target Percentage"
                android:textColor="?attr/black"
                android:textSize="19sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <View
                android:id="@+id/divider_target"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="8dp"
                android:background="?android:attr/listDivider"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/target_percentage_title" />

            <!-- Charge Alarm Button -->
            <TextView
                android:id="@+id/battery_alarm_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@drawable/grey_block"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:paddingStart="10dp"
                android:paddingTop="12.5dp"
                android:paddingEnd="10dp"
                android:paddingBottom="12.5dp"
                android:singleLine="true"
                android:text="@string/charge_alarm"
                android:textColor="?attr/black"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/divider_target" />

            <!-- Target percentage description -->
            <TextView
                android:id="@+id/target_percent_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="Target Percentage:"
                android:textColor="?attr/black"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/battery_alarm_btn" />

            <TextView
                android:id="@+id/tv_target_percentage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="80%"
                android:textColor="?attr/colorr"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/target_percent_label"
                app:layout_constraintStart_toEndOf="@+id/target_percent_label"
                app:layout_constraintTop_toTopOf="@+id/target_percent_label" />

            <!-- SeekBar for target percentage -->
            <SeekBar
                android:id="@+id/seekbar_target"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:max="100"
                android:progress="80"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/target_percent_label" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Current charging session section -->
        <LinearLayout
            android:id="@+id/charge_current_session_root"
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:padding="8dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp"
            android:layout_marginBottom="14dp">

            <!-- Section Title -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Current Charge Session"
                    android:textColor="?attr/black"
                    android:textSize="19sp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="?android:attr/listDivider"
                android:layout_marginBottom="8dp" />

            <!-- Session start time -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="6dp">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Started:"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />
                <TextView
                    android:id="@+id/tv_session_start_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-"
                    android:textColor="?attr/colorr"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- Session duration -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="6dp">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Duration:"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />
                <TextView
                    android:id="@+id/tv_session_duration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-"
                    android:textColor="?attr/colorr"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- Percentage charged -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="6dp">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Charged:"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />
                <TextView
                    android:id="@+id/tv_session_percentage_charged"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-"
                    android:textColor="?attr/colorr"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- Total charge mAh -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="6dp">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Total mAh:"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />
                <TextView
                    android:id="@+id/tv_session_total_charge_mah"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-"
                    android:textColor="?attr/colorr"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- Current -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="6dp">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Current:"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />
                <TextView
                    android:id="@+id/tv_current"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-"
                    android:textColor="?attr/colorr"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- Voltage -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="6dp">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Voltage:"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />
                <TextView
                    android:id="@+id/tv_voltage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-"
                    android:textColor="?attr/colorr"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- Temperature -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="6dp">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Temperature:"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />
                <TextView
                    android:id="@+id/tv_temperature"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-"
                    android:textColor="?attr/colorr"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- Power -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Power:"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />
                <TextView
                    android:id="@+id/tv_power"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-"
                    android:textColor="?attr/colorr"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- Reset button -->
            <Button
                android:id="@+id/btn_reset_session"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="Reset Session"
                android:textAllCaps="false" />
        </LinearLayout>

        <!-- Bottom margin to avoid overlap with navigation bar -->
        <Space
            android:layout_width="match_parent"
            android:layout_height="90dp"/>

    </LinearLayout>
</androidx.core.widget.NestedScrollView>
