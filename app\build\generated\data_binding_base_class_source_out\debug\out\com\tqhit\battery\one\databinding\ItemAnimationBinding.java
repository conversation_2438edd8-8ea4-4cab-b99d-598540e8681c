// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.google.android.material.imageview.ShapeableImageView;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAnimationBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout applyBlock;

  @NonNull
  public final Button applyButton;

  @NonNull
  public final ShapeableImageView cardImage;

  @NonNull
  public final AppCompatImageView iconAd;

  @NonNull
  public final TextView lockBtn;

  @NonNull
  public final ShimmerFrameLayout shimmerLayout;

  @NonNull
  public final TextView textBtn;

  private ItemAnimationBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout applyBlock, @NonNull Button applyButton,
      @NonNull ShapeableImageView cardImage, @NonNull AppCompatImageView iconAd,
      @NonNull TextView lockBtn, @NonNull ShimmerFrameLayout shimmerLayout,
      @NonNull TextView textBtn) {
    this.rootView = rootView;
    this.applyBlock = applyBlock;
    this.applyButton = applyButton;
    this.cardImage = cardImage;
    this.iconAd = iconAd;
    this.lockBtn = lockBtn;
    this.shimmerLayout = shimmerLayout;
    this.textBtn = textBtn;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAnimationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAnimationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_animation, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAnimationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.apply_block;
      ConstraintLayout applyBlock = ViewBindings.findChildViewById(rootView, id);
      if (applyBlock == null) {
        break missingId;
      }

      id = R.id.applyButton;
      Button applyButton = ViewBindings.findChildViewById(rootView, id);
      if (applyButton == null) {
        break missingId;
      }

      id = R.id.cardImage;
      ShapeableImageView cardImage = ViewBindings.findChildViewById(rootView, id);
      if (cardImage == null) {
        break missingId;
      }

      id = R.id.icon_ad;
      AppCompatImageView iconAd = ViewBindings.findChildViewById(rootView, id);
      if (iconAd == null) {
        break missingId;
      }

      id = R.id.lockBtn;
      TextView lockBtn = ViewBindings.findChildViewById(rootView, id);
      if (lockBtn == null) {
        break missingId;
      }

      id = R.id.shimmerLayout;
      ShimmerFrameLayout shimmerLayout = ViewBindings.findChildViewById(rootView, id);
      if (shimmerLayout == null) {
        break missingId;
      }

      id = R.id.text_btn;
      TextView textBtn = ViewBindings.findChildViewById(rootView, id);
      if (textBtn == null) {
        break missingId;
      }

      return new ItemAnimationBinding((ConstraintLayout) rootView, applyBlock, applyButton,
          cardImage, iconAd, lockBtn, shimmerLayout, textBtn);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
