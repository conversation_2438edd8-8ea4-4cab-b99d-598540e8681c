{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d148dadd36a9aeec86630cf6fa0b2687\\transformed\\jetified-applovin-sdk-13.3.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,161,215,280,437,588,666,729", "endColumns": "105,53,64,156,150,77,62,58", "endOffsets": "156,210,275,432,583,661,724,783"}, "to": {"startLines": "83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5922,6028,6082,6147,6304,6455,6533,6596", "endColumns": "105,53,64,156,150,77,62,58", "endOffsets": "6023,6077,6142,6299,6450,6528,6591,6650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,348,488,657,737", "endColumns": "71,87,82,139,168,79,75", "endOffsets": "172,260,343,483,652,732,808"}, "to": {"startLines": "200,299,479,514,565,630,631", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "18005,28536,44926,48034,52388,57161,57241", "endColumns": "71,87,82,139,168,79,75", "endOffsets": "18072,28619,45004,48169,52552,57236,57312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,294,576,640,782,901,1019,1069,1127,1259,1348,1390,1488,1525,1561,1613,1702,1741", "endColumns": "40,53,55,63,141,118,117,49,57,131,88,41,97,36,35,51,88,38,55", "endOffsets": "239,293,349,639,781,900,1018,1068,1126,1258,1347,1389,1487,1524,1560,1612,1701,1740,1796"}, "to": {"startLines": "467,468,469,488,489,490,491,492,493,494,495,529,530,531,532,533,534,535,636", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "43949,43994,44052,45663,45731,45877,46000,46122,46176,46238,46374,49343,49389,49491,49532,49572,49628,49721,57527", "endColumns": "44,57,59,67,145,122,121,53,61,135,92,45,101,40,39,55,92,42,59", "endOffsets": "43989,44047,44107,45726,45872,45995,46117,46171,46233,46369,46462,49384,49486,49527,49567,49623,49716,49759,57582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-de\\values-de.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3443,3507", "endColumns": "63,65", "endOffsets": "3502,3568"}, "to": {"startLines": "291,292", "startColumns": "4,4", "startOffsets": "28016,28080", "endColumns": "63,65", "endOffsets": "28075,28141"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,501,692,779,867,942,1032,1118,1197,1262,1366,1470,1539,1609,1681,1750,1877,2005,2138,2211,2295,2371,2448,2535,2623,2689,2754,2807,2867,2915,2976,3048,3118,3183,3254,3319,3377,3443,3495,3557,3633,3709,3765", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,126,127,132,72,83,75,76,86,87,65,64,52,59,47,60,71,69,64,70,64,57,65,51,61,75,75,55,67", "endOffsets": "306,496,687,774,862,937,1027,1113,1192,1257,1361,1465,1534,1604,1676,1745,1872,2000,2133,2206,2290,2366,2443,2530,2618,2684,2749,2802,2862,2910,2971,3043,3113,3178,3249,3314,3372,3438,3490,3552,3628,3704,3760,3828"}, "to": {"startLines": "2,11,15,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,280,281,282,283,284,285,286,287,288,289,290,293,294,295,296,297,298", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,406,596,24570,24657,24745,24820,24910,24996,25075,25140,25244,25348,25417,25487,25559,25628,25755,25883,26016,26089,26173,26249,26326,26413,26501,26567,27327,27380,27440,27488,27549,27621,27691,27756,27827,27892,27950,28146,28198,28260,28336,28412,28468", "endLines": "10,14,18,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,280,281,282,283,284,285,286,287,288,289,290,293,294,295,296,297,298", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,126,127,132,72,83,75,76,86,87,65,64,52,59,47,60,71,69,64,70,64,57,65,51,61,75,75,55,67", "endOffsets": "401,591,782,24652,24740,24815,24905,24991,25070,25135,25239,25343,25412,25482,25554,25623,25750,25878,26011,26084,26168,26244,26321,26408,26496,26562,26627,27375,27435,27483,27544,27616,27686,27751,27822,27887,27945,28011,28193,28255,28331,28407,28463,28531"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1085,1151,1245,1315,1374,1482,1548,1617,1675,1747,1811,1865,1993,2053,2115,2169,2247,2384,2476,2554,2648,2734,2818,2963,3047,3133,3266,3356,3435,3492,3543,3609,3683,3765,3836,3911,3985,4063,4135,4209,4319,4411,4493,4582,4671,4745,4823,4909,4964,5043,5110,5190,5274,5336,5400,5463,5532,5639,5746,5845,5951,6012,6067,6149,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "278,369,458,542,632,714,815,937,1018,1080,1146,1240,1310,1369,1477,1543,1612,1670,1742,1806,1860,1988,2048,2110,2164,2242,2379,2471,2549,2643,2729,2813,2958,3042,3128,3261,3351,3430,3487,3538,3604,3678,3760,3831,3906,3980,4058,4130,4204,4314,4406,4488,4577,4666,4740,4818,4904,4959,5038,5105,5185,5269,5331,5395,5458,5527,5634,5741,5840,5946,6007,6062,6144,6227,6304,6380"}, "to": {"startLines": "19,140,141,142,143,144,160,161,175,244,246,301,325,334,406,407,408,409,410,411,412,413,414,415,416,417,418,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,503,539,540,551", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,12205,12296,12385,12469,12559,13894,13995,15100,24333,24504,28690,30687,31316,39067,39175,39241,39310,39368,39440,39504,39558,39686,39746,39808,39862,39940,40209,40301,40379,40473,40559,40643,40788,40872,40958,41091,41181,41260,41317,41368,41434,41508,41590,41661,41736,41810,41888,41960,42034,42144,42236,42318,42407,42496,42570,42648,42734,42789,42868,42935,43015,43099,43161,43225,43288,43357,43464,43571,43670,43776,43837,47368,49981,50064,51038", "endLines": "22,140,141,142,143,144,160,161,175,244,246,301,325,334,406,407,408,409,410,411,412,413,414,415,416,417,418,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,503,539,540,551", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "965,12291,12380,12464,12554,12636,13990,14112,15176,24390,24565,28779,30752,31370,39170,39236,39305,39363,39435,39499,39553,39681,39741,39803,39857,39935,40072,40296,40374,40468,40554,40638,40783,40867,40953,41086,41176,41255,41312,41363,41429,41503,41585,41656,41731,41805,41883,41955,42029,42139,42231,42313,42402,42491,42565,42643,42729,42784,42863,42930,43010,43094,43156,43220,43283,43352,43459,43566,43665,43771,43832,43887,47445,50059,50136,51109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "178,179,180,181,182,183,184,185,187,188,189,190,191,192,193,194,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15365,15474,15638,15766,15878,16056,16187,16308,16572,16752,16864,17033,17164,17326,17502,17573,17636", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "15469,15633,15761,15873,16051,16182,16303,16422,16747,16859,17028,17159,17321,17497,17568,17631,17711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,185,231,321,365,415,471,531,592,657,716,759,859,977,1059,1155,1195,1266,1313,1405,1480,1539,1592,1703,1757,1817,1874,1947,1993,2060,2163,2219,2292,2395,2461,2528,2608,2666,2723,2775,2837,2907,2975,3069,3190", "endColumns": "64,64,45,89,43,49,55,59,60,64,58,42,99,117,81,95,39,70,46,91,74,58,52,110,53,59,56,72,45,66,102,55,72,102,65,66,79,57,56,51,61,69,67,93,120,56", "endOffsets": "115,180,226,316,360,410,466,526,587,652,711,754,854,972,1054,1150,1190,1261,1308,1400,1475,1534,1587,1698,1752,1812,1869,1942,1988,2055,2158,2214,2287,2390,2456,2523,2603,2661,2718,2770,2832,2902,2970,3064,3185,3242"}, "to": {"startLines": "578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "53585,53650,53715,53761,53851,53895,53945,54001,54061,54122,54187,54246,54289,54389,54507,54589,54685,54725,54796,54843,54935,55010,55069,55122,55233,55287,55347,55404,55477,55523,55590,55693,55749,55822,55925,55991,56058,56138,56196,56253,56305,56367,56437,56505,56599,56720", "endColumns": "64,64,45,89,43,49,55,59,60,64,58,42,99,117,81,95,39,70,46,91,74,58,52,110,53,59,56,72,45,66,102,55,72,102,65,66,79,57,56,51,61,69,67,93,120,56", "endOffsets": "53645,53710,53756,53846,53890,53940,53996,54056,54117,54182,54241,54284,54384,54502,54584,54680,54720,54791,54838,54930,55005,55064,55117,55228,55282,55342,55399,55472,55518,55585,55688,55744,55817,55920,55986,56053,56133,56191,56248,56300,56362,56432,56500,56594,56715,56772"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "186", "startColumns": "4", "startOffsets": "16427", "endColumns": "144", "endOffsets": "16567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,315,424,553,663,758,870,1014,1132,1288,1373,1478,1573,1675,1793,1919,2029,2165,2302,2437,2616,2744,2867,2995,3120,3216,3314,3434,3563,3663,3768,3870,4011,4159,4265,4367,4447,4543,4638,4758,4844,4933,5034,5114,5200,5300,5406,5501,5602,5690,5799,5900,6004,6142,6231,6336", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,119,85,88,100,79,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "181,310,419,548,658,753,865,1009,1127,1283,1368,1473,1568,1670,1788,1914,2024,2160,2297,2432,2611,2739,2862,2990,3115,3211,3309,3429,3558,3658,3763,3865,4006,4154,4260,4362,4442,4538,4633,4753,4839,4928,5029,5109,5195,5295,5401,5496,5597,5685,5794,5895,5999,6137,6226,6331,6427"}, "to": {"startLines": "345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "32511,32642,32771,32880,33009,33119,33214,33326,33470,33588,33744,33829,33934,34029,34131,34249,34375,34485,34621,34758,34893,35072,35200,35323,35451,35576,35672,35770,35890,36019,36119,36224,36326,36467,36615,36721,36823,36903,36999,37094,37214,37300,37389,37490,37570,37656,37756,37862,37957,38058,38146,38255,38356,38460,38598,38687,38792", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,119,85,88,100,79,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "32637,32766,32875,33004,33114,33209,33321,33465,33583,33739,33824,33929,34024,34126,34244,34370,34480,34616,34753,34888,35067,35195,35318,35446,35571,35667,35765,35885,36014,36114,36219,36321,36462,36610,36716,36818,36898,36994,37089,37209,37295,37384,37485,37565,37651,37751,37857,37952,38053,38141,38250,38351,38455,38593,38682,38787,38883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "150,151,152,153,154,155,156,562", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "12966,13064,13166,13266,13366,13474,13579,52150", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "13059,13161,13261,13361,13469,13574,13692,52246"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,1009,1093,1243,1315,1385,1464,1530", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,71,69,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,1004,1088,1163,1310,1380,1459,1525,1645"}, "to": {"startLines": "176,177,214,215,238,328,330,472,478,521,522,542,560,561,567,568,570", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15181,15277,20788,20886,23938,30895,31046,44335,44838,48652,48733,50201,52008,52080,52617,52696,52813", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,71,69,78,65,119", "endOffsets": "15272,15360,20881,20981,24020,30975,31133,44419,44921,48728,48812,50271,52075,52145,52691,52757,52928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,118", "endOffsets": "154,273"}, "to": {"startLines": "470,471", "startColumns": "4,4", "startOffsets": "44112,44216", "endColumns": "103,118", "endOffsets": "44211,44330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "201,302,303,304", "startColumns": "4,4,4,4", "startOffsets": "18077,28784,28885,28996", "endColumns": "103,100,110,99", "endOffsets": "18176,28880,28991,29091"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-de\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,267,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,268,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,269,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "121,187,242,293,346,396,450,502,552,611,677,730,781,832,883,22527,936,1019,1076,1125,1173,1226,1304,1374,1454,1528,1601,1668,25142,1723,1763,1938,1995,2085,2128,2191,22738,23576,24035,24160,2279,57,25094,22943,24963,2322,2372,2411,2501,2573,2661,3176,3694,12774,12840,12066,11980,12907,3735,3809,3880,4107,4429,4506,4555,4597,4659,4728,4781,22622,4851,4918,22864,4964,5039,5081,5154,5337,5407,5500,5577,5643,5730,23455,5801,5856,5911,5973,6034,6082,6159,6266,6316,6721,6779,6841,7491,8164,8236,8285,8439,8507,8834,8885,9439,9500,9633,9795,9955,10017,10060,10108,10184,10283,10369,10420,10550,10645,10702,10768,10835,11140,11403,11614,11778,11859,12994,13071,24766,13127,13169,13279,13346,13404,13634,13686,13765,13833,13895,14042,14101,14247,14492,14531,14590,14628,14666,14702,14738,14812,14859,14912,14957,15024,24828,15097,15162,25260,15219,15265,15305,15380,15846,15907,15991,16177,16244,16328,16365,16400,16441,16484,16548,16609,16682,16740,16825,16981,22787,17018,17082,17141,17236,17307,17384,17494,17622,17705,17744,17820,23262,23075,22987,23942,24507,18126,18166,18221,18272,18334,18425,24091,18476,18558,18607,22693,18651,24895,25319,18780,18873,18968,19027,19097,19304,19377,19435,23373,19500,19555,24554,19616,19703,19812,19898,19950,23867,20022,20099,20144,20203,20250,20375,25373,20787,20863,20909,20963,21048,21109,21161,25032,21202,21270,21497,21579,21646,21723,21780,21867,21930,21969,23517,22021,22078,22124,22166,22220,22279,22351,22410,22446,22482", "endColumns": "64,53,49,51,48,52,50,48,57,64,51,49,49,49,51,93,81,55,47,46,51,76,68,78,72,71,65,53,69,38,173,55,88,41,61,86,47,289,54,345,41,62,46,42,67,48,37,88,70,86,513,516,39,64,65,706,84,83,72,69,225,320,75,47,40,60,67,51,68,69,65,44,77,73,40,71,181,68,91,75,64,85,69,60,53,53,60,59,46,75,105,48,403,56,60,648,671,70,47,152,66,325,49,552,59,131,160,158,60,41,46,74,97,84,49,128,93,55,64,65,303,261,209,162,79,74,75,54,60,40,108,65,56,228,50,77,66,60,145,57,144,243,37,57,36,36,34,34,72,45,51,43,65,71,65,63,55,57,44,38,73,464,59,82,184,65,82,35,33,39,41,62,59,71,56,83,154,35,75,62,57,93,69,75,108,126,81,37,74,304,109,185,86,91,45,38,53,49,60,89,49,67,80,47,42,43,127,66,52,91,93,57,68,205,71,56,63,80,53,59,210,85,107,84,50,70,73,75,43,57,45,123,410,90,74,44,52,83,59,50,39,60,66,225,80,65,75,55,85,61,37,50,57,55,44,40,52,57,70,57,34,34,43", "endOffsets": "181,236,287,340,390,444,496,546,605,671,724,775,826,877,930,22616,1013,1070,1119,1167,1220,1298,1368,1448,1522,1595,1662,1717,25207,1757,1932,1989,2079,2122,2185,2273,22781,23861,24085,24501,2316,115,25136,22981,25026,2366,2405,2495,2567,2655,3170,3688,3729,12834,12901,12768,12060,12986,3803,3874,4101,4423,4500,4549,4591,4653,4722,4775,4845,22687,4912,4958,22937,5033,5075,5148,5331,5401,5494,5571,5637,5724,5795,23511,5850,5905,5967,6028,6076,6153,6260,6310,6715,6773,6835,7485,8158,8230,8279,8433,8501,8828,8879,9433,9494,9627,9789,9949,10011,10054,10102,10178,10277,10363,10414,10544,10639,10696,10762,10829,11134,11397,11608,11772,11853,11929,13065,13121,24822,13163,13273,13340,13398,13628,13680,13759,13827,13889,14036,14095,14241,14486,14525,14584,14622,14660,14696,14732,14806,14853,14906,14951,15018,15091,24889,15156,15213,25313,15259,15299,15374,15840,15901,15985,16171,16238,16322,16359,16394,16435,16478,16542,16603,16676,16734,16819,16975,17012,22858,17076,17135,17230,17301,17378,17488,17616,17699,17738,17814,18120,23367,23256,23069,24029,24548,18160,18215,18266,18328,18419,18470,24154,18552,18601,18645,22732,18774,24957,25367,18867,18962,19021,19091,19298,19371,19429,19494,23449,19549,19610,24760,19697,19806,19892,19944,20016,23936,20093,20138,20197,20244,20369,20781,25459,20857,20903,20957,21042,21103,21155,21196,25088,21264,21491,21573,21640,21717,21774,21861,21924,21963,22015,23570,22072,22118,22160,22214,22273,22345,22404,22440,22476,22521"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,45,46,57,58,60,61,62,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,137,138,139,145,146,147,148,149,157,158,159,162,163,164,165,166,167,168,169,170,171,172,173,174,196,197,198,199,202,203,204,205,206,207,208,209,210,211,212,213,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,239,240,241,242,243,245,300,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,326,327,329,331,332,333,335,336,337,338,339,340,341,342,343,344,402,403,404,405,419,420,466,473,474,475,476,477,480,481,482,483,484,485,486,487,496,497,498,499,500,501,502,504,505,506,507,508,509,510,511,512,513,515,516,517,518,519,520,523,524,525,526,527,528,536,537,541,543,544,545,546,547,548,549,550,552,553,554,555,556,557,558,559,563,564,566,569,571,572,573,574,575,624,625,626,627,628,629,632,633,634,635,637,638,639,640,641,642,643,644", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "970,1035,1089,1139,1191,1240,1293,1344,1393,1451,1516,1568,1618,1668,1718,1770,2067,2149,2426,2474,3508,3560,3739,3808,3887,4220,4292,4358,4412,4482,4521,4695,4751,4840,4882,4944,5031,5079,5369,5424,5770,5812,5875,6655,6698,6766,6815,6853,6942,7013,7100,7614,8131,8171,8236,8302,9009,9094,9178,9251,9321,9547,9868,12055,12103,12144,12641,12709,12761,12830,12900,13697,13742,13820,14117,14158,14230,14412,14481,14573,14649,14714,14800,14870,14931,14985,15039,17716,17776,17823,17899,18181,18230,18634,18691,18752,19401,20073,20144,20192,20345,20412,20738,20986,21539,21599,21731,21892,22051,22112,22154,22201,22276,22374,22459,22509,22638,22732,22788,22853,22919,23223,23485,23695,23858,24025,24100,24176,24231,24292,24395,28624,29096,29153,29382,29433,29511,29578,29639,29785,29843,29988,30232,30270,30328,30365,30402,30437,30472,30545,30591,30643,30757,30823,30980,31138,31202,31258,31375,31420,31459,31533,31998,32058,32141,32326,32392,32475,38888,38922,38962,39004,40077,40137,43892,44424,44508,44663,44699,44775,45009,45067,45161,45231,45307,45416,45543,45625,46467,46542,46847,46957,47143,47230,47322,47450,47489,47543,47593,47654,47744,47794,47862,47943,47991,48174,48218,48346,48413,48466,48558,48817,48875,48944,49150,49222,49279,49764,49845,50141,50276,50487,50573,50681,50766,50817,50888,50962,51114,51158,51216,51262,51386,51797,51888,51963,52251,52304,52557,52762,52933,52973,53034,53101,53327,56777,56843,56919,56975,57061,57123,57317,57368,57426,57482,57587,57628,57681,57739,57810,57868,57903,57938", "endColumns": "64,53,49,51,48,52,50,48,57,64,51,49,49,49,51,93,81,55,47,46,51,76,68,78,72,71,65,53,69,38,173,55,88,41,61,86,47,289,54,345,41,62,46,42,67,48,37,88,70,86,513,516,39,64,65,706,84,83,72,69,225,320,75,47,40,60,67,51,68,69,65,44,77,73,40,71,181,68,91,75,64,85,69,60,53,53,60,59,46,75,105,48,403,56,60,648,671,70,47,152,66,325,49,552,59,131,160,158,60,41,46,74,97,84,49,128,93,55,64,65,303,261,209,162,79,74,75,54,60,40,108,65,56,228,50,77,66,60,145,57,144,243,37,57,36,36,34,34,72,45,51,43,65,71,65,63,55,57,44,38,73,464,59,82,184,65,82,35,33,39,41,62,59,71,56,83,154,35,75,62,57,93,69,75,108,126,81,37,74,304,109,185,86,91,45,38,53,49,60,89,49,67,80,47,42,43,127,66,52,91,93,57,68,205,71,56,63,80,53,59,210,85,107,84,50,70,73,75,43,57,45,123,410,90,74,44,52,83,59,50,39,60,66,225,80,65,75,55,85,61,37,50,57,55,44,40,52,57,70,57,34,34,43", "endOffsets": "1030,1084,1134,1186,1235,1288,1339,1388,1446,1511,1563,1613,1663,1713,1765,1859,2144,2200,2469,2516,3555,3632,3803,3882,3955,4287,4353,4407,4477,4516,4690,4746,4835,4877,4939,5026,5074,5364,5419,5765,5807,5870,5917,6693,6761,6810,6848,6937,7008,7095,7609,8126,8166,8231,8297,9004,9089,9173,9246,9316,9542,9863,9939,12098,12139,12200,12704,12756,12825,12895,12961,13737,13815,13889,14153,14225,14407,14476,14568,14644,14709,14795,14865,14926,14980,15034,15095,17771,17818,17894,18000,18225,18629,18686,18747,19396,20068,20139,20187,20340,20407,20733,20783,21534,21594,21726,21887,22046,22107,22149,22196,22271,22369,22454,22504,22633,22727,22783,22848,22914,23218,23480,23690,23853,23933,24095,24171,24226,24287,24328,24499,28685,29148,29377,29428,29506,29573,29634,29780,29838,29983,30227,30265,30323,30360,30397,30432,30467,30540,30586,30638,30682,30818,30890,31041,31197,31253,31311,31415,31454,31528,31993,32053,32136,32321,32387,32470,32506,38917,38957,38999,39062,40132,40204,43944,44503,44658,44694,44770,44833,45062,45156,45226,45302,45411,45538,45620,45658,46537,46842,46952,47138,47225,47317,47363,47484,47538,47588,47649,47739,47789,47857,47938,47986,48029,48213,48341,48408,48461,48553,48647,48870,48939,49145,49217,49274,49338,49840,49894,50196,50482,50568,50676,50761,50812,50883,50957,51033,51153,51211,51257,51381,51792,51883,51958,52003,52299,52383,52612,52808,52968,53029,53096,53322,53403,56838,56914,56970,57056,57118,57156,57363,57421,57477,57522,57623,57676,57734,57805,57863,57898,57933,57977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,506,612,880,972,1066,1162,1263,1370,1470,1574,1672,1770,2060,2470,2574,2832", "endColumns": "104,97,105,114,91,93,95,100,106,99,103,97,97,96,101,103,155,81", "endOffsets": "205,303,607,722,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,2157,2569,2725,2909"}, "to": {"startLines": "39,40,43,44,47,48,49,50,51,52,53,54,55,56,59,63,64,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1864,1969,2205,2311,2521,2613,2707,2803,2904,3011,3111,3215,3313,3411,3637,3960,4064,49899", "endColumns": "104,97,105,114,91,93,95,100,106,99,103,97,97,96,101,103,155,81", "endOffsets": "1964,2062,2306,2421,2608,2702,2798,2899,3006,3106,3210,3308,3406,3503,3734,4059,4215,49976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,89", "endOffsets": "137,227"}, "to": {"startLines": "576,577", "startColumns": "4,4", "startOffsets": "53408,53495", "endColumns": "86,89", "endOffsets": "53490,53580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9b1d1c56b528abe32d69bef2c9ce868e\\transformed\\jetified-bigo-ads-5.3.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,198,263,325,395,477,569,656,720,781,839,1251,1326,1409,1466,1545,1606,1659,1720,1768,1852,1913,1967,2021,2088", "endColumns": "68,73,64,61,69,81,91,86,63,60,57,411,74,82,56,78,60,52,60,47,83,60,53,53,66,77", "endOffsets": "119,193,258,320,390,472,564,651,715,776,834,1246,1321,1404,1461,1540,1601,1654,1715,1763,1847,1908,1962,2016,2083,2161"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9944,10013,10087,10152,10214,10284,10366,10458,10545,10609,10670,10728,11140,11215,11298,11355,11434,11495,11548,11609,11657,11741,11802,11856,11910,11977", "endColumns": "68,73,64,61,69,81,91,86,63,60,57,411,74,82,56,78,60,52,60,47,83,60,53,53,66,77", "endOffsets": "10008,10082,10147,10209,10279,10361,10453,10540,10604,10665,10723,11135,11210,11293,11350,11429,11490,11543,11604,11652,11736,11797,11851,11905,11972,12050"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,270,342,419,486,583,674", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "124,191,265,337,414,481,578,669,745"}, "to": {"startLines": "271,272,273,274,275,276,277,278,279", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "26632,26706,26773,26847,26919,26996,27063,27160,27251", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "26701,26768,26842,26914,26991,27058,27155,27246,27322"}}]}]}