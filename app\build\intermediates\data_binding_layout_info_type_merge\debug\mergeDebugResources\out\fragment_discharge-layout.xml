<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_discharge" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_discharge.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView" rootNodeViewId="@+id/scroll_view"><Targets><Target id="@+id/scroll_view" tag="layout/fragment_discharge_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="1689" endOffset="39"/></Target><Target id="@+id/discharge_text_percent" view="TextView"><Expressions/><location startLine="23" startOffset="16" endLine="33" endOffset="62"/></Target><Target id="@+id/discharge_text_percent3" view="TextView"><Expressions/><location startLine="53" startOffset="16" endLine="70" endOffset="45"/></Target><Target id="@+id/day_block" view="RelativeLayout"><Expressions/><location startLine="72" startOffset="16" endLine="109" endOffset="32"/></Target><Target id="@+id/discharge_sun" view="TextView"><Expressions/><location startLine="81" startOffset="20" endLine="90" endOffset="49"/></Target><Target id="@+id/all_block" view="RelativeLayout"><Expressions/><location startLine="111" startOffset="16" endLine="148" endOffset="32"/></Target><Target id="@+id/discharge_all" view="TextView"><Expressions/><location startLine="120" startOffset="20" endLine="129" endOffset="49"/></Target><Target id="@+id/night_block" view="RelativeLayout"><Expressions/><location startLine="150" startOffset="16" endLine="187" endOffset="32"/></Target><Target id="@+id/discharge_night" view="TextView"><Expressions/><location startLine="159" startOffset="20" endLine="168" endOffset="49"/></Target><Target id="@+id/i_1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="203" startOffset="12" endLine="297" endOffset="63"/></Target><Target id="@+id/time_day_session" view="TextView"><Expressions/><location startLine="218" startOffset="16" endLine="232" endOffset="88"/></Target><Target id="@+id/iir" view="TextView"><Expressions/><location startLine="248" startOffset="16" endLine="258" endOffset="87"/></Target><Target id="@+id/info_day_speed_session" view="TextView"><Expressions/><location startLine="259" startOffset="16" endLine="272" endOffset="80"/></Target><Target id="@+id/info_day_percent_session" view="TextView"><Expressions/><location startLine="273" startOffset="16" endLine="283" endOffset="67"/></Target><Target id="@+id/i11" view="TextView"><Expressions/><location startLine="284" startOffset="16" endLine="296" endOffset="62"/></Target><Target id="@+id/i_2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="298" startOffset="12" endLine="391" endOffset="63"/></Target><Target id="@+id/i332" view="TextView"><Expressions/><location startLine="323" startOffset="16" endLine="338" endOffset="87"/></Target><Target id="@+id/info_night_speed_session" view="TextView"><Expressions/><location startLine="339" startOffset="16" endLine="352" endOffset="82"/></Target><Target id="@+id/time_night_session" view="TextView"><Expressions/><location startLine="353" startOffset="16" endLine="367" endOffset="90"/></Target><Target id="@+id/info_night_percent_session" view="TextView"><Expressions/><location startLine="368" startOffset="16" endLine="377" endOffset="67"/></Target><Target id="@+id/i93" view="TextView"><Expressions/><location startLine="378" startOffset="16" endLine="390" endOffset="62"/></Target><Target id="@+id/i_t" view="TextView"><Expressions/><location startLine="392" startOffset="12" endLine="409" endOffset="58"/></Target><Target id="@+id/discharge_rate_info" view="ImageView"><Expressions/><location startLine="410" startOffset="12" endLine="421" endOffset="60"/></Target><Target id="@+id/button_disscharge_using" view="TextView"><Expressions/><location startLine="422" startOffset="12" endLine="439" endOffset="63"/></Target><Target id="@+id/battery_alarm_btn" view="TextView"><Expressions/><location startLine="440" startOffset="12" endLine="457" endOffset="83"/></Target><Target id="@+id/st_text" view="TextView"><Expressions/><location startLine="472" startOffset="12" endLine="488" endOffset="58"/></Target><Target id="@+id/discharge_session_info" view="ImageView"><Expressions/><location startLine="489" startOffset="12" endLine="500" endOffset="64"/></Target><Target id="@+id/s_6" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="501" startOffset="12" endLine="572" endOffset="63"/></Target><Target id="@+id/text_speed_dis_day_session2" view="TextView"><Expressions/><location startLine="539" startOffset="16" endLine="548" endOffset="92"/></Target><Target id="@+id/text_percent_dis_day_session" view="TextView"><Expressions/><location startLine="549" startOffset="16" endLine="558" endOffset="68"/></Target><Target id="@+id/te77" view="TextView"><Expressions/><location startLine="559" startOffset="16" endLine="571" endOffset="62"/></Target><Target id="@+id/s_5" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="573" startOffset="12" endLine="644" endOffset="63"/></Target><Target id="@+id/text_speed_dis_night_session" view="TextView"><Expressions/><location startLine="611" startOffset="16" endLine="620" endOffset="94"/></Target><Target id="@+id/text_percent_dis_night_session" view="TextView"><Expressions/><location startLine="621" startOffset="16" endLine="630" endOffset="68"/></Target><Target id="@+id/te88" view="TextView"><Expressions/><location startLine="631" startOffset="16" endLine="643" endOffset="62"/></Target><Target id="@+id/s_3" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="645" startOffset="12" endLine="721" endOffset="63"/></Target><Target id="@+id/text_speed_dis_all_session" view="TextView"><Expressions/><location startLine="684" startOffset="16" endLine="697" endOffset="92"/></Target><Target id="@+id/text_percent_dis_all_session" view="TextView"><Expressions/><location startLine="698" startOffset="16" endLine="707" endOffset="68"/></Target><Target id="@+id/te99" view="TextView"><Expressions/><location startLine="708" startOffset="16" endLine="720" endOffset="62"/></Target><Target id="@+id/s_4" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="722" startOffset="12" endLine="816" endOffset="63"/></Target><Target id="@+id/textView_percent" view="TextView"><Expressions/><location startLine="737" startOffset="16" endLine="747" endOffset="92"/></Target><Target id="@+id/discharge_session_percent" view="TextView"><Expressions/><location startLine="748" startOffset="16" endLine="763" endOffset="79"/></Target><Target id="@+id/textView9" view="TextView"><Expressions/><location startLine="764" startOffset="16" endLine="778" endOffset="89"/></Target><Target id="@+id/text_speed_dis_day_session" view="TextView"><Expressions/><location startLine="779" startOffset="16" endLine="792" endOffset="93"/></Target><Target id="@+id/text_percent_dis_session_last" view="TextView"><Expressions/><location startLine="793" startOffset="16" endLine="802" endOffset="67"/></Target><Target id="@+id/te0" view="TextView"><Expressions/><location startLine="803" startOffset="16" endLine="815" endOffset="62"/></Target><Target id="@+id/s_2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="817" startOffset="12" endLine="876" endOffset="63"/></Target><Target id="@+id/textView7" view="TextView"><Expressions/><location startLine="832" startOffset="16" endLine="843" endOffset="83"/></Target><Target id="@+id/text_now_dis_session" view="TextView"><Expressions/><location startLine="853" startOffset="16" endLine="862" endOffset="71"/></Target><Target id="@+id/text226" view="TextView"><Expressions/><location startLine="863" startOffset="16" endLine="875" endOffset="62"/></Target><Target id="@+id/s_1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="877" startOffset="12" endLine="933" endOffset="63"/></Target><Target id="@+id/time_dis_session_start" view="TextView"><Expressions/><location startLine="892" startOffset="16" endLine="905" endOffset="89"/></Target><Target id="@+id/text_fulltime_dis_session" view="TextView"><Expressions/><location startLine="906" startOffset="16" endLine="919" endOffset="73"/></Target><Target id="@+id/textView6" view="TextView"><Expressions/><location startLine="920" startOffset="16" endLine="932" endOffset="62"/></Target><Target id="@+id/reset_session_discharge_button" view="TextView"><Expressions/><location startLine="934" startOffset="12" endLine="951" endOffset="63"/></Target><Target id="@+id/st_texeet" view="TextView"><Expressions/><location startLine="966" startOffset="12" endLine="982" endOffset="58"/></Target><Target id="@+id/operation_session_info" view="ImageView"><Expressions/><location startLine="983" startOffset="12" endLine="994" endOffset="66"/></Target><Target id="@+id/s_8" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="995" startOffset="12" endLine="1047" endOffset="63"/></Target><Target id="@+id/deep_percent" view="TextView"><Expressions/><location startLine="1009" startOffset="16" endLine="1023" endOffset="96"/></Target><Target id="@+id/text_percent_dis_day_session_deep" view="TextView"><Expressions/><location startLine="1024" startOffset="16" endLine="1033" endOffset="70"/></Target><Target id="@+id/te7722" view="TextView"><Expressions/><location startLine="1034" startOffset="16" endLine="1046" endOffset="62"/></Target><Target id="@+id/s_7" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1048" startOffset="12" endLine="1124" endOffset="63"/></Target><Target id="@+id/awake_percent" view="TextView"><Expressions/><location startLine="1062" startOffset="16" endLine="1076" endOffset="99"/></Target><Target id="@+id/text_speed_dis_night_session_awake" view="TextView"><Expressions/><location startLine="1091" startOffset="16" endLine="1100" endOffset="100"/></Target><Target id="@+id/text_percent_dis_night_session_awake" view="TextView"><Expressions/><location startLine="1101" startOffset="16" endLine="1110" endOffset="70"/></Target><Target id="@+id/te88ww" view="TextView"><Expressions/><location startLine="1111" startOffset="16" endLine="1123" endOffset="62"/></Target><Target id="@+id/i_text" view="TextView"><Expressions/><location startLine="1139" startOffset="12" endLine="1156" endOffset="58"/></Target><Target id="@+id/average_info" view="ImageView"><Expressions/><location startLine="1157" startOffset="12" endLine="1168" endOffset="63"/></Target><Target id="@+id/i3" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1169" startOffset="12" endLine="1250" endOffset="63"/></Target><Target id="@+id/mah_3" view="TextView"><Expressions/><location startLine="1177" startOffset="16" endLine="1188" endOffset="62"/></Target><Target id="@+id/discharge_speed_mah_all" view="TextView"><Expressions/><location startLine="1189" startOffset="16" endLine="1202" endOffset="62"/></Target><Target id="@+id/per_3" view="TextView"><Expressions/><location startLine="1203" startOffset="16" endLine="1213" endOffset="90"/></Target><Target id="@+id/discharge_speed_percent_all" view="TextView"><Expressions/><location startLine="1214" startOffset="16" endLine="1227" endOffset="62"/></Target><Target id="@+id/i2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1251" startOffset="12" endLine="1332" endOffset="63"/></Target><Target id="@+id/mah_2" view="TextView"><Expressions/><location startLine="1259" startOffset="16" endLine="1270" endOffset="62"/></Target><Target id="@+id/discharge_speed_mah_night" view="TextView"><Expressions/><location startLine="1271" startOffset="16" endLine="1284" endOffset="62"/></Target><Target id="@+id/per_2" view="TextView"><Expressions/><location startLine="1285" startOffset="16" endLine="1295" endOffset="92"/></Target><Target id="@+id/discharge_speed_percent_night" view="TextView"><Expressions/><location startLine="1296" startOffset="16" endLine="1309" endOffset="62"/></Target><Target id="@+id/i1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1333" startOffset="12" endLine="1414" endOffset="63"/></Target><Target id="@+id/mah_1" view="TextView"><Expressions/><location startLine="1341" startOffset="16" endLine="1352" endOffset="62"/></Target><Target id="@+id/discharge_speed_mah_day" view="TextView"><Expressions/><location startLine="1353" startOffset="16" endLine="1366" endOffset="62"/></Target><Target id="@+id/per_1" view="TextView"><Expressions/><location startLine="1367" startOffset="16" endLine="1377" endOffset="90"/></Target><Target id="@+id/discharge_speed_percent_day" view="TextView"><Expressions/><location startLine="1378" startOffset="16" endLine="1391" endOffset="62"/></Target><Target id="@+id/indent_down" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1417" startOffset="8" endLine="1658" endOffset="59"/></Target><Target id="@+id/f_text" view="TextView"><Expressions/><location startLine="1433" startOffset="12" endLine="1450" endOffset="59"/></Target><Target id="@+id/full_percent_info" view="ImageView"><Expressions/><location startLine="1452" startOffset="12" endLine="1463" endOffset="64"/></Target><Target id="@+id/f3" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1465" startOffset="12" endLine="1527" endOffset="63"/></Target><Target id="@+id/discharge_fulltime_remaining_all" view="TextView"><Expressions/><location startLine="1474" startOffset="16" endLine="1488" endOffset="63"/></Target><Target id="@+id/full2" view="TextView"><Expressions/><location startLine="1504" startOffset="16" endLine="1526" endOffset="63"/></Target><Target id="@+id/f2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1529" startOffset="12" endLine="1593" endOffset="63"/></Target><Target id="@+id/discharge_fulltime_remaining_night" view="TextView"><Expressions/><location startLine="1538" startOffset="16" endLine="1554" endOffset="63"/></Target><Target id="@+id/full3" view="TextView"><Expressions/><location startLine="1570" startOffset="16" endLine="1592" endOffset="63"/></Target><Target id="@+id/f1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1595" startOffset="12" endLine="1657" endOffset="63"/></Target><Target id="@+id/discharge_fulltime_remaining_day" view="TextView"><Expressions/><location startLine="1604" startOffset="16" endLine="1618" endOffset="63"/></Target><Target id="@+id/full1" view="TextView"><Expressions/><location startLine="1634" startOffset="16" endLine="1656" endOffset="63"/></Target><Target id="@+id/update_view" view="LinearLayout"><Expressions/><location startLine="1659" startOffset="8" endLine="1687" endOffset="22"/></Target><Target id="@+id/update_view_btn" view="TextView"><Expressions/><location startLine="1669" startOffset="12" endLine="1686" endOffset="47"/></Target></Targets></Layout>