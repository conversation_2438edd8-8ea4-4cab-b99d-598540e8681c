// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.Barrier;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.github.mikephil.charting.charts.LineChart;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentChargeBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final LinearLayout allBlock;

  @NonNull
  public final RelativeLayout amperageTable;

  @NonNull
  public final TextView batteryAlarmBtn;

  @NonNull
  public final ImageView batteryWearInfo;

  @NonNull
  public final TextView btn1;

  @NonNull
  public final TextView btn2;

  @NonNull
  public final TextView btn3;

  @NonNull
  public final TextView btnSelector1;

  @NonNull
  public final TextView btnSelector2;

  @NonNull
  public final TextView cText;

  @NonNull
  public final TextView cTextNiAmpere;

  @NonNull
  public final TextView cTextNiPower;

  @NonNull
  public final ImageView chargeAvgInfo;

  @NonNull
  public final CircularProgressIndicator chargeProgBarPercent;

  @NonNull
  public final ImageView chargeSessionInfo;

  @NonNull
  public final TextView chargeSessionPercent;

  @NonNull
  public final LinearLayout chargeUp;

  @NonNull
  public final RelativeLayout chart1Percent;

  @NonNull
  public final LineChart chartPercent;

  @NonNull
  public final ConstraintLayout cs1;

  @NonNull
  public final ConstraintLayout cs2;

  @NonNull
  public final ConstraintLayout cs21;

  @NonNull
  public final ConstraintLayout cs3;

  @NonNull
  public final ConstraintLayout cs4;

  @NonNull
  public final TextView csText;

  @NonNull
  public final ConstraintLayout currentSessionBlock;

  @NonNull
  public final ConstraintLayout daView;

  @NonNull
  public final ProgressBar damageBarPercent;

  @NonNull
  public final ProgressBar damageBarSeekwhite;

  @NonNull
  public final TextView day1Percent;

  @NonNull
  public final TextView day2Percent;

  @NonNull
  public final TextView day3Percent;

  @NonNull
  public final TextView day4Percent;

  @NonNull
  public final TextView day5Percent;

  @NonNull
  public final TextView day6Percent;

  @NonNull
  public final TextView day7Percent;

  @NonNull
  public final LinearLayout dayBlock;

  @NonNull
  public final RelativeLayout graphPercent;

  @NonNull
  public final ConstraintLayout i1;

  @NonNull
  public final ConstraintLayout i2;

  @NonNull
  public final ConstraintLayout i3;

  @NonNull
  public final TextView iText;

  @NonNull
  public final ImageView imageView18;

  @NonNull
  public final ConstraintLayout indentDown;

  @NonNull
  public final TextView mah1;

  @NonNull
  public final TextView mah2;

  @NonNull
  public final TextView mah3;

  @NonNull
  public final LayoutNativeAdsBinding nativeAd;

  @NonNull
  public final LinearLayout nightBlock;

  @NonNull
  public final TextView notChargingMessage;

  @NonNull
  public final TextView pText;

  @NonNull
  public final TextView per1;

  @NonNull
  public final TextView per2;

  @NonNull
  public final TextView per3;

  @NonNull
  public final ConstraintLayout percentGraphChange;

  @NonNull
  public final RelativeLayout percentLayout;

  @NonNull
  public final ProgressBar progressBarAveragespeed;

  @NonNull
  public final ProgressBar progressBarAveragespeedGrey;

  @NonNull
  public final ProgressBar progressBarCurrent;

  @NonNull
  public final ProgressBar progressBarCurrentMinus;

  @NonNull
  public final ProgressBar progressBarPower;

  @NonNull
  public final ProgressBar progressBarPowerMinus;

  @NonNull
  public final ProgressBar progressBarRemainTo100;

  @NonNull
  public final ProgressBar progressBarRemainTo1002;

  @NonNull
  public final ProgressBar progressBarRemainToVar;

  @NonNull
  public final ProgressBar progressBarRemainToVar2;

  @NonNull
  public final ProgressBar progressBarTemp;

  @NonNull
  public final ProgressBar progressBarVoltage;

  @NonNull
  public final LinearLayout promoContainer;

  @NonNull
  public final RelativeLayout pus;

  @NonNull
  public final ConstraintLayout rem1;

  @NonNull
  public final ConstraintLayout rem2;

  @NonNull
  public final Barrier remBarrier;

  @NonNull
  public final TextView remText;

  @NonNull
  public final Barrier remValBarrier;

  @NonNull
  public final ConstraintLayout remainingTable;

  @NonNull
  public final TextView resetSessionChargeButton;

  @NonNull
  public final ConstraintLayout s1;

  @NonNull
  public final ConstraintLayout s2;

  @NonNull
  public final ConstraintLayout s3;

  @NonNull
  public final ConstraintLayout s4;

  @NonNull
  public final ConstraintLayout s5;

  @NonNull
  public final ConstraintLayout s6;

  @NonNull
  public final LinearLayout saleContainer;

  @NonNull
  public final NestedScrollView scrollView;

  @NonNull
  public final LinearLayout sdfsd;

  @NonNull
  public final SeekBar seekBar;

  @NonNull
  public final ConstraintLayout selectorAmperage;

  @NonNull
  public final TextView speedchargePercentText;

  @NonNull
  public final TextView stText;

  @NonNull
  public final TextView t1;

  @NonNull
  public final TextView t2;

  @NonNull
  public final TextView t26;

  @NonNull
  public final TextView t3;

  @NonNull
  public final TextView t4;

  @NonNull
  public final TextView t5;

  @NonNull
  public final TextView t6;

  @NonNull
  public final TextView t7;

  @NonNull
  public final TextView t8;

  @NonNull
  public final TextView t9;

  @NonNull
  public final TextView te0;

  @NonNull
  public final TextView te77;

  @NonNull
  public final TextView te88;

  @NonNull
  public final TextView te99;

  @NonNull
  public final TextView testView;

  @NonNull
  public final TextView text226;

  @NonNull
  public final TextView textFulltimeChargeSession;

  @NonNull
  public final TextView textPercent;

  @NonNull
  public final TextView textPercentChargeAll;

  @NonNull
  public final TextView textPercentChargeAllSession;

  @NonNull
  public final TextView textPercentChargeDay;

  @NonNull
  public final TextView textPercentChargeDaySession;

  @NonNull
  public final TextView textPercentChargeNight;

  @NonNull
  public final TextView textPercentChargeNightSession;

  @NonNull
  public final TextView textPercentChargeSessionLast;

  @NonNull
  public final TextView textRemainTo100;

  @NonNull
  public final TextView textRemainTo100Charge;

  @NonNull
  public final TextView textRemainVar;

  @NonNull
  public final TextView textRemainVar22;

  @NonNull
  public final TextView textRemainVar33;

  @NonNull
  public final TextView textRemainVar44;

  @NonNull
  public final TextView textRemainVar55;

  @NonNull
  public final TextView textSpeedChargeAll;

  @NonNull
  public final TextView textSpeedChargeAllSession;

  @NonNull
  public final TextView textSpeedChargeDay;

  @NonNull
  public final TextView textSpeedChargeDaySession;

  @NonNull
  public final TextView textSpeedChargeDaySession2;

  @NonNull
  public final TextView textSpeedChargeNight;

  @NonNull
  public final TextView textSpeedChargeNightSession;

  @NonNull
  public final TextView textTimeDay;

  @NonNull
  public final TextView textTimeDaynight;

  @NonNull
  public final TextView textTimeNight;

  @NonNull
  public final TextView textView6;

  @NonNull
  public final TextView textView7;

  @NonNull
  public final TextView textView9;

  @NonNull
  public final TextView textViewPercent;

  @NonNull
  public final TextView textedd;

  @NonNull
  public final TextView timeChargeSessionStart;

  @NonNull
  public final LinearLayout timeNum;

  @NonNull
  public final LinearLayout underGraphPercent;

  @NonNull
  public final LinearLayout updateView;

  @NonNull
  public final TextView updateViewBtn;

  @NonNull
  public final TextView vText;

  @NonNull
  public final TextView valAverageSpeed;

  @NonNull
  public final TextView valCurrrentCharging;

  @NonNull
  public final TextView valPowerCharging;

  @NonNull
  public final TextView valRemainTo100;

  @NonNull
  public final TextView valRemainToVar;

  @NonNull
  public final TextView valTemp2;

  @NonNull
  public final TextView valTempText;

  @NonNull
  public final TextView valVoltage;

  @NonNull
  public final TextView varDamageUp4;

  @NonNull
  public final TextView varSpeedchargePercentNow;

  @NonNull
  public final TextView wearRatePercent;

  private FragmentChargeBinding(@NonNull NestedScrollView rootView, @NonNull LinearLayout allBlock,
      @NonNull RelativeLayout amperageTable, @NonNull TextView batteryAlarmBtn,
      @NonNull ImageView batteryWearInfo, @NonNull TextView btn1, @NonNull TextView btn2,
      @NonNull TextView btn3, @NonNull TextView btnSelector1, @NonNull TextView btnSelector2,
      @NonNull TextView cText, @NonNull TextView cTextNiAmpere, @NonNull TextView cTextNiPower,
      @NonNull ImageView chargeAvgInfo, @NonNull CircularProgressIndicator chargeProgBarPercent,
      @NonNull ImageView chargeSessionInfo, @NonNull TextView chargeSessionPercent,
      @NonNull LinearLayout chargeUp, @NonNull RelativeLayout chart1Percent,
      @NonNull LineChart chartPercent, @NonNull ConstraintLayout cs1, @NonNull ConstraintLayout cs2,
      @NonNull ConstraintLayout cs21, @NonNull ConstraintLayout cs3, @NonNull ConstraintLayout cs4,
      @NonNull TextView csText, @NonNull ConstraintLayout currentSessionBlock,
      @NonNull ConstraintLayout daView, @NonNull ProgressBar damageBarPercent,
      @NonNull ProgressBar damageBarSeekwhite, @NonNull TextView day1Percent,
      @NonNull TextView day2Percent, @NonNull TextView day3Percent, @NonNull TextView day4Percent,
      @NonNull TextView day5Percent, @NonNull TextView day6Percent, @NonNull TextView day7Percent,
      @NonNull LinearLayout dayBlock, @NonNull RelativeLayout graphPercent,
      @NonNull ConstraintLayout i1, @NonNull ConstraintLayout i2, @NonNull ConstraintLayout i3,
      @NonNull TextView iText, @NonNull ImageView imageView18, @NonNull ConstraintLayout indentDown,
      @NonNull TextView mah1, @NonNull TextView mah2, @NonNull TextView mah3,
      @NonNull LayoutNativeAdsBinding nativeAd, @NonNull LinearLayout nightBlock,
      @NonNull TextView notChargingMessage, @NonNull TextView pText, @NonNull TextView per1,
      @NonNull TextView per2, @NonNull TextView per3, @NonNull ConstraintLayout percentGraphChange,
      @NonNull RelativeLayout percentLayout, @NonNull ProgressBar progressBarAveragespeed,
      @NonNull ProgressBar progressBarAveragespeedGrey, @NonNull ProgressBar progressBarCurrent,
      @NonNull ProgressBar progressBarCurrentMinus, @NonNull ProgressBar progressBarPower,
      @NonNull ProgressBar progressBarPowerMinus, @NonNull ProgressBar progressBarRemainTo100,
      @NonNull ProgressBar progressBarRemainTo1002, @NonNull ProgressBar progressBarRemainToVar,
      @NonNull ProgressBar progressBarRemainToVar2, @NonNull ProgressBar progressBarTemp,
      @NonNull ProgressBar progressBarVoltage, @NonNull LinearLayout promoContainer,
      @NonNull RelativeLayout pus, @NonNull ConstraintLayout rem1, @NonNull ConstraintLayout rem2,
      @NonNull Barrier remBarrier, @NonNull TextView remText, @NonNull Barrier remValBarrier,
      @NonNull ConstraintLayout remainingTable, @NonNull TextView resetSessionChargeButton,
      @NonNull ConstraintLayout s1, @NonNull ConstraintLayout s2, @NonNull ConstraintLayout s3,
      @NonNull ConstraintLayout s4, @NonNull ConstraintLayout s5, @NonNull ConstraintLayout s6,
      @NonNull LinearLayout saleContainer, @NonNull NestedScrollView scrollView,
      @NonNull LinearLayout sdfsd, @NonNull SeekBar seekBar,
      @NonNull ConstraintLayout selectorAmperage, @NonNull TextView speedchargePercentText,
      @NonNull TextView stText, @NonNull TextView t1, @NonNull TextView t2, @NonNull TextView t26,
      @NonNull TextView t3, @NonNull TextView t4, @NonNull TextView t5, @NonNull TextView t6,
      @NonNull TextView t7, @NonNull TextView t8, @NonNull TextView t9, @NonNull TextView te0,
      @NonNull TextView te77, @NonNull TextView te88, @NonNull TextView te99,
      @NonNull TextView testView, @NonNull TextView text226,
      @NonNull TextView textFulltimeChargeSession, @NonNull TextView textPercent,
      @NonNull TextView textPercentChargeAll, @NonNull TextView textPercentChargeAllSession,
      @NonNull TextView textPercentChargeDay, @NonNull TextView textPercentChargeDaySession,
      @NonNull TextView textPercentChargeNight, @NonNull TextView textPercentChargeNightSession,
      @NonNull TextView textPercentChargeSessionLast, @NonNull TextView textRemainTo100,
      @NonNull TextView textRemainTo100Charge, @NonNull TextView textRemainVar,
      @NonNull TextView textRemainVar22, @NonNull TextView textRemainVar33,
      @NonNull TextView textRemainVar44, @NonNull TextView textRemainVar55,
      @NonNull TextView textSpeedChargeAll, @NonNull TextView textSpeedChargeAllSession,
      @NonNull TextView textSpeedChargeDay, @NonNull TextView textSpeedChargeDaySession,
      @NonNull TextView textSpeedChargeDaySession2, @NonNull TextView textSpeedChargeNight,
      @NonNull TextView textSpeedChargeNightSession, @NonNull TextView textTimeDay,
      @NonNull TextView textTimeDaynight, @NonNull TextView textTimeNight,
      @NonNull TextView textView6, @NonNull TextView textView7, @NonNull TextView textView9,
      @NonNull TextView textViewPercent, @NonNull TextView textedd,
      @NonNull TextView timeChargeSessionStart, @NonNull LinearLayout timeNum,
      @NonNull LinearLayout underGraphPercent, @NonNull LinearLayout updateView,
      @NonNull TextView updateViewBtn, @NonNull TextView vText, @NonNull TextView valAverageSpeed,
      @NonNull TextView valCurrrentCharging, @NonNull TextView valPowerCharging,
      @NonNull TextView valRemainTo100, @NonNull TextView valRemainToVar,
      @NonNull TextView valTemp2, @NonNull TextView valTempText, @NonNull TextView valVoltage,
      @NonNull TextView varDamageUp4, @NonNull TextView varSpeedchargePercentNow,
      @NonNull TextView wearRatePercent) {
    this.rootView = rootView;
    this.allBlock = allBlock;
    this.amperageTable = amperageTable;
    this.batteryAlarmBtn = batteryAlarmBtn;
    this.batteryWearInfo = batteryWearInfo;
    this.btn1 = btn1;
    this.btn2 = btn2;
    this.btn3 = btn3;
    this.btnSelector1 = btnSelector1;
    this.btnSelector2 = btnSelector2;
    this.cText = cText;
    this.cTextNiAmpere = cTextNiAmpere;
    this.cTextNiPower = cTextNiPower;
    this.chargeAvgInfo = chargeAvgInfo;
    this.chargeProgBarPercent = chargeProgBarPercent;
    this.chargeSessionInfo = chargeSessionInfo;
    this.chargeSessionPercent = chargeSessionPercent;
    this.chargeUp = chargeUp;
    this.chart1Percent = chart1Percent;
    this.chartPercent = chartPercent;
    this.cs1 = cs1;
    this.cs2 = cs2;
    this.cs21 = cs21;
    this.cs3 = cs3;
    this.cs4 = cs4;
    this.csText = csText;
    this.currentSessionBlock = currentSessionBlock;
    this.daView = daView;
    this.damageBarPercent = damageBarPercent;
    this.damageBarSeekwhite = damageBarSeekwhite;
    this.day1Percent = day1Percent;
    this.day2Percent = day2Percent;
    this.day3Percent = day3Percent;
    this.day4Percent = day4Percent;
    this.day5Percent = day5Percent;
    this.day6Percent = day6Percent;
    this.day7Percent = day7Percent;
    this.dayBlock = dayBlock;
    this.graphPercent = graphPercent;
    this.i1 = i1;
    this.i2 = i2;
    this.i3 = i3;
    this.iText = iText;
    this.imageView18 = imageView18;
    this.indentDown = indentDown;
    this.mah1 = mah1;
    this.mah2 = mah2;
    this.mah3 = mah3;
    this.nativeAd = nativeAd;
    this.nightBlock = nightBlock;
    this.notChargingMessage = notChargingMessage;
    this.pText = pText;
    this.per1 = per1;
    this.per2 = per2;
    this.per3 = per3;
    this.percentGraphChange = percentGraphChange;
    this.percentLayout = percentLayout;
    this.progressBarAveragespeed = progressBarAveragespeed;
    this.progressBarAveragespeedGrey = progressBarAveragespeedGrey;
    this.progressBarCurrent = progressBarCurrent;
    this.progressBarCurrentMinus = progressBarCurrentMinus;
    this.progressBarPower = progressBarPower;
    this.progressBarPowerMinus = progressBarPowerMinus;
    this.progressBarRemainTo100 = progressBarRemainTo100;
    this.progressBarRemainTo1002 = progressBarRemainTo1002;
    this.progressBarRemainToVar = progressBarRemainToVar;
    this.progressBarRemainToVar2 = progressBarRemainToVar2;
    this.progressBarTemp = progressBarTemp;
    this.progressBarVoltage = progressBarVoltage;
    this.promoContainer = promoContainer;
    this.pus = pus;
    this.rem1 = rem1;
    this.rem2 = rem2;
    this.remBarrier = remBarrier;
    this.remText = remText;
    this.remValBarrier = remValBarrier;
    this.remainingTable = remainingTable;
    this.resetSessionChargeButton = resetSessionChargeButton;
    this.s1 = s1;
    this.s2 = s2;
    this.s3 = s3;
    this.s4 = s4;
    this.s5 = s5;
    this.s6 = s6;
    this.saleContainer = saleContainer;
    this.scrollView = scrollView;
    this.sdfsd = sdfsd;
    this.seekBar = seekBar;
    this.selectorAmperage = selectorAmperage;
    this.speedchargePercentText = speedchargePercentText;
    this.stText = stText;
    this.t1 = t1;
    this.t2 = t2;
    this.t26 = t26;
    this.t3 = t3;
    this.t4 = t4;
    this.t5 = t5;
    this.t6 = t6;
    this.t7 = t7;
    this.t8 = t8;
    this.t9 = t9;
    this.te0 = te0;
    this.te77 = te77;
    this.te88 = te88;
    this.te99 = te99;
    this.testView = testView;
    this.text226 = text226;
    this.textFulltimeChargeSession = textFulltimeChargeSession;
    this.textPercent = textPercent;
    this.textPercentChargeAll = textPercentChargeAll;
    this.textPercentChargeAllSession = textPercentChargeAllSession;
    this.textPercentChargeDay = textPercentChargeDay;
    this.textPercentChargeDaySession = textPercentChargeDaySession;
    this.textPercentChargeNight = textPercentChargeNight;
    this.textPercentChargeNightSession = textPercentChargeNightSession;
    this.textPercentChargeSessionLast = textPercentChargeSessionLast;
    this.textRemainTo100 = textRemainTo100;
    this.textRemainTo100Charge = textRemainTo100Charge;
    this.textRemainVar = textRemainVar;
    this.textRemainVar22 = textRemainVar22;
    this.textRemainVar33 = textRemainVar33;
    this.textRemainVar44 = textRemainVar44;
    this.textRemainVar55 = textRemainVar55;
    this.textSpeedChargeAll = textSpeedChargeAll;
    this.textSpeedChargeAllSession = textSpeedChargeAllSession;
    this.textSpeedChargeDay = textSpeedChargeDay;
    this.textSpeedChargeDaySession = textSpeedChargeDaySession;
    this.textSpeedChargeDaySession2 = textSpeedChargeDaySession2;
    this.textSpeedChargeNight = textSpeedChargeNight;
    this.textSpeedChargeNightSession = textSpeedChargeNightSession;
    this.textTimeDay = textTimeDay;
    this.textTimeDaynight = textTimeDaynight;
    this.textTimeNight = textTimeNight;
    this.textView6 = textView6;
    this.textView7 = textView7;
    this.textView9 = textView9;
    this.textViewPercent = textViewPercent;
    this.textedd = textedd;
    this.timeChargeSessionStart = timeChargeSessionStart;
    this.timeNum = timeNum;
    this.underGraphPercent = underGraphPercent;
    this.updateView = updateView;
    this.updateViewBtn = updateViewBtn;
    this.vText = vText;
    this.valAverageSpeed = valAverageSpeed;
    this.valCurrrentCharging = valCurrrentCharging;
    this.valPowerCharging = valPowerCharging;
    this.valRemainTo100 = valRemainTo100;
    this.valRemainToVar = valRemainToVar;
    this.valTemp2 = valTemp2;
    this.valTempText = valTempText;
    this.valVoltage = valVoltage;
    this.varDamageUp4 = varDamageUp4;
    this.varSpeedchargePercentNow = varSpeedchargePercentNow;
    this.wearRatePercent = wearRatePercent;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentChargeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentChargeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_charge, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentChargeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.all_block;
      LinearLayout allBlock = ViewBindings.findChildViewById(rootView, id);
      if (allBlock == null) {
        break missingId;
      }

      id = R.id.amperage_table;
      RelativeLayout amperageTable = ViewBindings.findChildViewById(rootView, id);
      if (amperageTable == null) {
        break missingId;
      }

      id = R.id.battery_alarm_btn;
      TextView batteryAlarmBtn = ViewBindings.findChildViewById(rootView, id);
      if (batteryAlarmBtn == null) {
        break missingId;
      }

      id = R.id.battery_wear_info;
      ImageView batteryWearInfo = ViewBindings.findChildViewById(rootView, id);
      if (batteryWearInfo == null) {
        break missingId;
      }

      id = R.id.btn1;
      TextView btn1 = ViewBindings.findChildViewById(rootView, id);
      if (btn1 == null) {
        break missingId;
      }

      id = R.id.btn2;
      TextView btn2 = ViewBindings.findChildViewById(rootView, id);
      if (btn2 == null) {
        break missingId;
      }

      id = R.id.btn3;
      TextView btn3 = ViewBindings.findChildViewById(rootView, id);
      if (btn3 == null) {
        break missingId;
      }

      id = R.id.btn_selector1;
      TextView btnSelector1 = ViewBindings.findChildViewById(rootView, id);
      if (btnSelector1 == null) {
        break missingId;
      }

      id = R.id.btn_selector2;
      TextView btnSelector2 = ViewBindings.findChildViewById(rootView, id);
      if (btnSelector2 == null) {
        break missingId;
      }

      id = R.id.c_text;
      TextView cText = ViewBindings.findChildViewById(rootView, id);
      if (cText == null) {
        break missingId;
      }

      id = R.id.c_text_ni_ampere;
      TextView cTextNiAmpere = ViewBindings.findChildViewById(rootView, id);
      if (cTextNiAmpere == null) {
        break missingId;
      }

      id = R.id.c_text_ni_power;
      TextView cTextNiPower = ViewBindings.findChildViewById(rootView, id);
      if (cTextNiPower == null) {
        break missingId;
      }

      id = R.id.charge_avg_info;
      ImageView chargeAvgInfo = ViewBindings.findChildViewById(rootView, id);
      if (chargeAvgInfo == null) {
        break missingId;
      }

      id = R.id.charge_prog_bar_percent;
      CircularProgressIndicator chargeProgBarPercent = ViewBindings.findChildViewById(rootView, id);
      if (chargeProgBarPercent == null) {
        break missingId;
      }

      id = R.id.charge_session_info;
      ImageView chargeSessionInfo = ViewBindings.findChildViewById(rootView, id);
      if (chargeSessionInfo == null) {
        break missingId;
      }

      id = R.id.charge_session_percent;
      TextView chargeSessionPercent = ViewBindings.findChildViewById(rootView, id);
      if (chargeSessionPercent == null) {
        break missingId;
      }

      id = R.id.charge_up;
      LinearLayout chargeUp = ViewBindings.findChildViewById(rootView, id);
      if (chargeUp == null) {
        break missingId;
      }

      id = R.id.chart1_percent;
      RelativeLayout chart1Percent = ViewBindings.findChildViewById(rootView, id);
      if (chart1Percent == null) {
        break missingId;
      }

      id = R.id.chart_percent;
      LineChart chartPercent = ViewBindings.findChildViewById(rootView, id);
      if (chartPercent == null) {
        break missingId;
      }

      id = R.id.cs_1;
      ConstraintLayout cs1 = ViewBindings.findChildViewById(rootView, id);
      if (cs1 == null) {
        break missingId;
      }

      id = R.id.cs_2;
      ConstraintLayout cs2 = ViewBindings.findChildViewById(rootView, id);
      if (cs2 == null) {
        break missingId;
      }

      id = R.id.cs_2_1;
      ConstraintLayout cs21 = ViewBindings.findChildViewById(rootView, id);
      if (cs21 == null) {
        break missingId;
      }

      id = R.id.cs_3;
      ConstraintLayout cs3 = ViewBindings.findChildViewById(rootView, id);
      if (cs3 == null) {
        break missingId;
      }

      id = R.id.cs_4;
      ConstraintLayout cs4 = ViewBindings.findChildViewById(rootView, id);
      if (cs4 == null) {
        break missingId;
      }

      id = R.id.cs_text;
      TextView csText = ViewBindings.findChildViewById(rootView, id);
      if (csText == null) {
        break missingId;
      }

      id = R.id.current_session_block;
      ConstraintLayout currentSessionBlock = ViewBindings.findChildViewById(rootView, id);
      if (currentSessionBlock == null) {
        break missingId;
      }

      id = R.id.da_view;
      ConstraintLayout daView = ViewBindings.findChildViewById(rootView, id);
      if (daView == null) {
        break missingId;
      }

      id = R.id.damage_bar_percent;
      ProgressBar damageBarPercent = ViewBindings.findChildViewById(rootView, id);
      if (damageBarPercent == null) {
        break missingId;
      }

      id = R.id.damage_bar_seekwhite;
      ProgressBar damageBarSeekwhite = ViewBindings.findChildViewById(rootView, id);
      if (damageBarSeekwhite == null) {
        break missingId;
      }

      id = R.id.day_1_percent;
      TextView day1Percent = ViewBindings.findChildViewById(rootView, id);
      if (day1Percent == null) {
        break missingId;
      }

      id = R.id.day_2_percent;
      TextView day2Percent = ViewBindings.findChildViewById(rootView, id);
      if (day2Percent == null) {
        break missingId;
      }

      id = R.id.day_3_percent;
      TextView day3Percent = ViewBindings.findChildViewById(rootView, id);
      if (day3Percent == null) {
        break missingId;
      }

      id = R.id.day_4_percent;
      TextView day4Percent = ViewBindings.findChildViewById(rootView, id);
      if (day4Percent == null) {
        break missingId;
      }

      id = R.id.day_5_percent;
      TextView day5Percent = ViewBindings.findChildViewById(rootView, id);
      if (day5Percent == null) {
        break missingId;
      }

      id = R.id.day_6_percent;
      TextView day6Percent = ViewBindings.findChildViewById(rootView, id);
      if (day6Percent == null) {
        break missingId;
      }

      id = R.id.day_7_percent;
      TextView day7Percent = ViewBindings.findChildViewById(rootView, id);
      if (day7Percent == null) {
        break missingId;
      }

      id = R.id.day_block;
      LinearLayout dayBlock = ViewBindings.findChildViewById(rootView, id);
      if (dayBlock == null) {
        break missingId;
      }

      id = R.id.graph_percent;
      RelativeLayout graphPercent = ViewBindings.findChildViewById(rootView, id);
      if (graphPercent == null) {
        break missingId;
      }

      id = R.id.i1;
      ConstraintLayout i1 = ViewBindings.findChildViewById(rootView, id);
      if (i1 == null) {
        break missingId;
      }

      id = R.id.i2;
      ConstraintLayout i2 = ViewBindings.findChildViewById(rootView, id);
      if (i2 == null) {
        break missingId;
      }

      id = R.id.i3;
      ConstraintLayout i3 = ViewBindings.findChildViewById(rootView, id);
      if (i3 == null) {
        break missingId;
      }

      id = R.id.i_text;
      TextView iText = ViewBindings.findChildViewById(rootView, id);
      if (iText == null) {
        break missingId;
      }

      id = R.id.imageView18;
      ImageView imageView18 = ViewBindings.findChildViewById(rootView, id);
      if (imageView18 == null) {
        break missingId;
      }

      id = R.id.indent_down;
      ConstraintLayout indentDown = ViewBindings.findChildViewById(rootView, id);
      if (indentDown == null) {
        break missingId;
      }

      id = R.id.mah_1;
      TextView mah1 = ViewBindings.findChildViewById(rootView, id);
      if (mah1 == null) {
        break missingId;
      }

      id = R.id.mah_2;
      TextView mah2 = ViewBindings.findChildViewById(rootView, id);
      if (mah2 == null) {
        break missingId;
      }

      id = R.id.mah_3;
      TextView mah3 = ViewBindings.findChildViewById(rootView, id);
      if (mah3 == null) {
        break missingId;
      }

      id = R.id.native_ad;
      View nativeAd = ViewBindings.findChildViewById(rootView, id);
      if (nativeAd == null) {
        break missingId;
      }
      LayoutNativeAdsBinding binding_nativeAd = LayoutNativeAdsBinding.bind(nativeAd);

      id = R.id.night_block;
      LinearLayout nightBlock = ViewBindings.findChildViewById(rootView, id);
      if (nightBlock == null) {
        break missingId;
      }

      id = R.id.not_charging_message;
      TextView notChargingMessage = ViewBindings.findChildViewById(rootView, id);
      if (notChargingMessage == null) {
        break missingId;
      }

      id = R.id.p_text;
      TextView pText = ViewBindings.findChildViewById(rootView, id);
      if (pText == null) {
        break missingId;
      }

      id = R.id.per_1;
      TextView per1 = ViewBindings.findChildViewById(rootView, id);
      if (per1 == null) {
        break missingId;
      }

      id = R.id.per_2;
      TextView per2 = ViewBindings.findChildViewById(rootView, id);
      if (per2 == null) {
        break missingId;
      }

      id = R.id.per_3;
      TextView per3 = ViewBindings.findChildViewById(rootView, id);
      if (per3 == null) {
        break missingId;
      }

      id = R.id.percent_graph_change;
      ConstraintLayout percentGraphChange = ViewBindings.findChildViewById(rootView, id);
      if (percentGraphChange == null) {
        break missingId;
      }

      id = R.id.percent_layout;
      RelativeLayout percentLayout = ViewBindings.findChildViewById(rootView, id);
      if (percentLayout == null) {
        break missingId;
      }

      id = R.id.progressBar_averagespeed;
      ProgressBar progressBarAveragespeed = ViewBindings.findChildViewById(rootView, id);
      if (progressBarAveragespeed == null) {
        break missingId;
      }

      id = R.id.progressBar_averagespeed_grey;
      ProgressBar progressBarAveragespeedGrey = ViewBindings.findChildViewById(rootView, id);
      if (progressBarAveragespeedGrey == null) {
        break missingId;
      }

      id = R.id.progressBar_current;
      ProgressBar progressBarCurrent = ViewBindings.findChildViewById(rootView, id);
      if (progressBarCurrent == null) {
        break missingId;
      }

      id = R.id.progressBar_current_minus;
      ProgressBar progressBarCurrentMinus = ViewBindings.findChildViewById(rootView, id);
      if (progressBarCurrentMinus == null) {
        break missingId;
      }

      id = R.id.progressBar_power;
      ProgressBar progressBarPower = ViewBindings.findChildViewById(rootView, id);
      if (progressBarPower == null) {
        break missingId;
      }

      id = R.id.progressBar_power_minus;
      ProgressBar progressBarPowerMinus = ViewBindings.findChildViewById(rootView, id);
      if (progressBarPowerMinus == null) {
        break missingId;
      }

      id = R.id.progressBar_remain_to_100;
      ProgressBar progressBarRemainTo100 = ViewBindings.findChildViewById(rootView, id);
      if (progressBarRemainTo100 == null) {
        break missingId;
      }

      id = R.id.progressBar_remain_to_100_2;
      ProgressBar progressBarRemainTo1002 = ViewBindings.findChildViewById(rootView, id);
      if (progressBarRemainTo1002 == null) {
        break missingId;
      }

      id = R.id.progressBar_remain_to_var;
      ProgressBar progressBarRemainToVar = ViewBindings.findChildViewById(rootView, id);
      if (progressBarRemainToVar == null) {
        break missingId;
      }

      id = R.id.progressBar_remain_to_var2;
      ProgressBar progressBarRemainToVar2 = ViewBindings.findChildViewById(rootView, id);
      if (progressBarRemainToVar2 == null) {
        break missingId;
      }

      id = R.id.progressBar_temp;
      ProgressBar progressBarTemp = ViewBindings.findChildViewById(rootView, id);
      if (progressBarTemp == null) {
        break missingId;
      }

      id = R.id.progressBar_voltage;
      ProgressBar progressBarVoltage = ViewBindings.findChildViewById(rootView, id);
      if (progressBarVoltage == null) {
        break missingId;
      }

      id = R.id.promo_container;
      LinearLayout promoContainer = ViewBindings.findChildViewById(rootView, id);
      if (promoContainer == null) {
        break missingId;
      }

      id = R.id.pus;
      RelativeLayout pus = ViewBindings.findChildViewById(rootView, id);
      if (pus == null) {
        break missingId;
      }

      id = R.id.rem_1;
      ConstraintLayout rem1 = ViewBindings.findChildViewById(rootView, id);
      if (rem1 == null) {
        break missingId;
      }

      id = R.id.rem_2;
      ConstraintLayout rem2 = ViewBindings.findChildViewById(rootView, id);
      if (rem2 == null) {
        break missingId;
      }

      id = R.id.rem_barrier;
      Barrier remBarrier = ViewBindings.findChildViewById(rootView, id);
      if (remBarrier == null) {
        break missingId;
      }

      id = R.id.rem_text;
      TextView remText = ViewBindings.findChildViewById(rootView, id);
      if (remText == null) {
        break missingId;
      }

      id = R.id.rem_val_barrier;
      Barrier remValBarrier = ViewBindings.findChildViewById(rootView, id);
      if (remValBarrier == null) {
        break missingId;
      }

      id = R.id.remaining_table;
      ConstraintLayout remainingTable = ViewBindings.findChildViewById(rootView, id);
      if (remainingTable == null) {
        break missingId;
      }

      id = R.id.reset_session_charge_button;
      TextView resetSessionChargeButton = ViewBindings.findChildViewById(rootView, id);
      if (resetSessionChargeButton == null) {
        break missingId;
      }

      id = R.id.s_1;
      ConstraintLayout s1 = ViewBindings.findChildViewById(rootView, id);
      if (s1 == null) {
        break missingId;
      }

      id = R.id.s_2;
      ConstraintLayout s2 = ViewBindings.findChildViewById(rootView, id);
      if (s2 == null) {
        break missingId;
      }

      id = R.id.s_3;
      ConstraintLayout s3 = ViewBindings.findChildViewById(rootView, id);
      if (s3 == null) {
        break missingId;
      }

      id = R.id.s_4;
      ConstraintLayout s4 = ViewBindings.findChildViewById(rootView, id);
      if (s4 == null) {
        break missingId;
      }

      id = R.id.s_5;
      ConstraintLayout s5 = ViewBindings.findChildViewById(rootView, id);
      if (s5 == null) {
        break missingId;
      }

      id = R.id.s_6;
      ConstraintLayout s6 = ViewBindings.findChildViewById(rootView, id);
      if (s6 == null) {
        break missingId;
      }

      id = R.id.sale_container;
      LinearLayout saleContainer = ViewBindings.findChildViewById(rootView, id);
      if (saleContainer == null) {
        break missingId;
      }

      NestedScrollView scrollView = (NestedScrollView) rootView;

      id = R.id.sdfsd;
      LinearLayout sdfsd = ViewBindings.findChildViewById(rootView, id);
      if (sdfsd == null) {
        break missingId;
      }

      id = R.id.seekBar;
      SeekBar seekBar = ViewBindings.findChildViewById(rootView, id);
      if (seekBar == null) {
        break missingId;
      }

      id = R.id.selector_amperage;
      ConstraintLayout selectorAmperage = ViewBindings.findChildViewById(rootView, id);
      if (selectorAmperage == null) {
        break missingId;
      }

      id = R.id.speedcharge_percent_text;
      TextView speedchargePercentText = ViewBindings.findChildViewById(rootView, id);
      if (speedchargePercentText == null) {
        break missingId;
      }

      id = R.id.st_text;
      TextView stText = ViewBindings.findChildViewById(rootView, id);
      if (stText == null) {
        break missingId;
      }

      id = R.id.t1;
      TextView t1 = ViewBindings.findChildViewById(rootView, id);
      if (t1 == null) {
        break missingId;
      }

      id = R.id.t2;
      TextView t2 = ViewBindings.findChildViewById(rootView, id);
      if (t2 == null) {
        break missingId;
      }

      id = R.id.t26;
      TextView t26 = ViewBindings.findChildViewById(rootView, id);
      if (t26 == null) {
        break missingId;
      }

      id = R.id.t3;
      TextView t3 = ViewBindings.findChildViewById(rootView, id);
      if (t3 == null) {
        break missingId;
      }

      id = R.id.t4;
      TextView t4 = ViewBindings.findChildViewById(rootView, id);
      if (t4 == null) {
        break missingId;
      }

      id = R.id.t5;
      TextView t5 = ViewBindings.findChildViewById(rootView, id);
      if (t5 == null) {
        break missingId;
      }

      id = R.id.t6;
      TextView t6 = ViewBindings.findChildViewById(rootView, id);
      if (t6 == null) {
        break missingId;
      }

      id = R.id.t7;
      TextView t7 = ViewBindings.findChildViewById(rootView, id);
      if (t7 == null) {
        break missingId;
      }

      id = R.id.t8;
      TextView t8 = ViewBindings.findChildViewById(rootView, id);
      if (t8 == null) {
        break missingId;
      }

      id = R.id.t9;
      TextView t9 = ViewBindings.findChildViewById(rootView, id);
      if (t9 == null) {
        break missingId;
      }

      id = R.id.te0;
      TextView te0 = ViewBindings.findChildViewById(rootView, id);
      if (te0 == null) {
        break missingId;
      }

      id = R.id.te77;
      TextView te77 = ViewBindings.findChildViewById(rootView, id);
      if (te77 == null) {
        break missingId;
      }

      id = R.id.te88;
      TextView te88 = ViewBindings.findChildViewById(rootView, id);
      if (te88 == null) {
        break missingId;
      }

      id = R.id.te99;
      TextView te99 = ViewBindings.findChildViewById(rootView, id);
      if (te99 == null) {
        break missingId;
      }

      id = R.id.test_view;
      TextView testView = ViewBindings.findChildViewById(rootView, id);
      if (testView == null) {
        break missingId;
      }

      id = R.id.text226;
      TextView text226 = ViewBindings.findChildViewById(rootView, id);
      if (text226 == null) {
        break missingId;
      }

      id = R.id.text_fulltime_charge_session;
      TextView textFulltimeChargeSession = ViewBindings.findChildViewById(rootView, id);
      if (textFulltimeChargeSession == null) {
        break missingId;
      }

      id = R.id.text_percent;
      TextView textPercent = ViewBindings.findChildViewById(rootView, id);
      if (textPercent == null) {
        break missingId;
      }

      id = R.id.text_percent_charge_all;
      TextView textPercentChargeAll = ViewBindings.findChildViewById(rootView, id);
      if (textPercentChargeAll == null) {
        break missingId;
      }

      id = R.id.text_percent_charge_all_session;
      TextView textPercentChargeAllSession = ViewBindings.findChildViewById(rootView, id);
      if (textPercentChargeAllSession == null) {
        break missingId;
      }

      id = R.id.text_percent_charge_day;
      TextView textPercentChargeDay = ViewBindings.findChildViewById(rootView, id);
      if (textPercentChargeDay == null) {
        break missingId;
      }

      id = R.id.text_percent_charge_day_session;
      TextView textPercentChargeDaySession = ViewBindings.findChildViewById(rootView, id);
      if (textPercentChargeDaySession == null) {
        break missingId;
      }

      id = R.id.text_percent_charge_night;
      TextView textPercentChargeNight = ViewBindings.findChildViewById(rootView, id);
      if (textPercentChargeNight == null) {
        break missingId;
      }

      id = R.id.text_percent_charge_night_session;
      TextView textPercentChargeNightSession = ViewBindings.findChildViewById(rootView, id);
      if (textPercentChargeNightSession == null) {
        break missingId;
      }

      id = R.id.text_percent_charge_session_last;
      TextView textPercentChargeSessionLast = ViewBindings.findChildViewById(rootView, id);
      if (textPercentChargeSessionLast == null) {
        break missingId;
      }

      id = R.id.text_remain_to_100;
      TextView textRemainTo100 = ViewBindings.findChildViewById(rootView, id);
      if (textRemainTo100 == null) {
        break missingId;
      }

      id = R.id.text_remain_to_100_charge;
      TextView textRemainTo100Charge = ViewBindings.findChildViewById(rootView, id);
      if (textRemainTo100Charge == null) {
        break missingId;
      }

      id = R.id.text_remain_var;
      TextView textRemainVar = ViewBindings.findChildViewById(rootView, id);
      if (textRemainVar == null) {
        break missingId;
      }

      id = R.id.text_remain_var22;
      TextView textRemainVar22 = ViewBindings.findChildViewById(rootView, id);
      if (textRemainVar22 == null) {
        break missingId;
      }

      id = R.id.text_remain_var33;
      TextView textRemainVar33 = ViewBindings.findChildViewById(rootView, id);
      if (textRemainVar33 == null) {
        break missingId;
      }

      id = R.id.text_remain_var44;
      TextView textRemainVar44 = ViewBindings.findChildViewById(rootView, id);
      if (textRemainVar44 == null) {
        break missingId;
      }

      id = R.id.text_remain_var55;
      TextView textRemainVar55 = ViewBindings.findChildViewById(rootView, id);
      if (textRemainVar55 == null) {
        break missingId;
      }

      id = R.id.text_speed_charge_all;
      TextView textSpeedChargeAll = ViewBindings.findChildViewById(rootView, id);
      if (textSpeedChargeAll == null) {
        break missingId;
      }

      id = R.id.text_speed_charge_all_session;
      TextView textSpeedChargeAllSession = ViewBindings.findChildViewById(rootView, id);
      if (textSpeedChargeAllSession == null) {
        break missingId;
      }

      id = R.id.text_speed_charge_day;
      TextView textSpeedChargeDay = ViewBindings.findChildViewById(rootView, id);
      if (textSpeedChargeDay == null) {
        break missingId;
      }

      id = R.id.text_speed_charge_day_session;
      TextView textSpeedChargeDaySession = ViewBindings.findChildViewById(rootView, id);
      if (textSpeedChargeDaySession == null) {
        break missingId;
      }

      id = R.id.text_speed_charge_day_session2;
      TextView textSpeedChargeDaySession2 = ViewBindings.findChildViewById(rootView, id);
      if (textSpeedChargeDaySession2 == null) {
        break missingId;
      }

      id = R.id.text_speed_charge_night;
      TextView textSpeedChargeNight = ViewBindings.findChildViewById(rootView, id);
      if (textSpeedChargeNight == null) {
        break missingId;
      }

      id = R.id.text_speed_charge_night_session;
      TextView textSpeedChargeNightSession = ViewBindings.findChildViewById(rootView, id);
      if (textSpeedChargeNightSession == null) {
        break missingId;
      }

      id = R.id.text_time_day;
      TextView textTimeDay = ViewBindings.findChildViewById(rootView, id);
      if (textTimeDay == null) {
        break missingId;
      }

      id = R.id.text_time_daynight;
      TextView textTimeDaynight = ViewBindings.findChildViewById(rootView, id);
      if (textTimeDaynight == null) {
        break missingId;
      }

      id = R.id.text_time_night;
      TextView textTimeNight = ViewBindings.findChildViewById(rootView, id);
      if (textTimeNight == null) {
        break missingId;
      }

      id = R.id.textView6;
      TextView textView6 = ViewBindings.findChildViewById(rootView, id);
      if (textView6 == null) {
        break missingId;
      }

      id = R.id.textView7;
      TextView textView7 = ViewBindings.findChildViewById(rootView, id);
      if (textView7 == null) {
        break missingId;
      }

      id = R.id.textView9;
      TextView textView9 = ViewBindings.findChildViewById(rootView, id);
      if (textView9 == null) {
        break missingId;
      }

      id = R.id.textView_percent;
      TextView textViewPercent = ViewBindings.findChildViewById(rootView, id);
      if (textViewPercent == null) {
        break missingId;
      }

      id = R.id.textedd;
      TextView textedd = ViewBindings.findChildViewById(rootView, id);
      if (textedd == null) {
        break missingId;
      }

      id = R.id.time_charge_session_start;
      TextView timeChargeSessionStart = ViewBindings.findChildViewById(rootView, id);
      if (timeChargeSessionStart == null) {
        break missingId;
      }

      id = R.id.time_num;
      LinearLayout timeNum = ViewBindings.findChildViewById(rootView, id);
      if (timeNum == null) {
        break missingId;
      }

      id = R.id.under_graph_percent;
      LinearLayout underGraphPercent = ViewBindings.findChildViewById(rootView, id);
      if (underGraphPercent == null) {
        break missingId;
      }

      id = R.id.update_view;
      LinearLayout updateView = ViewBindings.findChildViewById(rootView, id);
      if (updateView == null) {
        break missingId;
      }

      id = R.id.update_view_btn;
      TextView updateViewBtn = ViewBindings.findChildViewById(rootView, id);
      if (updateViewBtn == null) {
        break missingId;
      }

      id = R.id.v_text;
      TextView vText = ViewBindings.findChildViewById(rootView, id);
      if (vText == null) {
        break missingId;
      }

      id = R.id.val_average_speed;
      TextView valAverageSpeed = ViewBindings.findChildViewById(rootView, id);
      if (valAverageSpeed == null) {
        break missingId;
      }

      id = R.id.val_currrent_charging;
      TextView valCurrrentCharging = ViewBindings.findChildViewById(rootView, id);
      if (valCurrrentCharging == null) {
        break missingId;
      }

      id = R.id.val_power_charging;
      TextView valPowerCharging = ViewBindings.findChildViewById(rootView, id);
      if (valPowerCharging == null) {
        break missingId;
      }

      id = R.id.val_remain_to_100;
      TextView valRemainTo100 = ViewBindings.findChildViewById(rootView, id);
      if (valRemainTo100 == null) {
        break missingId;
      }

      id = R.id.val_remain_to_var;
      TextView valRemainToVar = ViewBindings.findChildViewById(rootView, id);
      if (valRemainToVar == null) {
        break missingId;
      }

      id = R.id.val_temp2;
      TextView valTemp2 = ViewBindings.findChildViewById(rootView, id);
      if (valTemp2 == null) {
        break missingId;
      }

      id = R.id.val_temp_text;
      TextView valTempText = ViewBindings.findChildViewById(rootView, id);
      if (valTempText == null) {
        break missingId;
      }

      id = R.id.val_voltage;
      TextView valVoltage = ViewBindings.findChildViewById(rootView, id);
      if (valVoltage == null) {
        break missingId;
      }

      id = R.id.var_damage_up4;
      TextView varDamageUp4 = ViewBindings.findChildViewById(rootView, id);
      if (varDamageUp4 == null) {
        break missingId;
      }

      id = R.id.var_speedcharge_percent_now;
      TextView varSpeedchargePercentNow = ViewBindings.findChildViewById(rootView, id);
      if (varSpeedchargePercentNow == null) {
        break missingId;
      }

      id = R.id.wear_rate_percent;
      TextView wearRatePercent = ViewBindings.findChildViewById(rootView, id);
      if (wearRatePercent == null) {
        break missingId;
      }

      return new FragmentChargeBinding((NestedScrollView) rootView, allBlock, amperageTable,
          batteryAlarmBtn, batteryWearInfo, btn1, btn2, btn3, btnSelector1, btnSelector2, cText,
          cTextNiAmpere, cTextNiPower, chargeAvgInfo, chargeProgBarPercent, chargeSessionInfo,
          chargeSessionPercent, chargeUp, chart1Percent, chartPercent, cs1, cs2, cs21, cs3, cs4,
          csText, currentSessionBlock, daView, damageBarPercent, damageBarSeekwhite, day1Percent,
          day2Percent, day3Percent, day4Percent, day5Percent, day6Percent, day7Percent, dayBlock,
          graphPercent, i1, i2, i3, iText, imageView18, indentDown, mah1, mah2, mah3,
          binding_nativeAd, nightBlock, notChargingMessage, pText, per1, per2, per3,
          percentGraphChange, percentLayout, progressBarAveragespeed, progressBarAveragespeedGrey,
          progressBarCurrent, progressBarCurrentMinus, progressBarPower, progressBarPowerMinus,
          progressBarRemainTo100, progressBarRemainTo1002, progressBarRemainToVar,
          progressBarRemainToVar2, progressBarTemp, progressBarVoltage, promoContainer, pus, rem1,
          rem2, remBarrier, remText, remValBarrier, remainingTable, resetSessionChargeButton, s1,
          s2, s3, s4, s5, s6, saleContainer, scrollView, sdfsd, seekBar, selectorAmperage,
          speedchargePercentText, stText, t1, t2, t26, t3, t4, t5, t6, t7, t8, t9, te0, te77, te88,
          te99, testView, text226, textFulltimeChargeSession, textPercent, textPercentChargeAll,
          textPercentChargeAllSession, textPercentChargeDay, textPercentChargeDaySession,
          textPercentChargeNight, textPercentChargeNightSession, textPercentChargeSessionLast,
          textRemainTo100, textRemainTo100Charge, textRemainVar, textRemainVar22, textRemainVar33,
          textRemainVar44, textRemainVar55, textSpeedChargeAll, textSpeedChargeAllSession,
          textSpeedChargeDay, textSpeedChargeDaySession, textSpeedChargeDaySession2,
          textSpeedChargeNight, textSpeedChargeNightSession, textTimeDay, textTimeDaynight,
          textTimeNight, textView6, textView7, textView9, textViewPercent, textedd,
          timeChargeSessionStart, timeNum, underGraphPercent, updateView, updateViewBtn, vText,
          valAverageSpeed, valCurrrentCharging, valPowerCharging, valRemainTo100, valRemainToVar,
          valTemp2, valTempText, valVoltage, varDamageUp4, varSpeedchargePercentNow,
          wearRatePercent);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
