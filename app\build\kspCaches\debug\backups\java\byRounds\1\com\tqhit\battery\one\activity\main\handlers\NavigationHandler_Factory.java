package com.tqhit.battery.one.activity.main.handlers;

import com.tqhit.battery.one.features.navigation.DynamicNavigationManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class NavigationHandler_Factory implements Factory<NavigationHandler> {
  private final Provider<DynamicNavigationManager> dynamicNavigationManagerProvider;

  public NavigationHandler_Factory(
      Provider<DynamicNavigationManager> dynamicNavigationManagerProvider) {
    this.dynamicNavigationManagerProvider = dynamicNavigationManagerProvider;
  }

  @Override
  public NavigationHandler get() {
    return newInstance(dynamicNavigationManagerProvider.get());
  }

  public static NavigationHandler_Factory create(
      Provider<DynamicNavigationManager> dynamicNavigationManagerProvider) {
    return new NavigationHandler_Factory(dynamicNavigationManagerProvider);
  }

  public static NavigationHandler newInstance(DynamicNavigationManager dynamicNavigationManager) {
    return new NavigationHandler(dynamicNavigationManager);
  }
}
