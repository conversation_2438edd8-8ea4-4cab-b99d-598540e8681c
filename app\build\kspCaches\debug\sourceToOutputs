{"<This is a virtual key for removed outputs; DO NOT USE>": [], "src\\main\\java\\com\\tqhit\\battery\\one\\BatteryApplication.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\dagger\\hilt\\internal\\aggregatedroot\\codegen\\_com_tqhit_battery_one_BatteryApplication.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\BatteryApplication_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\BatteryApplication_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\navigation\\SharedNavigationViewModel.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\navigation\\SharedNavigationViewModel_HiltModules.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\navigation\\SharedNavigationViewModel_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeViewModel.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeViewModel_HiltModules.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeViewModel_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeViewModel.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeViewModel_HiltModules.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeViewModel_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\presentation\\HealthViewModel.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\presentation\\HealthViewModel_HiltModules.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\presentation\\HealthViewModel_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\fragment\\onboarding\\LanguageSelectionViewModel.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\onboarding\\LanguageSelectionViewModel_HiltModules.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\onboarding\\LanguageSelectionViewModel_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\viewmodel\\AppViewModel.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\AppViewModel_HiltModules.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\AppViewModel_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\viewmodel\\animation\\AnimationViewModel.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\animation\\AnimationViewModel_HiltModules.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\animation\\AnimationViewModel_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\viewmodel\\battery\\BatteryViewModel.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\battery\\BatteryViewModel_HiltModules.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\battery\\BatteryViewModel_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\animation\\AnimationActivity.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\animation\\AnimationActivity_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\animation\\Hilt_AnimationActivity.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\animation\\AnimationActivity_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\main\\MainActivity.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\main\\MainActivity_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\main\\Hilt_MainActivity.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\main\\MainActivity_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\onboarding\\LanguageSelectionActivity.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\onboarding\\LanguageSelectionActivity_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\onboarding\\Hilt_LanguageSelectionActivity.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\onboarding\\LanguageSelectionActivity_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\overlay\\ChargingOverlayActivity.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\overlay\\ChargingOverlayActivity_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\overlay\\Hilt_ChargingOverlayActivity.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\overlay\\ChargingOverlayActivity_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\password\\EnterPasswordActivity.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\password\\EnterPasswordActivity_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\password\\Hilt_EnterPasswordActivity.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\password\\EnterPasswordActivity_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\splash\\SplashActivity.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\splash\\SplashActivity_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\splash\\Hilt_SplashActivity.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\splash\\SplashActivity_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\starting\\StartingActivity.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\starting\\StartingActivity_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\starting\\Hilt_StartingActivity.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\starting\\StartingActivity_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeFragment.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeFragment_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\Hilt_StatsChargeFragment.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeFragment_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\service\\CoreBatteryStatsService.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\service\\CoreBatteryStatsService_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\service\\Hilt_CoreBatteryStatsService.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\service\\CoreBatteryStatsService_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeFragment.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeFragment_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\Hilt_DischargeFragment.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeFragment_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\service\\EnhancedDischargeTimerService.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\service\\EnhancedDischargeTimerService_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\service\\Hilt_EnhancedDischargeTimerService.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\service\\EnhancedDischargeTimerService_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\notifications\\UnifiedBatteryNotificationService.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\notifications\\UnifiedBatteryNotificationService_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\notifications\\Hilt_UnifiedBatteryNotificationService.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\notifications\\UnifiedBatteryNotificationService_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\fragment\\main\\HealthFragment.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\HealthFragment_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\Hilt_HealthFragment.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\HealthFragment_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\fragment\\main\\SettingsFragment.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\SettingsFragment_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\Hilt_SettingsFragment.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\SettingsFragment_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\fragment\\main\\animation\\AnimationGridFragment.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\animation\\AnimationGridFragment_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\animation\\Hilt_AnimationGridFragment.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\animation\\AnimationGridFragment_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\fragment\\main\\others\\OthersFragment.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\others\\OthersFragment_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\others\\Hilt_OthersFragment.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\others\\OthersFragment_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\service\\ChargingOverlayService.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\ChargingOverlayService_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\Hilt_ChargingOverlayService.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\ChargingOverlayService_MembersInjector.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\di\\ThumbnailPreloadingModule.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_tqhit_battery_one_di_ThumbnailPreloadingModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\di\\ThumbnailPreloadingModule_ProvideThumbnailFileManagerFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\di\\ThumbnailPreloadingModule_ProvideThumbnailPreloaderFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\di\\ThumbnailPreloadingModule_ProvideThumbnailDataServiceFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\di\\ThumbnailPreloadingModule_ProvideThumbnailPreloadingRepositoryFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\di\\ThumbnailPreloadingModule_ProvideDeferredThumbnailPreloadingServiceFactory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\dialog\\utils\\InterfaceApplovinAd.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_tqhit_battery_one_dialog_utils_ApplovinAdEntryPoint.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\navigation\\di\\NavigationDIModule.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_navigation_di_NavigationDIModule.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\di\\StatsChargeDIModule.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_charge_di_StatsChargeDIModule.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\di\\CoreBatteryDIModule.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_corebattery_di_CoreBatteryDIModule.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\di\\StatsDischargeModule.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_discharge_di_StatsDischargeModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_discharge_di_StatsDischargeProvidersModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\di\\StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\di\\HealthDIModule.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_health_di_HealthDIModule.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\dialog\\alarm\\SelectBatteryAlarmDialog.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\dialog\\alarm\\SelectBatteryAlarmDialog_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\main\\handlers\\FragmentLifecycleManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\main\\handlers\\FragmentLifecycleManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\main\\handlers\\NavigationHandler.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\main\\handlers\\NavigationHandler_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\main\\handlers\\ServiceManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\main\\handlers\\ServiceManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinAppOpenAdManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinAppOpenAdManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinBannerAdManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinBannerAdManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinInterstitialAdManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinInterstitialAdManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinNativeAdManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinNativeAdManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinRewardedAdManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinRewardedAdManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\dialog\\alarm\\SelectBatteryAlarmLowDialog.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\dialog\\alarm\\SelectBatteryAlarmLowDialog_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\navigation\\AppNavigator.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\navigation\\AppNavigator_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\navigation\\DynamicNavigationManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\navigation\\DynamicNavigationManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\permission\\UsageStatsPermissionManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\permission\\UsageStatsPermissionManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\presentation\\AppPowerConsumptionDialogFactory.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\presentation\\AppPowerConsumptionDialogFactory_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\repository\\AppUsageStatsRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\repository\\AppUsageStatsRepository_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\cache\\StatsChargePrefsCache.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\cache\\PrefsStatsChargeCache_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\domain\\CalculateSimpleChargeEstimateUseCase.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\domain\\CalculateSimpleChargeEstimateUseCase_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\repository\\StatsChargeRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\repository\\DefaultStatsChargeRepository_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\domain\\CoreBatteryStatsProvider.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\domain\\DefaultCoreBatteryStatsProvider_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\service\\CoreBatteryServiceHelper.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\service\\CoreBatteryServiceHelper_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\cache\\PrefsCurrentSessionCache.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\cache\\PrefsCurrentSessionCache_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\cache\\PrefsDischargeRatesCache.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\cache\\PrefsDischargeRatesCache_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\datasource\\ScreenStateReceiver.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\datasource\\ScreenStateReceiver_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\AppLifecycleManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\AppLifecycleManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\DischargeCalculator.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\DischargeCalculator_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\DischargeRateCalculator.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\DischargeRateCalculator_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\FullSessionReEstimator.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\FullSessionReEstimator_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\GapEstimationCalculator.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\GapEstimationCalculator_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\ScreenTimeCalculator.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\ScreenTimeCalculator_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\ScreenTimeValidationService.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\ScreenTimeValidationService_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\SessionManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\SessionManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\SessionMetricsCalculator.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\SessionMetricsCalculator_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\TimeConverter.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\TimeConverter_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\InfoButtonManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\InfoButtonManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\repository\\BatteryRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\repository\\BatteryRepository_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\repository\\DischargeSessionRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\repository\\DischargeSessionRepository_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\service\\EnhancedDischargeTimerServiceHelper.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\service\\EnhancedDischargeTimerServiceHelper_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\cache\\HealthCache.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\cache\\DefaultHealthCache_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\domain\\CalculateBatteryHealthUseCase.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\domain\\CalculateBatteryHealthUseCase_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\domain\\GetHealthHistoryUseCase.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\domain\\GetHealthHistoryUseCase_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\repository\\HealthRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\repository\\DefaultHealthRepository_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\repository\\HistoryBatteryRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\repository\\HistoryBatteryRepository_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\notifications\\UnifiedBatteryNotificationServiceHelper.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\notifications\\UnifiedBatteryNotificationServiceHelper_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\initialization\\InitializationProgressManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\initialization\\InitializationProgressManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\initialization\\ServiceInitializationHelper.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\initialization\\ServiceInitializationHelper_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\manager\\animation\\AnimationFileManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\manager\\animation\\AnimationFileManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\manager\\charge\\ChargingSessionManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\manager\\charge\\ChargingSessionManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\manager\\discharge\\DischargeSessionManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\manager\\discharge\\DischargeSessionManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\manager\\graph\\HistoryManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\manager\\graph\\BatteryHistoryManager_Factory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\manager\\graph\\TemperatureHistoryManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\manager\\thumbnail\\ThumbnailFileManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\manager\\thumbnail\\ThumbnailFileManager_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\repository\\AnimationPreloadingRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\repository\\AnimationPreloadingRepository_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\repository\\AnimationRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\repository\\AnimationRepository_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\repository\\AppRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\repository\\AppRepository_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\repository\\BatteryRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\repository\\BatteryRepository_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\repository\\ThumbnailPreloadingRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\repository\\ThumbnailPreloadingRepository_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\service\\ChargingOverlayServiceHelper.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\ChargingOverlayServiceHelper_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\service\\VibrationService.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\VibrationService_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\service\\animation\\AnimationDataService.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\animation\\AnimationDataService_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\service\\animation\\AnimationPreloader.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\animation\\AnimationPreloader_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\service\\thumbnail\\DeferredThumbnailPreloadingService.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\thumbnail\\DeferredThumbnailPreloadingService_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\service\\thumbnail\\ThumbnailDataService.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\thumbnail\\ThumbnailDataService_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\service\\thumbnail\\ThumbnailPreloader.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\thumbnail\\ThumbnailPreloader_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\utils\\PreloadingMonitor.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\utils\\PreloadingMonitor_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\utils\\VideoUtils.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\utils\\VideoUtils_Factory.java"], "src\\main\\java\\com\\tqhit\\battery\\one\\base\\LocaleAwareActivity.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\base\\LocaleAwareActivity_MembersInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\animation\\AnimationActivity_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_activity_animation_AnimationActivity_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\main\\MainActivity_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_activity_main_MainActivity_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\onboarding\\LanguageSelectionActivity_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_activity_onboarding_LanguageSelectionActivity_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\overlay\\ChargingOverlayActivity_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_activity_overlay_ChargingOverlayActivity_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\password\\EnterPasswordActivity_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_activity_password_EnterPasswordActivity_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\splash\\SplashActivity_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_activity_splash_SplashActivity_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\starting\\StartingActivity_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_activity_starting_StartingActivity_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\BatteryApplication_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_BatteryApplication_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\navigation\\SharedNavigationViewModel_HiltModules.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_navigation_SharedNavigationViewModel_HiltModules_BindsModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_navigation_SharedNavigationViewModel_HiltModules_KeyModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\navigation\\SharedNavigationViewModel_HiltModules_KeyModule_ProvideFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\navigation\\SharedNavigationViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\navigation\\SharedNavigationViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_features_navigation_SharedNavigationViewModel_HiltModules_BindsModule_LazyClassKeys.pro", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_features_navigation_SharedNavigationViewModel_HiltModules_KeyModule_LazyClassKeys.pro"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeFragment_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeFragment_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeViewModel_HiltModules.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_BindsModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_KeyModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeViewModel_HiltModules_KeyModule_ProvideFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_BindsModule_LazyClassKeys.pro", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_KeyModule_LazyClassKeys.pro"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\service\\CoreBatteryStatsService_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_corebattery_service_CoreBatteryStatsService_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeFragment_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_discharge_presentation_DischargeFragment_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeViewModel_HiltModules.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_discharge_presentation_DischargeViewModel_HiltModules_BindsModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_discharge_presentation_DischargeViewModel_HiltModules_KeyModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeViewModel_HiltModules_KeyModule_ProvideFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_features_stats_discharge_presentation_DischargeViewModel_HiltModules_BindsModule_LazyClassKeys.pro", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_features_stats_discharge_presentation_DischargeViewModel_HiltModules_KeyModule_LazyClassKeys.pro"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\service\\EnhancedDischargeTimerService_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_discharge_service_EnhancedDischargeTimerService_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\presentation\\HealthViewModel_HiltModules.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_health_presentation_HealthViewModel_HiltModules_BindsModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_health_presentation_HealthViewModel_HiltModules_KeyModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\health\\presentation\\HealthViewModel_HiltModules_KeyModule_ProvideFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\health\\presentation\\HealthViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\health\\presentation\\HealthViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_features_stats_health_presentation_HealthViewModel_HiltModules_BindsModule_LazyClassKeys.pro", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_features_stats_health_presentation_HealthViewModel_HiltModules_KeyModule_LazyClassKeys.pro"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\notifications\\UnifiedBatteryNotificationService_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_notifications_UnifiedBatteryNotificationService_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\animation\\AnimationGridFragment_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_fragment_main_animation_AnimationGridFragment_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\HealthFragment_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_fragment_main_HealthFragment_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\others\\OthersFragment_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_fragment_main_others_OthersFragment_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\SettingsFragment_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_fragment_main_SettingsFragment_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\onboarding\\LanguageSelectionViewModel_HiltModules.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_fragment_onboarding_LanguageSelectionViewModel_HiltModules_BindsModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_fragment_onboarding_LanguageSelectionViewModel_HiltModules_KeyModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\fragment\\onboarding\\LanguageSelectionViewModel_HiltModules_KeyModule_ProvideFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\fragment\\onboarding\\LanguageSelectionViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\fragment\\onboarding\\LanguageSelectionViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_fragment_onboarding_LanguageSelectionViewModel_HiltModules_BindsModule_LazyClassKeys.pro", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_fragment_onboarding_LanguageSelectionViewModel_HiltModules_KeyModule_LazyClassKeys.pro"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\ChargingOverlayService_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_service_ChargingOverlayService_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\animation\\AnimationViewModel_HiltModules.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_viewmodel_animation_AnimationViewModel_HiltModules_BindsModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_viewmodel_animation_AnimationViewModel_HiltModules_KeyModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\animation\\AnimationViewModel_HiltModules_KeyModule_ProvideFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\animation\\AnimationViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\animation\\AnimationViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_viewmodel_animation_AnimationViewModel_HiltModules_BindsModule_LazyClassKeys.pro", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_viewmodel_animation_AnimationViewModel_HiltModules_KeyModule_LazyClassKeys.pro"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\AppViewModel_HiltModules.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_viewmodel_AppViewModel_HiltModules_BindsModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_viewmodel_AppViewModel_HiltModules_KeyModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\AppViewModel_HiltModules_KeyModule_ProvideFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\AppViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\AppViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_viewmodel_AppViewModel_HiltModules_BindsModule_LazyClassKeys.pro", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_viewmodel_AppViewModel_HiltModules_KeyModule_LazyClassKeys.pro"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\battery\\BatteryViewModel_HiltModules.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_viewmodel_battery_BatteryViewModel_HiltModules_BindsModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_viewmodel_battery_BatteryViewModel_HiltModules_KeyModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\battery\\BatteryViewModel_HiltModules_KeyModule_ProvideFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\battery\\BatteryViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\battery\\BatteryViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_viewmodel_battery_BatteryViewModel_HiltModules_BindsModule_LazyClassKeys.pro", "build\\generated\\ksp\\debug\\resources\\META-INF\\proguard\\com_tqhit_battery_one_viewmodel_battery_BatteryViewModel_HiltModules_KeyModule_LazyClassKeys.pro"]}