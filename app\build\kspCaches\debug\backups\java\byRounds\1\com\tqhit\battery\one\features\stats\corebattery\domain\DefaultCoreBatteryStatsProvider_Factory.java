package com.tqhit.battery.one.features.stats.corebattery.domain;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DefaultCoreBatteryStatsProvider_Factory implements Factory<DefaultCoreBatteryStatsProvider> {
  @Override
  public DefaultCoreBatteryStatsProvider get() {
    return newInstance();
  }

  public static DefaultCoreBatteryStatsProvider_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static DefaultCoreBatteryStatsProvider newInstance() {
    return new DefaultCoreBatteryStatsProvider();
  }

  private static final class InstanceHolder {
    static final DefaultCoreBatteryStatsProvider_Factory INSTANCE = new DefaultCoreBatteryStatsProvider_Factory();
  }
}
