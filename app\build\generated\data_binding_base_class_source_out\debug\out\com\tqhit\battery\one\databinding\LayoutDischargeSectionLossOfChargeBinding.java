// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutDischargeSectionLossOfChargeBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView locBtnAppPowerConsumption;

  @NonNull
  public final ConstraintLayout locClScreenOffConsumption;

  @NonNull
  public final ConstraintLayout locClScreenOnConsumption;

  @NonNull
  public final ImageView locIvInfoButton;

  @NonNull
  public final TextView locTvScreenOffMahConsumed;

  @NonNull
  public final TextView locTvScreenOffMahUnit;

  @NonNull
  public final TextView locTvScreenOffPercentageDropped;

  @NonNull
  public final TextView locTvScreenOffPercentageUnit;

  @NonNull
  public final TextView locTvScreenOffTime;

  @NonNull
  public final TextView locTvScreenOffTitle;

  @NonNull
  public final TextView locTvScreenOnMahConsumed;

  @NonNull
  public final TextView locTvScreenOnMahUnit;

  @NonNull
  public final TextView locTvScreenOnPercentageDropped;

  @NonNull
  public final TextView locTvScreenOnPercentageUnit;

  @NonNull
  public final TextView locTvScreenOnTime;

  @NonNull
  public final TextView locTvScreenOnTitle;

  @NonNull
  public final TextView locTvTitle;

  @NonNull
  public final ConstraintLayout lossOfChargeRoot;

  private LayoutDischargeSectionLossOfChargeBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView locBtnAppPowerConsumption,
      @NonNull ConstraintLayout locClScreenOffConsumption,
      @NonNull ConstraintLayout locClScreenOnConsumption, @NonNull ImageView locIvInfoButton,
      @NonNull TextView locTvScreenOffMahConsumed, @NonNull TextView locTvScreenOffMahUnit,
      @NonNull TextView locTvScreenOffPercentageDropped,
      @NonNull TextView locTvScreenOffPercentageUnit, @NonNull TextView locTvScreenOffTime,
      @NonNull TextView locTvScreenOffTitle, @NonNull TextView locTvScreenOnMahConsumed,
      @NonNull TextView locTvScreenOnMahUnit, @NonNull TextView locTvScreenOnPercentageDropped,
      @NonNull TextView locTvScreenOnPercentageUnit, @NonNull TextView locTvScreenOnTime,
      @NonNull TextView locTvScreenOnTitle, @NonNull TextView locTvTitle,
      @NonNull ConstraintLayout lossOfChargeRoot) {
    this.rootView = rootView;
    this.locBtnAppPowerConsumption = locBtnAppPowerConsumption;
    this.locClScreenOffConsumption = locClScreenOffConsumption;
    this.locClScreenOnConsumption = locClScreenOnConsumption;
    this.locIvInfoButton = locIvInfoButton;
    this.locTvScreenOffMahConsumed = locTvScreenOffMahConsumed;
    this.locTvScreenOffMahUnit = locTvScreenOffMahUnit;
    this.locTvScreenOffPercentageDropped = locTvScreenOffPercentageDropped;
    this.locTvScreenOffPercentageUnit = locTvScreenOffPercentageUnit;
    this.locTvScreenOffTime = locTvScreenOffTime;
    this.locTvScreenOffTitle = locTvScreenOffTitle;
    this.locTvScreenOnMahConsumed = locTvScreenOnMahConsumed;
    this.locTvScreenOnMahUnit = locTvScreenOnMahUnit;
    this.locTvScreenOnPercentageDropped = locTvScreenOnPercentageDropped;
    this.locTvScreenOnPercentageUnit = locTvScreenOnPercentageUnit;
    this.locTvScreenOnTime = locTvScreenOnTime;
    this.locTvScreenOnTitle = locTvScreenOnTitle;
    this.locTvTitle = locTvTitle;
    this.lossOfChargeRoot = lossOfChargeRoot;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutDischargeSectionLossOfChargeBinding inflate(
      @NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutDischargeSectionLossOfChargeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_discharge_section_loss_of_charge, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutDischargeSectionLossOfChargeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.loc_btn_app_power_consumption;
      TextView locBtnAppPowerConsumption = ViewBindings.findChildViewById(rootView, id);
      if (locBtnAppPowerConsumption == null) {
        break missingId;
      }

      id = R.id.loc_cl_screen_off_consumption;
      ConstraintLayout locClScreenOffConsumption = ViewBindings.findChildViewById(rootView, id);
      if (locClScreenOffConsumption == null) {
        break missingId;
      }

      id = R.id.loc_cl_screen_on_consumption;
      ConstraintLayout locClScreenOnConsumption = ViewBindings.findChildViewById(rootView, id);
      if (locClScreenOnConsumption == null) {
        break missingId;
      }

      id = R.id.loc_iv_info_button;
      ImageView locIvInfoButton = ViewBindings.findChildViewById(rootView, id);
      if (locIvInfoButton == null) {
        break missingId;
      }

      id = R.id.loc_tv_screen_off_mah_consumed;
      TextView locTvScreenOffMahConsumed = ViewBindings.findChildViewById(rootView, id);
      if (locTvScreenOffMahConsumed == null) {
        break missingId;
      }

      id = R.id.loc_tv_screen_off_mah_unit;
      TextView locTvScreenOffMahUnit = ViewBindings.findChildViewById(rootView, id);
      if (locTvScreenOffMahUnit == null) {
        break missingId;
      }

      id = R.id.loc_tv_screen_off_percentage_dropped;
      TextView locTvScreenOffPercentageDropped = ViewBindings.findChildViewById(rootView, id);
      if (locTvScreenOffPercentageDropped == null) {
        break missingId;
      }

      id = R.id.loc_tv_screen_off_percentage_unit;
      TextView locTvScreenOffPercentageUnit = ViewBindings.findChildViewById(rootView, id);
      if (locTvScreenOffPercentageUnit == null) {
        break missingId;
      }

      id = R.id.loc_tv_screen_off_time;
      TextView locTvScreenOffTime = ViewBindings.findChildViewById(rootView, id);
      if (locTvScreenOffTime == null) {
        break missingId;
      }

      id = R.id.loc_tv_screen_off_title;
      TextView locTvScreenOffTitle = ViewBindings.findChildViewById(rootView, id);
      if (locTvScreenOffTitle == null) {
        break missingId;
      }

      id = R.id.loc_tv_screen_on_mah_consumed;
      TextView locTvScreenOnMahConsumed = ViewBindings.findChildViewById(rootView, id);
      if (locTvScreenOnMahConsumed == null) {
        break missingId;
      }

      id = R.id.loc_tv_screen_on_mah_unit;
      TextView locTvScreenOnMahUnit = ViewBindings.findChildViewById(rootView, id);
      if (locTvScreenOnMahUnit == null) {
        break missingId;
      }

      id = R.id.loc_tv_screen_on_percentage_dropped;
      TextView locTvScreenOnPercentageDropped = ViewBindings.findChildViewById(rootView, id);
      if (locTvScreenOnPercentageDropped == null) {
        break missingId;
      }

      id = R.id.loc_tv_screen_on_percentage_unit;
      TextView locTvScreenOnPercentageUnit = ViewBindings.findChildViewById(rootView, id);
      if (locTvScreenOnPercentageUnit == null) {
        break missingId;
      }

      id = R.id.loc_tv_screen_on_time;
      TextView locTvScreenOnTime = ViewBindings.findChildViewById(rootView, id);
      if (locTvScreenOnTime == null) {
        break missingId;
      }

      id = R.id.loc_tv_screen_on_title;
      TextView locTvScreenOnTitle = ViewBindings.findChildViewById(rootView, id);
      if (locTvScreenOnTitle == null) {
        break missingId;
      }

      id = R.id.loc_tv_title;
      TextView locTvTitle = ViewBindings.findChildViewById(rootView, id);
      if (locTvTitle == null) {
        break missingId;
      }

      ConstraintLayout lossOfChargeRoot = (ConstraintLayout) rootView;

      return new LayoutDischargeSectionLossOfChargeBinding((ConstraintLayout) rootView,
          locBtnAppPowerConsumption, locClScreenOffConsumption, locClScreenOnConsumption,
          locIvInfoButton, locTvScreenOffMahConsumed, locTvScreenOffMahUnit,
          locTvScreenOffPercentageDropped, locTvScreenOffPercentageUnit, locTvScreenOffTime,
          locTvScreenOffTitle, locTvScreenOnMahConsumed, locTvScreenOnMahUnit,
          locTvScreenOnPercentageDropped, locTvScreenOnPercentageUnit, locTvScreenOnTime,
          locTvScreenOnTitle, locTvTitle, lossOfChargeRoot);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
