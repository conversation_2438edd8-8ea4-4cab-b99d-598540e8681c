package com.tqhit.battery.one.activity.password;

import com.tqhit.battery.one.repository.AppRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class EnterPasswordActivity_MembersInjector implements MembersInjector<EnterPasswordActivity> {
  private final Provider<AppRepository> appRepositoryProvider;

  public EnterPasswordActivity_MembersInjector(Provider<AppRepository> appRepositoryProvider) {
    this.appRepositoryProvider = appRepositoryProvider;
  }

  public static MembersInjector<EnterPasswordActivity> create(
      Provider<AppRepository> appRepositoryProvider) {
    return new EnterPasswordActivity_MembersInjector(appRepositoryProvider);
  }

  @Override
  public void injectMembers(EnterPasswordActivity instance) {
    injectAppRepository(instance, appRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.password.EnterPasswordActivity.appRepository")
  public static void injectAppRepository(EnterPasswordActivity instance,
      AppRepository appRepository) {
    instance.appRepository = appRepository;
  }
}
