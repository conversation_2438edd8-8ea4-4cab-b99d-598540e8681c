package com.tqhit.battery.one.features.stats.health.cache;

import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DefaultHealthCache_Factory implements Factory<DefaultHealthCache> {
  private final Provider<PreferencesHelper> preferencesHelperProvider;

  public DefaultHealthCache_Factory(Provider<PreferencesHelper> preferencesHelperProvider) {
    this.preferencesHelperProvider = preferencesHelperProvider;
  }

  @Override
  public DefaultHealthCache get() {
    return newInstance(preferencesHelperProvider.get());
  }

  public static DefaultHealthCache_Factory create(
      Provider<PreferencesHelper> preferencesHelperProvider) {
    return new DefaultHealthCache_Factory(preferencesHelperProvider);
  }

  public static DefaultHealthCache newInstance(PreferencesHelper preferencesHelper) {
    return new DefaultHealthCache(preferencesHelper);
  }
}
