// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager.widget.ViewPager;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.tbuonomo.viewpagerdotsindicator.SpringDotsIndicator;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityStartingBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout display;

  @NonNull
  public final ShimmerFrameLayout nativeAd;

  @NonNull
  public final ViewPager slidePager;

  @NonNull
  public final SpringDotsIndicator springDotsIndicator;

  private ActivityStartingBinding(@NonNull LinearLayout rootView, @NonNull LinearLayout display,
      @NonNull ShimmerFrameLayout nativeAd, @NonNull ViewPager slidePager,
      @NonNull SpringDotsIndicator springDotsIndicator) {
    this.rootView = rootView;
    this.display = display;
    this.nativeAd = nativeAd;
    this.slidePager = slidePager;
    this.springDotsIndicator = springDotsIndicator;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityStartingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityStartingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_starting, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityStartingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      LinearLayout display = (LinearLayout) rootView;

      id = R.id.nativeAd;
      ShimmerFrameLayout nativeAd = ViewBindings.findChildViewById(rootView, id);
      if (nativeAd == null) {
        break missingId;
      }

      id = R.id.slide_pager;
      ViewPager slidePager = ViewBindings.findChildViewById(rootView, id);
      if (slidePager == null) {
        break missingId;
      }

      id = R.id.spring_dots_indicator;
      SpringDotsIndicator springDotsIndicator = ViewBindings.findChildViewById(rootView, id);
      if (springDotsIndicator == null) {
        break missingId;
      }

      return new ActivityStartingBinding((LinearLayout) rootView, display, nativeAd, slidePager,
          springDotsIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
