<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_select_theme" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\dialog_select_theme.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/dialog_select_theme_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="380" endOffset="16"/></Target><Target id="@+id/strelka" view="RelativeLayout"><Expressions/><location startLine="24" startOffset="16" endLine="56" endOffset="32"/></Target><Target id="@+id/exit_theme" view="Button"><Expressions/><location startLine="31" startOffset="20" endLine="42" endOffset="74"/></Target><Target id="@+id/textView20" view="TextView"><Expressions/><location startLine="57" startOffset="16" endLine="69" endOffset="59"/></Target><Target id="@+id/dfsdfb11" view="RelativeLayout"><Expressions/><location startLine="76" startOffset="16" endLine="109" endOffset="32"/></Target><Target id="@+id/auto" view="Button"><Expressions/><location startLine="81" startOffset="20" endLine="93" endOffset="74"/></Target><Target id="@+id/sdfgsdf1g" view="LinearLayout"><Expressions/><location startLine="94" startOffset="20" endLine="108" endOffset="34"/></Target><Target id="@+id/dfsdfb1" view="RelativeLayout"><Expressions/><location startLine="110" startOffset="16" endLine="142" endOffset="32"/></Target><Target id="@+id/light_theme" view="Button"><Expressions/><location startLine="114" startOffset="20" endLine="126" endOffset="74"/></Target><Target id="@+id/sdfgsdfg" view="LinearLayout"><Expressions/><location startLine="127" startOffset="20" endLine="141" endOffset="34"/></Target><Target id="@+id/dgdsfg" view="RelativeLayout"><Expressions/><location startLine="143" startOffset="16" endLine="177" endOffset="32"/></Target><Target id="@+id/dark_theme" view="Button"><Expressions/><location startLine="148" startOffset="20" endLine="160" endOffset="74"/></Target><Target id="@+id/czsx" view="LinearLayout"><Expressions/><location startLine="161" startOffset="20" endLine="176" endOffset="34"/></Target><Target id="@+id/amoled_theme" view="Button"><Expressions/><location startLine="182" startOffset="20" endLine="194" endOffset="74"/></Target><Target id="@+id/rgrtjk" view="LinearLayout"><Expressions/><location startLine="195" startOffset="20" endLine="209" endOffset="34"/></Target><Target id="@+id/grey_theme" view="Button"><Expressions/><location startLine="215" startOffset="20" endLine="227" endOffset="74"/></Target><Target id="@+id/rg2rtwjk" view="LinearLayout"><Expressions/><location startLine="228" startOffset="20" endLine="242" endOffset="34"/></Target><Target id="@+id/light_theme_inverted" view="Button"><Expressions/><location startLine="248" startOffset="20" endLine="260" endOffset="74"/></Target><Target id="@+id/f1221" view="LinearLayout"><Expressions/><location startLine="261" startOffset="20" endLine="275" endOffset="34"/></Target><Target id="@+id/dark_theme_inverted" view="Button"><Expressions/><location startLine="281" startOffset="20" endLine="293" endOffset="74"/></Target><Target id="@+id/czsux" view="LinearLayout"><Expressions/><location startLine="294" startOffset="20" endLine="309" endOffset="34"/></Target><Target id="@+id/amoled_theme_inverted" view="Button"><Expressions/><location startLine="315" startOffset="20" endLine="327" endOffset="74"/></Target><Target id="@+id/rgrtwj1k" view="LinearLayout"><Expressions/><location startLine="328" startOffset="20" endLine="342" endOffset="34"/></Target><Target id="@+id/grey_theme_inverted" view="Button"><Expressions/><location startLine="348" startOffset="20" endLine="360" endOffset="74"/></Target><Target id="@+id/rgrtwjk" view="LinearLayout"><Expressions/><location startLine="361" startOffset="20" endLine="375" endOffset="34"/></Target></Targets></Layout>