{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9b1d1c56b528abe32d69bef2c9ce868e\\transformed\\jetified-bigo-ads-5.3.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,200,260,326,390,470,559,634,699,761,818,1150,1220,1301,1355,1423,1485,1545,1609,1656,1740,1801,1859,1908,1970", "endColumns": "75,68,59,65,63,79,88,74,64,61,56,331,69,80,53,67,61,59,63,46,83,60,57,48,61,73", "endOffsets": "126,195,255,321,385,465,554,629,694,756,813,1145,1215,1296,1350,1418,1480,1540,1604,1651,1735,1796,1854,1903,1965,2039"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3612,3688,3757,3817,3883,3947,4027,4116,4191,4256,4318,4375,4707,4777,4858,4912,4980,5042,5102,5166,5213,5297,5358,5416,5465,5527", "endColumns": "75,68,59,65,63,79,88,74,64,61,56,331,69,80,53,67,61,59,63,46,83,60,57,48,61,73", "endOffsets": "3683,3752,3812,3878,3942,4022,4111,4186,4251,4313,4370,4702,4772,4853,4907,4975,5037,5097,5161,5208,5292,5353,5411,5460,5522,5596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-ms\\values-ms.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3238,3302", "endColumns": "63,65", "endOffsets": "3297,3363"}, "to": {"startLines": "162,163", "startColumns": "4,4", "startOffsets": "13439,13503", "endColumns": "63,65", "endOffsets": "13498,13564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,324", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "917,1028,1133,1241,1328,1432,1543,1622,1700,1791,1884,1979,2073,2171,2264,2359,2453,2544,2635,2715,2827,2935,3032,3141,3245,3352,3511,28132", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "1023,1128,1236,1323,1427,1538,1617,1695,1786,1879,1974,2068,2166,2259,2354,2448,2539,2630,2710,2822,2930,3027,3136,3240,3347,3506,3607,28208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,279,359,438,525,617,704,807,923,1006,1068,1133,1226,1291,1350,1437,1499,1561,1621,1687,1749,1803,1911,1968,2029,2084,2155,2275,2366,2443,2540,2625,2711,2859,2945,3031,3159,3247,3325,3378,3429,3495,3566,3644,3715,3794,3867,3943,4016,4087,4194,4286,4359,4449,4542,4616,4687,4778,4830,4910,4978,5062,5147,5209,5273,5336,5408,5512,5620,5716,5822,5879,5934,6020,6105,6183", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,78,86,91,86,102,115,82,61,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,76,96,84,85,147,85,85,127,87,77,52,50,65,70,77,70,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85,84,77,76", "endOffsets": "274,354,433,520,612,699,802,918,1001,1063,1128,1221,1286,1345,1432,1494,1556,1616,1682,1744,1798,1906,1963,2024,2079,2150,2270,2361,2438,2535,2620,2706,2854,2940,3026,3154,3242,3320,3373,3424,3490,3561,3639,3710,3789,3862,3938,4011,4082,4189,4281,4354,4444,4537,4611,4682,4773,4825,4905,4973,5057,5142,5204,5268,5331,5403,5507,5615,5711,5817,5874,5929,6015,6100,6178,6255"}, "to": {"startLines": "19,76,77,78,79,80,88,89,90,116,117,171,175,178,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,313,325,326,328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "738,5601,5681,5760,5847,5939,6769,6872,6988,10058,10120,14060,14474,14717,21079,21166,21228,21290,21350,21416,21478,21532,21640,21697,21758,21813,21884,22004,22095,22172,22269,22354,22440,22588,22674,22760,22888,22976,23054,23107,23158,23224,23295,23373,23444,23523,23596,23672,23745,23816,23923,24015,24088,24178,24271,24345,24416,24507,24559,24639,24707,24791,24876,24938,25002,25065,25137,25241,25349,25445,25551,25608,27372,28213,28298,28448", "endLines": "22,76,77,78,79,80,88,89,90,116,117,171,175,178,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,313,325,326,328", "endColumns": "12,79,78,86,91,86,102,115,82,61,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,76,96,84,85,147,85,85,127,87,77,52,50,65,70,77,70,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85,84,77,76", "endOffsets": "912,5676,5755,5842,5934,6021,6867,6983,7066,10115,10180,14148,14534,14771,21161,21223,21285,21345,21411,21473,21527,21635,21692,21753,21808,21879,21999,22090,22167,22264,22349,22435,22583,22669,22755,22883,22971,23049,23102,23153,23219,23290,23368,23439,23518,23591,23667,23740,23811,23918,24010,24083,24173,24266,24340,24411,24502,24554,24634,24702,24786,24871,24933,24997,25060,25132,25236,25344,25440,25546,25603,25658,27453,28293,28371,28520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "81,82,83,84,85,86,87,332", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "6026,6121,6223,6320,6430,6536,6654,28748", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "6116,6218,6315,6425,6531,6649,6764,28844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,471,643,726,810,887,978,1071,1144,1213,1309,1403,1467,1530,1595,1668,1774,1883,1988,2055,2137,2207,2278,2362,2447,2514,2577,2630,2688,2736,2797,2861,2923,2984,3050,3113,3172,3238,3290,3352,3428,3504,3566", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,51,61,75,75,61,73", "endOffsets": "279,466,638,721,805,882,973,1066,1139,1208,1304,1398,1462,1525,1590,1663,1769,1878,1983,2050,2132,2202,2273,2357,2442,2509,2572,2625,2683,2731,2792,2856,2918,2979,3045,3108,3167,3233,3285,3347,3423,3499,3561,3635"}, "to": {"startLines": "2,11,15,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,151,152,153,154,155,156,157,158,159,160,161,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,379,566,10185,10268,10352,10429,10520,10613,10686,10755,10851,10945,11009,11072,11137,11210,11316,11425,11530,11597,11679,11749,11820,11904,11989,12056,12778,12831,12889,12937,12998,13062,13124,13185,13251,13314,13373,13569,13621,13683,13759,13835,13897", "endLines": "10,14,18,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,151,152,153,154,155,156,157,158,159,160,161,164,165,166,167,168,169", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,51,61,75,75,61,73", "endOffsets": "374,561,733,10263,10347,10424,10515,10608,10681,10750,10846,10940,11004,11067,11132,11205,11311,11420,11525,11592,11674,11744,11815,11899,11984,12051,12114,12826,12884,12932,12993,13057,13119,13180,13246,13309,13368,13434,13616,13678,13754,13830,13892,13966"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,181,231,290,334,422,463,523,568,646,697,752,860,930,992", "endColumns": "79,45,49,58,43,87,40,59,44,77,50,54,107,69,61,57", "endOffsets": "130,176,226,285,329,417,458,518,563,641,692,747,855,925,987,1045"}, "to": {"startLines": "339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "29458,29538,29584,29634,29693,29737,29825,29866,29926,29971,30049,30100,30155,30263,30333,30395", "endColumns": "79,45,49,58,43,87,40,59,44,77,50,54,107,69,61,57", "endOffsets": "29533,29579,29629,29688,29732,29820,29861,29921,29966,30044,30095,30150,30258,30328,30390,30448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,379", "endColumns": "104,99,118,101", "endOffsets": "155,255,374,476"}, "to": {"startLines": "112,172,173,174", "startColumns": "4,4,4,4", "startOffsets": "9670,14153,14253,14372", "endColumns": "104,99,118,101", "endOffsets": "9770,14248,14367,14469"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ms\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "101", "startColumns": "4", "startOffsets": "8291", "endColumns": "145", "endOffsets": "8432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ms\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,464,590,692,860,988,1104,1207,1388,1493,1664,1795,1962,2133,2196,2256", "endColumns": "101,168,125,101,167,127,115,102,180,104,170,130,166,170,62,59,78", "endOffsets": "294,463,589,691,859,987,1103,1206,1387,1492,1663,1794,1961,2132,2195,2255,2334"}, "to": {"startLines": "93,94,95,96,97,98,99,100,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7245,7351,7524,7654,7760,7932,8064,8184,8437,8622,8731,8906,9041,9212,9387,9454,9518", "endColumns": "105,172,129,105,171,131,119,106,184,108,174,134,170,174,66,63,82", "endOffsets": "7346,7519,7649,7755,7927,8059,8179,8286,8617,8726,8901,9036,9207,9382,9449,9513,9596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,112", "endOffsets": "161,274"}, "to": {"startLines": "297,298", "startColumns": "4,4", "startOffsets": "25823,25934", "endColumns": "110,112", "endOffsets": "25929,26042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,263,346,485,654,735", "endColumns": "68,88,82,138,168,80,78", "endOffsets": "169,258,341,480,649,730,809"}, "to": {"startLines": "111,170,301,314,333,355,356", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9601,13971,26219,27458,28849,30453,30534", "endColumns": "68,88,82,138,168,80,78", "endOffsets": "9665,14055,26297,27592,29013,30529,30608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,294,408,527,627,732,854,1004,1132,1280,1366,1466,1558,1656,1772,1898,2003,2141,2276,2408,2587,2712,2837,2965,3094,3187,3288,3409,3537,3638,3745,3851,3992,4138,4245,4344,4420,4518,4616,4718,4805,4894,4996,5076,5159,5258,5357,5454,5557,5644,5747,5846,5953,6075,6156,6262", "endColumns": "119,118,113,118,99,104,121,149,127,147,85,99,91,97,115,125,104,137,134,131,178,124,124,127,128,92,100,120,127,100,106,105,140,145,106,98,75,97,97,101,86,88,101,79,82,98,98,96,102,86,102,98,106,121,80,105,95", "endOffsets": "170,289,403,522,622,727,849,999,1127,1275,1361,1461,1553,1651,1767,1893,1998,2136,2271,2403,2582,2707,2832,2960,3089,3182,3283,3404,3532,3633,3740,3846,3987,4133,4240,4339,4415,4513,4611,4713,4800,4889,4991,5071,5154,5253,5352,5449,5552,5639,5742,5841,5948,6070,6151,6257,6353"}, "to": {"startLines": "179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14776,14896,15015,15129,15248,15348,15453,15575,15725,15853,16001,16087,16187,16279,16377,16493,16619,16724,16862,16997,17129,17308,17433,17558,17686,17815,17908,18009,18130,18258,18359,18466,18572,18713,18859,18966,19065,19141,19239,19337,19439,19526,19615,19717,19797,19880,19979,20078,20175,20278,20365,20468,20567,20674,20796,20877,20983", "endColumns": "119,118,113,118,99,104,121,149,127,147,85,99,91,97,115,125,104,137,134,131,178,124,124,127,128,92,100,120,127,100,106,105,140,145,106,98,75,97,97,101,86,88,101,79,82,98,98,96,102,86,102,98,106,121,80,105,95", "endOffsets": "14891,15010,15124,15243,15343,15448,15570,15720,15848,15996,16082,16182,16274,16372,16488,16614,16719,16857,16992,17124,17303,17428,17553,17681,17810,17903,18004,18125,18253,18354,18461,18567,18708,18854,18961,19060,19136,19234,19332,19434,19521,19610,19712,19792,19875,19974,20073,20170,20273,20360,20463,20562,20669,20791,20872,20978,21074"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-ms\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,291,347,413,486,592,654,789,915,1048,1098,1151,1279,1373,1412,1490,1524,1557,1604,1670,1709", "endColumns": "45,45,55,65,72,105,61,134,125,132,49,52,127,93,38,77,33,32,46,65,38,55", "endOffsets": "244,290,346,412,485,591,653,788,914,1047,1097,1150,1278,1372,1411,1489,1523,1556,1603,1669,1708,1764"}, "to": {"startLines": "294,295,296,302,303,304,305,306,307,308,309,310,311,312,317,318,319,320,321,322,323,357", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25663,25713,25763,26302,26372,26449,26559,26625,26764,26894,27031,27085,27142,27274,27768,27811,27893,27931,27968,28019,28089,30613", "endColumns": "49,49,59,69,76,109,65,138,129,136,53,56,131,97,42,81,37,36,50,69,42,59", "endOffsets": "25708,25758,25818,26367,26444,26554,26620,26759,26889,27026,27080,27137,27269,27367,27806,27888,27926,27963,28014,28084,28127,30668"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,279,375,477,562,645,740,827,912,997,1083,1155,1232,1305,1378,1454,1520", "endColumns": "89,83,95,101,84,82,94,86,84,84,85,71,76,72,72,75,65,119", "endOffsets": "190,274,370,472,557,640,735,822,907,992,1078,1150,1227,1300,1373,1449,1515,1635"}, "to": {"startLines": "91,92,113,114,115,176,177,299,300,315,316,327,329,330,331,334,335,336", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7071,7161,9775,9871,9973,14539,14622,26047,26134,27597,27682,28376,28525,28602,28675,29018,29094,29160", "endColumns": "89,83,95,101,84,82,94,86,84,84,85,71,76,72,72,75,65,119", "endOffsets": "7156,7240,9866,9968,10053,14617,14712,26129,26214,27677,27763,28443,28597,28670,28743,29089,29155,29275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,189,255,320,398,464,554,637", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "120,184,250,315,393,459,549,632,709"}, "to": {"startLines": "142,143,144,145,146,147,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "12119,12189,12253,12319,12384,12462,12528,12618,12701", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "12184,12248,12314,12379,12457,12523,12613,12696,12773"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,90", "endOffsets": "137,228"}, "to": {"startLines": "337,338", "startColumns": "4,4", "startOffsets": "29280,29367", "endColumns": "86,90", "endOffsets": "29362,29453"}}]}]}