// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogSelectThemeBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final Button amoledTheme;

  @NonNull
  public final Button amoledThemeInverted;

  @NonNull
  public final Button auto;

  @NonNull
  public final LinearLayout czsux;

  @NonNull
  public final LinearLayout czsx;

  @NonNull
  public final Button darkTheme;

  @NonNull
  public final Button darkThemeInverted;

  @NonNull
  public final RelativeLayout dfsdfb1;

  @NonNull
  public final RelativeLayout dfsdfb11;

  @NonNull
  public final RelativeLayout dgdsfg;

  @NonNull
  public final Button exitTheme;

  @NonNull
  public final LinearLayout f1221;

  @NonNull
  public final Button greyTheme;

  @NonNull
  public final Button greyThemeInverted;

  @NonNull
  public final Button lightTheme;

  @NonNull
  public final Button lightThemeInverted;

  @NonNull
  public final LinearLayout rg2rtwjk;

  @NonNull
  public final LinearLayout rgrtjk;

  @NonNull
  public final LinearLayout rgrtwj1k;

  @NonNull
  public final LinearLayout rgrtwjk;

  @NonNull
  public final LinearLayout sdfgsdf1g;

  @NonNull
  public final LinearLayout sdfgsdfg;

  @NonNull
  public final RelativeLayout strelka;

  @NonNull
  public final TextView textView20;

  private DialogSelectThemeBinding(@NonNull RelativeLayout rootView, @NonNull Button amoledTheme,
      @NonNull Button amoledThemeInverted, @NonNull Button auto, @NonNull LinearLayout czsux,
      @NonNull LinearLayout czsx, @NonNull Button darkTheme, @NonNull Button darkThemeInverted,
      @NonNull RelativeLayout dfsdfb1, @NonNull RelativeLayout dfsdfb11,
      @NonNull RelativeLayout dgdsfg, @NonNull Button exitTheme, @NonNull LinearLayout f1221,
      @NonNull Button greyTheme, @NonNull Button greyThemeInverted, @NonNull Button lightTheme,
      @NonNull Button lightThemeInverted, @NonNull LinearLayout rg2rtwjk,
      @NonNull LinearLayout rgrtjk, @NonNull LinearLayout rgrtwj1k, @NonNull LinearLayout rgrtwjk,
      @NonNull LinearLayout sdfgsdf1g, @NonNull LinearLayout sdfgsdfg,
      @NonNull RelativeLayout strelka, @NonNull TextView textView20) {
    this.rootView = rootView;
    this.amoledTheme = amoledTheme;
    this.amoledThemeInverted = amoledThemeInverted;
    this.auto = auto;
    this.czsux = czsux;
    this.czsx = czsx;
    this.darkTheme = darkTheme;
    this.darkThemeInverted = darkThemeInverted;
    this.dfsdfb1 = dfsdfb1;
    this.dfsdfb11 = dfsdfb11;
    this.dgdsfg = dgdsfg;
    this.exitTheme = exitTheme;
    this.f1221 = f1221;
    this.greyTheme = greyTheme;
    this.greyThemeInverted = greyThemeInverted;
    this.lightTheme = lightTheme;
    this.lightThemeInverted = lightThemeInverted;
    this.rg2rtwjk = rg2rtwjk;
    this.rgrtjk = rgrtjk;
    this.rgrtwj1k = rgrtwj1k;
    this.rgrtwjk = rgrtwjk;
    this.sdfgsdf1g = sdfgsdf1g;
    this.sdfgsdfg = sdfgsdfg;
    this.strelka = strelka;
    this.textView20 = textView20;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogSelectThemeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogSelectThemeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_select_theme, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogSelectThemeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.amoled_theme;
      Button amoledTheme = ViewBindings.findChildViewById(rootView, id);
      if (amoledTheme == null) {
        break missingId;
      }

      id = R.id.amoled_theme_inverted;
      Button amoledThemeInverted = ViewBindings.findChildViewById(rootView, id);
      if (amoledThemeInverted == null) {
        break missingId;
      }

      id = R.id.auto;
      Button auto = ViewBindings.findChildViewById(rootView, id);
      if (auto == null) {
        break missingId;
      }

      id = R.id.czsux;
      LinearLayout czsux = ViewBindings.findChildViewById(rootView, id);
      if (czsux == null) {
        break missingId;
      }

      id = R.id.czsx;
      LinearLayout czsx = ViewBindings.findChildViewById(rootView, id);
      if (czsx == null) {
        break missingId;
      }

      id = R.id.dark_theme;
      Button darkTheme = ViewBindings.findChildViewById(rootView, id);
      if (darkTheme == null) {
        break missingId;
      }

      id = R.id.dark_theme_inverted;
      Button darkThemeInverted = ViewBindings.findChildViewById(rootView, id);
      if (darkThemeInverted == null) {
        break missingId;
      }

      id = R.id.dfsdfb1;
      RelativeLayout dfsdfb1 = ViewBindings.findChildViewById(rootView, id);
      if (dfsdfb1 == null) {
        break missingId;
      }

      id = R.id.dfsdfb11;
      RelativeLayout dfsdfb11 = ViewBindings.findChildViewById(rootView, id);
      if (dfsdfb11 == null) {
        break missingId;
      }

      id = R.id.dgdsfg;
      RelativeLayout dgdsfg = ViewBindings.findChildViewById(rootView, id);
      if (dgdsfg == null) {
        break missingId;
      }

      id = R.id.exit_theme;
      Button exitTheme = ViewBindings.findChildViewById(rootView, id);
      if (exitTheme == null) {
        break missingId;
      }

      id = R.id.f1221;
      LinearLayout f1221 = ViewBindings.findChildViewById(rootView, id);
      if (f1221 == null) {
        break missingId;
      }

      id = R.id.grey_theme;
      Button greyTheme = ViewBindings.findChildViewById(rootView, id);
      if (greyTheme == null) {
        break missingId;
      }

      id = R.id.grey_theme_inverted;
      Button greyThemeInverted = ViewBindings.findChildViewById(rootView, id);
      if (greyThemeInverted == null) {
        break missingId;
      }

      id = R.id.light_theme;
      Button lightTheme = ViewBindings.findChildViewById(rootView, id);
      if (lightTheme == null) {
        break missingId;
      }

      id = R.id.light_theme_inverted;
      Button lightThemeInverted = ViewBindings.findChildViewById(rootView, id);
      if (lightThemeInverted == null) {
        break missingId;
      }

      id = R.id.rg2rtwjk;
      LinearLayout rg2rtwjk = ViewBindings.findChildViewById(rootView, id);
      if (rg2rtwjk == null) {
        break missingId;
      }

      id = R.id.rgrtjk;
      LinearLayout rgrtjk = ViewBindings.findChildViewById(rootView, id);
      if (rgrtjk == null) {
        break missingId;
      }

      id = R.id.rgrtwj1k;
      LinearLayout rgrtwj1k = ViewBindings.findChildViewById(rootView, id);
      if (rgrtwj1k == null) {
        break missingId;
      }

      id = R.id.rgrtwjk;
      LinearLayout rgrtwjk = ViewBindings.findChildViewById(rootView, id);
      if (rgrtwjk == null) {
        break missingId;
      }

      id = R.id.sdfgsdf1g;
      LinearLayout sdfgsdf1g = ViewBindings.findChildViewById(rootView, id);
      if (sdfgsdf1g == null) {
        break missingId;
      }

      id = R.id.sdfgsdfg;
      LinearLayout sdfgsdfg = ViewBindings.findChildViewById(rootView, id);
      if (sdfgsdfg == null) {
        break missingId;
      }

      id = R.id.strelka;
      RelativeLayout strelka = ViewBindings.findChildViewById(rootView, id);
      if (strelka == null) {
        break missingId;
      }

      id = R.id.textView20;
      TextView textView20 = ViewBindings.findChildViewById(rootView, id);
      if (textView20 == null) {
        break missingId;
      }

      return new DialogSelectThemeBinding((RelativeLayout) rootView, amoledTheme,
          amoledThemeInverted, auto, czsux, czsx, darkTheme, darkThemeInverted, dfsdfb1, dfsdfb11,
          dgdsfg, exitTheme, f1221, greyTheme, greyThemeInverted, lightTheme, lightThemeInverted,
          rg2rtwjk, rgrtjk, rgrtwj1k, rgrtwjk, sdfgsdf1g, sdfgsdfg, strelka, textView20);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
