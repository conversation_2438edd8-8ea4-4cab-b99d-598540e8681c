<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_category" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\item_category.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/categoryBlock"><Targets><Target id="@+id/categoryBlock" tag="layout/item_category_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="26" endOffset="51"/></Target><Target id="@+id/categoryName" view="TextView"><Expressions/><location startLine="12" startOffset="4" endLine="25" endOffset="50"/></Target></Targets></Layout>