// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSlideLayout6Binding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout autorunView;

  @NonNull
  public final RelativeLayout button;

  @NonNull
  public final Button buttonEnable;

  @NonNull
  public final RelativeLayout buttonLayout;

  @NonNull
  public final Button dontkillmyappButton;

  @NonNull
  public final Button nextPage;

  @NonNull
  public final TextView textView15;

  @NonNull
  public final TextView textView16;

  @NonNull
  public final TextView textView18;

  @NonNull
  public final TextView textView19;

  @NonNull
  public final TextView textView4;

  @NonNull
  public final TextView textViewResetCharge;

  @NonNull
  public final Button workInBackgroundPermission;

  private ItemSlideLayout6Binding(@NonNull LinearLayout rootView, @NonNull LinearLayout autorunView,
      @NonNull RelativeLayout button, @NonNull Button buttonEnable,
      @NonNull RelativeLayout buttonLayout, @NonNull Button dontkillmyappButton,
      @NonNull Button nextPage, @NonNull TextView textView15, @NonNull TextView textView16,
      @NonNull TextView textView18, @NonNull TextView textView19, @NonNull TextView textView4,
      @NonNull TextView textViewResetCharge, @NonNull Button workInBackgroundPermission) {
    this.rootView = rootView;
    this.autorunView = autorunView;
    this.button = button;
    this.buttonEnable = buttonEnable;
    this.buttonLayout = buttonLayout;
    this.dontkillmyappButton = dontkillmyappButton;
    this.nextPage = nextPage;
    this.textView15 = textView15;
    this.textView16 = textView16;
    this.textView18 = textView18;
    this.textView19 = textView19;
    this.textView4 = textView4;
    this.textViewResetCharge = textViewResetCharge;
    this.workInBackgroundPermission = workInBackgroundPermission;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSlideLayout6Binding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSlideLayout6Binding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_slide_layout_6, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSlideLayout6Binding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.autorun_view;
      LinearLayout autorunView = ViewBindings.findChildViewById(rootView, id);
      if (autorunView == null) {
        break missingId;
      }

      id = R.id.button;
      RelativeLayout button = ViewBindings.findChildViewById(rootView, id);
      if (button == null) {
        break missingId;
      }

      id = R.id.buttonEnable;
      Button buttonEnable = ViewBindings.findChildViewById(rootView, id);
      if (buttonEnable == null) {
        break missingId;
      }

      id = R.id.button_layout;
      RelativeLayout buttonLayout = ViewBindings.findChildViewById(rootView, id);
      if (buttonLayout == null) {
        break missingId;
      }

      id = R.id.dontkillmyapp_button;
      Button dontkillmyappButton = ViewBindings.findChildViewById(rootView, id);
      if (dontkillmyappButton == null) {
        break missingId;
      }

      id = R.id.next_page;
      Button nextPage = ViewBindings.findChildViewById(rootView, id);
      if (nextPage == null) {
        break missingId;
      }

      id = R.id.textView15;
      TextView textView15 = ViewBindings.findChildViewById(rootView, id);
      if (textView15 == null) {
        break missingId;
      }

      id = R.id.textView16;
      TextView textView16 = ViewBindings.findChildViewById(rootView, id);
      if (textView16 == null) {
        break missingId;
      }

      id = R.id.textView18;
      TextView textView18 = ViewBindings.findChildViewById(rootView, id);
      if (textView18 == null) {
        break missingId;
      }

      id = R.id.textView19;
      TextView textView19 = ViewBindings.findChildViewById(rootView, id);
      if (textView19 == null) {
        break missingId;
      }

      id = R.id.textView4;
      TextView textView4 = ViewBindings.findChildViewById(rootView, id);
      if (textView4 == null) {
        break missingId;
      }

      id = R.id.text_view_reset_charge;
      TextView textViewResetCharge = ViewBindings.findChildViewById(rootView, id);
      if (textViewResetCharge == null) {
        break missingId;
      }

      id = R.id.work_in_background_permission;
      Button workInBackgroundPermission = ViewBindings.findChildViewById(rootView, id);
      if (workInBackgroundPermission == null) {
        break missingId;
      }

      return new ItemSlideLayout6Binding((LinearLayout) rootView, autorunView, button, buttonEnable,
          buttonLayout, dontkillmyappButton, nextPage, textView15, textView16, textView18,
          textView19, textView4, textViewResetCharge, workInBackgroundPermission);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
