package com.tqhit.battery.one.manager.graph;

import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class BatteryHistoryManager_Factory implements Factory<BatteryHistoryManager> {
  private final Provider<PreferencesHelper> preferencesHelperProvider;

  public BatteryHistoryManager_Factory(Provider<PreferencesHelper> preferencesHelperProvider) {
    this.preferencesHelperProvider = preferencesHelperProvider;
  }

  @Override
  public BatteryHistoryManager get() {
    return newInstance(preferencesHelperProvider.get());
  }

  public static BatteryHistoryManager_Factory create(
      Provider<PreferencesHelper> preferencesHelperProvider) {
    return new BatteryHistoryManager_Factory(preferencesHelperProvider);
  }

  public static BatteryHistoryManager newInstance(PreferencesHelper preferencesHelper) {
    return new BatteryHistoryManager(preferencesHelper);
  }
}
