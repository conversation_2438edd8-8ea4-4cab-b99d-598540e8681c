package com.tqhit.battery.one.features.stats.discharge.domain;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DischargeRateCalculator_Factory implements Factory<DischargeRateCalculator> {
  private final Provider<TimeConverter> timeConverterProvider;

  public DischargeRateCalculator_Factory(Provider<TimeConverter> timeConverterProvider) {
    this.timeConverterProvider = timeConverterProvider;
  }

  @Override
  public DischargeRateCalculator get() {
    return newInstance(timeConverterProvider.get());
  }

  public static DischargeRateCalculator_Factory create(
      Provider<TimeConverter> timeConverterProvider) {
    return new DischargeRateCalculator_Factory(timeConverterProvider);
  }

  public static DischargeRateCalculator newInstance(TimeConverter timeConverter) {
    return new DischargeRateCalculator(timeConverter);
  }
}
