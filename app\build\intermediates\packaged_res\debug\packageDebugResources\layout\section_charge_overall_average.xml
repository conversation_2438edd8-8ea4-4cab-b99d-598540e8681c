<?xml version="1.0" encoding="utf-8"?>
<!-- 
 * Average speed charge section
 * This section contains average charging speeds across sessions
 */
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/charge_overall_average_root"
    android:orientation="vertical"
    android:background="@drawable/white_block"
    android:padding="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="9dp"
    android:layout_marginEnd="9dp"
    android:layout_marginBottom="14dp">

    <!-- Section Title with info button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">
        
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/average_speed_charge"
            android:textColor="?attr/black"
            android:textSize="19sp" />
            
        <ImageView
            android:id="@+id/charge_avg_info_button"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_note"
            android:contentDescription="@string/information" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?android:attr/listDivider"
        android:layout_marginBottom="8dp" />

    <!-- Average speed (screen on) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/avg_speed_screen_on"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_overall_avg_speed_screen_on"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Average speed (screen off) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/avg_speed_screen_off"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_overall_avg_speed_screen_off"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Average speed (mixed) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/avg_speed_mixed"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_overall_avg_speed_mixed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout> 