// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemLayoutOthersBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final LinearLayout contentContainer;

  @NonNull
  public final TextView itemDesc;

  @NonNull
  public final ImageView itemIcon;

  @NonNull
  public final TextView itemTitle;

  private ItemLayoutOthersBinding(@NonNull ConstraintLayout rootView,
      @NonNull LinearLayout contentContainer, @NonNull TextView itemDesc,
      @NonNull ImageView itemIcon, @NonNull TextView itemTitle) {
    this.rootView = rootView;
    this.contentContainer = contentContainer;
    this.itemDesc = itemDesc;
    this.itemIcon = itemIcon;
    this.itemTitle = itemTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemLayoutOthersBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemLayoutOthersBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_layout_others, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemLayoutOthersBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.contentContainer;
      LinearLayout contentContainer = ViewBindings.findChildViewById(rootView, id);
      if (contentContainer == null) {
        break missingId;
      }

      id = R.id.itemDesc;
      TextView itemDesc = ViewBindings.findChildViewById(rootView, id);
      if (itemDesc == null) {
        break missingId;
      }

      id = R.id.itemIcon;
      ImageView itemIcon = ViewBindings.findChildViewById(rootView, id);
      if (itemIcon == null) {
        break missingId;
      }

      id = R.id.itemTitle;
      TextView itemTitle = ViewBindings.findChildViewById(rootView, id);
      if (itemTitle == null) {
        break missingId;
      }

      return new ItemLayoutOthersBinding((ConstraintLayout) rootView, contentContainer, itemDesc,
          itemIcon, itemTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
