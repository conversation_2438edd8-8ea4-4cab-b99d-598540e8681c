{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tqhit.battery.one.app-mergeDebugResources-132:\\values-ro\\values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1142,1208,1303,1377,1437,1521,1583,1649,1707,1780,1843,1899,2018,2075,2136,2192,2266,2411,2497,2572,2661,2740,2824,2957,3039,3122,3268,3358,3438,3493,3544,3610,3683,3761,3832,3917,3988,4065,4139,4211,4317,4408,4482,4577,4675,4749,4829,4930,4983,5069,5135,5224,5314,5376,5440,5503,5577,5689,5799,5909,6014,6073,6128,6207,6293,6370", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1137,1203,1298,1372,1432,1516,1578,1644,1702,1775,1838,1894,2013,2070,2131,2187,2261,2406,2492,2567,2656,2735,2819,2952,3034,3117,3263,3353,3433,3488,3539,3605,3678,3756,3827,3912,3983,4060,4134,4206,4312,4403,4477,4572,4670,4744,4824,4925,4978,5064,5130,5219,5309,5371,5435,5498,5572,5684,5794,5904,6009,6068,6123,6202,6288,6365,6444"}, "to": {"startLines": "21,109,110,111,112,113,129,130,144,213,215,270,294,302,374,375,376,377,378,379,380,381,382,383,384,385,386,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,471,506,507,518", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "947,9153,9245,9333,9420,9516,10871,10972,12094,20928,21089,25227,27211,27782,35464,35548,35610,35676,35734,35807,35870,35926,36045,36102,36163,36219,36293,36571,36657,36732,36821,36900,36984,37117,37199,37282,37428,37518,37598,37653,37704,37770,37843,37921,37992,38077,38148,38225,38299,38371,38477,38568,38642,38737,38835,38909,38989,39090,39143,39229,39295,39384,39474,39536,39600,39663,39737,39849,39959,40069,40174,40233,43675,46263,46349,47276", "endLines": "25,109,110,111,112,113,129,130,144,213,215,270,294,302,374,375,376,377,378,379,380,381,382,383,384,385,386,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,471,506,507,518", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "1163,9240,9328,9415,9511,9601,10967,11088,12173,20985,21150,25317,27280,27837,35543,35605,35671,35729,35802,35865,35921,36040,36097,36158,36214,36288,36433,36652,36727,36816,36895,36979,37112,37194,37277,37423,37513,37593,37648,37699,37765,37838,37916,37987,38072,38143,38220,38294,38366,38472,38563,38637,38732,38830,38904,38984,39085,39138,39224,39290,39379,39469,39531,39595,39658,39732,39844,39954,40064,40169,40228,40283,43749,46344,46421,47350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,262,337,476,645,732", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "171,257,332,471,640,727,808"}, "to": {"startLines": "169,268,447,482,531,550,551", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14902,25076,41319,44345,48522,50087,50174", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "14968,25157,41389,44479,48686,50169,50250"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "139", "endOffsets": "334"}, "to": {"startLines": "155", "startColumns": "4", "startOffsets": "13380", "endColumns": "143", "endOffsets": "13519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,579,685,832,958,1077,1182,1340,1447,1602,1731,1873,2035,2100,2164", "endColumns": "103,155,125,105,146,125,118,104,157,106,154,128,141,161,64,63,78", "endOffsets": "296,452,578,684,831,957,1076,1181,1339,1446,1601,1730,1872,2034,2099,2163,2242"}, "to": {"startLines": "147,148,149,150,151,152,153,154,156,157,158,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12359,12467,12627,12757,12867,13018,13148,13271,13524,13686,13797,13956,14089,14235,14401,14470,14538", "endColumns": "107,159,129,109,150,129,122,108,161,110,158,132,145,165,68,67,82", "endOffsets": "12462,12622,12752,12862,13013,13143,13266,13375,13681,13792,13951,14084,14230,14396,14465,14533,14616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,198,263,335,413,493,583,676", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "131,193,258,330,408,488,578,671,745"}, "to": {"startLines": "240,241,242,243,244,245,246,247,248", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23185,23266,23328,23393,23465,23543,23623,23713,23806", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "23261,23323,23388,23460,23538,23618,23708,23801,23875"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,150", "endColumns": "94,99", "endOffsets": "145,245"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "49523,49618", "endColumns": "94,99", "endOffsets": "49613,49713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "119,120,121,122,123,124,125,528", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9937,10035,10137,10237,10336,10438,10547,48284", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "10030,10132,10232,10331,10433,10542,10659,48380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,531,636,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,2049,2473,2577,2840", "endColumns": "122,105,104,118,90,92,94,93,99,92,94,93,90,91,107,103,161,81", "endOffsets": "223,329,631,750,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,2152,2572,2734,2917"}, "to": {"startLines": "42,43,46,47,50,51,52,53,54,55,56,57,58,59,62,66,67,505", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2041,2164,2407,2512,2732,2823,2916,3011,3105,3205,3298,3393,3487,3578,3803,4139,4243,46181", "endColumns": "122,105,104,118,90,92,94,93,99,92,94,93,90,91,107,103,161,81", "endOffsets": "2159,2265,2507,2626,2818,2911,3006,3100,3200,3293,3388,3482,3573,3665,3906,4238,4400,46258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,383,485,573,651,738,829,911,999,1089,1236,1315,1390,1467,1534", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,78,74,76,66,114", "endOffsets": "197,281,378,480,568,646,733,824,906,994,1084,1157,1310,1385,1462,1529,1644"}, "to": {"startLines": "145,146,183,184,207,297,299,440,446,488,489,509,526,527,533,534,536", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12178,12275,17406,17503,20539,27423,27566,40750,41237,44926,45014,46490,48130,48209,48748,48825,48944", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,78,74,76,66,114", "endOffsets": "12270,12354,17498,17600,20622,27496,27648,40836,41314,45009,45099,46558,48204,48279,48820,48887,49054"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,377", "endColumns": "106,101,112,102", "endOffsets": "157,259,372,475"}, "to": {"startLines": "170,271,272,273", "startColumns": "4,4,4,4", "startOffsets": "14973,25322,25424,25537", "endColumns": "106,101,112,102", "endOffsets": "15075,25419,25532,25635"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-ro\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "125,192,241,291,341,391,444,493,543,599,673,723,770,821,869,21735,922,1006,1061,1112,1164,1218,1299,1370,1453,1530,1602,1667,24278,1721,1760,1927,1983,2078,2121,2182,21943,22800,23224,23347,2267,57,24233,22153,24115,2310,2357,2396,2481,2537,2613,3005,3402,12165,12230,11473,11386,12295,3443,3514,3584,3784,4106,4180,4235,4280,4343,4413,4464,21827,4534,4608,22074,4653,4739,4783,4857,5018,5088,5182,5264,5332,5427,22672,5509,5566,5619,5683,5744,5790,5860,5968,6018,6377,6431,6492,7055,7633,7702,7754,7899,7962,8253,8306,8801,8871,9012,9168,9320,9391,9439,9489,9563,9661,9749,9802,9951,10055,10110,10176,10245,10526,10805,11006,11169,11262,12382,12453,23920,12508,12550,12650,12716,12774,13025,13075,13152,13217,13273,13411,13470,13602,13836,13874,13930,13968,14006,14042,14078,14152,14197,14252,14307,14373,23979,14447,14522,14578,14622,14665,14741,15193,15250,15324,15490,15567,15639,15676,15711,15752,15795,15852,15917,15987,16045,16124,16270,21991,16305,16363,16416,16511,16581,16654,16761,16893,16965,17009,17090,22462,22281,22195,23124,23681,17392,17432,17487,17538,17601,17693,23274,17741,17828,17878,21898,17920,24045,18056,18153,18252,18310,18381,18578,18653,18714,22583,18777,18829,23726,18894,18974,19071,19156,19201,23055,19273,19352,19396,19456,19503,19627,20010,20089,20134,20193,20273,20331,20384,24178,20424,20497,20717,20798,20864,20938,20993,21067,21134,21173,22742,21234,21293,21339,21381,21434,21499,21565,21618,21654,21690", "endColumns": "65,47,48,48,48,51,47,48,54,72,48,45,49,46,51,90,82,53,49,50,52,79,69,81,75,70,63,52,75,37,165,54,93,41,59,83,46,253,48,332,41,66,43,40,61,45,37,83,54,74,390,395,39,63,63,690,85,83,69,68,198,320,72,53,43,61,68,49,68,69,72,43,77,84,42,72,159,68,92,80,66,93,80,68,55,51,62,59,44,68,106,48,357,52,59,561,576,67,50,143,61,289,51,493,68,139,154,150,69,46,48,72,96,86,51,147,102,53,64,67,279,277,199,161,91,77,69,53,57,40,98,64,56,249,48,75,63,54,136,57,130,232,36,54,36,36,34,34,72,43,53,53,64,72,64,73,54,42,41,74,450,55,72,164,75,70,35,33,39,41,55,63,68,56,77,144,33,81,56,51,93,68,71,105,130,70,42,79,300,119,179,84,98,43,38,53,49,61,90,46,71,85,48,40,43,134,68,95,97,56,69,195,73,59,61,87,50,63,192,78,95,83,43,70,67,77,42,58,45,122,381,77,43,57,78,56,51,38,53,71,218,79,64,72,53,72,65,37,59,56,57,44,40,51,63,64,51,34,34,43", "endOffsets": "186,235,285,335,385,438,487,537,593,667,717,764,815,863,916,21821,1000,1055,1106,1158,1212,1293,1364,1447,1524,1596,1661,1715,24349,1754,1921,1977,2072,2115,2176,2261,21985,23049,23268,23675,2304,119,24272,22189,24172,2351,2390,2475,2531,2607,2999,3396,3437,12224,12289,12159,11467,12374,3508,3578,3778,4100,4174,4229,4274,4337,4407,4458,4528,21892,4602,4647,22147,4733,4777,4851,5012,5082,5176,5258,5326,5421,5503,22736,5560,5613,5677,5738,5784,5854,5962,6012,6371,6425,6486,7049,7627,7696,7748,7893,7956,8247,8300,8795,8865,9006,9162,9314,9385,9433,9483,9557,9655,9743,9796,9945,10049,10104,10170,10239,10520,10799,11000,11163,11256,11335,12447,12502,23973,12544,12644,12710,12768,13019,13069,13146,13211,13267,13405,13464,13596,13830,13868,13924,13962,14000,14036,14072,14146,14191,14246,14301,14367,14441,24039,14516,14572,14616,14659,14735,15187,15244,15318,15484,15561,15633,15670,15705,15746,15789,15846,15911,15981,16039,16118,16264,16299,22068,16357,16410,16505,16575,16648,16755,16887,16959,17003,17084,17386,22577,22456,22275,23218,23720,17426,17481,17532,17595,17687,17735,23341,17822,17872,17914,21937,18050,24109,18147,18246,18304,18375,18572,18647,18708,18771,22666,18823,18888,23914,18968,19065,19150,19195,19267,23118,19346,19390,19450,19497,19621,20004,20083,20128,20187,20267,20325,20378,20418,24227,20491,20711,20792,20858,20932,20987,21061,21128,21167,21228,22794,21287,21333,21375,21428,21493,21559,21612,21648,21684,21729"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,44,45,48,49,60,61,63,64,65,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,114,115,116,117,118,126,127,128,131,132,133,134,135,136,137,138,139,140,141,142,143,165,166,167,168,171,172,173,174,175,176,177,178,179,180,181,182,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,208,209,210,211,212,214,269,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,295,296,298,300,301,303,304,305,306,307,308,309,310,311,312,370,371,372,373,387,388,434,441,442,443,444,445,448,449,450,451,452,453,454,455,464,465,466,467,468,469,470,472,473,474,475,476,477,478,479,480,481,483,484,485,486,487,490,491,492,493,494,495,503,504,508,510,511,512,513,514,515,516,517,519,520,521,522,523,524,525,529,530,532,535,537,538,539,540,541,544,545,546,547,548,549,552,553,554,555,557,558,559,560,561,562,563,564", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1168,1234,1282,1331,1380,1429,1481,1529,1578,1633,1706,1755,1801,1851,1898,1950,2270,2353,2631,2681,3670,3723,3911,3981,4063,4405,4476,4540,4593,4669,4707,4873,4928,5022,5064,5124,5208,5255,5509,5558,5891,5933,6000,6044,6085,6147,6193,6231,6315,6370,6445,6836,7232,7272,7336,7400,8091,8177,8261,8331,8400,8599,8920,8993,9047,9091,9606,9675,9725,9794,9864,10664,10708,10786,11093,11136,11209,11369,11438,11531,11612,11679,11773,11854,11923,11979,12031,14621,14681,14726,14795,15080,15129,15487,15540,15600,16162,16739,16807,16858,17002,17064,17354,17605,18099,18168,18308,18463,18614,18684,18731,18780,18853,18950,19037,19089,19237,19340,19394,19459,19527,19807,20085,20285,20447,20627,20705,20775,20829,20887,20990,25162,25640,25697,25947,25996,26072,26136,26191,26328,26386,26517,26750,26787,26842,26879,26916,26951,26986,27059,27103,27157,27285,27350,27501,27653,27727,27842,27885,27927,28002,28453,28509,28582,28747,28823,28894,35292,35326,35366,35408,36438,36502,40288,40841,40919,41064,41098,41180,41394,41446,41540,41609,41681,41787,41918,41989,42766,42846,43147,43267,43447,43532,43631,43754,43793,43847,43897,43959,44050,44097,44169,44255,44304,44484,44528,44663,44732,44828,45104,45161,45231,45427,45501,45561,46042,46130,46426,46563,46756,46835,46931,47015,47059,47130,47198,47355,47398,47457,47503,47626,48008,48086,48385,48443,48691,48892,49059,49098,49152,49224,49443,49718,49783,49856,49910,49983,50049,50255,50315,50372,50430,50535,50576,50628,50692,50757,50809,50844,50879", "endColumns": "65,47,48,48,48,51,47,48,54,72,48,45,49,46,51,90,82,53,49,50,52,79,69,81,75,70,63,52,75,37,165,54,93,41,59,83,46,253,48,332,41,66,43,40,61,45,37,83,54,74,390,395,39,63,63,690,85,83,69,68,198,320,72,53,43,61,68,49,68,69,72,43,77,84,42,72,159,68,92,80,66,93,80,68,55,51,62,59,44,68,106,48,357,52,59,561,576,67,50,143,61,289,51,493,68,139,154,150,69,46,48,72,96,86,51,147,102,53,64,67,279,277,199,161,91,77,69,53,57,40,98,64,56,249,48,75,63,54,136,57,130,232,36,54,36,36,34,34,72,43,53,53,64,72,64,73,54,42,41,74,450,55,72,164,75,70,35,33,39,41,55,63,68,56,77,144,33,81,56,51,93,68,71,105,130,70,42,79,300,119,179,84,98,43,38,53,49,61,90,46,71,85,48,40,43,134,68,95,97,56,69,195,73,59,61,87,50,63,192,78,95,83,43,70,67,77,42,58,45,122,381,77,43,57,78,56,51,38,53,71,218,79,64,72,53,72,65,37,59,56,57,44,40,51,63,64,51,34,34,43", "endOffsets": "1229,1277,1326,1375,1424,1476,1524,1573,1628,1701,1750,1796,1846,1893,1945,2036,2348,2402,2676,2727,3718,3798,3976,4058,4134,4471,4535,4588,4664,4702,4868,4923,5017,5059,5119,5203,5250,5504,5553,5886,5928,5995,6039,6080,6142,6188,6226,6310,6365,6440,6831,7227,7267,7331,7395,8086,8172,8256,8326,8395,8594,8915,8988,9042,9086,9148,9670,9720,9789,9859,9932,10703,10781,10866,11131,11204,11364,11433,11526,11607,11674,11768,11849,11918,11974,12026,12089,14676,14721,14790,14897,15124,15482,15535,15595,16157,16734,16802,16853,16997,17059,17349,17401,18094,18163,18303,18458,18609,18679,18726,18775,18848,18945,19032,19084,19232,19335,19389,19454,19522,19802,20080,20280,20442,20534,20700,20770,20824,20882,20923,21084,25222,25692,25942,25991,26067,26131,26186,26323,26381,26512,26745,26782,26837,26874,26911,26946,26981,27054,27098,27152,27206,27345,27418,27561,27722,27777,27880,27922,27997,28448,28504,28577,28742,28818,28889,28925,35321,35361,35403,35459,36497,36566,40340,40914,41059,41093,41175,41232,41441,41535,41604,41676,41782,41913,41984,42027,42841,43142,43262,43442,43527,43626,43670,43788,43842,43892,43954,44045,44092,44164,44250,44299,44340,44523,44658,44727,44823,44921,45156,45226,45422,45496,45556,45618,46125,46176,46485,46751,46830,46926,47010,47054,47125,47193,47271,47393,47452,47498,47621,48003,48081,48125,48438,48517,48743,48939,49093,49147,49219,49438,49518,49778,49851,49905,49978,50044,50082,50310,50367,50425,50470,50571,50623,50687,50752,50804,50839,50874,50918"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,940,1030,1119,1216,1310,1385,1451,1548,1646,1715,1778,1841,1910,2024,2137,2251,2328,2408,2477,2553,2652,2753,2819,2882,2935,2993,3041,3102,3166,3236,3301,3370,3431,3489,3555,3607,3669,3745,3821,3878", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,51,61,75,75,56,69", "endOffsets": "281,577,847,935,1025,1114,1211,1305,1380,1446,1543,1641,1710,1773,1836,1905,2019,2132,2246,2323,2403,2472,2548,2647,2748,2814,2877,2930,2988,3036,3097,3161,3231,3296,3365,3426,3484,3550,3602,3664,3740,3816,3873,3943"}, "to": {"startLines": "2,11,16,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,249,250,251,252,253,254,255,256,257,258,259,262,263,264,265,266,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,677,21155,21243,21333,21422,21519,21613,21688,21754,21851,21949,22018,22081,22144,22213,22327,22440,22554,22631,22711,22780,22856,22955,23056,23122,23880,23933,23991,24039,24100,24164,24234,24299,24368,24429,24487,24683,24735,24797,24873,24949,25006", "endLines": "10,15,20,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,249,250,251,252,253,254,255,256,257,258,259,262,263,264,265,266,267", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,51,61,75,75,56,69", "endOffsets": "376,672,942,21238,21328,21417,21514,21608,21683,21749,21846,21944,22013,22076,22139,22208,22322,22435,22549,22626,22706,22775,22851,22950,23051,23117,23180,23928,23986,24034,24095,24159,24229,24294,24363,24424,24482,24548,24730,24792,24868,24944,25001,25071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,244,290,592,660,788,878,999,1049,1099,1213,1294,1339,1432,1467,1501,1561,1643,1685", "endColumns": "44,45,67,67,127,89,120,49,49,113,80,44,92,34,33,59,81,41,55", "endOffsets": "243,289,357,659,787,877,998,1048,1098,1212,1293,1338,1431,1466,1500,1560,1642,1684,1740"}, "to": {"startLines": "435,436,437,456,457,458,459,460,461,462,463,496,497,498,499,500,501,502,556", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "40345,40394,40444,42032,42104,42236,42330,42455,42509,42563,42681,45623,45672,45769,45808,45846,45910,45996,50475", "endColumns": "48,49,71,71,131,93,124,53,53,117,84,48,96,38,37,63,85,45,59", "endOffsets": "40389,40439,40511,42099,42231,42325,42450,42504,42558,42676,42761,45667,45764,45803,45841,45905,45991,46037,50530"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,312,429,555,665,762,876,1013,1133,1276,1360,1462,1557,1655,1775,1902,2009,2147,2283,2424,2600,2737,2856,2979,3105,3201,3297,3424,3565,3665,3770,3881,4021,4167,4279,4383,4459,4554,4646,4753,4839,4926,5027,5109,5192,5291,5395,5490,5591,5678,5789,5889,5995,6116,6198,6313", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,106,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "180,307,424,550,660,757,871,1008,1128,1271,1355,1457,1552,1650,1770,1897,2004,2142,2278,2419,2595,2732,2851,2974,3100,3196,3292,3419,3560,3660,3765,3876,4016,4162,4274,4378,4454,4549,4641,4748,4834,4921,5022,5104,5187,5286,5390,5485,5586,5673,5784,5884,5990,6111,6193,6308,6412"}, "to": {"startLines": "313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28930,29060,29187,29304,29430,29540,29637,29751,29888,30008,30151,30235,30337,30432,30530,30650,30777,30884,31022,31158,31299,31475,31612,31731,31854,31980,32076,32172,32299,32440,32540,32645,32756,32896,33042,33154,33258,33334,33429,33521,33628,33714,33801,33902,33984,34067,34166,34270,34365,34466,34553,34664,34764,34870,34991,35073,35188", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,106,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "29055,29182,29299,29425,29535,29632,29746,29883,30003,30146,30230,30332,30427,30525,30645,30772,30879,31017,31153,31294,31470,31607,31726,31849,31975,32071,32167,32294,32435,32535,32640,32751,32891,33037,33149,33253,33329,33424,33516,33623,33709,33796,33897,33979,34062,34161,34265,34360,34461,34548,34659,34759,34865,34986,35068,35183,35287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,123", "endOffsets": "160,284"}, "to": {"startLines": "438,439", "startColumns": "4,4", "startOffsets": "40516,40626", "endColumns": "109,123", "endOffsets": "40621,40745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-ro\\values-ro.xml", "from": {"startLines": "56,57", "startColumns": "4,4", "startOffsets": "3560,3624", "endColumns": "63,65", "endOffsets": "3619,3685"}, "to": {"startLines": "260,261", "startColumns": "4,4", "startOffsets": "24553,24617", "endColumns": "63,65", "endOffsets": "24612,24678"}}]}, {"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "21,109,110,111,112,113,129,130,144,213,215,270,294,303,375,376,377,378,379,380,381,382,383,384,385,386,387,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,472,508,509,520", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "947,9153,9245,9333,9420,9516,10871,10972,12094,20928,21089,25227,27211,27837,35519,35603,35665,35731,35789,35862,35925,35981,36100,36157,36218,36274,36348,36626,36712,36787,36876,36955,37039,37172,37254,37337,37483,37573,37653,37708,37759,37825,37898,37976,38047,38132,38203,38280,38354,38426,38532,38623,38697,38792,38890,38964,39044,39145,39198,39284,39350,39439,39529,39591,39655,39718,39792,39904,40014,40124,40229,40288,43730,46371,46457,47384", "endLines": "25,109,110,111,112,113,129,130,144,213,215,270,294,303,375,376,377,378,379,380,381,382,383,384,385,386,387,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,472,508,509,520", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "1163,9240,9328,9415,9511,9601,10967,11088,12173,20985,21150,25317,27280,27892,35598,35660,35726,35784,35857,35920,35976,36095,36152,36213,36269,36343,36488,36707,36782,36871,36950,37034,37167,37249,37332,37478,37568,37648,37703,37754,37820,37893,37971,38042,38127,38198,38275,38349,38421,38527,38618,38692,38787,38885,38959,39039,39140,39193,39279,39345,39434,39524,39586,39650,39713,39787,39899,40009,40119,40224,40283,40338,43804,46452,46529,47458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "169,268,448,483,534,553,554", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14902,25076,41374,44400,48713,50278,50365", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "14968,25157,41444,44534,48877,50360,50441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ro\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "155", "startColumns": "4", "startOffsets": "13380", "endColumns": "143", "endOffsets": "13519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ro\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "147,148,149,150,151,152,153,154,156,157,158,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12359,12467,12627,12757,12867,13018,13148,13271,13524,13686,13797,13956,14089,14235,14401,14470,14538", "endColumns": "107,159,129,109,150,129,122,108,161,110,158,132,145,165,68,67,82", "endOffsets": "12462,12622,12752,12862,13013,13143,13266,13375,13681,13792,13951,14084,14230,14396,14465,14533,14616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "240,241,242,243,244,245,246,247,248", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23185,23266,23328,23393,23465,23543,23623,23713,23806", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "23261,23323,23388,23460,23538,23618,23708,23801,23875"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "545,546", "startColumns": "4,4", "startOffsets": "49714,49809", "endColumns": "94,99", "endOffsets": "49804,49904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "119,120,121,122,123,124,125,531", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9937,10035,10137,10237,10336,10438,10547,48475", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "10030,10132,10232,10331,10433,10542,10659,48571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "42,43,46,47,50,51,52,53,54,55,56,57,58,59,62,66,67,507", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2041,2164,2407,2512,2732,2823,2916,3011,3105,3205,3298,3393,3487,3578,3803,4139,4243,46289", "endColumns": "122,105,104,118,90,92,94,93,99,92,94,93,90,91,107,103,161,81", "endOffsets": "2159,2265,2507,2626,2818,2911,3006,3100,3200,3293,3388,3482,3573,3665,3906,4238,4400,46366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "145,146,183,184,207,297,299,441,447,490,491,511,529,530,536,537,539", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12178,12275,17406,17503,20539,27423,27566,40805,41292,45034,45122,46598,48321,48400,48939,49016,49135", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,78,74,76,66,114", "endOffsets": "12270,12354,17498,17600,20622,27496,27648,40891,41369,45117,45207,46666,48395,48470,49011,49078,49245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "170,271,272,273", "startColumns": "4,4,4,4", "startOffsets": "14973,25322,25424,25537", "endColumns": "106,101,112,102", "endOffsets": "15075,25419,25532,25635"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-ro\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,267,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,268,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,269,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24402,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24458,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24512,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,54,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,52,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,82,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24452,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24506,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24590,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,44,45,48,49,60,61,63,64,65,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,114,115,116,117,118,126,127,128,131,132,133,134,135,136,137,138,139,140,141,142,143,165,166,167,168,171,172,173,174,175,176,177,178,179,180,181,182,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,208,209,210,211,212,214,269,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,295,296,298,300,301,302,304,305,306,307,308,309,310,311,312,313,371,372,373,374,388,389,435,442,443,444,445,446,449,450,451,452,453,454,455,456,465,466,467,468,469,470,471,473,474,475,476,477,478,479,480,481,482,484,485,486,487,488,489,492,493,494,495,496,497,505,506,510,512,513,514,515,516,517,518,519,521,522,523,524,525,526,527,528,532,533,535,538,540,541,542,543,544,547,548,549,550,551,552,555,556,557,558,560,561,562,563,564,565,566,567", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1168,1234,1282,1331,1380,1429,1481,1529,1578,1633,1706,1755,1801,1851,1898,1950,2270,2353,2631,2681,3670,3723,3911,3981,4063,4405,4476,4540,4593,4669,4707,4873,4928,5022,5064,5124,5208,5255,5509,5558,5891,5933,6000,6044,6085,6147,6193,6231,6315,6370,6445,6836,7232,7272,7336,7400,8091,8177,8261,8331,8400,8599,8920,8993,9047,9091,9606,9675,9725,9794,9864,10664,10708,10786,11093,11136,11209,11369,11438,11531,11612,11679,11773,11854,11923,11979,12031,14621,14681,14726,14795,15080,15129,15487,15540,15600,16162,16739,16807,16858,17002,17064,17354,17605,18099,18168,18308,18463,18614,18684,18731,18780,18853,18950,19037,19089,19237,19340,19394,19459,19527,19807,20085,20285,20447,20627,20705,20775,20829,20887,20990,25162,25640,25697,25947,25996,26072,26136,26191,26328,26386,26517,26750,26787,26842,26879,26916,26951,26986,27059,27103,27157,27285,27350,27501,27653,27727,27782,27897,27940,27982,28057,28508,28564,28637,28802,28878,28949,35347,35381,35421,35463,36493,36557,40343,40896,40974,41119,41153,41235,41449,41501,41595,41664,41736,41842,41973,42044,42821,42901,43202,43322,43502,43587,43686,43809,43848,43902,43952,44014,44105,44152,44224,44310,44359,44539,44583,44718,44787,44840,44936,45212,45269,45339,45535,45609,45669,46150,46238,46534,46671,46864,46943,47039,47123,47167,47238,47306,47463,47506,47565,47611,47734,48116,48199,48277,48576,48634,48882,49083,49250,49289,49343,49415,49634,49909,49974,50047,50101,50174,50240,50446,50506,50563,50621,50726,50767,50819,50883,50948,51000,51035,51070", "endColumns": "65,47,48,48,48,51,47,48,54,72,48,45,49,46,51,90,82,53,49,50,52,79,69,81,75,70,63,52,75,37,165,54,93,41,59,83,46,253,48,332,41,66,43,40,61,45,37,83,54,74,390,395,39,63,63,690,85,83,69,68,198,320,72,53,43,61,68,49,68,69,72,43,77,84,42,72,159,68,92,80,66,93,80,68,55,51,62,59,44,68,106,48,357,52,59,561,576,67,50,143,61,289,51,493,68,139,154,150,69,46,48,72,96,86,51,147,102,53,64,67,279,277,199,161,91,77,69,53,57,40,98,64,56,249,48,75,63,54,136,57,130,232,36,54,36,36,34,34,72,43,53,53,64,72,64,73,54,54,42,41,74,450,55,72,164,75,70,35,33,39,41,55,63,68,56,77,144,33,81,56,51,93,68,71,105,130,70,42,79,300,119,179,84,98,43,38,53,49,61,90,46,71,85,48,40,43,134,68,52,95,97,56,69,195,73,59,61,87,50,63,192,78,95,83,43,70,67,77,42,58,45,122,381,82,77,43,57,78,56,51,38,53,71,218,79,64,72,53,72,65,37,59,56,57,44,40,51,63,64,51,34,34,43", "endOffsets": "1229,1277,1326,1375,1424,1476,1524,1573,1628,1701,1750,1796,1846,1893,1945,2036,2348,2402,2676,2727,3718,3798,3976,4058,4134,4471,4535,4588,4664,4702,4868,4923,5017,5059,5119,5203,5250,5504,5553,5886,5928,5995,6039,6080,6142,6188,6226,6310,6365,6440,6831,7227,7267,7331,7395,8086,8172,8256,8326,8395,8594,8915,8988,9042,9086,9148,9670,9720,9789,9859,9932,10703,10781,10866,11131,11204,11364,11433,11526,11607,11674,11768,11849,11918,11974,12026,12089,14676,14721,14790,14897,15124,15482,15535,15595,16157,16734,16802,16853,16997,17059,17349,17401,18094,18163,18303,18458,18609,18679,18726,18775,18848,18945,19032,19084,19232,19335,19389,19454,19522,19802,20080,20280,20442,20534,20700,20770,20824,20882,20923,21084,25222,25692,25942,25991,26067,26131,26186,26323,26381,26512,26745,26782,26837,26874,26911,26946,26981,27054,27098,27152,27206,27345,27418,27561,27722,27777,27832,27935,27977,28052,28503,28559,28632,28797,28873,28944,28980,35376,35416,35458,35514,36552,36621,40395,40969,41114,41148,41230,41287,41496,41590,41659,41731,41837,41968,42039,42082,42896,43197,43317,43497,43582,43681,43725,43843,43897,43947,44009,44100,44147,44219,44305,44354,44395,44578,44713,44782,44835,44931,45029,45264,45334,45530,45604,45664,45726,46233,46284,46593,46859,46938,47034,47118,47162,47233,47301,47379,47501,47560,47606,47729,48111,48194,48272,48316,48629,48708,48934,49130,49284,49338,49410,49629,49709,49969,50042,50096,50169,50235,50273,50501,50558,50616,50661,50762,50814,50878,50943,50995,51030,51065,51109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,11,16,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,249,250,251,252,253,254,255,256,257,258,259,262,263,264,265,266,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,677,21155,21243,21333,21422,21519,21613,21688,21754,21851,21949,22018,22081,22144,22213,22327,22440,22554,22631,22711,22780,22856,22955,23056,23122,23880,23933,23991,24039,24100,24164,24234,24299,24368,24429,24487,24683,24735,24797,24873,24949,25006", "endLines": "10,15,20,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,249,250,251,252,253,254,255,256,257,258,259,262,263,264,265,266,267", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,51,61,75,75,56,69", "endOffsets": "376,672,942,21238,21328,21417,21514,21608,21683,21749,21846,21944,22013,22076,22139,22208,22322,22435,22549,22626,22706,22775,22851,22950,23051,23117,23180,23928,23986,24034,24095,24159,24229,24294,24363,24424,24482,24548,24730,24792,24868,24944,25001,25071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-ro\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "436,437,438,457,458,459,460,461,462,463,464,498,499,500,501,502,503,504,559", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "40400,40449,40499,42087,42159,42291,42385,42510,42564,42618,42736,45731,45780,45877,45916,45954,46018,46104,50666", "endColumns": "48,49,71,71,131,93,124,53,53,117,84,48,96,38,37,63,85,45,59", "endOffsets": "40444,40494,40566,42154,42286,42380,42505,42559,42613,42731,42816,45775,45872,45911,45949,46013,46099,46145,50721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28985,29115,29242,29359,29485,29595,29692,29806,29943,30063,30206,30290,30392,30487,30585,30705,30832,30939,31077,31213,31354,31530,31667,31786,31909,32035,32131,32227,32354,32495,32595,32700,32811,32951,33097,33209,33313,33389,33484,33576,33683,33769,33856,33957,34039,34122,34221,34325,34420,34521,34608,34719,34819,34925,35046,35128,35243", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,106,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "29110,29237,29354,29480,29590,29687,29801,29938,30058,30201,30285,30387,30482,30580,30700,30827,30934,31072,31208,31349,31525,31662,31781,31904,32030,32126,32222,32349,32490,32590,32695,32806,32946,33092,33204,33308,33384,33479,33571,33678,33764,33851,33952,34034,34117,34216,34320,34415,34516,34603,34714,34814,34920,35041,35123,35238,35342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-ro\\values-ro.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "439,440", "startColumns": "4,4", "startOffsets": "40571,40681", "endColumns": "109,123", "endOffsets": "40676,40800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-ro\\values-ro.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "260,261", "startColumns": "4,4", "startOffsets": "24553,24617", "endColumns": "63,65", "endOffsets": "24612,24678"}}]}]}