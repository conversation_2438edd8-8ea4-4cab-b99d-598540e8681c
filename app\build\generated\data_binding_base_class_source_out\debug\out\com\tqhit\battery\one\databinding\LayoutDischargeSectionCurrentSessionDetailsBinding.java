// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutDischargeSectionCurrentSessionDetailsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ConstraintLayout csdClAvgSpeed;

  @NonNull
  public final ConstraintLayout csdClCurrentRate;

  @NonNull
  public final ConstraintLayout csdClScreenOffStats;

  @NonNull
  public final ConstraintLayout csdClScreenOnStats;

  @NonNull
  public final ConstraintLayout csdClTotalConsumed;

  @NonNull
  public final ConstraintLayout csdClTotalTime;

  @NonNull
  public final ImageView csdIvInfoButton;

  @NonNull
  public final TextView csdTvAvgSpeedLabel;

  @NonNull
  public final TextView csdTvAvgSpeedMahValue;

  @NonNull
  public final TextView csdTvAvgSpeedPercentValue;

  @NonNull
  public final TextView csdTvCurrentRateLabel;

  @NonNull
  public final TextView csdTvCurrentRateValue;

  @NonNull
  public final TextView csdTvScreenOffMahValue;

  @NonNull
  public final TextView csdTvScreenOffPercentValue;

  @NonNull
  public final TextView csdTvScreenOffStatsLabel;

  @NonNull
  public final TextView csdTvScreenOnMahValue;

  @NonNull
  public final TextView csdTvScreenOnPercentValue;

  @NonNull
  public final TextView csdTvScreenOnStatsLabel;

  @NonNull
  public final TextView csdTvSessionStartTime;

  @NonNull
  public final TextView csdTvTitle;

  @NonNull
  public final TextView csdTvTotalConsumedLabel;

  @NonNull
  public final TextView csdTvTotalConsumedMahValue;

  @NonNull
  public final TextView csdTvTotalConsumedPercentUnitAndRange;

  @NonNull
  public final TextView csdTvTotalConsumedPercentValue;

  @NonNull
  public final TextView csdTvTotalTimeLabel;

  @NonNull
  public final TextView csdTvTotalTimeValue;

  @NonNull
  public final LinearLayout currentSessionDetailsRoot;

  private LayoutDischargeSectionCurrentSessionDetailsBinding(@NonNull LinearLayout rootView,
      @NonNull ConstraintLayout csdClAvgSpeed, @NonNull ConstraintLayout csdClCurrentRate,
      @NonNull ConstraintLayout csdClScreenOffStats, @NonNull ConstraintLayout csdClScreenOnStats,
      @NonNull ConstraintLayout csdClTotalConsumed, @NonNull ConstraintLayout csdClTotalTime,
      @NonNull ImageView csdIvInfoButton, @NonNull TextView csdTvAvgSpeedLabel,
      @NonNull TextView csdTvAvgSpeedMahValue, @NonNull TextView csdTvAvgSpeedPercentValue,
      @NonNull TextView csdTvCurrentRateLabel, @NonNull TextView csdTvCurrentRateValue,
      @NonNull TextView csdTvScreenOffMahValue, @NonNull TextView csdTvScreenOffPercentValue,
      @NonNull TextView csdTvScreenOffStatsLabel, @NonNull TextView csdTvScreenOnMahValue,
      @NonNull TextView csdTvScreenOnPercentValue, @NonNull TextView csdTvScreenOnStatsLabel,
      @NonNull TextView csdTvSessionStartTime, @NonNull TextView csdTvTitle,
      @NonNull TextView csdTvTotalConsumedLabel, @NonNull TextView csdTvTotalConsumedMahValue,
      @NonNull TextView csdTvTotalConsumedPercentUnitAndRange,
      @NonNull TextView csdTvTotalConsumedPercentValue, @NonNull TextView csdTvTotalTimeLabel,
      @NonNull TextView csdTvTotalTimeValue, @NonNull LinearLayout currentSessionDetailsRoot) {
    this.rootView = rootView;
    this.csdClAvgSpeed = csdClAvgSpeed;
    this.csdClCurrentRate = csdClCurrentRate;
    this.csdClScreenOffStats = csdClScreenOffStats;
    this.csdClScreenOnStats = csdClScreenOnStats;
    this.csdClTotalConsumed = csdClTotalConsumed;
    this.csdClTotalTime = csdClTotalTime;
    this.csdIvInfoButton = csdIvInfoButton;
    this.csdTvAvgSpeedLabel = csdTvAvgSpeedLabel;
    this.csdTvAvgSpeedMahValue = csdTvAvgSpeedMahValue;
    this.csdTvAvgSpeedPercentValue = csdTvAvgSpeedPercentValue;
    this.csdTvCurrentRateLabel = csdTvCurrentRateLabel;
    this.csdTvCurrentRateValue = csdTvCurrentRateValue;
    this.csdTvScreenOffMahValue = csdTvScreenOffMahValue;
    this.csdTvScreenOffPercentValue = csdTvScreenOffPercentValue;
    this.csdTvScreenOffStatsLabel = csdTvScreenOffStatsLabel;
    this.csdTvScreenOnMahValue = csdTvScreenOnMahValue;
    this.csdTvScreenOnPercentValue = csdTvScreenOnPercentValue;
    this.csdTvScreenOnStatsLabel = csdTvScreenOnStatsLabel;
    this.csdTvSessionStartTime = csdTvSessionStartTime;
    this.csdTvTitle = csdTvTitle;
    this.csdTvTotalConsumedLabel = csdTvTotalConsumedLabel;
    this.csdTvTotalConsumedMahValue = csdTvTotalConsumedMahValue;
    this.csdTvTotalConsumedPercentUnitAndRange = csdTvTotalConsumedPercentUnitAndRange;
    this.csdTvTotalConsumedPercentValue = csdTvTotalConsumedPercentValue;
    this.csdTvTotalTimeLabel = csdTvTotalTimeLabel;
    this.csdTvTotalTimeValue = csdTvTotalTimeValue;
    this.currentSessionDetailsRoot = currentSessionDetailsRoot;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutDischargeSectionCurrentSessionDetailsBinding inflate(
      @NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutDischargeSectionCurrentSessionDetailsBinding inflate(
      @NonNull LayoutInflater inflater, @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_discharge_section_current_session_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutDischargeSectionCurrentSessionDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.csd_cl_avg_speed;
      ConstraintLayout csdClAvgSpeed = ViewBindings.findChildViewById(rootView, id);
      if (csdClAvgSpeed == null) {
        break missingId;
      }

      id = R.id.csd_cl_current_rate;
      ConstraintLayout csdClCurrentRate = ViewBindings.findChildViewById(rootView, id);
      if (csdClCurrentRate == null) {
        break missingId;
      }

      id = R.id.csd_cl_screen_off_stats;
      ConstraintLayout csdClScreenOffStats = ViewBindings.findChildViewById(rootView, id);
      if (csdClScreenOffStats == null) {
        break missingId;
      }

      id = R.id.csd_cl_screen_on_stats;
      ConstraintLayout csdClScreenOnStats = ViewBindings.findChildViewById(rootView, id);
      if (csdClScreenOnStats == null) {
        break missingId;
      }

      id = R.id.csd_cl_total_consumed;
      ConstraintLayout csdClTotalConsumed = ViewBindings.findChildViewById(rootView, id);
      if (csdClTotalConsumed == null) {
        break missingId;
      }

      id = R.id.csd_cl_total_time;
      ConstraintLayout csdClTotalTime = ViewBindings.findChildViewById(rootView, id);
      if (csdClTotalTime == null) {
        break missingId;
      }

      id = R.id.csd_iv_info_button;
      ImageView csdIvInfoButton = ViewBindings.findChildViewById(rootView, id);
      if (csdIvInfoButton == null) {
        break missingId;
      }

      id = R.id.csd_tv_avg_speed_label;
      TextView csdTvAvgSpeedLabel = ViewBindings.findChildViewById(rootView, id);
      if (csdTvAvgSpeedLabel == null) {
        break missingId;
      }

      id = R.id.csd_tv_avg_speed_mah_value;
      TextView csdTvAvgSpeedMahValue = ViewBindings.findChildViewById(rootView, id);
      if (csdTvAvgSpeedMahValue == null) {
        break missingId;
      }

      id = R.id.csd_tv_avg_speed_percent_value;
      TextView csdTvAvgSpeedPercentValue = ViewBindings.findChildViewById(rootView, id);
      if (csdTvAvgSpeedPercentValue == null) {
        break missingId;
      }

      id = R.id.csd_tv_current_rate_label;
      TextView csdTvCurrentRateLabel = ViewBindings.findChildViewById(rootView, id);
      if (csdTvCurrentRateLabel == null) {
        break missingId;
      }

      id = R.id.csd_tv_current_rate_value;
      TextView csdTvCurrentRateValue = ViewBindings.findChildViewById(rootView, id);
      if (csdTvCurrentRateValue == null) {
        break missingId;
      }

      id = R.id.csd_tv_screen_off_mah_value;
      TextView csdTvScreenOffMahValue = ViewBindings.findChildViewById(rootView, id);
      if (csdTvScreenOffMahValue == null) {
        break missingId;
      }

      id = R.id.csd_tv_screen_off_percent_value;
      TextView csdTvScreenOffPercentValue = ViewBindings.findChildViewById(rootView, id);
      if (csdTvScreenOffPercentValue == null) {
        break missingId;
      }

      id = R.id.csd_tv_screen_off_stats_label;
      TextView csdTvScreenOffStatsLabel = ViewBindings.findChildViewById(rootView, id);
      if (csdTvScreenOffStatsLabel == null) {
        break missingId;
      }

      id = R.id.csd_tv_screen_on_mah_value;
      TextView csdTvScreenOnMahValue = ViewBindings.findChildViewById(rootView, id);
      if (csdTvScreenOnMahValue == null) {
        break missingId;
      }

      id = R.id.csd_tv_screen_on_percent_value;
      TextView csdTvScreenOnPercentValue = ViewBindings.findChildViewById(rootView, id);
      if (csdTvScreenOnPercentValue == null) {
        break missingId;
      }

      id = R.id.csd_tv_screen_on_stats_label;
      TextView csdTvScreenOnStatsLabel = ViewBindings.findChildViewById(rootView, id);
      if (csdTvScreenOnStatsLabel == null) {
        break missingId;
      }

      id = R.id.csd_tv_session_start_time;
      TextView csdTvSessionStartTime = ViewBindings.findChildViewById(rootView, id);
      if (csdTvSessionStartTime == null) {
        break missingId;
      }

      id = R.id.csd_tv_title;
      TextView csdTvTitle = ViewBindings.findChildViewById(rootView, id);
      if (csdTvTitle == null) {
        break missingId;
      }

      id = R.id.csd_tv_total_consumed_label;
      TextView csdTvTotalConsumedLabel = ViewBindings.findChildViewById(rootView, id);
      if (csdTvTotalConsumedLabel == null) {
        break missingId;
      }

      id = R.id.csd_tv_total_consumed_mah_value;
      TextView csdTvTotalConsumedMahValue = ViewBindings.findChildViewById(rootView, id);
      if (csdTvTotalConsumedMahValue == null) {
        break missingId;
      }

      id = R.id.csd_tv_total_consumed_percent_unit_and_range;
      TextView csdTvTotalConsumedPercentUnitAndRange = ViewBindings.findChildViewById(rootView, id);
      if (csdTvTotalConsumedPercentUnitAndRange == null) {
        break missingId;
      }

      id = R.id.csd_tv_total_consumed_percent_value;
      TextView csdTvTotalConsumedPercentValue = ViewBindings.findChildViewById(rootView, id);
      if (csdTvTotalConsumedPercentValue == null) {
        break missingId;
      }

      id = R.id.csd_tv_total_time_label;
      TextView csdTvTotalTimeLabel = ViewBindings.findChildViewById(rootView, id);
      if (csdTvTotalTimeLabel == null) {
        break missingId;
      }

      id = R.id.csd_tv_total_time_value;
      TextView csdTvTotalTimeValue = ViewBindings.findChildViewById(rootView, id);
      if (csdTvTotalTimeValue == null) {
        break missingId;
      }

      LinearLayout currentSessionDetailsRoot = (LinearLayout) rootView;

      return new LayoutDischargeSectionCurrentSessionDetailsBinding((LinearLayout) rootView,
          csdClAvgSpeed, csdClCurrentRate, csdClScreenOffStats, csdClScreenOnStats,
          csdClTotalConsumed, csdClTotalTime, csdIvInfoButton, csdTvAvgSpeedLabel,
          csdTvAvgSpeedMahValue, csdTvAvgSpeedPercentValue, csdTvCurrentRateLabel,
          csdTvCurrentRateValue, csdTvScreenOffMahValue, csdTvScreenOffPercentValue,
          csdTvScreenOffStatsLabel, csdTvScreenOnMahValue, csdTvScreenOnPercentValue,
          csdTvScreenOnStatsLabel, csdTvSessionStartTime, csdTvTitle, csdTvTotalConsumedLabel,
          csdTvTotalConsumedMahValue, csdTvTotalConsumedPercentUnitAndRange,
          csdTvTotalConsumedPercentValue, csdTvTotalTimeLabel, csdTvTotalTimeValue,
          currentSessionDetailsRoot);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
