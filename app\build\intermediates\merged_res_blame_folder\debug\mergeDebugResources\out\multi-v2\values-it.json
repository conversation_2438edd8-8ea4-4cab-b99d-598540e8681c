{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "148,149,150,151,152,153,154,155,157,158,159,160,161,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12386,12491,12642,12767,12875,13033,13161,13281,13521,13678,13785,13939,14066,14222,14403,14470,14531", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "12486,12637,12762,12870,13028,13156,13276,13380,13673,13780,13934,14061,14217,14398,14465,14526,14603"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,122", "endOffsets": "159,282"}, "to": {"startLines": "440,441", "startColumns": "4,4", "startOffsets": "40612,40721", "endColumns": "108,122", "endOffsets": "40716,40839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,306,428,552,657,753,866,1009,1128,1286,1370,1482,1576,1676,1795,1917,2034,2176,2316,2459,2635,2770,2890,3013,3143,3238,3335,3462,3600,3700,3810,3916,4059,4207,4317,4418,4507,4603,4696,4811,4897,4983,5086,5166,5249,5348,5454,5554,5655,5743,5853,5953,6058,6176,6256,6370", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,114,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "177,301,423,547,652,748,861,1004,1123,1281,1365,1477,1571,1671,1790,1912,2029,2171,2311,2454,2630,2765,2885,3008,3138,3233,3330,3457,3595,3695,3805,3911,4054,4202,4312,4413,4502,4598,4691,4806,4892,4978,5081,5161,5244,5343,5449,5549,5650,5738,5848,5948,6053,6171,6251,6365,6472"}, "to": {"startLines": "315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28948,29075,29199,29321,29445,29550,29646,29759,29902,30021,30179,30263,30375,30469,30569,30688,30810,30927,31069,31209,31352,31528,31663,31783,31906,32036,32131,32228,32355,32493,32593,32703,32809,32952,33100,33210,33311,33400,33496,33589,33704,33790,33876,33979,34059,34142,34241,34347,34447,34548,34636,34746,34846,34951,35069,35149,35263", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,114,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "29070,29194,29316,29440,29545,29641,29754,29897,30016,30174,30258,30370,30464,30564,30683,30805,30922,31064,31204,31347,31523,31658,31778,31901,32031,32126,32223,32350,32488,32588,32698,32804,32947,33095,33205,33306,33395,33491,33584,33699,33785,33871,33974,34054,34137,34236,34342,34442,34543,34631,34741,34841,34946,35064,35144,35258,35365"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "171,272,273,274", "startColumns": "4,4,4,4", "startOffsets": "14936,25260,25358,25468", "endColumns": "99,97,109,102", "endOffsets": "15031,25353,25463,25566"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,663,749,837,916,1008,1100,1178,1243,1343,1441,1506,1574,1639,1710,1838,1972,2098,2168,2261,2336,2412,2508,2606,2675,2743,2796,2854,2902,2963,3037,3108,3171,3252,3310,3371,3437,3489,3551,3627,3703,3761", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,127,133,125,69,92,74,75,95,97,68,67,52,57,47,60,73,70,62,80,57,60,65,51,61,75,75,57,69", "endOffsets": "281,470,658,744,832,911,1003,1095,1173,1238,1338,1436,1501,1569,1634,1705,1833,1967,2093,2163,2256,2331,2407,2503,2601,2670,2738,2791,2849,2897,2958,3032,3103,3166,3247,3305,3366,3432,3484,3546,3622,3698,3756,3826"}, "to": {"startLines": "2,11,15,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,250,251,252,253,254,255,256,257,258,259,260,263,264,265,266,267,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,570,21056,21142,21230,21309,21401,21493,21571,21636,21736,21834,21899,21967,22032,22103,22231,22365,22491,22561,22654,22729,22805,22901,22999,23068,23798,23851,23909,23957,24018,24092,24163,24226,24307,24365,24426,24623,24675,24737,24813,24889,24947", "endLines": "10,14,18,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,250,251,252,253,254,255,256,257,258,259,260,263,264,265,266,267,268", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,127,133,125,69,92,74,75,95,97,68,67,52,57,47,60,73,70,62,80,57,60,65,51,61,75,75,57,69", "endOffsets": "376,565,753,21137,21225,21304,21396,21488,21566,21631,21731,21829,21894,21962,22027,22098,22226,22360,22486,22556,22649,22724,22800,22896,22994,23063,23131,23846,23904,23952,24013,24087,24158,24221,24302,24360,24421,24487,24670,24732,24808,24884,24942,25012"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,116,175,221,296,336,386,445,499,562,627,698,739,841,942,1021,1104,1145,1215,1265,1353,1428,1482,1533,1623,1678,1735,1790,1866,1912,1983,2079,2135,2205,2299,2359,2424,2497,2552,2601,2652,2711,2773,2842,2928,3046", "endColumns": "60,58,45,74,39,49,58,53,62,64,70,40,101,100,78,82,40,69,49,87,74,53,50,89,54,56,54,75,45,70,95,55,69,93,59,64,72,54,48,50,58,61,68,85,117,57", "endOffsets": "111,170,216,291,331,381,440,494,557,622,693,734,836,937,1016,1099,1140,1210,1260,1348,1423,1477,1528,1618,1673,1730,1785,1861,1907,1978,2074,2130,2200,2294,2354,2419,2492,2547,2596,2647,2706,2768,2837,2923,3041,3099"}, "to": {"startLines": "548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "49841,49902,49961,50007,50082,50122,50172,50231,50285,50348,50413,50484,50525,50627,50728,50807,50890,50931,51001,51051,51139,51214,51268,51319,51409,51464,51521,51576,51652,51698,51769,51865,51921,51991,52085,52145,52210,52283,52338,52387,52438,52497,52559,52628,52714,52832", "endColumns": "60,58,45,74,39,49,58,53,62,64,70,40,101,100,78,82,40,69,49,87,74,53,50,89,54,56,54,75,45,70,95,55,69,93,59,64,72,54,48,50,58,61,68,85,117,57", "endOffsets": "49897,49956,50002,50077,50117,50167,50226,50280,50343,50408,50479,50520,50622,50723,50802,50885,50926,50996,51046,51134,51209,51263,51314,51404,51459,51516,51571,51647,51693,51764,51860,51916,51986,52080,52140,52205,52278,52333,52382,52433,52492,52554,52623,52709,52827,52885"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1090,1156,1251,1336,1398,1486,1548,1617,1680,1753,1816,1870,1991,2048,2110,2164,2241,2378,2463,2543,2642,2728,2810,2945,3026,3107,3253,3344,3434,3489,3540,3606,3679,3759,3830,3910,3985,4062,4131,4208,4313,4401,4490,4583,4676,4750,4830,4924,4975,5059,5125,5209,5297,5359,5423,5486,5554,5669,5783,5889,5998,6057,6112,6192,6277,6356", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "263,347,428,505,604,699,798,938,1021,1085,1151,1246,1331,1393,1481,1543,1612,1675,1748,1811,1865,1986,2043,2105,2159,2236,2373,2458,2538,2637,2723,2805,2940,3021,3102,3248,3339,3429,3484,3535,3601,3674,3754,3825,3905,3980,4057,4126,4203,4308,4396,4485,4578,4671,4745,4825,4919,4970,5054,5120,5204,5292,5354,5418,5481,5549,5664,5778,5884,5993,6052,6107,6187,6272,6351,6433"}, "to": {"startLines": "19,110,111,112,113,114,130,131,145,214,216,271,295,304,376,377,378,379,380,381,382,383,384,385,386,387,388,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,473,509,510,521", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "758,9171,9255,9336,9413,9512,10883,10982,12116,20838,20990,25165,27148,27805,35542,35630,35692,35761,35824,35897,35960,36014,36135,36192,36254,36308,36385,36653,36738,36818,36917,37003,37085,37220,37301,37382,37528,37619,37709,37764,37815,37881,37954,38034,38105,38185,38260,38337,38406,38483,38588,38676,38765,38858,38951,39025,39105,39199,39250,39334,39400,39484,39572,39634,39698,39761,39829,39944,40058,40164,40273,40332,43824,46358,46443,47395", "endLines": "22,110,111,112,113,114,130,131,145,214,216,271,295,304,376,377,378,379,380,381,382,383,384,385,386,387,388,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,473,509,510,521", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "921,9250,9331,9408,9507,9602,10977,11117,12194,20897,21051,25255,27228,27862,35625,35687,35756,35819,35892,35955,36009,36130,36187,36249,36303,36380,36517,36733,36813,36912,36998,37080,37215,37296,37377,37523,37614,37704,37759,37810,37876,37949,38029,38100,38180,38255,38332,38401,38478,38583,38671,38760,38853,38946,39020,39100,39194,39245,39329,39395,39479,39567,39629,39693,39756,39824,39939,40053,40159,40268,40327,40382,43899,46438,46517,47472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,506,611,883,975,1069,1162,1256,1357,1451,1548,1643,1735,2014,2429,2536,2799", "endColumns": "104,102,104,118,91,93,92,93,100,93,96,94,91,91,106,106,162,81", "endOffsets": "205,308,606,725,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,2116,2531,2694,2876"}, "to": {"startLines": "39,40,43,44,47,48,49,50,51,52,53,54,55,56,59,63,64,508", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1792,1897,2134,2239,2459,2551,2645,2738,2832,2933,3027,3124,3219,3311,3530,3855,3962,46276", "endColumns": "104,102,104,118,91,93,92,93,100,93,96,94,91,91,106,106,162,81", "endOffsets": "1892,1995,2234,2353,2546,2640,2733,2827,2928,3022,3119,3214,3306,3398,3632,3957,4120,46353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,288,588,651,782,892,1018,1068,1126,1252,1344,1386,1485,1520,1555,1608,1690,1735", "endColumns": "41,46,63,62,130,109,125,49,57,125,91,41,98,34,34,52,81,44,55", "endOffsets": "240,287,351,650,781,891,1017,1067,1125,1251,1343,1385,1484,1519,1554,1607,1689,1734,1790"}, "to": {"startLines": "437,438,439,458,459,460,461,462,463,464,465,499,500,501,502,503,504,505,606", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "40447,40493,40544,42140,42207,42342,42456,42586,42640,42702,42832,45730,45776,45879,45918,45957,46014,46100,53635", "endColumns": "45,50,67,66,134,113,129,53,61,129,95,45,102,38,38,56,85,48,59", "endOffsets": "40488,40539,40607,42202,42337,42451,42581,42635,42697,42827,42923,45771,45874,45913,45952,46009,46095,46144,53690"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9b1d1c56b528abe32d69bef2c9ce868e\\transformed\\jetified-bigo-ads-5.3.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,131,183,230", "endColumns": "75,51,46,69", "endOffsets": "126,178,225,295"}, "to": {"startLines": "103,104,105,106", "startColumns": "4,4,4,4", "startOffsets": "8771,8847,8899,8946", "endColumns": "75,51,46,69", "endOffsets": "8842,8894,8941,9011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "156", "startColumns": "4", "startOffsets": "13385", "endColumns": "135", "endOffsets": "13516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-it\\values-it.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3437,3502", "endColumns": "64,65", "endOffsets": "3497,3563"}, "to": {"startLines": "261,262", "startColumns": "4,4", "startOffsets": "24492,24557", "endColumns": "64,65", "endOffsets": "24552,24618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,261,341,476,645,725", "endColumns": "68,86,79,134,168,79,75", "endOffsets": "169,256,336,471,640,720,796"}, "to": {"startLines": "170,269,449,484,535,600,601", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14867,25017,41434,44477,48658,53252,53332", "endColumns": "68,86,79,134,168,79,75", "endOffsets": "14931,25099,41509,44607,48822,53327,53403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "120,121,122,123,124,125,126,532", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9938,10036,10138,10237,10339,10448,10555,48423", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "10031,10133,10232,10334,10443,10550,10680,48519"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-it\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,265,30,31,32,33,34,35,36,241,251,254,256,37,2,264,244,262,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,239,57,58,243,59,60,61,62,63,64,65,66,67,68,249,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,259,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,260,146,147,268,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,242,168,169,170,171,172,173,174,175,176,177,178,247,246,245,253,257,179,180,181,182,183,184,255,185,186,187,240,188,261,269,189,190,191,192,193,194,195,196,248,197,198,258,199,200,201,202,203,252,204,205,206,207,208,209,270,210,211,212,213,214,215,216,263,217,218,219,220,221,222,223,224,225,226,250,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "130,197,246,296,346,396,450,499,548,604,674,724,772,823,871,21454,921,1001,1057,1109,1160,1212,1289,1358,1433,1510,1581,1659,24069,1715,1755,1934,1992,2083,2126,2188,21719,22549,23002,23126,2278,57,24022,21922,23898,2324,2371,2410,2498,2556,2631,3024,3422,12094,12160,11414,11325,12224,3464,3538,3611,3823,4121,4203,4260,4301,4361,4435,4487,21604,4555,4627,21845,4671,4751,4794,4866,5030,5098,5197,5278,5344,5435,22425,5511,5570,5627,5689,5744,5790,5859,5952,6003,6337,6394,6456,7021,7599,7668,7716,7870,7935,8224,8275,8763,8829,8958,9116,9272,9339,9388,9435,9507,9610,9698,9748,9900,9994,10053,10120,10192,10477,10732,10941,11103,11197,12311,12377,23704,12434,12476,12565,12627,12685,12901,12955,13036,13099,13157,13305,13365,13510,13750,13791,13851,13889,13927,13963,13999,14071,14114,14171,14224,14286,23767,14362,14433,24187,14492,14537,14579,14655,15079,15137,15210,15385,15473,15546,15583,15618,15659,15702,15759,15821,15892,15953,16032,16188,21769,16223,16284,16335,16432,16503,16580,16683,16800,16878,16918,17004,22237,22057,21965,22892,23458,17278,17318,17373,17424,17490,17569,23052,17621,17696,17744,21674,17787,23832,24247,17890,17985,18081,18134,18207,18385,18462,18521,22351,18580,18635,23505,18697,18782,18874,18957,19008,22826,19086,19167,19210,19266,19314,19434,24301,19772,19848,19895,19954,20031,20092,20145,23962,20185,20255,20447,20527,20592,20664,20717,20791,20856,20895,22494,20957,21024,21071,21113,21167,21219,21288,21337,21373,21409", "endColumns": "65,47,48,48,48,52,47,47,54,68,48,46,49,46,48,89,78,54,50,49,50,75,67,73,75,69,76,54,69,38,177,56,89,41,60,88,48,275,48,330,44,71,45,41,62,45,37,86,56,73,391,396,40,64,62,678,87,83,72,71,210,296,80,55,39,58,72,50,66,68,70,42,75,78,41,70,162,66,97,79,64,89,74,67,57,55,60,53,44,67,91,49,332,55,60,563,576,67,46,152,63,287,49,486,64,127,156,154,65,47,45,70,101,86,48,150,92,57,65,70,283,253,207,160,92,81,64,55,61,40,87,60,56,214,52,79,61,56,146,58,143,238,39,58,36,36,34,34,70,41,55,51,60,74,63,69,57,58,43,40,74,422,56,71,173,86,71,35,33,39,41,55,60,69,59,77,154,33,74,59,49,95,69,75,101,115,76,38,84,272,112,178,90,108,45,38,53,49,64,77,50,72,73,46,41,43,101,64,52,93,94,51,71,176,75,57,57,72,53,60,197,83,90,81,49,76,64,79,41,54,46,118,336,82,74,45,57,75,59,51,38,58,68,190,78,63,70,51,72,63,37,60,53,65,45,40,52,50,67,47,34,34,43", "endOffsets": "191,240,290,340,390,444,493,542,598,668,718,766,817,865,915,21539,995,1051,1103,1154,1206,1283,1352,1427,1504,1575,1653,1709,24134,1749,1928,1986,2077,2120,2182,2272,21763,22820,23046,23452,2318,124,24063,21959,23956,2365,2404,2492,2550,2625,3018,3416,3458,12154,12218,12088,11408,12303,3532,3605,3817,4115,4197,4254,4295,4355,4429,4481,4549,21668,4621,4665,21916,4745,4788,4860,5024,5092,5191,5272,5338,5429,5505,22488,5564,5621,5683,5738,5784,5853,5946,5997,6331,6388,6450,7015,7593,7662,7710,7864,7929,8218,8269,8757,8823,8952,9110,9266,9333,9382,9429,9501,9604,9692,9742,9894,9988,10047,10114,10186,10471,10726,10935,11097,11191,11274,12371,12428,23761,12470,12559,12621,12679,12895,12949,13030,13093,13151,13299,13359,13504,13744,13785,13845,13883,13921,13957,13993,14065,14108,14165,14218,14280,14356,23826,14427,14486,24241,14531,14573,14649,15073,15131,15204,15379,15467,15540,15577,15612,15653,15696,15753,15815,15886,15947,16026,16182,16217,21839,16278,16329,16426,16497,16574,16677,16794,16872,16912,16998,17272,22345,22231,22051,22996,23499,17312,17367,17418,17484,17563,17615,23120,17690,17738,17781,21713,17884,23892,24295,17979,18075,18128,18201,18379,18456,18515,18574,22419,18629,18691,23698,18776,18868,18951,19002,19080,22886,19161,19204,19260,19308,19428,19766,24379,19842,19889,19948,20025,20086,20139,20179,24016,20249,20441,20521,20586,20658,20711,20785,20850,20889,20951,22543,21018,21065,21107,21161,21213,21282,21331,21367,21403,21448"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,45,46,57,58,60,61,62,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,107,108,109,115,116,117,118,119,127,128,129,132,133,134,135,136,137,138,139,140,141,142,143,144,166,167,168,169,172,173,174,175,176,177,178,179,180,181,182,183,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,209,210,211,212,213,215,270,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,296,297,299,301,302,303,305,306,307,308,309,310,311,312,313,314,372,373,374,375,389,390,436,443,444,445,446,447,450,451,452,453,454,455,456,457,466,467,468,469,470,471,472,474,475,476,477,478,479,480,481,482,483,485,486,487,488,489,490,493,494,495,496,497,498,506,507,511,513,514,515,516,517,518,519,520,522,523,524,525,526,527,528,529,533,534,536,539,541,542,543,544,545,594,595,596,597,598,599,602,603,604,605,607,608,609,610,611,612,613,614", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "926,992,1040,1089,1138,1187,1240,1288,1336,1391,1460,1509,1556,1606,1653,1702,2000,2079,2358,2409,3403,3454,3637,3705,3779,4125,4195,4272,4327,4397,4436,4614,4671,4761,4803,4864,4953,5002,5278,5327,5658,5703,5775,5821,5863,5926,5972,6010,6097,6154,6228,6620,7017,7058,7123,7186,7865,7953,8037,8110,8182,8393,8690,9016,9072,9112,9607,9680,9731,9798,9867,10685,10728,10804,11122,11164,11235,11398,11465,11563,11643,11708,11798,11873,11941,11999,12055,14608,14662,14707,14775,15036,15086,15419,15475,15536,16100,16677,16745,16792,16945,17009,17297,17545,18032,18097,18225,18382,18537,18603,18651,18697,18768,18870,18957,19006,19157,19250,19308,19374,19445,19729,19983,20191,20352,20532,20614,20679,20735,20797,20902,25104,25571,25628,25843,25896,25976,26038,26095,26242,26301,26445,26684,26724,26783,26820,26857,26892,26927,26998,27040,27096,27233,27294,27448,27618,27688,27746,27867,27911,27952,28027,28450,28507,28579,28753,28840,28912,35370,35404,35444,35486,36522,36583,40387,40937,41015,41170,41204,41279,41514,41564,41660,41730,41806,41908,42024,42101,42928,43013,43286,43399,43578,43669,43778,43904,43943,43997,44047,44112,44190,44241,44314,44388,44435,44612,44656,44758,44823,44876,44970,45237,45289,45361,45538,45614,45672,46149,46222,46522,46668,46866,46950,47041,47123,47173,47250,47315,47477,47519,47574,47621,47740,48077,48160,48235,48524,48582,48827,49034,49207,49246,49305,49374,49565,52890,52954,53025,53077,53150,53214,53408,53469,53523,53589,53695,53736,53789,53840,53908,53956,53991,54026", "endColumns": "65,47,48,48,48,52,47,47,54,68,48,46,49,46,48,89,78,54,50,49,50,75,67,73,75,69,76,54,69,38,177,56,89,41,60,88,48,275,48,330,44,71,45,41,62,45,37,86,56,73,391,396,40,64,62,678,87,83,72,71,210,296,80,55,39,58,72,50,66,68,70,42,75,78,41,70,162,66,97,79,64,89,74,67,57,55,60,53,44,67,91,49,332,55,60,563,576,67,46,152,63,287,49,486,64,127,156,154,65,47,45,70,101,86,48,150,92,57,65,70,283,253,207,160,92,81,64,55,61,40,87,60,56,214,52,79,61,56,146,58,143,238,39,58,36,36,34,34,70,41,55,51,60,74,63,69,57,58,43,40,74,422,56,71,173,86,71,35,33,39,41,55,60,69,59,77,154,33,74,59,49,95,69,75,101,115,76,38,84,272,112,178,90,108,45,38,53,49,64,77,50,72,73,46,41,43,101,64,52,93,94,51,71,176,75,57,57,72,53,60,197,83,90,81,49,76,64,79,41,54,46,118,336,82,74,45,57,75,59,51,38,58,68,190,78,63,70,51,72,63,37,60,53,65,45,40,52,50,67,47,34,34,43", "endOffsets": "987,1035,1084,1133,1182,1235,1283,1331,1386,1455,1504,1551,1601,1648,1697,1787,2074,2129,2404,2454,3449,3525,3700,3774,3850,4190,4267,4322,4392,4431,4609,4666,4756,4798,4859,4948,4997,5273,5322,5653,5698,5770,5816,5858,5921,5967,6005,6092,6149,6223,6615,7012,7053,7118,7181,7860,7948,8032,8105,8177,8388,8685,8766,9067,9107,9166,9675,9726,9793,9862,9933,10723,10799,10878,11159,11230,11393,11460,11558,11638,11703,11793,11868,11936,11994,12050,12111,14657,14702,14770,14862,15081,15414,15470,15531,16095,16672,16740,16787,16940,17004,17292,17342,18027,18092,18220,18377,18532,18598,18646,18692,18763,18865,18952,19001,19152,19245,19303,19369,19440,19724,19978,20186,20347,20440,20609,20674,20730,20792,20833,20985,25160,25623,25838,25891,25971,26033,26090,26237,26296,26440,26679,26719,26778,26815,26852,26887,26922,26993,27035,27091,27143,27289,27364,27507,27683,27741,27800,27906,27947,28022,28445,28502,28574,28748,28835,28907,28943,35399,35439,35481,35537,36578,36648,40442,41010,41165,41199,41274,41334,41559,41655,41725,41801,41903,42019,42096,42135,43008,43281,43394,43573,43664,43773,43819,43938,43992,44042,44107,44185,44236,44309,44383,44430,44472,44651,44753,44818,44871,44965,45060,45284,45356,45533,45609,45667,45725,46217,46271,46578,46861,46945,47036,47118,47168,47245,47310,47390,47514,47569,47616,47735,48072,48155,48230,48276,48577,48653,48882,49081,49241,49300,49369,49560,49639,52949,53020,53072,53145,53209,53247,53464,53518,53584,53630,53731,53784,53835,53903,53951,53986,54021,54065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,292,390,490,577,656,762,855,950,1034,1122,1283,1355,1425,1503,1572", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,71,69,77,68,120", "endOffsets": "200,287,385,485,572,651,757,850,945,1029,1117,1202,1350,1420,1498,1567,1688"}, "to": {"startLines": "146,147,184,185,208,298,300,442,448,491,492,512,530,531,537,538,540", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12199,12299,17347,17445,20445,27369,27512,40844,41339,45065,45149,46583,48281,48353,48887,48965,49086", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,71,69,77,68,120", "endOffsets": "12294,12381,17440,17540,20527,27443,27613,40932,41429,45144,45232,46663,48348,48418,48960,49029,49202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,405,471,558,643", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "121,182,254,324,400,466,553,638,712"}, "to": {"startLines": "241,242,243,244,245,246,247,248,249", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23136,23207,23268,23340,23410,23486,23552,23639,23724", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "23202,23263,23335,23405,23481,23547,23634,23719,23793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,98", "endOffsets": "148,247"}, "to": {"startLines": "546,547", "startColumns": "4,4", "startOffsets": "49644,49742", "endColumns": "97,98", "endOffsets": "49737,49836"}}]}]}