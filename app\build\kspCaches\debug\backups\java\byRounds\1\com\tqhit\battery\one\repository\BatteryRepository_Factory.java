package com.tqhit.battery.one.repository;

import android.content.Context;
import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import com.tqhit.battery.one.manager.charge.ChargingSessionManager;
import com.tqhit.battery.one.manager.discharge.DischargeSessionManager;
import com.tqhit.battery.one.manager.graph.BatteryHistoryManager;
import com.tqhit.battery.one.manager.graph.TemperatureHistoryManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class BatteryRepository_Factory implements Factory<BatteryRepository> {
  private final Provider<Context> contextProvider;

  private final Provider<PreferencesHelper> preferencesHelperProvider;

  private final Provider<ChargingSessionManager> chargingSessionManagerProvider;

  private final Provider<DischargeSessionManager> dischargeSessionManagerProvider;

  private final Provider<BatteryHistoryManager> batteryHistoryManagerProvider;

  private final Provider<TemperatureHistoryManager> temperatureHistoryManagerProvider;

  public BatteryRepository_Factory(Provider<Context> contextProvider,
      Provider<PreferencesHelper> preferencesHelperProvider,
      Provider<ChargingSessionManager> chargingSessionManagerProvider,
      Provider<DischargeSessionManager> dischargeSessionManagerProvider,
      Provider<BatteryHistoryManager> batteryHistoryManagerProvider,
      Provider<TemperatureHistoryManager> temperatureHistoryManagerProvider) {
    this.contextProvider = contextProvider;
    this.preferencesHelperProvider = preferencesHelperProvider;
    this.chargingSessionManagerProvider = chargingSessionManagerProvider;
    this.dischargeSessionManagerProvider = dischargeSessionManagerProvider;
    this.batteryHistoryManagerProvider = batteryHistoryManagerProvider;
    this.temperatureHistoryManagerProvider = temperatureHistoryManagerProvider;
  }

  @Override
  public BatteryRepository get() {
    return newInstance(contextProvider.get(), preferencesHelperProvider.get(), chargingSessionManagerProvider.get(), dischargeSessionManagerProvider.get(), batteryHistoryManagerProvider.get(), temperatureHistoryManagerProvider.get());
  }

  public static BatteryRepository_Factory create(Provider<Context> contextProvider,
      Provider<PreferencesHelper> preferencesHelperProvider,
      Provider<ChargingSessionManager> chargingSessionManagerProvider,
      Provider<DischargeSessionManager> dischargeSessionManagerProvider,
      Provider<BatteryHistoryManager> batteryHistoryManagerProvider,
      Provider<TemperatureHistoryManager> temperatureHistoryManagerProvider) {
    return new BatteryRepository_Factory(contextProvider, preferencesHelperProvider, chargingSessionManagerProvider, dischargeSessionManagerProvider, batteryHistoryManagerProvider, temperatureHistoryManagerProvider);
  }

  public static BatteryRepository newInstance(Context context, PreferencesHelper preferencesHelper,
      ChargingSessionManager chargingSessionManager,
      DischargeSessionManager dischargeSessionManager, BatteryHistoryManager batteryHistoryManager,
      TemperatureHistoryManager temperatureHistoryManager) {
    return new BatteryRepository(context, preferencesHelper, chargingSessionManager, dischargeSessionManager, batteryHistoryManager, temperatureHistoryManager);
  }
}
