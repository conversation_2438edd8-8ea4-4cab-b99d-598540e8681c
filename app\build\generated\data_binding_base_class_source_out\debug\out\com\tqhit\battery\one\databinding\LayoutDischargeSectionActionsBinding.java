// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutDischargeSectionActionsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView actionsBtnBatteryAlarm;

  @NonNull
  public final TextView actionsBtnResetSession;

  @NonNull
  public final LinearLayout actionsRoot;

  private LayoutDischargeSectionActionsBinding(@NonNull LinearLayout rootView,
      @NonNull TextView actionsBtnBatteryAlarm, @NonNull TextView actionsBtnResetSession,
      @NonNull LinearLayout actionsRoot) {
    this.rootView = rootView;
    this.actionsBtnBatteryAlarm = actionsBtnBatteryAlarm;
    this.actionsBtnResetSession = actionsBtnResetSession;
    this.actionsRoot = actionsRoot;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutDischargeSectionActionsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutDischargeSectionActionsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_discharge_section_actions, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutDischargeSectionActionsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.actions_btn_battery_alarm;
      TextView actionsBtnBatteryAlarm = ViewBindings.findChildViewById(rootView, id);
      if (actionsBtnBatteryAlarm == null) {
        break missingId;
      }

      id = R.id.actions_btn_reset_session;
      TextView actionsBtnResetSession = ViewBindings.findChildViewById(rootView, id);
      if (actionsBtnResetSession == null) {
        break missingId;
      }

      LinearLayout actionsRoot = (LinearLayout) rootView;

      return new LayoutDischargeSectionActionsBinding((LinearLayout) rootView,
          actionsBtnBatteryAlarm, actionsBtnResetSession, actionsRoot);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
