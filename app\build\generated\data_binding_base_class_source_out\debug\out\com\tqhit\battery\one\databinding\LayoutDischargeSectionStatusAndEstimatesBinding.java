// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutDischargeSectionStatusAndEstimatesBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RelativeLayout saeRlMixedUsageEstimate;

  @NonNull
  public final RelativeLayout saeRlScreenOffEstimate;

  @NonNull
  public final RelativeLayout saeRlScreenOnEstimate;

  @NonNull
  public final TextView saeTvFormattedStatusText;

  @NonNull
  public final TextView saeTvMixedUsageTime;

  @NonNull
  public final TextView saeTvPercentage;

  @NonNull
  public final TextView saeTvScreenOffTime;

  @NonNull
  public final TextView saeTvScreenOnTime;

  @NonNull
  public final LinearLayout statusAndEstimatesRoot;

  private LayoutDischargeSectionStatusAndEstimatesBinding(@NonNull LinearLayout rootView,
      @NonNull RelativeLayout saeRlMixedUsageEstimate,
      @NonNull RelativeLayout saeRlScreenOffEstimate, @NonNull RelativeLayout saeRlScreenOnEstimate,
      @NonNull TextView saeTvFormattedStatusText, @NonNull TextView saeTvMixedUsageTime,
      @NonNull TextView saeTvPercentage, @NonNull TextView saeTvScreenOffTime,
      @NonNull TextView saeTvScreenOnTime, @NonNull LinearLayout statusAndEstimatesRoot) {
    this.rootView = rootView;
    this.saeRlMixedUsageEstimate = saeRlMixedUsageEstimate;
    this.saeRlScreenOffEstimate = saeRlScreenOffEstimate;
    this.saeRlScreenOnEstimate = saeRlScreenOnEstimate;
    this.saeTvFormattedStatusText = saeTvFormattedStatusText;
    this.saeTvMixedUsageTime = saeTvMixedUsageTime;
    this.saeTvPercentage = saeTvPercentage;
    this.saeTvScreenOffTime = saeTvScreenOffTime;
    this.saeTvScreenOnTime = saeTvScreenOnTime;
    this.statusAndEstimatesRoot = statusAndEstimatesRoot;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutDischargeSectionStatusAndEstimatesBinding inflate(
      @NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutDischargeSectionStatusAndEstimatesBinding inflate(
      @NonNull LayoutInflater inflater, @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_discharge_section_status_and_estimates, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutDischargeSectionStatusAndEstimatesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.sae_rl_mixed_usage_estimate;
      RelativeLayout saeRlMixedUsageEstimate = ViewBindings.findChildViewById(rootView, id);
      if (saeRlMixedUsageEstimate == null) {
        break missingId;
      }

      id = R.id.sae_rl_screen_off_estimate;
      RelativeLayout saeRlScreenOffEstimate = ViewBindings.findChildViewById(rootView, id);
      if (saeRlScreenOffEstimate == null) {
        break missingId;
      }

      id = R.id.sae_rl_screen_on_estimate;
      RelativeLayout saeRlScreenOnEstimate = ViewBindings.findChildViewById(rootView, id);
      if (saeRlScreenOnEstimate == null) {
        break missingId;
      }

      id = R.id.sae_tv_formatted_status_text;
      TextView saeTvFormattedStatusText = ViewBindings.findChildViewById(rootView, id);
      if (saeTvFormattedStatusText == null) {
        break missingId;
      }

      id = R.id.sae_tv_mixed_usage_time;
      TextView saeTvMixedUsageTime = ViewBindings.findChildViewById(rootView, id);
      if (saeTvMixedUsageTime == null) {
        break missingId;
      }

      id = R.id.sae_tv_percentage;
      TextView saeTvPercentage = ViewBindings.findChildViewById(rootView, id);
      if (saeTvPercentage == null) {
        break missingId;
      }

      id = R.id.sae_tv_screen_off_time;
      TextView saeTvScreenOffTime = ViewBindings.findChildViewById(rootView, id);
      if (saeTvScreenOffTime == null) {
        break missingId;
      }

      id = R.id.sae_tv_screen_on_time;
      TextView saeTvScreenOnTime = ViewBindings.findChildViewById(rootView, id);
      if (saeTvScreenOnTime == null) {
        break missingId;
      }

      LinearLayout statusAndEstimatesRoot = (LinearLayout) rootView;

      return new LayoutDischargeSectionStatusAndEstimatesBinding((LinearLayout) rootView,
          saeRlMixedUsageEstimate, saeRlScreenOffEstimate, saeRlScreenOnEstimate,
          saeTvFormattedStatusText, saeTvMixedUsageTime, saeTvPercentage, saeTvScreenOffTime,
          saeTvScreenOnTime, statusAndEstimatesRoot);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
