// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.applovin.mediation.ads.MaxAdView;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout adsBanerLayout;

  @NonNull
  public final MaxAdView bannerContainer;

  @NonNull
  public final BottomNavigationView bottomView;

  @NonNull
  public final ConstraintLayout mainL;

  @NonNull
  public final FrameLayout navHostFragment;

  @NonNull
  public final LinearLayout panel;

  @NonNull
  public final LinearLayout updateView;

  @NonNull
  public final TextView updateViewBtn;

  @NonNull
  public final TextView updateViewText;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout adsBanerLayout, @NonNull MaxAdView bannerContainer,
      @NonNull BottomNavigationView bottomView, @NonNull ConstraintLayout mainL,
      @NonNull FrameLayout navHostFragment, @NonNull LinearLayout panel,
      @NonNull LinearLayout updateView, @NonNull TextView updateViewBtn,
      @NonNull TextView updateViewText) {
    this.rootView = rootView;
    this.adsBanerLayout = adsBanerLayout;
    this.bannerContainer = bannerContainer;
    this.bottomView = bottomView;
    this.mainL = mainL;
    this.navHostFragment = navHostFragment;
    this.panel = panel;
    this.updateView = updateView;
    this.updateViewBtn = updateViewBtn;
    this.updateViewText = updateViewText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ads_baner_layout;
      ConstraintLayout adsBanerLayout = ViewBindings.findChildViewById(rootView, id);
      if (adsBanerLayout == null) {
        break missingId;
      }

      id = R.id.banner_container;
      MaxAdView bannerContainer = ViewBindings.findChildViewById(rootView, id);
      if (bannerContainer == null) {
        break missingId;
      }

      id = R.id.bottom_view;
      BottomNavigationView bottomView = ViewBindings.findChildViewById(rootView, id);
      if (bottomView == null) {
        break missingId;
      }

      ConstraintLayout mainL = (ConstraintLayout) rootView;

      id = R.id.nav_host_fragment;
      FrameLayout navHostFragment = ViewBindings.findChildViewById(rootView, id);
      if (navHostFragment == null) {
        break missingId;
      }

      id = R.id.panel;
      LinearLayout panel = ViewBindings.findChildViewById(rootView, id);
      if (panel == null) {
        break missingId;
      }

      id = R.id.update_view;
      LinearLayout updateView = ViewBindings.findChildViewById(rootView, id);
      if (updateView == null) {
        break missingId;
      }

      id = R.id.update_view_btn;
      TextView updateViewBtn = ViewBindings.findChildViewById(rootView, id);
      if (updateViewBtn == null) {
        break missingId;
      }

      id = R.id.update_view_text;
      TextView updateViewText = ViewBindings.findChildViewById(rootView, id);
      if (updateViewText == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, adsBanerLayout, bannerContainer,
          bottomView, mainL, navHostFragment, panel, updateView, updateViewBtn, updateViewText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
