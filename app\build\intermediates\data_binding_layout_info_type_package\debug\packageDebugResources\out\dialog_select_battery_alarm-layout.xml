<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_select_battery_alarm" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\dialog_select_battery_alarm.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/dialog_select_battery_alarm_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="517" endOffset="16"/></Target><Target id="@+id/strelka" view="RelativeLayout"><Expressions/><location startLine="26" startOffset="16" endLine="58" endOffset="32"/></Target><Target id="@+id/exit" view="Button"><Expressions/><location startLine="33" startOffset="20" endLine="44" endOffset="74"/></Target><Target id="@+id/textView20" view="TextView"><Expressions/><location startLine="59" startOffset="16" endLine="76" endOffset="59"/></Target><Target id="@+id/relativ33" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="78" startOffset="12" endLine="228" endOffset="63"/></Target><Target id="@+id/charge_l" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="84" startOffset="16" endLine="130" endOffset="67"/></Target><Target id="@+id/p192" view="TextView"><Expressions/><location startLine="95" startOffset="20" endLine="114" endOffset="66"/></Target><Target id="@+id/switch_charge_alarm" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="115" startOffset="20" endLine="129" endOffset="59"/></Target><Target id="@+id/relat223" view="LinearLayout"><Expressions/><location startLine="131" startOffset="16" endLine="155" endOffset="30"/></Target><Target id="@+id/text_buy_access2" view="TextView"><Expressions/><location startLine="143" startOffset="20" endLine="154" endOffset="55"/></Target><Target id="@+id/linearLayout2" view="LinearLayout"><Expressions/><location startLine="156" startOffset="16" endLine="227" endOffset="30"/></Target><Target id="@+id/progressbar_charge_alarm" view="ProgressBar"><Expressions/><location startLine="173" startOffset="24" endLine="189" endOffset="77"/></Target><Target id="@+id/seekBar_charge_alarm" view="SeekBar"><Expressions/><location startLine="190" startOffset="24" endLine="205" endOffset="70"/></Target><Target id="@+id/linearLayout" view="LinearLayout"><Expressions/><location startLine="207" startOffset="20" endLine="226" endOffset="34"/></Target><Target id="@+id/charge_alarm_percent" view="TextView"><Expressions/><location startLine="219" startOffset="24" endLine="225" endOffset="47"/></Target><Target id="@+id/p7" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="229" startOffset="12" endLine="273" endOffset="63"/></Target><Target id="@+id/p_1192" view="TextView"><Expressions/><location startLine="238" startOffset="16" endLine="257" endOffset="62"/></Target><Target id="@+id/switch_full" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="258" startOffset="16" endLine="272" endOffset="55"/></Target><Target id="@+id/p6" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="274" startOffset="12" endLine="318" endOffset="63"/></Target><Target id="@+id/p_112" view="TextView"><Expressions/><location startLine="283" startOffset="16" endLine="302" endOffset="62"/></Target><Target id="@+id/switch_vibration" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="303" startOffset="16" endLine="317" endOffset="55"/></Target><Target id="@+id/p5" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="319" startOffset="12" endLine="374" endOffset="63"/></Target><Target id="@+id/dont_disturb_from_layout" view="LinearLayout"><Expressions/><location startLine="328" startOffset="16" endLine="338" endOffset="62"/></Target><Target id="@+id/p_11" view="TextView"><Expressions/><location startLine="339" startOffset="16" endLine="358" endOffset="62"/></Target><Target id="@+id/switch_dont_disturb" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="359" startOffset="16" endLine="373" endOffset="55"/></Target><Target id="@+id/dont_disturb_until_layout" view="LinearLayout"><Expressions/><location startLine="375" startOffset="12" endLine="476" endOffset="26"/></Target><Target id="@+id/dont_disturb_from" view="Button"><Expressions/><location startLine="390" startOffset="20" endLine="399" endOffset="74"/></Target><Target id="@+id/p_12er" view="TextView"><Expressions/><location startLine="400" startOffset="20" endLine="416" endOffset="66"/></Target><Target id="@+id/p_12er1" view="TextView"><Expressions/><location startLine="417" startOffset="20" endLine="432" endOffset="66"/></Target><Target id="@+id/dont_disturb_until" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="434" startOffset="16" endLine="475" endOffset="67"/></Target><Target id="@+id/p_1221" view="TextView"><Expressions/><location startLine="443" startOffset="20" endLine="459" endOffset="66"/></Target><Target id="@+id/p_12e2" view="TextView"><Expressions/><location startLine="460" startOffset="20" endLine="474" endOffset="66"/></Target><Target id="@+id/button_charge_alarm" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="477" startOffset="12" endLine="514" endOffset="63"/></Target><Target id="@+id/battery_4" view="Button"><Expressions/><location startLine="485" startOffset="16" endLine="496" endOffset="70"/></Target></Targets></Layout>