<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_app_power_consumption" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\item_app_power_consumption.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_app_power_consumption_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="90" endOffset="14"/></Target><Target id="@+id/iv_app_icon" view="ImageView"><Expressions/><location startLine="10" startOffset="4" endLine="15" endOffset="40"/></Target><Target id="@+id/tv_app_name" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="33" endOffset="37"/></Target><Target id="@+id/tv_usage_time" view="TextView"><Expressions/><location startLine="41" startOffset="12" endLine="48" endOffset="49"/></Target><Target id="@+id/tv_percentage" view="TextView"><Expressions/><location startLine="50" startOffset="12" endLine="57" endOffset="50"/></Target><Target id="@+id/tv_power_consumption" view="TextView"><Expressions/><location startLine="71" startOffset="8" endLine="78" endOffset="45"/></Target></Targets></Layout>