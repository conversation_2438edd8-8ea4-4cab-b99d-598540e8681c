{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tqhit.battery.one.app-mergeDebugResources-132:\\values-pl\\values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,622,944,1022,1100,1183,1272,1361,1444,1511,1605,1699,1768,1834,1899,1971,2098,2221,2344,2420,2501,2574,2657,2754,2851,2919,2983,3036,3094,3142,3203,3276,3342,3406,3483,3550,3608,3675,3727,3794,3885,3976,4031", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,126,122,122,75,80,72,82,96,96,67,63,52,57,47,60,72,65,63,76,66,57,66,51,66,90,90,54,66", "endOffsets": "282,617,939,1017,1095,1178,1267,1356,1439,1506,1600,1694,1763,1829,1894,1966,2093,2216,2339,2415,2496,2569,2652,2749,2846,2914,2978,3031,3089,3137,3198,3271,3337,3401,3478,3545,3603,3670,3722,3789,3880,3971,4026,4093"}, "to": {"startLines": "2,11,17,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,252,253,254,255,256,257,258,259,260,261,262,265,266,267,268,269,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,717,21186,21264,21342,21425,21514,21603,21686,21753,21847,21941,22010,22076,22141,22213,22340,22463,22586,22662,22743,22816,22899,22996,23093,23161,23887,23940,23998,24046,24107,24180,24246,24310,24387,24454,24512,24709,24761,24828,24919,25010,25065", "endLines": "10,16,22,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,252,253,254,255,256,257,258,259,260,261,262,265,266,267,268,269,270", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,126,122,122,75,80,72,82,96,96,67,63,52,57,47,60,72,65,63,76,66,57,66,51,66,90,90,54,66", "endOffsets": "377,712,1034,21259,21337,21420,21509,21598,21681,21748,21842,21936,22005,22071,22136,22208,22335,22458,22581,22657,22738,22811,22894,22991,23088,23156,23220,23935,23993,24041,24102,24175,24241,24305,24382,24449,24507,24574,24756,24823,24914,25005,25060,25127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,116", "endOffsets": "158,275"}, "to": {"startLines": "441,442", "startColumns": "4,4", "startOffsets": "40337,40445", "endColumns": "107,116", "endOffsets": "40440,40557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,394,499,576,653,746,836,919,1002,1089,1237,1315,1391,1473,1541", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,77,75,81,67,119", "endOffsets": "195,280,389,494,571,648,741,831,914,997,1084,1156,1310,1386,1468,1536,1656"}, "to": {"startLines": "148,149,186,187,210,300,302,443,449,491,492,512,529,530,536,537,539", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12271,12366,17525,17634,20594,27400,27545,40562,41054,44676,44759,46189,47807,47885,48421,48503,48623", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,77,75,81,67,119", "endOffsets": "12361,12446,17629,17734,20666,27472,27633,40647,41132,44754,44841,46256,47880,47956,48498,48566,48738"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,266,346,480,649,730", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "170,261,341,475,644,725,802"}, "to": {"startLines": "172,271,450,485,534,569,570", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14979,25132,41137,44128,48196,50742,50823", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "15044,25218,41212,44257,48360,50818,50895"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,386,461,536,615,719,814,899,1016,1098,1163,1227,1308,1372,1433,1544,1608,1676,1730,1799,1861,1915,2026,2087,2149,2203,2275,2404,2493,2572,2667,2752,2834,2983,3065,3148,3285,3372,3449,3503,3554,3620,3691,3767,3838,3921,3998,4076,4154,4230,4338,4428,4501,4596,4693,4765,4839,4939,4991,5076,5142,5230,5320,5382,5446,5509,5580,5687,5799,5898,6005,6063,6118,6194,6278,6355", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75,83,76,77", "endOffsets": "381,456,531,610,714,809,894,1011,1093,1158,1222,1303,1367,1428,1539,1603,1671,1725,1794,1856,1910,2021,2082,2144,2198,2270,2399,2488,2567,2662,2747,2829,2978,3060,3143,3280,3367,3444,3498,3549,3615,3686,3762,3833,3916,3993,4071,4149,4225,4333,4423,4496,4591,4688,4760,4834,4934,4986,5071,5137,5225,5315,5377,5441,5504,5575,5682,5794,5893,6000,6058,6113,6189,6273,6350,6428"}, "to": {"startLines": "23,112,113,114,115,116,132,133,147,216,218,273,297,305,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,474,509,510,521", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1039,9293,9368,9443,9522,9626,10991,11076,12189,20967,21122,25289,27203,27761,35300,35411,35475,35543,35597,35666,35728,35782,35893,35954,36016,36070,36142,36402,36491,36570,36665,36750,36832,36981,37063,37146,37283,37370,37447,37501,37552,37618,37689,37765,37836,37919,37996,38074,38152,38228,38336,38426,38499,38594,38691,38763,38837,38937,38989,39074,39140,39228,39318,39380,39444,39507,39578,39685,39797,39896,40003,40061,43465,45968,46052,46966", "endLines": "28,112,113,114,115,116,132,133,147,216,218,273,297,305,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,474,509,510,521", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75,83,76,77", "endOffsets": "1320,9363,9438,9517,9621,9716,11071,11188,12266,21027,21181,25365,27262,27817,35406,35470,35538,35592,35661,35723,35777,35888,35949,36011,36065,36137,36266,36486,36565,36660,36745,36827,36976,37058,37141,37278,37365,37442,37496,37547,37613,37684,37760,37831,37914,37991,38069,38147,38223,38331,38421,38494,38589,38686,38758,38832,38932,38984,39069,39135,39223,39313,39375,39439,39502,39573,39680,39792,39891,39998,40056,40111,43536,46047,46124,47039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "173,274,275,276", "startColumns": "4,4,4,4", "startOffsets": "15049,25370,25469,25584", "endColumns": "99,98,114,103", "endOffsets": "15144,25464,25579,25683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,409,524,624,723,839,977,1099,1241,1325,1424,1516,1612,1729,1853,1957,2097,2233,2377,2538,2670,2791,2916,3037,3130,3230,3350,3474,3573,3677,3783,3924,4071,4182,4281,4355,4450,4546,4650,4737,4824,4936,5016,5103,5198,5303,5394,5503,5591,5697,5798,5908,6026,6106,6209", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "165,282,404,519,619,718,834,972,1094,1236,1320,1419,1511,1607,1724,1848,1952,2092,2228,2372,2533,2665,2786,2911,3032,3125,3225,3345,3469,3568,3672,3778,3919,4066,4177,4276,4350,4445,4541,4645,4732,4819,4931,5011,5098,5193,5298,5389,5498,5586,5692,5793,5903,6021,6101,6204,6301"}, "to": {"startLines": "316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28890,29005,29122,29244,29359,29459,29558,29674,29812,29934,30076,30160,30259,30351,30447,30564,30688,30792,30932,31068,31212,31373,31505,31626,31751,31872,31965,32065,32185,32309,32408,32512,32618,32759,32906,33017,33116,33190,33285,33381,33485,33572,33659,33771,33851,33938,34033,34138,34229,34338,34426,34532,34633,34743,34861,34941,35044", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "29000,29117,29239,29354,29454,29553,29669,29807,29929,30071,30155,30254,30346,30442,30559,30683,30787,30927,31063,31207,31368,31500,31621,31746,31867,31960,32060,32180,32304,32403,32507,32613,32754,32901,33012,33111,33185,33280,33376,33480,33567,33654,33766,33846,33933,34028,34133,34224,34333,34421,34527,34628,34738,34856,34936,35039,35136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,516,623,897,988,1081,1176,1270,1371,1464,1559,1654,1745,2027,2447,2558,2817", "endColumns": "114,101,106,118,90,92,94,93,100,92,94,94,90,90,99,110,162,82", "endOffsets": "215,317,618,737,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,2122,2553,2716,2895"}, "to": {"startLines": "45,46,49,50,53,54,55,56,57,58,59,60,61,62,65,69,70,508", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2207,2322,2558,2665,2880,2971,3064,3159,3253,3354,3447,3542,3637,3728,3950,4280,4391,45885", "endColumns": "114,101,106,118,90,92,94,93,100,92,94,94,90,90,99,110,162,82", "endOffsets": "2317,2419,2660,2779,2966,3059,3154,3248,3349,3442,3537,3632,3723,3814,4045,4386,4549,45963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "545,546", "startColumns": "4,4", "startOffsets": "49205,49293", "endColumns": "87,87", "endOffsets": "49288,49376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,189,251,320,398,468,561,652", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "123,184,246,315,393,463,556,647,712"}, "to": {"startLines": "243,244,245,246,247,248,249,250,251", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23225,23298,23359,23421,23490,23568,23638,23731,23822", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "23293,23354,23416,23485,23563,23633,23726,23817,23882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-pl\\values-pl.xml", "from": {"startLines": "58,59", "startColumns": "4,4", "startOffsets": "3675,3740", "endColumns": "64,64", "endOffsets": "3735,3800"}, "to": {"startLines": "263,264", "startColumns": "4,4", "startOffsets": "24579,24644", "endColumns": "64,64", "endOffsets": "24639,24704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,177,227,290,333,430,473,543,590,663,709,765,865,929,988", "endColumns": "82,38,49,62,42,96,42,69,46,72,45,55,99,63,58,57", "endOffsets": "133,172,222,285,328,425,468,538,585,658,704,760,860,924,983,1041"}, "to": {"startLines": "547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "49381,49464,49503,49553,49616,49659,49756,49799,49869,49916,49989,50035,50091,50191,50255,50314", "endColumns": "82,38,49,62,42,96,42,69,46,72,45,55,99,63,58,57", "endOffsets": "49459,49498,49548,49611,49654,49751,49794,49864,49911,49984,50030,50086,50186,50250,50309,50367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-pl\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,290,584,647,778,892,1016,1066,1117,1223,1321,1361,1443,1481,1515,1573,1657,1700", "endColumns": "41,48,59,62,130,113,123,49,50,105,97,39,81,37,33,57,83,42,55", "endOffsets": "240,289,349,646,777,891,1015,1065,1116,1222,1320,1360,1442,1480,1514,1572,1656,1699,1755"}, "to": {"startLines": "438,439,440,459,460,461,462,463,464,465,466,499,500,501,502,503,504,505,575", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "40174,40220,40273,41835,41902,42037,42155,42283,42337,42392,42502,45346,45390,45476,45518,45556,45618,45706,51119", "endColumns": "45,52,63,66,134,117,127,53,54,109,101,43,85,41,37,61,87,46,59", "endOffsets": "40215,40268,40332,41897,42032,42150,42278,42332,42387,42497,42599,45385,45471,45513,45551,45613,45701,45748,51174"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-pl\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "125,196,249,301,352,404,458,505,553,610,675,727,778,831,881,21486,933,1012,1069,1119,1167,1220,1300,1370,1450,1533,1600,1664,23963,1720,1759,1923,1983,2075,2118,2180,21688,22498,22921,23050,2266,57,23914,21888,23787,2309,2358,2397,2483,2543,2620,3031,3451,12144,12208,11500,11418,12273,3494,3567,3649,3852,4166,4243,4304,4346,4407,4484,4543,21576,4612,4686,21815,4729,4808,4850,4930,5094,5169,5264,5349,5421,5508,22375,5574,5630,5686,5750,5807,5854,5927,6030,6082,6433,6489,6548,7144,7763,7833,7884,8028,8092,8366,8418,8885,8951,9088,9251,9411,9478,9526,9574,9655,9755,9847,9904,10045,10142,10203,10272,10342,10624,10876,11055,11207,11295,12360,12429,23591,12484,12524,12615,12682,12740,12966,13015,13093,13160,13218,13350,13409,13540,13754,13794,13853,13891,13929,13965,14001,14069,14114,14167,14217,14283,23650,14352,14420,14477,14521,14562,14637,15056,15118,15205,15369,15442,15518,15555,15590,15626,15664,15718,15781,15851,15910,15991,16138,21736,16174,16238,16294,16385,16454,16529,16634,16751,16825,16864,16940,22190,22016,21932,22819,23367,17221,17261,17311,17362,17426,17521,22974,17570,17652,17703,21643,17742,23719,17858,17952,18048,18099,18172,18366,18442,18495,22297,18554,18610,23411,18671,18755,18855,18943,18992,22750,19062,19135,19178,19235,19284,19403,19783,19858,19905,19965,20041,20098,20151,23855,20192,20258,20467,20559,20625,20698,20753,20827,20901,20935,22442,20992,21056,21102,21139,21194,21248,21308,21368,21405,21441", "endColumns": "69,51,50,49,50,52,45,46,55,63,50,49,51,48,50,88,77,55,48,46,51,78,68,78,81,65,62,54,65,37,162,58,90,41,60,84,46,250,51,315,41,66,47,42,66,47,37,84,58,75,409,418,41,62,63,642,80,83,71,80,201,312,75,59,40,59,75,57,67,65,72,41,71,77,40,78,162,73,93,83,70,85,64,65,54,54,62,55,45,71,101,50,349,54,57,594,617,68,49,142,62,272,50,465,64,135,161,158,65,46,46,79,98,90,55,139,95,59,67,68,280,250,177,150,86,76,67,53,57,38,89,65,56,224,47,76,65,56,130,57,129,212,38,57,36,36,34,34,66,43,51,48,64,67,67,66,55,42,39,73,417,60,85,162,71,74,35,33,34,36,52,61,68,57,79,145,34,77,62,54,89,67,73,103,115,72,37,74,279,105,172,82,100,42,38,48,49,62,93,47,74,80,49,37,43,114,66,92,94,49,71,192,74,51,57,76,54,59,178,82,98,86,47,68,67,71,41,55,47,117,378,73,45,58,74,55,51,39,57,64,207,90,64,71,53,72,72,32,55,54,62,44,35,53,52,58,58,35,34,43", "endOffsets": "190,243,295,346,398,452,499,547,604,669,721,772,825,875,927,21570,1006,1063,1113,1161,1214,1294,1364,1444,1527,1594,1658,1714,24024,1753,1917,1977,2069,2112,2174,2260,21730,22744,22968,23361,2303,119,23957,21926,23849,2352,2391,2477,2537,2614,3025,3445,3488,12202,12267,12138,11494,12352,3561,3643,3846,4160,4237,4298,4340,4401,4478,4537,4606,21637,4680,4723,21882,4802,4844,4924,5088,5163,5258,5343,5415,5502,5568,22436,5624,5680,5744,5801,5848,5921,6024,6076,6427,6483,6542,7138,7757,7827,7878,8022,8086,8360,8412,8879,8945,9082,9245,9405,9472,9520,9568,9649,9749,9841,9898,10039,10136,10197,10266,10336,10618,10870,11049,11201,11289,11367,12423,12478,23644,12518,12609,12676,12734,12960,13009,13087,13154,13212,13344,13403,13534,13748,13788,13847,13885,13923,13959,13995,14063,14108,14161,14211,14277,14346,23713,14414,14471,14515,14556,14631,15050,15112,15199,15363,15436,15512,15549,15584,15620,15658,15712,15775,15845,15904,15985,16132,16168,21809,16232,16288,16379,16448,16523,16628,16745,16819,16858,16934,17215,22291,22184,22010,22915,23405,17255,17305,17356,17420,17515,17564,23044,17646,17697,17736,21682,17852,23781,17946,18042,18093,18166,18360,18436,18489,18548,22369,18604,18665,23585,18749,18849,18937,18986,19056,22813,19129,19172,19229,19278,19397,19777,19852,19899,19959,20035,20092,20145,20186,23908,20252,20461,20553,20619,20692,20747,20821,20895,20929,20986,22492,21050,21096,21133,21188,21242,21302,21362,21399,21435,21480"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,47,48,51,52,63,64,66,67,68,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,117,118,119,120,121,129,130,131,134,135,136,137,138,139,140,141,142,143,144,145,146,168,169,170,171,174,175,176,177,178,179,180,181,182,183,184,185,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,211,212,213,214,215,217,272,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,298,299,301,303,304,306,307,308,309,310,311,312,313,314,315,373,374,375,376,390,391,437,444,445,446,447,448,451,452,453,454,455,456,457,458,467,468,469,470,471,472,473,475,476,477,478,479,480,481,482,483,484,486,487,488,489,490,493,494,495,496,497,498,506,507,511,513,514,515,516,517,518,519,520,522,523,524,525,526,527,528,532,533,535,538,540,541,542,543,544,563,564,565,566,567,568,571,572,573,574,576,577,578,579,580,581,582,583", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1325,1395,1447,1498,1548,1599,1652,1698,1745,1801,1865,1916,1966,2018,2067,2118,2424,2502,2784,2833,3819,3871,4050,4119,4198,4554,4620,4683,4738,4804,4842,5005,5064,5155,5197,5258,5343,5390,5641,5693,6009,6051,6118,6166,6209,6276,6324,6362,6447,6506,6582,6992,7411,7453,7516,7580,8223,8304,8388,8460,8541,8743,9056,9132,9192,9233,9721,9797,9855,9923,9989,10799,10841,10913,11193,11234,11313,11476,11550,11644,11728,11799,11885,11950,12016,12071,12126,14703,14759,14805,14877,15149,15200,15550,15605,15663,16258,16876,16945,16995,17138,17201,17474,17739,18205,18270,18406,18568,18727,18793,18840,18887,18967,19066,19157,19213,19353,19449,19509,19577,19646,19927,20178,20356,20507,20671,20748,20816,20870,20928,21032,25223,25688,25745,25970,26018,26095,26161,26218,26349,26407,26537,26750,26789,26847,26884,26921,26956,26991,27058,27102,27154,27267,27332,27477,27638,27705,27822,27865,27905,27979,28397,28458,28544,28707,28779,28854,35141,35175,35210,35247,36271,36333,40116,40652,40732,40878,40913,40991,41217,41272,41362,41430,41504,41608,41724,41797,42604,42679,42959,43065,43238,43321,43422,43541,43580,43629,43679,43742,43836,43884,43959,44040,44090,44262,44306,44421,44488,44581,44846,44896,44968,45161,45236,45288,45753,45830,46129,46261,46440,46523,46622,46709,46757,46826,46894,47044,47086,47142,47190,47308,47687,47761,48062,48121,48365,48571,48743,48783,48841,48906,49114,50372,50437,50509,50563,50636,50709,50900,50956,51011,51074,51179,51215,51269,51322,51381,51440,51476,51511", "endColumns": "69,51,50,49,50,52,45,46,55,63,50,49,51,48,50,88,77,55,48,46,51,78,68,78,81,65,62,54,65,37,162,58,90,41,60,84,46,250,51,315,41,66,47,42,66,47,37,84,58,75,409,418,41,62,63,642,80,83,71,80,201,312,75,59,40,59,75,57,67,65,72,41,71,77,40,78,162,73,93,83,70,85,64,65,54,54,62,55,45,71,101,50,349,54,57,594,617,68,49,142,62,272,50,465,64,135,161,158,65,46,46,79,98,90,55,139,95,59,67,68,280,250,177,150,86,76,67,53,57,38,89,65,56,224,47,76,65,56,130,57,129,212,38,57,36,36,34,34,66,43,51,48,64,67,67,66,55,42,39,73,417,60,85,162,71,74,35,33,34,36,52,61,68,57,79,145,34,77,62,54,89,67,73,103,115,72,37,74,279,105,172,82,100,42,38,48,49,62,93,47,74,80,49,37,43,114,66,92,94,49,71,192,74,51,57,76,54,59,178,82,98,86,47,68,67,71,41,55,47,117,378,73,45,58,74,55,51,39,57,64,207,90,64,71,53,72,72,32,55,54,62,44,35,53,52,58,58,35,34,43", "endOffsets": "1390,1442,1493,1543,1594,1647,1693,1740,1796,1860,1911,1961,2013,2062,2113,2202,2497,2553,2828,2875,3866,3945,4114,4193,4275,4615,4678,4733,4799,4837,5000,5059,5150,5192,5253,5338,5385,5636,5688,6004,6046,6113,6161,6204,6271,6319,6357,6442,6501,6577,6987,7406,7448,7511,7575,8218,8299,8383,8455,8536,8738,9051,9127,9187,9228,9288,9792,9850,9918,9984,10057,10836,10908,10986,11229,11308,11471,11545,11639,11723,11794,11880,11945,12011,12066,12121,12184,14754,14800,14872,14974,15195,15545,15600,15658,16253,16871,16940,16990,17133,17196,17469,17520,18200,18265,18401,18563,18722,18788,18835,18882,18962,19061,19152,19208,19348,19444,19504,19572,19641,19922,20173,20351,20502,20589,20743,20811,20865,20923,20962,21117,25284,25740,25965,26013,26090,26156,26213,26344,26402,26532,26745,26784,26842,26879,26916,26951,26986,27053,27097,27149,27198,27327,27395,27540,27700,27756,27860,27900,27974,28392,28453,28539,28702,28774,28849,28885,35170,35205,35242,35295,36328,36397,40169,40727,40873,40908,40986,41049,41267,41357,41425,41499,41603,41719,41792,41830,42674,42954,43060,43233,43316,43417,43460,43575,43624,43674,43737,43831,43879,43954,44035,44085,44123,44301,44416,44483,44576,44671,44891,44963,45156,45231,45283,45341,45825,45880,46184,46435,46518,46617,46704,46752,46821,46889,46961,47081,47137,47185,47303,47682,47756,47802,48116,48191,48416,48618,48778,48836,48901,49109,49200,50432,50504,50558,50631,50704,50737,50951,51006,51069,51114,51210,51264,51317,51376,51435,51471,51506,51550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-pl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "13441", "endColumns": "139", "endOffsets": "13576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,457,575,681,828,949,1056,1151,1318,1423,1594,1718,1873,2030,2095,2157", "endColumns": "99,163,117,105,146,120,106,94,166,104,170,123,154,156,64,61,79", "endOffsets": "292,456,574,680,827,948,1055,1150,1317,1422,1593,1717,1872,2029,2094,2156,2236"}, "to": {"startLines": "150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12451,12555,12723,12845,12955,13106,13231,13342,13581,13752,13861,14036,14164,14323,14484,14553,14619", "endColumns": "103,167,121,109,150,124,110,98,170,108,174,127,158,160,68,65,83", "endOffsets": "12550,12718,12840,12950,13101,13226,13337,13436,13747,13856,14031,14159,14318,14479,14548,14614,14698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "122,123,124,125,126,127,128,531", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "10062,10159,10261,10359,10458,10572,10677,47961", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "10154,10256,10354,10453,10567,10672,10794,48057"}}]}, {"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,11,17,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,252,253,254,255,256,257,258,259,260,261,262,265,266,267,268,269,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,717,21186,21264,21342,21425,21514,21603,21686,21753,21847,21941,22010,22076,22141,22213,22340,22463,22586,22662,22743,22816,22899,22996,23093,23161,23887,23940,23998,24046,24107,24180,24246,24310,24387,24454,24512,24709,24761,24828,24919,25010,25065", "endLines": "10,16,22,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,252,253,254,255,256,257,258,259,260,261,262,265,266,267,268,269,270", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,126,122,122,75,80,72,82,96,96,67,63,52,57,47,60,72,65,63,76,66,57,66,51,66,90,90,54,66", "endOffsets": "377,712,1034,21259,21337,21420,21509,21598,21681,21748,21842,21936,22005,22071,22136,22208,22335,22458,22581,22657,22738,22811,22894,22991,23088,23156,23220,23935,23993,24041,24102,24175,24241,24305,24382,24449,24507,24574,24756,24823,24914,25005,25060,25127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-pl\\values-pl.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "442,443", "startColumns": "4,4", "startOffsets": "40393,40501", "endColumns": "107,116", "endOffsets": "40496,40613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "148,149,186,187,210,300,302,444,450,493,494,514,532,533,539,540,542", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12271,12366,17525,17634,20594,27400,27545,40618,41110,44785,44868,46298,48004,48082,48618,48700,48820", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,77,75,81,67,119", "endOffsets": "12361,12446,17629,17734,20666,27472,27633,40703,41188,44863,44950,46365,48077,48153,48695,48763,48935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "172,271,451,486,537,572,573", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14979,25132,41193,44184,48393,50939,51020", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "15044,25218,41268,44313,48557,51015,51092"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "23,112,113,114,115,116,132,133,147,216,218,273,297,306,378,379,380,381,382,383,384,385,386,387,388,389,390,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,475,511,512,523", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1039,9293,9368,9443,9522,9626,10991,11076,12189,20967,21122,25289,27203,27817,35356,35467,35531,35599,35653,35722,35784,35838,35949,36010,36072,36126,36198,36458,36547,36626,36721,36806,36888,37037,37119,37202,37339,37426,37503,37557,37608,37674,37745,37821,37892,37975,38052,38130,38208,38284,38392,38482,38555,38650,38747,38819,38893,38993,39045,39130,39196,39284,39374,39436,39500,39563,39634,39741,39853,39952,40059,40117,43521,46077,46161,47075", "endLines": "28,112,113,114,115,116,132,133,147,216,218,273,297,306,378,379,380,381,382,383,384,385,386,387,388,389,390,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,475,511,512,523", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75,83,76,77", "endOffsets": "1320,9363,9438,9517,9621,9716,11071,11188,12266,21027,21181,25365,27262,27873,35462,35526,35594,35648,35717,35779,35833,35944,36005,36067,36121,36193,36322,36542,36621,36716,36801,36883,37032,37114,37197,37334,37421,37498,37552,37603,37669,37740,37816,37887,37970,38047,38125,38203,38279,38387,38477,38550,38645,38742,38814,38888,38988,39040,39125,39191,39279,39369,39431,39495,39558,39629,39736,39848,39947,40054,40112,40167,43592,46156,46233,47148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "173,274,275,276", "startColumns": "4,4,4,4", "startOffsets": "15049,25370,25469,25584", "endColumns": "99,98,114,103", "endOffsets": "15144,25464,25579,25683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28946,29061,29178,29300,29415,29515,29614,29730,29868,29990,30132,30216,30315,30407,30503,30620,30744,30848,30988,31124,31268,31429,31561,31682,31807,31928,32021,32121,32241,32365,32464,32568,32674,32815,32962,33073,33172,33246,33341,33437,33541,33628,33715,33827,33907,33994,34089,34194,34285,34394,34482,34588,34689,34799,34917,34997,35100", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "29056,29173,29295,29410,29510,29609,29725,29863,29985,30127,30211,30310,30402,30498,30615,30739,30843,30983,31119,31263,31424,31556,31677,31802,31923,32016,32116,32236,32360,32459,32563,32669,32810,32957,33068,33167,33241,33336,33432,33536,33623,33710,33822,33902,33989,34084,34189,34280,34389,34477,34583,34684,34794,34912,34992,35095,35192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "45,46,49,50,53,54,55,56,57,58,59,60,61,62,65,69,70,510", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2207,2322,2558,2665,2880,2971,3064,3159,3253,3354,3447,3542,3637,3728,3950,4280,4391,45994", "endColumns": "114,101,106,118,90,92,94,93,100,92,94,94,90,90,99,110,162,82", "endOffsets": "2317,2419,2660,2779,2966,3059,3154,3248,3349,3442,3537,3632,3723,3814,4045,4386,4549,46072"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "548,549", "startColumns": "4,4", "startOffsets": "49402,49490", "endColumns": "87,87", "endOffsets": "49485,49573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "243,244,245,246,247,248,249,250,251", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23225,23298,23359,23421,23490,23568,23638,23731,23822", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "23293,23354,23416,23485,23563,23633,23726,23817,23882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-pl\\values-pl.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "263,264", "startColumns": "4,4", "startOffsets": "24579,24644", "endColumns": "64,64", "endOffsets": "24639,24704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-pl\\values-pl.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "49578,49661,49700,49750,49813,49856,49953,49996,50066,50113,50186,50232,50288,50388,50452,50511", "endColumns": "82,38,49,62,42,96,42,69,46,72,45,55,99,63,58,57", "endOffsets": "49656,49695,49745,49808,49851,49948,49991,50061,50108,50181,50227,50283,50383,50447,50506,50564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-pl\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "439,440,441,460,461,462,463,464,465,466,467,501,502,503,504,505,506,507,578", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "40230,40276,40329,41891,41958,42093,42211,42339,42393,42448,42558,45455,45499,45585,45627,45665,45727,45815,51316", "endColumns": "45,52,63,66,134,117,127,53,54,109,101,43,85,41,37,61,87,46,59", "endOffsets": "40271,40324,40388,41953,42088,42206,42334,42388,42443,42553,42655,45494,45580,45622,45660,45722,45810,45857,51371"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-pl\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,267,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,268,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,269,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24077,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24134,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24188,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,55,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,52,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,87,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24128,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24182,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24271,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,47,48,51,52,63,64,66,67,68,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,117,118,119,120,121,129,130,131,134,135,136,137,138,139,140,141,142,143,144,145,146,168,169,170,171,174,175,176,177,178,179,180,181,182,183,184,185,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,211,212,213,214,215,217,272,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,298,299,301,303,304,305,307,308,309,310,311,312,313,314,315,316,374,375,376,377,391,392,438,445,446,447,448,449,452,453,454,455,456,457,458,459,468,469,470,471,472,473,474,476,477,478,479,480,481,482,483,484,485,487,488,489,490,491,492,495,496,497,498,499,500,508,509,513,515,516,517,518,519,520,521,522,524,525,526,527,528,529,530,531,535,536,538,541,543,544,545,546,547,566,567,568,569,570,571,574,575,576,577,579,580,581,582,583,584,585,586", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1325,1395,1447,1498,1548,1599,1652,1698,1745,1801,1865,1916,1966,2018,2067,2118,2424,2502,2784,2833,3819,3871,4050,4119,4198,4554,4620,4683,4738,4804,4842,5005,5064,5155,5197,5258,5343,5390,5641,5693,6009,6051,6118,6166,6209,6276,6324,6362,6447,6506,6582,6992,7411,7453,7516,7580,8223,8304,8388,8460,8541,8743,9056,9132,9192,9233,9721,9797,9855,9923,9989,10799,10841,10913,11193,11234,11313,11476,11550,11644,11728,11799,11885,11950,12016,12071,12126,14703,14759,14805,14877,15149,15200,15550,15605,15663,16258,16876,16945,16995,17138,17201,17474,17739,18205,18270,18406,18568,18727,18793,18840,18887,18967,19066,19157,19213,19353,19449,19509,19577,19646,19927,20178,20356,20507,20671,20748,20816,20870,20928,21032,25223,25688,25745,25970,26018,26095,26161,26218,26349,26407,26537,26750,26789,26847,26884,26921,26956,26991,27058,27102,27154,27267,27332,27477,27638,27705,27761,27878,27921,27961,28035,28453,28514,28600,28763,28835,28910,35197,35231,35266,35303,36327,36389,40172,40708,40788,40934,40969,41047,41273,41328,41418,41486,41560,41664,41780,41853,42660,42735,43015,43121,43294,43377,43478,43597,43636,43685,43735,43798,43892,43940,44015,44096,44146,44318,44362,44477,44544,44597,44690,44955,45005,45077,45270,45345,45397,45862,45939,46238,46370,46549,46632,46731,46818,46866,46935,47003,47153,47195,47251,47299,47417,47796,47884,47958,48259,48318,48562,48768,48940,48980,49038,49103,49311,50569,50634,50706,50760,50833,50906,51097,51153,51208,51271,51376,51412,51466,51519,51578,51637,51673,51708", "endColumns": "69,51,50,49,50,52,45,46,55,63,50,49,51,48,50,88,77,55,48,46,51,78,68,78,81,65,62,54,65,37,162,58,90,41,60,84,46,250,51,315,41,66,47,42,66,47,37,84,58,75,409,418,41,62,63,642,80,83,71,80,201,312,75,59,40,59,75,57,67,65,72,41,71,77,40,78,162,73,93,83,70,85,64,65,54,54,62,55,45,71,101,50,349,54,57,594,617,68,49,142,62,272,50,465,64,135,161,158,65,46,46,79,98,90,55,139,95,59,67,68,280,250,177,150,86,76,67,53,57,38,89,65,56,224,47,76,65,56,130,57,129,212,38,57,36,36,34,34,66,43,51,48,64,67,67,66,55,55,42,39,73,417,60,85,162,71,74,35,33,34,36,52,61,68,57,79,145,34,77,62,54,89,67,73,103,115,72,37,74,279,105,172,82,100,42,38,48,49,62,93,47,74,80,49,37,43,114,66,52,92,94,49,71,192,74,51,57,76,54,59,178,82,98,86,47,68,67,71,41,55,47,117,378,87,73,45,58,74,55,51,39,57,64,207,90,64,71,53,72,72,32,55,54,62,44,35,53,52,58,58,35,34,43", "endOffsets": "1390,1442,1493,1543,1594,1647,1693,1740,1796,1860,1911,1961,2013,2062,2113,2202,2497,2553,2828,2875,3866,3945,4114,4193,4275,4615,4678,4733,4799,4837,5000,5059,5150,5192,5253,5338,5385,5636,5688,6004,6046,6113,6161,6204,6271,6319,6357,6442,6501,6577,6987,7406,7448,7511,7575,8218,8299,8383,8455,8536,8738,9051,9127,9187,9228,9288,9792,9850,9918,9984,10057,10836,10908,10986,11229,11308,11471,11545,11639,11723,11794,11880,11945,12011,12066,12121,12184,14754,14800,14872,14974,15195,15545,15600,15658,16253,16871,16940,16990,17133,17196,17469,17520,18200,18265,18401,18563,18722,18788,18835,18882,18962,19061,19152,19208,19348,19444,19504,19572,19641,19922,20173,20351,20502,20589,20743,20811,20865,20923,20962,21117,25284,25740,25965,26013,26090,26156,26213,26344,26402,26532,26745,26784,26842,26879,26916,26951,26986,27053,27097,27149,27198,27327,27395,27540,27700,27756,27812,27916,27956,28030,28448,28509,28595,28758,28830,28905,28941,35226,35261,35298,35351,36384,36453,40225,40783,40929,40964,41042,41105,41323,41413,41481,41555,41659,41775,41848,41886,42730,43010,43116,43289,43372,43473,43516,43631,43680,43730,43793,43887,43935,44010,44091,44141,44179,44357,44472,44539,44592,44685,44780,45000,45072,45265,45340,45392,45450,45934,45989,46293,46544,46627,46726,46813,46861,46930,46998,47070,47190,47246,47294,47412,47791,47879,47953,47999,48313,48388,48613,48815,48975,49033,49098,49306,49397,50629,50701,50755,50828,50901,50934,51148,51203,51266,51311,51407,51461,51514,51573,51632,51668,51703,51747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-pl\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "13441", "endColumns": "139", "endOffsets": "13576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pl\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12451,12555,12723,12845,12955,13106,13231,13342,13581,13752,13861,14036,14164,14323,14484,14553,14619", "endColumns": "103,167,121,109,150,124,110,98,170,108,174,127,158,160,68,65,83", "endOffsets": "12550,12718,12840,12950,13101,13226,13337,13436,13747,13856,14031,14159,14318,14479,14548,14614,14698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "122,123,124,125,126,127,128,534", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "10062,10159,10261,10359,10458,10572,10677,48158", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "10154,10256,10354,10453,10567,10672,10794,48254"}}]}]}