<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/grey">
    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:paddingTop="8dp"
            android:visibility="visible"
            android:layout_marginTop="14dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/othersRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:clipToPadding="false"
                android:layout_marginTop="5dp"
                android:padding="8dp"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="1"/>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/anti_thief_block"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                android:layout_marginTop="0dp"
                android:background="@drawable/white_block_card"
                android:orientation="vertical"
                android:visibility="visible">


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/switch_anti_thief_block"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:background="@drawable/white_block_card"
                    android:visibility="visible"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/switch_anti_thief_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="16dp"
                        android:ellipsize="marquee"
                        android:focusableInTouchMode="true"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:rotation="0"
                        android:singleLine="true"
                        android:text="@string/anti_thief"
                        android:textAlignment="viewStart"
                        android:textColor="?attr/black"
                        android:textSize="24sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/anti_thief_info"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/anti_thief_info"
                        android:layout_width="22sp"
                        android:layout_height="22sp"
                        android:layout_marginStart="5dp"
                        android:scaleType="fitEnd"
                        android:src="@drawable/ic_note"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/anti_thief_spacer"
                        app:layout_constraintStart_toEndOf="@id/switch_anti_thief_title"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/anti_thief_spacer"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintStart_toEndOf="@id/anti_thief_info"
                        app:layout_constraintEnd_toStartOf="@id/switch_enable_anti_thief"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"/>

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/switch_enable_anti_thief"
                        style="@android:style/Widget.Material.CompoundButton.Switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:checked="false"
                        android:thumb="@drawable/switch_thumb"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:track="@drawable/switch_track" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

</ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>