package com.tqhit.battery.one.ads.core;

import com.tqhit.adlib.sdk.analytics.AnalyticsTracker;
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ApplovinInterstitialAdManager_Factory implements Factory<ApplovinInterstitialAdManager> {
  private final Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider;

  private final Provider<ApplovinRewardedAdManager> applovinRewardedAdManagerProvider;

  private final Provider<AnalyticsTracker> analyticsTrackerProvider;

  public ApplovinInterstitialAdManager_Factory(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<ApplovinRewardedAdManager> applovinRewardedAdManagerProvider,
      Provider<AnalyticsTracker> analyticsTrackerProvider) {
    this.remoteConfigHelperProvider = remoteConfigHelperProvider;
    this.applovinRewardedAdManagerProvider = applovinRewardedAdManagerProvider;
    this.analyticsTrackerProvider = analyticsTrackerProvider;
  }

  @Override
  public ApplovinInterstitialAdManager get() {
    return newInstance(remoteConfigHelperProvider.get(), applovinRewardedAdManagerProvider.get(), analyticsTrackerProvider.get());
  }

  public static ApplovinInterstitialAdManager_Factory create(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<ApplovinRewardedAdManager> applovinRewardedAdManagerProvider,
      Provider<AnalyticsTracker> analyticsTrackerProvider) {
    return new ApplovinInterstitialAdManager_Factory(remoteConfigHelperProvider, applovinRewardedAdManagerProvider, analyticsTrackerProvider);
  }

  public static ApplovinInterstitialAdManager newInstance(
      FirebaseRemoteConfigHelper remoteConfigHelper,
      ApplovinRewardedAdManager applovinRewardedAdManager, AnalyticsTracker analyticsTracker) {
    return new ApplovinInterstitialAdManager(remoteConfigHelper, applovinRewardedAdManager, analyticsTracker);
  }
}
