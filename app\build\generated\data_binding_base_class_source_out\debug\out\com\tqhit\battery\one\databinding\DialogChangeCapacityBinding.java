// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogChangeCapacityBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final Button cancelChangeCapacity;

  @NonNull
  public final Button confirmChangeCapacity;

  @NonNull
  public final Button exitChangeCapacity;

  @NonNull
  public final LinearLayout sdfgsdfg1;

  @NonNull
  public final RelativeLayout strelka;

  @NonNull
  public final TextInputEditText textInputEdit;

  @NonNull
  public final TextView textView20;

  @NonNull
  public final LinearLayout w22;

  private DialogChangeCapacityBinding(@NonNull RelativeLayout rootView,
      @NonNull Button cancelChangeCapacity, @NonNull Button confirmChangeCapacity,
      @NonNull Button exitChangeCapacity, @NonNull LinearLayout sdfgsdfg1,
      @NonNull RelativeLayout strelka, @NonNull TextInputEditText textInputEdit,
      @NonNull TextView textView20, @NonNull LinearLayout w22) {
    this.rootView = rootView;
    this.cancelChangeCapacity = cancelChangeCapacity;
    this.confirmChangeCapacity = confirmChangeCapacity;
    this.exitChangeCapacity = exitChangeCapacity;
    this.sdfgsdfg1 = sdfgsdfg1;
    this.strelka = strelka;
    this.textInputEdit = textInputEdit;
    this.textView20 = textView20;
    this.w22 = w22;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogChangeCapacityBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogChangeCapacityBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_change_capacity, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogChangeCapacityBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cancel_change_capacity;
      Button cancelChangeCapacity = ViewBindings.findChildViewById(rootView, id);
      if (cancelChangeCapacity == null) {
        break missingId;
      }

      id = R.id.confirm_change_capacity;
      Button confirmChangeCapacity = ViewBindings.findChildViewById(rootView, id);
      if (confirmChangeCapacity == null) {
        break missingId;
      }

      id = R.id.exit_change_capacity;
      Button exitChangeCapacity = ViewBindings.findChildViewById(rootView, id);
      if (exitChangeCapacity == null) {
        break missingId;
      }

      id = R.id.sdfgsdfg1;
      LinearLayout sdfgsdfg1 = ViewBindings.findChildViewById(rootView, id);
      if (sdfgsdfg1 == null) {
        break missingId;
      }

      id = R.id.strelka;
      RelativeLayout strelka = ViewBindings.findChildViewById(rootView, id);
      if (strelka == null) {
        break missingId;
      }

      id = R.id.textInputEdit;
      TextInputEditText textInputEdit = ViewBindings.findChildViewById(rootView, id);
      if (textInputEdit == null) {
        break missingId;
      }

      id = R.id.textView20;
      TextView textView20 = ViewBindings.findChildViewById(rootView, id);
      if (textView20 == null) {
        break missingId;
      }

      id = R.id.w22;
      LinearLayout w22 = ViewBindings.findChildViewById(rootView, id);
      if (w22 == null) {
        break missingId;
      }

      return new DialogChangeCapacityBinding((RelativeLayout) rootView, cancelChangeCapacity,
          confirmChangeCapacity, exitChangeCapacity, sdfgsdfg1, strelka, textInputEdit, textView20,
          w22);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
