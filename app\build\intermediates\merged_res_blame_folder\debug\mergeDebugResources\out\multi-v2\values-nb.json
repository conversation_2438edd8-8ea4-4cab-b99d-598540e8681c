{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "55,56,57,58,59,60,61,306", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3965,4059,4161,4258,4357,4465,4571,26182", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "4054,4156,4253,4352,4460,4566,4686,26278"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,257,328,408,486,580,677", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "126,188,252,323,403,481,575,672,743"}, "to": {"startLines": "116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9954,10030,10092,10156,10227,10307,10385,10479,10576", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "10025,10087,10151,10222,10302,10380,10474,10571,10642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-nb\\values-nb.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3340,3405", "endColumns": "64,65", "endOffsets": "3400,3466"}, "to": {"startLines": "136,137", "startColumns": "4,4", "startOffsets": "11328,11393", "endColumns": "64,65", "endOffsets": "11388,11454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,87", "endOffsets": "140,228"}, "to": {"startLines": "311,312", "startColumns": "4,4", "startOffsets": "26716,26806", "endColumns": "89,87", "endOffsets": "26801,26889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,298", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "928,1031,1126,1240,1326,1426,1539,1616,1691,1782,1875,1969,2063,2163,2256,2351,2449,2540,2631,2709,2812,2910,3006,3110,3209,3310,3463,25583", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "1026,1121,1235,1321,1421,1534,1611,1686,1777,1870,1964,2058,2158,2251,2346,2444,2535,2626,2704,2807,2905,3001,3105,3204,3305,3458,3555,25658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,113", "endOffsets": "156,270"}, "to": {"startLines": "271,272", "startColumns": "4,4", "startOffsets": "23403,23509", "endColumns": "105,113", "endOffsets": "23504,23618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,482,672,758,843,921,1007,1095,1170,1234,1327,1418,1491,1558,1624,1694,1803,1913,2020,2093,2176,2252,2325,2428,2530,2594,2659,2712,2770,2818,2879,2949,3017,3083,3153,3217,3276,3340,3392,3452,3526,3600,3653", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,108,109,106,72,82,75,72,102,101,63,64,52,57,47,60,69,67,65,69,63,58,63,51,59,73,73,52,64", "endOffsets": "280,477,667,753,838,916,1002,1090,1165,1229,1322,1413,1486,1553,1619,1689,1798,1908,2015,2088,2171,2247,2320,2423,2525,2589,2654,2707,2765,2813,2874,2944,3012,3078,3148,3212,3271,3335,3387,3447,3521,3595,3648,3713"}, "to": {"startLines": "2,11,15,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,125,126,127,128,129,130,131,132,133,134,135,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,577,7967,8053,8138,8216,8302,8390,8465,8529,8622,8713,8786,8853,8919,8989,9098,9208,9315,9388,9471,9547,9620,9723,9825,9889,10647,10700,10758,10806,10867,10937,11005,11071,11141,11205,11264,11459,11511,11571,11645,11719,11772", "endLines": "10,14,18,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,125,126,127,128,129,130,131,132,133,134,135,138,139,140,141,142,143", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,108,109,106,72,82,75,72,102,101,63,64,52,57,47,60,69,67,65,69,63,58,63,51,59,73,73,52,64", "endOffsets": "375,572,762,8048,8133,8211,8297,8385,8460,8524,8617,8708,8781,8848,8914,8984,9093,9203,9310,9383,9466,9542,9615,9718,9820,9884,9949,10695,10753,10801,10862,10932,11000,11066,11136,11200,11259,11323,11506,11566,11640,11714,11767,11832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1025,1089,1181,1249,1309,1396,1460,1522,1586,1654,1719,1773,1882,1940,2002,2056,2131,2251,2333,2410,2500,2584,2664,2798,2876,2956,3079,3167,3245,3299,3350,3416,3484,3558,3629,3705,3776,3854,3924,3994,4094,4183,4261,4349,4439,4511,4583,4667,4718,4796,4862,4943,5026,5088,5152,5215,5284,5384,5488,5581,5681,5739,5794,5872,5956,6034", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "256,333,406,493,581,661,760,879,961,1020,1084,1176,1244,1304,1391,1455,1517,1581,1649,1714,1768,1877,1935,1997,2051,2126,2246,2328,2405,2495,2579,2659,2793,2871,2951,3074,3162,3240,3294,3345,3411,3479,3553,3624,3700,3771,3849,3919,3989,4089,4178,4256,4344,4434,4506,4578,4662,4713,4791,4857,4938,5021,5083,5147,5210,5279,5379,5483,5576,5676,5734,5789,5867,5951,6029,6101"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,90,91,145,149,152,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,287,299,300,302", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "767,3560,3637,3710,3797,3885,4691,4790,4909,7844,7903,11924,12326,12558,18758,18845,18909,18971,19035,19103,19168,19222,19331,19389,19451,19505,19580,19700,19782,19859,19949,20033,20113,20247,20325,20405,20528,20616,20694,20748,20799,20865,20933,21007,21078,21154,21225,21303,21373,21443,21543,21632,21710,21798,21888,21960,22032,22116,22167,22245,22311,22392,22475,22537,22601,22664,22733,22833,22937,23030,23130,23188,24822,25663,25747,25895", "endLines": "22,50,51,52,53,54,62,63,64,90,91,145,149,152,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,287,299,300,302", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "923,3632,3705,3792,3880,3960,4785,4904,4986,7898,7962,12011,12389,12613,18840,18904,18966,19030,19098,19163,19217,19326,19384,19446,19500,19575,19695,19777,19854,19944,20028,20108,20242,20320,20400,20523,20611,20689,20743,20794,20860,20928,21002,21073,21149,21220,21298,21368,21438,21538,21627,21705,21793,21883,21955,22027,22111,22162,22240,22306,22387,22470,22532,22596,22659,22728,22828,22932,23025,23125,23183,23238,24895,25742,25820,25962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6174", "endColumns": "129", "endOffsets": "6299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "86,146,147,148", "startColumns": "4,4,4,4", "startOffsets": "7449,12016,12117,12229", "endColumns": "109,100,111,96", "endOffsets": "7554,12112,12224,12321"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,979,1061,1131,1205,1276,1346,1423,1490", "endColumns": "92,80,96,99,87,75,87,88,81,79,81,69,73,70,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,974,1056,1126,1200,1271,1341,1418,1485,1605"}, "to": {"startLines": "65,66,87,88,89,150,151,273,274,289,290,301,303,304,305,308,309,310", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4991,5084,7559,7656,7756,12394,12470,23623,23712,25042,25122,25825,25967,26041,26112,26452,26529,26596", "endColumns": "92,80,96,99,87,75,87,88,81,79,81,69,73,70,69,76,66,119", "endOffsets": "5079,5160,7651,7751,7839,12465,12553,23707,23789,25117,25199,25890,26036,26107,26177,26524,26591,26711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,292,347,411,480,570,632,738,842,954,1004,1060,1167,1253,1292,1369,1402,1435,1488,1565,1604", "endColumns": "41,50,54,63,68,89,61,105,103,111,49,55,106,85,38,76,32,32,52,76,38,55", "endOffsets": "240,291,346,410,479,569,631,737,841,953,1003,1059,1166,1252,1291,1368,1401,1434,1487,1564,1603,1659"}, "to": {"startLines": "268,269,270,276,277,278,279,280,281,282,283,284,285,286,291,292,293,294,295,296,297,315", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "23243,23289,23344,23872,23940,24013,24107,24173,24283,24391,24507,24561,24621,24732,25204,25247,25328,25365,25402,25459,25540,27049", "endColumns": "45,54,58,67,72,93,65,109,107,115,53,59,110,89,42,80,36,36,56,80,42,59", "endOffsets": "23284,23339,23398,23935,24008,24102,24168,24278,24386,24502,24556,24616,24727,24817,25242,25323,25360,25397,25454,25535,25578,27104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,482,651,730", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "170,257,335,477,646,725,801"}, "to": {"startLines": "85,144,275,288,307,313,314", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7379,11837,23794,24900,26283,26894,26973", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "7444,11919,23867,25037,26447,26968,27044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,394,507,606,700,811,955,1077,1227,1311,1411,1500,1594,1701,1819,1924,2051,2173,2306,2473,2600,2716,2837,2958,3048,3146,3265,3396,3497,3607,3710,3844,3985,4090,4188,4268,4362,4453,4562,4646,4730,4841,4921,5005,5106,5205,5296,5396,5484,5589,5691,5796,5913,5993,6096", "endColumns": "116,114,106,112,98,93,110,143,121,149,83,99,88,93,106,117,104,126,121,132,166,126,115,120,120,89,97,118,130,100,109,102,133,140,104,97,79,93,90,108,83,83,110,79,83,100,98,90,99,87,104,101,104,116,79,102,98", "endOffsets": "167,282,389,502,601,695,806,950,1072,1222,1306,1406,1495,1589,1696,1814,1919,2046,2168,2301,2468,2595,2711,2832,2953,3043,3141,3260,3391,3492,3602,3705,3839,3980,4085,4183,4263,4357,4448,4557,4641,4725,4836,4916,5000,5101,5200,5291,5391,5479,5584,5686,5791,5908,5988,6091,6190"}, "to": {"startLines": "153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12618,12735,12850,12957,13070,13169,13263,13374,13518,13640,13790,13874,13974,14063,14157,14264,14382,14487,14614,14736,14869,15036,15163,15279,15400,15521,15611,15709,15828,15959,16060,16170,16273,16407,16548,16653,16751,16831,16925,17016,17125,17209,17293,17404,17484,17568,17669,17768,17859,17959,18047,18152,18254,18359,18476,18556,18659", "endColumns": "116,114,106,112,98,93,110,143,121,149,83,99,88,93,106,117,104,126,121,132,166,126,115,120,120,89,97,118,130,100,109,102,133,140,104,97,79,93,90,108,83,83,110,79,83,100,98,90,99,87,104,101,104,116,79,102,98", "endOffsets": "12730,12845,12952,13065,13164,13258,13369,13513,13635,13785,13869,13969,14058,14152,14259,14377,14482,14609,14731,14864,15031,15158,15274,15395,15516,15606,15704,15823,15954,16055,16165,16268,16402,16543,16648,16746,16826,16920,17011,17120,17204,17288,17399,17479,17563,17664,17763,17854,17954,18042,18147,18249,18354,18471,18551,18654,18753"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5165,5271,5430,5556,5665,5821,5951,6071,6304,6458,6565,6726,6854,6996,7172,7239,7301", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "5266,5425,5551,5660,5816,5946,6066,6169,6453,6560,6721,6849,6991,7167,7234,7296,7374"}}]}]}