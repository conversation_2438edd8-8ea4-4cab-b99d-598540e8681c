<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_others" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_others.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_others_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="124" endOffset="51"/></Target><Target id="@+id/scrollView" view="ScrollView"><Expressions/><location startLine="6" startOffset="4" endLine="122" endOffset="12"/></Target><Target id="@+id/othersRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="28" startOffset="12" endLine="37" endOffset="34"/></Target><Target id="@+id/anti_thief_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="40" startOffset="12" endLine="118" endOffset="63"/></Target><Target id="@+id/switch_anti_thief_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="51" startOffset="16" endLine="116" endOffset="67"/></Target><Target id="@+id/switch_anti_thief_title" view="TextView"><Expressions/><location startLine="61" startOffset="20" endLine="81" endOffset="67"/></Target><Target id="@+id/anti_thief_info" view="ImageView"><Expressions/><location startLine="83" startOffset="20" endLine="93" endOffset="67"/></Target><Target id="@+id/anti_thief_spacer" view="View"><Expressions/><location startLine="95" startOffset="20" endLine="102" endOffset="72"/></Target><Target id="@+id/switch_enable_anti_thief" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="104" startOffset="20" endLine="115" endOffset="60"/></Target></Targets></Layout>