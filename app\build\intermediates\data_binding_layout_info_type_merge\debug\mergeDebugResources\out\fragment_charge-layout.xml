<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_charge" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_charge.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView" rootNodeViewId="@+id/scroll_view"><Targets><Target id="@+id/scroll_view" tag="layout/fragment_charge_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="2335" endOffset="39"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="5" startOffset="4" endLine="2334" endOffset="18"/></Target><Target id="@+id/native_ad" tag="binding_1" include="layout_native_ads"><Expressions/><location startLine="202" startOffset="8" endLine="204" endOffset="48"/></Target><Target id="@+id/sale_container" view="LinearLayout"><Expressions/><location startLine="10" startOffset="8" endLine="15" endOffset="48"/></Target><Target id="@+id/promo_container" view="LinearLayout"><Expressions/><location startLine="16" startOffset="8" endLine="21" endOffset="48"/></Target><Target id="@+id/charge_up" view="LinearLayout"><Expressions/><location startLine="22" startOffset="8" endLine="184" endOffset="22"/></Target><Target id="@+id/percent_layout" view="RelativeLayout"><Expressions/><location startLine="29" startOffset="12" endLine="68" endOffset="28"/></Target><Target id="@+id/pus" view="RelativeLayout"><Expressions/><location startLine="40" startOffset="16" endLine="67" endOffset="32"/></Target><Target id="@+id/text_percent" view="TextView"><Expressions/><location startLine="45" startOffset="20" endLine="52" endOffset="52"/></Target><Target id="@+id/charge_prog_bar_percent" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="53" startOffset="20" endLine="66" endOffset="49"/></Target><Target id="@+id/time_num" view="LinearLayout"><Expressions/><location startLine="69" startOffset="12" endLine="183" endOffset="26"/></Target><Target id="@+id/day_block" view="LinearLayout"><Expressions/><location startLine="100" startOffset="16" endLine="127" endOffset="30"/></Target><Target id="@+id/imageView18" view="ImageView"><Expressions/><location startLine="113" startOffset="20" endLine="118" endOffset="57"/></Target><Target id="@+id/text_time_day" view="TextView"><Expressions/><location startLine="119" startOffset="20" endLine="126" endOffset="52"/></Target><Target id="@+id/all_block" view="LinearLayout"><Expressions/><location startLine="128" startOffset="16" endLine="155" endOffset="30"/></Target><Target id="@+id/text_time_night" view="TextView"><Expressions/><location startLine="147" startOffset="20" endLine="154" endOffset="52"/></Target><Target id="@+id/night_block" view="LinearLayout"><Expressions/><location startLine="156" startOffset="16" endLine="182" endOffset="30"/></Target><Target id="@+id/text_time_daynight" view="TextView"><Expressions/><location startLine="174" startOffset="20" endLine="181" endOffset="52"/></Target><Target id="@+id/not_charging_message" view="TextView"><Expressions/><location startLine="187" startOffset="8" endLine="200" endOffset="39"/></Target><Target id="@+id/battery_alarm_btn" view="TextView"><Expressions/><location startLine="219" startOffset="12" endLine="237" endOffset="67"/></Target><Target id="@+id/battery_wear_info" view="ImageView"><Expressions/><location startLine="238" startOffset="12" endLine="249" endOffset="66"/></Target><Target id="@+id/test_view" view="TextView"><Expressions/><location startLine="250" startOffset="12" endLine="264" endOffset="58"/></Target><Target id="@+id/da_view" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="265" startOffset="12" endLine="354" endOffset="63"/></Target><Target id="@+id/damage_bar_seekwhite" view="ProgressBar"><Expressions/><location startLine="287" startOffset="16" endLine="302" endOffset="69"/></Target><Target id="@+id/damage_bar_percent" view="ProgressBar"><Expressions/><location startLine="303" startOffset="16" endLine="318" endOffset="69"/></Target><Target id="@+id/seekBar" view="SeekBar"><Expressions/><location startLine="319" startOffset="16" endLine="332" endOffset="78"/></Target><Target id="@+id/var_damage_up4" view="TextView"><Expressions/><location startLine="333" startOffset="16" endLine="353" endOffset="62"/></Target><Target id="@+id/cs_text" view="TextView"><Expressions/><location startLine="369" startOffset="12" endLine="382" endOffset="58"/></Target><Target id="@+id/cs_2_1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="383" startOffset="12" endLine="469" endOffset="63"/></Target><Target id="@+id/c_text_ni_power" view="TextView"><Expressions/><location startLine="390" startOffset="16" endLine="400" endOffset="62"/></Target><Target id="@+id/progressBar_power_minus" view="ProgressBar"><Expressions/><location startLine="401" startOffset="16" endLine="412" endOffset="69"/></Target><Target id="@+id/progressBar_power" view="ProgressBar"><Expressions/><location startLine="413" startOffset="16" endLine="428" endOffset="69"/></Target><Target id="@+id/val_power_charging" view="TextView"><Expressions/><location startLine="429" startOffset="16" endLine="441" endOffset="62"/></Target><Target id="@+id/p_text" view="TextView"><Expressions/><location startLine="442" startOffset="16" endLine="451" endOffset="62"/></Target><Target id="@+id/textedd" view="TextView"><Expressions/><location startLine="452" startOffset="16" endLine="468" endOffset="62"/></Target><Target id="@+id/cs_1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="470" startOffset="12" endLine="536" endOffset="63"/></Target><Target id="@+id/v_text" view="TextView"><Expressions/><location startLine="477" startOffset="16" endLine="486" endOffset="62"/></Target><Target id="@+id/progressBar_voltage" view="ProgressBar"><Expressions/><location startLine="487" startOffset="16" endLine="502" endOffset="69"/></Target><Target id="@+id/val_voltage" view="TextView"><Expressions/><location startLine="503" startOffset="16" endLine="518" endOffset="62"/></Target><Target id="@+id/text_remain_var22" view="TextView"><Expressions/><location startLine="519" startOffset="16" endLine="535" endOffset="62"/></Target><Target id="@+id/cs_2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="537" startOffset="12" endLine="623" endOffset="63"/></Target><Target id="@+id/c_text_ni_ampere" view="TextView"><Expressions/><location startLine="544" startOffset="16" endLine="554" endOffset="62"/></Target><Target id="@+id/progressBar_current_minus" view="ProgressBar"><Expressions/><location startLine="555" startOffset="16" endLine="566" endOffset="69"/></Target><Target id="@+id/progressBar_current" view="ProgressBar"><Expressions/><location startLine="567" startOffset="16" endLine="582" endOffset="69"/></Target><Target id="@+id/val_currrent_charging" view="TextView"><Expressions/><location startLine="583" startOffset="16" endLine="595" endOffset="62"/></Target><Target id="@+id/c_text" view="TextView"><Expressions/><location startLine="596" startOffset="16" endLine="605" endOffset="62"/></Target><Target id="@+id/text_remain_var33" view="TextView"><Expressions/><location startLine="606" startOffset="16" endLine="622" endOffset="62"/></Target><Target id="@+id/cs_3" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="624" startOffset="12" endLine="687" endOffset="63"/></Target><Target id="@+id/progressBar_temp" view="ProgressBar"><Expressions/><location startLine="631" startOffset="16" endLine="646" endOffset="69"/></Target><Target id="@+id/val_temp2" view="TextView"><Expressions/><location startLine="647" startOffset="16" endLine="659" endOffset="62"/></Target><Target id="@+id/val_temp_text" view="TextView"><Expressions/><location startLine="660" startOffset="16" endLine="669" endOffset="62"/></Target><Target id="@+id/text_remain_var44" view="TextView"><Expressions/><location startLine="670" startOffset="16" endLine="686" endOffset="62"/></Target><Target id="@+id/cs_4" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="688" startOffset="12" endLine="763" endOffset="63"/></Target><Target id="@+id/progressBar_averagespeed_grey" view="ProgressBar"><Expressions/><location startLine="695" startOffset="16" endLine="710" endOffset="69"/></Target><Target id="@+id/progressBar_averagespeed" view="ProgressBar"><Expressions/><location startLine="711" startOffset="16" endLine="722" endOffset="69"/></Target><Target id="@+id/speedcharge_percent_text" view="TextView"><Expressions/><location startLine="723" startOffset="16" endLine="732" endOffset="62"/></Target><Target id="@+id/val_average_speed" view="TextView"><Expressions/><location startLine="733" startOffset="16" endLine="745" endOffset="62"/></Target><Target id="@+id/text_remain_var55" view="TextView"><Expressions/><location startLine="746" startOffset="16" endLine="762" endOffset="62"/></Target><Target id="@+id/remaining_table" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="765" startOffset="8" endLine="959" endOffset="59"/></Target><Target id="@+id/rem_text" view="TextView"><Expressions/><location startLine="779" startOffset="12" endLine="792" endOffset="58"/></Target><Target id="@+id/rem_1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="793" startOffset="12" endLine="860" endOffset="63"/></Target><Target id="@+id/progressBar_remain_to_var2" view="ProgressBar"><Expressions/><location startLine="800" startOffset="16" endLine="815" endOffset="69"/></Target><Target id="@+id/progressBar_remain_to_var" view="ProgressBar"><Expressions/><location startLine="816" startOffset="16" endLine="827" endOffset="69"/></Target><Target id="@+id/val_remain_to_var" view="TextView"><Expressions/><location startLine="828" startOffset="16" endLine="840" endOffset="62"/></Target><Target id="@+id/text_remain_var" view="TextView"><Expressions/><location startLine="841" startOffset="16" endLine="859" endOffset="62"/></Target><Target id="@+id/rem_2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="861" startOffset="12" endLine="958" endOffset="63"/></Target><Target id="@+id/progressBar_remain_to_100_2" view="ProgressBar"><Expressions/><location startLine="868" startOffset="16" endLine="883" endOffset="69"/></Target><Target id="@+id/progressBar_remain_to_100" view="ProgressBar"><Expressions/><location startLine="884" startOffset="16" endLine="895" endOffset="69"/></Target><Target id="@+id/val_remain_to_100" view="TextView"><Expressions/><location startLine="896" startOffset="16" endLine="908" endOffset="62"/></Target><Target id="@+id/rem_val_barrier" view="androidx.constraintlayout.widget.Barrier"><Expressions/><location startLine="909" startOffset="16" endLine="914" endOffset="98"/></Target><Target id="@+id/text_remain_to_100_charge" view="TextView"><Expressions/><location startLine="915" startOffset="16" endLine="933" endOffset="62"/></Target><Target id="@+id/text_remain_to_100" view="TextView"><Expressions/><location startLine="934" startOffset="16" endLine="951" endOffset="62"/></Target><Target id="@+id/rem_barrier" view="androidx.constraintlayout.widget.Barrier"><Expressions/><location startLine="952" startOffset="16" endLine="957" endOffset="117"/></Target><Target id="@+id/current_session_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="960" startOffset="8" endLine="1451" endOffset="59"/></Target><Target id="@+id/charge_session_info" view="ImageView"><Expressions/><location startLine="974" startOffset="12" endLine="985" endOffset="64"/></Target><Target id="@+id/st_text" view="TextView"><Expressions/><location startLine="986" startOffset="12" endLine="1001" endOffset="58"/></Target><Target id="@+id/s_6" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1002" startOffset="12" endLine="1073" endOffset="63"/></Target><Target id="@+id/text_speed_charge_day_session2" view="TextView"><Expressions/><location startLine="1040" startOffset="16" endLine="1049" endOffset="95"/></Target><Target id="@+id/text_percent_charge_day_session" view="TextView"><Expressions/><location startLine="1050" startOffset="16" endLine="1059" endOffset="68"/></Target><Target id="@+id/te77" view="TextView"><Expressions/><location startLine="1060" startOffset="16" endLine="1072" endOffset="62"/></Target><Target id="@+id/s_5" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1074" startOffset="12" endLine="1145" endOffset="63"/></Target><Target id="@+id/text_speed_charge_night_session" view="TextView"><Expressions/><location startLine="1112" startOffset="16" endLine="1121" endOffset="97"/></Target><Target id="@+id/text_percent_charge_night_session" view="TextView"><Expressions/><location startLine="1122" startOffset="16" endLine="1131" endOffset="68"/></Target><Target id="@+id/te88" view="TextView"><Expressions/><location startLine="1132" startOffset="16" endLine="1144" endOffset="62"/></Target><Target id="@+id/s_3" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1146" startOffset="12" endLine="1222" endOffset="63"/></Target><Target id="@+id/text_speed_charge_all_session" view="TextView"><Expressions/><location startLine="1185" startOffset="16" endLine="1198" endOffset="95"/></Target><Target id="@+id/text_percent_charge_all_session" view="TextView"><Expressions/><location startLine="1199" startOffset="16" endLine="1208" endOffset="68"/></Target><Target id="@+id/te99" view="TextView"><Expressions/><location startLine="1209" startOffset="16" endLine="1221" endOffset="62"/></Target><Target id="@+id/s_4" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1223" startOffset="12" endLine="1316" endOffset="63"/></Target><Target id="@+id/textView_percent" view="TextView"><Expressions/><location startLine="1238" startOffset="16" endLine="1248" endOffset="95"/></Target><Target id="@+id/charge_session_percent" view="TextView"><Expressions/><location startLine="1249" startOffset="16" endLine="1263" endOffset="79"/></Target><Target id="@+id/textView9" view="TextView"><Expressions/><location startLine="1264" startOffset="16" endLine="1278" endOffset="92"/></Target><Target id="@+id/text_speed_charge_day_session" view="TextView"><Expressions/><location startLine="1279" startOffset="16" endLine="1292" endOffset="96"/></Target><Target id="@+id/text_percent_charge_session_last" view="TextView"><Expressions/><location startLine="1293" startOffset="16" endLine="1302" endOffset="67"/></Target><Target id="@+id/te0" view="TextView"><Expressions/><location startLine="1303" startOffset="16" endLine="1315" endOffset="62"/></Target><Target id="@+id/s_2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1317" startOffset="12" endLine="1375" endOffset="63"/></Target><Target id="@+id/textView7" view="TextView"><Expressions/><location startLine="1332" startOffset="16" endLine="1342" endOffset="90"/></Target><Target id="@+id/var_speedcharge_percent_now" view="TextView"><Expressions/><location startLine="1352" startOffset="16" endLine="1361" endOffset="71"/></Target><Target id="@+id/text226" view="TextView"><Expressions/><location startLine="1362" startOffset="16" endLine="1374" endOffset="62"/></Target><Target id="@+id/s_1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1376" startOffset="12" endLine="1432" endOffset="63"/></Target><Target id="@+id/time_charge_session_start" view="TextView"><Expressions/><location startLine="1391" startOffset="16" endLine="1404" endOffset="92"/></Target><Target id="@+id/text_fulltime_charge_session" view="TextView"><Expressions/><location startLine="1405" startOffset="16" endLine="1418" endOffset="73"/></Target><Target id="@+id/textView6" view="TextView"><Expressions/><location startLine="1419" startOffset="16" endLine="1431" endOffset="62"/></Target><Target id="@+id/reset_session_charge_button" view="TextView"><Expressions/><location startLine="1433" startOffset="12" endLine="1450" endOffset="63"/></Target><Target id="@+id/amperage_table" view="RelativeLayout"><Expressions/><location startLine="1452" startOffset="8" endLine="2014" endOffset="24"/></Target><Target id="@+id/selector_amperage" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1463" startOffset="12" endLine="1507" endOffset="63"/></Target><Target id="@+id/btn_selector1" view="TextView"><Expressions/><location startLine="1471" startOffset="16" endLine="1487" endOffset="62"/></Target><Target id="@+id/btn_selector2" view="TextView"><Expressions/><location startLine="1488" startOffset="16" endLine="1506" endOffset="62"/></Target><Target id="@+id/percent_graph_change" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1508" startOffset="12" endLine="1570" endOffset="63"/></Target><Target id="@+id/btn1" view="TextView"><Expressions/><location startLine="1516" startOffset="16" endLine="1532" endOffset="62"/></Target><Target id="@+id/btn2" view="TextView"><Expressions/><location startLine="1533" startOffset="16" endLine="1550" endOffset="62"/></Target><Target id="@+id/btn3" view="TextView"><Expressions/><location startLine="1551" startOffset="16" endLine="1569" endOffset="62"/></Target><Target id="@+id/wear_rate_percent" view="TextView"><Expressions/><location startLine="1571" startOffset="12" endLine="1584" endOffset="48"/></Target><Target id="@+id/sdfsd" view="LinearLayout"><Expressions/><location startLine="1585" startOffset="12" endLine="1608" endOffset="26"/></Target><Target id="@+id/under_graph_percent" view="LinearLayout"><Expressions/><location startLine="1609" startOffset="12" endLine="1738" endOffset="26"/></Target><Target id="@+id/day_7_percent" view="TextView"><Expressions/><location startLine="1632" startOffset="20" endLine="1639" endOffset="52"/></Target><Target id="@+id/day_6_percent" view="TextView"><Expressions/><location startLine="1647" startOffset="20" endLine="1654" endOffset="52"/></Target><Target id="@+id/day_5_percent" view="TextView"><Expressions/><location startLine="1662" startOffset="20" endLine="1669" endOffset="52"/></Target><Target id="@+id/day_4_percent" view="TextView"><Expressions/><location startLine="1677" startOffset="20" endLine="1684" endOffset="52"/></Target><Target id="@+id/day_3_percent" view="TextView"><Expressions/><location startLine="1692" startOffset="20" endLine="1699" endOffset="52"/></Target><Target id="@+id/day_2_percent" view="TextView"><Expressions/><location startLine="1707" startOffset="20" endLine="1714" endOffset="52"/></Target><Target id="@+id/day_1_percent" view="TextView"><Expressions/><location startLine="1722" startOffset="20" endLine="1729" endOffset="52"/></Target><Target id="@+id/graph_percent" view="RelativeLayout"><Expressions/><location startLine="1739" startOffset="12" endLine="2013" endOffset="28"/></Target><Target id="@+id/t26" view="TextView"><Expressions/><location startLine="1828" startOffset="24" endLine="1832" endOffset="65"/></Target><Target id="@+id/t1" view="TextView"><Expressions/><location startLine="1842" startOffset="24" endLine="1849" endOffset="47"/></Target><Target id="@+id/t2" view="TextView"><Expressions/><location startLine="1859" startOffset="24" endLine="1866" endOffset="47"/></Target><Target id="@+id/t3" view="TextView"><Expressions/><location startLine="1876" startOffset="24" endLine="1883" endOffset="47"/></Target><Target id="@+id/t4" view="TextView"><Expressions/><location startLine="1893" startOffset="24" endLine="1900" endOffset="47"/></Target><Target id="@+id/t5" view="TextView"><Expressions/><location startLine="1910" startOffset="24" endLine="1917" endOffset="47"/></Target><Target id="@+id/t6" view="TextView"><Expressions/><location startLine="1927" startOffset="24" endLine="1934" endOffset="47"/></Target><Target id="@+id/t7" view="TextView"><Expressions/><location startLine="1944" startOffset="24" endLine="1951" endOffset="47"/></Target><Target id="@+id/t8" view="TextView"><Expressions/><location startLine="1961" startOffset="24" endLine="1968" endOffset="47"/></Target><Target id="@+id/t9" view="TextView"><Expressions/><location startLine="1978" startOffset="24" endLine="1985" endOffset="47"/></Target><Target id="@+id/chart1_percent" view="RelativeLayout"><Expressions/><location startLine="2000" startOffset="16" endLine="2012" endOffset="32"/></Target><Target id="@+id/chart_percent" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="2007" startOffset="20" endLine="2011" endOffset="48"/></Target><Target id="@+id/indent_down" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2015" startOffset="8" endLine="2301" endOffset="59"/></Target><Target id="@+id/i_text" view="TextView"><Expressions/><location startLine="2029" startOffset="12" endLine="2045" endOffset="58"/></Target><Target id="@+id/charge_avg_info" view="ImageView"><Expressions/><location startLine="2046" startOffset="12" endLine="2057" endOffset="63"/></Target><Target id="@+id/i3" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2058" startOffset="12" endLine="2138" endOffset="63"/></Target><Target id="@+id/mah_3" view="TextView"><Expressions/><location startLine="2066" startOffset="16" endLine="2077" endOffset="62"/></Target><Target id="@+id/text_speed_charge_all" view="TextView"><Expressions/><location startLine="2078" startOffset="16" endLine="2091" endOffset="62"/></Target><Target id="@+id/per_3" view="TextView"><Expressions/><location startLine="2092" startOffset="16" endLine="2102" endOffset="86"/></Target><Target id="@+id/text_percent_charge_all" view="TextView"><Expressions/><location startLine="2103" startOffset="16" endLine="2116" endOffset="62"/></Target><Target id="@+id/i2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2139" startOffset="12" endLine="2219" endOffset="63"/></Target><Target id="@+id/mah_2" view="TextView"><Expressions/><location startLine="2147" startOffset="16" endLine="2158" endOffset="62"/></Target><Target id="@+id/text_speed_charge_night" view="TextView"><Expressions/><location startLine="2159" startOffset="16" endLine="2172" endOffset="62"/></Target><Target id="@+id/per_2" view="TextView"><Expressions/><location startLine="2173" startOffset="16" endLine="2183" endOffset="88"/></Target><Target id="@+id/text_percent_charge_night" view="TextView"><Expressions/><location startLine="2184" startOffset="16" endLine="2197" endOffset="62"/></Target><Target id="@+id/i1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2220" startOffset="12" endLine="2300" endOffset="63"/></Target><Target id="@+id/mah_1" view="TextView"><Expressions/><location startLine="2228" startOffset="16" endLine="2239" endOffset="62"/></Target><Target id="@+id/text_speed_charge_day" view="TextView"><Expressions/><location startLine="2240" startOffset="16" endLine="2253" endOffset="62"/></Target><Target id="@+id/per_1" view="TextView"><Expressions/><location startLine="2254" startOffset="16" endLine="2264" endOffset="86"/></Target><Target id="@+id/text_percent_charge_day" view="TextView"><Expressions/><location startLine="2265" startOffset="16" endLine="2278" endOffset="62"/></Target><Target id="@+id/update_view" view="LinearLayout"><Expressions/><location startLine="2305" startOffset="8" endLine="2333" endOffset="22"/></Target><Target id="@+id/update_view_btn" view="TextView"><Expressions/><location startLine="2315" startOffset="12" endLine="2332" endOffset="47"/></Target></Targets></Layout>