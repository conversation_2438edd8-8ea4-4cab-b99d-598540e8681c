<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="section_charge_current_session" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\section_charge_current_session.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@+id/charge_current_session_root"><Targets><Target id="@+id/charge_current_session_root" tag="layout/section_charge_current_session_0" view="LinearLayout"><Expressions/><location startLine="12" startOffset="0" endLine="257" endOffset="14"/></Target><Target id="@+id/charge_session_info_button" view="ImageView"><Expressions/><location startLine="38" startOffset="8" endLine="43" endOffset="62"/></Target><Target id="@+id/val_session_duration" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="71" endOffset="37"/></Target><Target id="@+id/val_current_rate" view="TextView"><Expressions/><location startLine="87" startOffset="8" endLine="93" endOffset="37"/></Target><Target id="@+id/val_avg_speed_screen_on" view="TextView"><Expressions/><location startLine="109" startOffset="8" endLine="115" endOffset="37"/></Target><Target id="@+id/val_avg_speed_screen_off" view="TextView"><Expressions/><location startLine="131" startOffset="8" endLine="137" endOffset="37"/></Target><Target id="@+id/val_avg_speed_mixed" view="TextView"><Expressions/><location startLine="153" startOffset="8" endLine="159" endOffset="37"/></Target><Target id="@+id/val_total_charged_percent" view="TextView"><Expressions/><location startLine="175" startOffset="8" endLine="181" endOffset="37"/></Target><Target id="@+id/val_total_charged_mah" view="TextView"><Expressions/><location startLine="197" startOffset="8" endLine="203" endOffset="37"/></Target><Target id="@+id/val_screen_on_time" view="TextView"><Expressions/><location startLine="219" startOffset="8" endLine="225" endOffset="37"/></Target><Target id="@+id/val_screen_off_time" view="TextView"><Expressions/><location startLine="240" startOffset="8" endLine="246" endOffset="37"/></Target><Target id="@+id/btn_reset_session" view="Button"><Expressions/><location startLine="250" startOffset="4" endLine="256" endOffset="37"/></Target></Targets></Layout>