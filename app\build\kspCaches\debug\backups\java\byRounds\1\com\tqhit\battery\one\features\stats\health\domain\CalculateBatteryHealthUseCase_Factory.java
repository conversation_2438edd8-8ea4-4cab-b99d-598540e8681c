package com.tqhit.battery.one.features.stats.health.domain;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CalculateBatteryHealthUseCase_Factory implements Factory<CalculateBatteryHealthUseCase> {
  @Override
  public CalculateBatteryHealthUseCase get() {
    return newInstance();
  }

  public static CalculateBatteryHealthUseCase_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static CalculateBatteryHealthUseCase newInstance() {
    return new CalculateBatteryHealthUseCase();
  }

  private static final class InstanceHolder {
    static final CalculateBatteryHealthUseCase_Factory INSTANCE = new CalculateBatteryHealthUseCase_Factory();
  }
}
