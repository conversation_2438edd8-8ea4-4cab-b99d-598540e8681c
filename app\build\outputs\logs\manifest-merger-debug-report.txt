-- Merging decision tree log ---
manifest
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:2:1-135:12
MERGED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:2:1-135:12
INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml:2:1-12:12
INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml:2:1-12:12
INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml:2:1-12:12
MERGED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:2:1-35:12
MERGED from [androidx.databinding:databinding-adapters:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b7ff9644f9bcc389ba68543038801f1\transformed\databinding-adapters-8.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2678ee8bb276bfc54f76961656065b7\transformed\jetified-databinding-ktx-8.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f20495e20024fda37c8f96aaa63601b2\transformed\databinding-runtime-8.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.applovin.mediation:hyprmx-adapter:6.4.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c8eebd14327ab0a36a0dc1e8dba9bdd\transformed\jetified-hyprmx-adapter-6.4.2.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:2:1-35:12
MERGED from [androidx.databinding:viewbinding:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a494ff65eb638f9f55cea72e0c3e1459\transformed\jetified-viewbinding-8.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e61ea0f6cce98a887ff0b34296c2f1\transformed\navigation-common-2.8.9\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\851c966c3a3a74d27be92daf16d2791c\transformed\navigation-common-ktx-2.8.9\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af8efe70626cb8e9c0d6e052b6cf9531\transformed\navigation-runtime-2.8.9\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07043f27e938bb6f52a2666836684d65\transformed\navigation-runtime-ktx-2.8.9\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93083ef1e6366bffd04033cf787c80e9\transformed\navigation-fragment-2.8.9\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\602e78263552bfcb9815dd2cc56e2e29\transformed\navigation-fragment-ktx-2.8.9\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0351e0abb9229ab6e1e70a2532f29db\transformed\navigation-ui-ktx-2.8.9\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a5d12d8d5558ab0105ad2ffa8804aca\transformed\navigation-ui-2.8.9\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9717de49df575d5db38658124185973\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\520991d59a821d447a15ee9584bb77d4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.airbnb.android:lottie:6.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a681cc97135fe2b710ac72b7f2a1e7\transformed\jetified-lottie-6.6.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.tbuonomo:dotsindicator:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91f7115f29ef844ad3280fc681005d6c\transformed\jetified-dotsindicator-5.1.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.applovin.mediation:moloco-adapter:3.11.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab80a7a7d0f93c9404249fb6f6ed9f66\transformed\jetified-moloco-adapter-3.11.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:2:1-77:12
MERGED from [io.coil-kt:coil-compose:2.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20efc1155011d1de6286ab59fbdb44e0\transformed\jetified-coil-compose-2.2.2\AndroidManifest.xml:2:1-9:12
MERGED from [io.coil-kt:coil-compose-base:2.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19e7a01abb8dbb71ea0d1d6450d274c6\transformed\jetified-coil-compose-base-2.2.2\AndroidManifest.xml:2:1-9:12
MERGED from [io.coil-kt:coil:2.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\825d267d2f8c0bca9d798fe1391cda41\transformed\jetified-coil-2.2.2\AndroidManifest.xml:2:1-9:12
MERGED from [io.coil-kt:coil-base:2.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c3f25d2f7f2bb1af05f599b9b81123f\transformed\jetified-coil-base-2.2.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d51353db4733a732208d23b03395f158\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:2:1-21:12
MERGED from [com.applovin.mediation:yandex-adapter:7.14.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c5305312426893ebd5a390b6dfa151e\transformed\jetified-yandex-adapter-7.14.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:2:1-44:12
MERGED from [com.yandex.div:div:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2cfb4b9f48d45f96c328421e469b00b\transformed\jetified-div-31.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06e53aa7b654d8fb600b3cf6b112eca3\transformed\jetified-preference-ktx-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f209d101051ecb1c65632381d22aacb\transformed\preference-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.yandex.div:beacon:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\675354ce1e55c14ae89a98b1306fa0ce\transformed\jetified-beacon-31.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.yandex.div:div-storage:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e087961eb177aae30ba405d855a3d0e2\transformed\jetified-div-storage-31.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.yandex.div:div-json:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99621e8555094ab88f3ea71375017117\transformed\jetified-div-json-31.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.yandex.div:div-data:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6dfd260fcc4c9c905eda4c50f8b72b0f\transformed\jetified-div-data-31.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.yandex.div:div-histogram:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5abcb0c3183cfcece71bfffd8fb915ee\transformed\jetified-div-histogram-31.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.yandex.div:div-states:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a03307a70679f5c8d63dc5d4c0f9e08f\transformed\jetified-div-states-31.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.yandex.div:utils:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a992c74f4f9b89d6cbf67eb315d2048a\transformed\jetified-utils-31.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72a95338dd352f6906f9af3c130aa8a2\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.56.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8fd753638b74007cf06349d765e36077\transformed\jetified-hilt-android-2.56.1\AndroidManifest.xml:16:1-19:12
MERGED from [com.applovin.mediation:unityads-adapter:4.15.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cd1f147c79c58fca59a228b085a3e0f\transformed\jetified-unityads-adapter-4.15.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:2:1-65:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbca92afabb0df51ea44b5c94da4bed2\transformed\jetified-glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.media3:media3-exoplayer:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\872bae3c38ca42cf14648f81ccb65b49\transformed\jetified-media3-exoplayer-1.6.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-extractor:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0372eeb5bc1a1b07bf81f19912ff10e5\transformed\jetified-media3-extractor-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb874e46aeaed2f00c1e00fc4df1211f\transformed\jetified-media3-container-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e584669f7093e51e53ad9b0799f5f7b2\transformed\jetified-media3-datasource-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf06e561e3202509b2d0e4246c414eba\transformed\jetified-media3-decoder-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c30c0d6ccfeb00bda739cdf5cc8f4b0\transformed\jetified-media3-database-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05f75c4787b75960f57a7adac61c4aa1\transformed\jetified-media3-common-1.6.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7be73d2863fd2f56cbc8ece5355c1b16\transformed\jetified-media3-ui-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.applovin.mediation:inmobi-adapter:10.8.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8399b9610a3c9f1f00164a74c9cf489f\transformed\jetified-inmobi-adapter-10.8.3.1\AndroidManifest.xml:2:1-16:12
MERGED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:2:1-47:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d4ed03ab3978d697e580782f6ec8862\transformed\jetified-exoplayer-ui-2.18.2\AndroidManifest.xml:17:1-24:12
MERGED from [com.applovin.mediation:bytedance-adapter:7.2.0.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5cb68ed3aafc97ea8f3f859f3c33992a\transformed\jetified-bytedance-adapter-7.2.0.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.pangle.global:pag-sdk:7.2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73db033df5aa5eeb280fcc2d541f2ff9\transformed\jetified-pag-sdk-7.2.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:2:1-103:12
MERGED from [com.applovin.mediation:bidmachine-adapter:3.3.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9220dc50da7019b23803500a6770262\transformed\jetified-bidmachine-adapter-3.3.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:2:1-29:12
MERGED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:2:1-46:12
MERGED from [io.bidmachine.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0434fd7afae5b54a7c71df69e81dc9d6\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [io.bidmachine.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85fa1109f90e54854ce3d450994e0ab6\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [io.bidmachine.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4dbf87090256f61108355beb9116dc24\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [io.bidmachine.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\098bc09b5962b6f0c09c21d2c1a2df84\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [io.bidmachine.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30a7ff715f1640b658699a88f406439f\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [io.bidmachine.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4719ddcb1c6ae13d354974751d20674\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [io.bidmachine.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf8c4aa99908d6440ca1b18943a2bd3b\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [io.bidmachine.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20dce44f6bb8650f38d51e68750c822c\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [io.bidmachine.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36b462227b4e5d7c6e407c1d2796bff9\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [io.bidmachine.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a21f2baa5561d54c8fa5b650f4a4da41\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9c175028df0e95fd03879102b3fed9\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c5a1dfd144670779ebc4c283f54a71\transformed\jetified-viewpager2-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.applovin.mediation:google-ad-manager-adapter:24.4.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1b7b2e330d8d1f8b64ad5762700892c\transformed\jetified-google-ad-manager-adapter-24.4.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.applovin.mediation:google-adapter:24.4.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6950a6070f00d8580e474fc6055a8467\transformed\jetified-google-adapter-24.4.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4941c30c1ad49227b5c20962bf70d62\transformed\jetified-play-services-ads-24.4.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:17:1-115:12
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbe03fbf86154f7f3be28d344a3b9966\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bc415f7c900b5ccf3ce30320edfbec\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31d8f5382c2e9a6afd5cf9560ebc4af6\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.applovin.mediation:chartboost-adapter:9.8.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb2bd86024dfa08360b47ddf30fd7e70\transformed\jetified-chartboost-adapter-9.8.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.applovin.mediation:fyber-adapter:8.3.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b26160a15c757b4ee134ded7c104c19a\transformed\jetified-fyber-adapter-8.3.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.applovin.mediation:ironsource-adapter:8.9.1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c027bc0c53547b95a1186507cd76a2b\transformed\jetified-ironsource-adapter-8.9.1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.applovin.mediation:vungle-adapter:7.5.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\952f3a9025104d1d17578b9389d2fab1\transformed\jetified-vungle-adapter-7.5.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.applovin.mediation:facebook-adapter:6.20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd73d3dfabf49af28deeb91bdcd1861a\transformed\jetified-facebook-adapter-6.20.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.applovin.mediation:mintegral-adapter:16.9.71.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b5d64f445082364d6ab48802db3287d\transformed\jetified-mintegral-adapter-16.9.71.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:2:1-117:12
MERGED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:2:1-56:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:17:1-32:12
MERGED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:2:1-37:12
MERGED from [io.appmetrica.analytics:analytics-identifiers:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d04ec4c2b2f3151dba7358ca13e4c61\transformed\jetified-analytics-identifiers-7.9.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e255d8d77b59114a40f1aaa73e5ae5f\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-cronet:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b5c1e35d4af0b0924e34cebaf332274\transformed\jetified-play-services-cronet-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\482727c790f4fa36828f100f2e85d0b2\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a79d0ee80f37b73e6e18409ef0430f1e\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a694c913856ce98298f2bca61edc6d4b\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [io.appmetrica.analytics:analytics-appsetid:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18124c1db7b9def1bfc0200821a7e363\transformed\jetified-analytics-appsetid-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-appset:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af57918450edeadc1cb0515e366c0aab\transformed\jetified-play-services-appset-16.1.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e49b927a59fedf1bc868a7e7210b3864\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02dfe3ab0f932f4f0e9ef2842779a125\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b81c398e9455b04c7c579a154b9259d\transformed\jetified-material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a86a759daaa48920ee4dd21c9c95d0b\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cdb6ecbbdfc73b7f317dcb3f8c53b93\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7db2ee1439c5d3769f8da93e15f521aa\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\369187bcb5b4f8207444574e9a516d9c\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f4d0fa4a686c23a8f4eb4f76a27791f\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c045bfa763bb62c5417c6b4a71ceb2b6\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7155000ab77f8d0dd05a583f4ae1dd7\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd0a438922362988cccb7effef37f568\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0add04636e395b56e4b08c0e0a219f03\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddb673f31ce31038eef2591f89a81015\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9b19ece8daa5ba1ac609f37960aaeaf\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\417e408e52c7a9c0d0eb3eff1b22d77c\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.25.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b14d77f72be5547f29cf737780f433fc\transformed\jetified-accompanist-drawablepainter-0.25.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d4dc131dd11f51515a8ef122210917\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50402698ac7a23eac4a4d32b68f3d8ff\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a1ae21e7a3e151f984c500b8027704d\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50d36757b18b2c2b6f3eef970a8ccb63\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57098a42ad5b0249685a13f5864ed3df\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211659168c3565fdaf688939189fa6d6\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\470c5fcfaaaf2826abb50f9e27be3471\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c97eb39be37150a987f04ba73519036\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1acb5be24c25eaef400aff20adc1dc8d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76066564e51505754d88eda134219b01\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\844640439d3094dd9757cc4dd4efdc58\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001cb4152dcd13f0cac354cdd72d55de\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4ef46b7837d44eefd2fdaf7c5649e53\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c081f8f86e3b98a2aafe24a2c4948a42\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be221c00c7ce3e196e5e2fce895a3a70\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2bddcf99854293f296b0d3ec5a62fcd\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da49ffbc74db9ed719ed21d8a1ff6f79\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [com.moloco.sdk.acm:moloco-android-client-metrics:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66fbe96fce09d74adbe23d3f33340b9f\transformed\jetified-moloco-android-client-metrics-3.11.0\AndroidManifest.xml:2:1-8:12
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e2799ba8628e61f13a05a486e272f74\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a95787e4d569d5c65431698cfea0a710\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.yandex.div:assertion:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fa19596d38e54466a4cafe71c26ade0\transformed\jetified-assertion-31.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.yandex.div:logging:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9200a6cb07d25335d3c68943b2347744\transformed\jetified-logging-31.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.yandex.div:div-svg:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5670478a1f0dceed29d19b63b9e2e79\transformed\jetified-div-svg-31.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.yandex.div:div-core:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce557585ca1aefa72321f36e4579fc82\transformed\jetified-div-core-31.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9503b90828062b9442b0f92079897a6\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\157d9cb1f378b2e49c3d8a5bd09cf673\transformed\jetified-graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e46f960dff35e735cf5d824e3bce25a5\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\564190644e700145d329e0387c23df9e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62e94942f0c845f31cfee224a4ca802c\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bfd4ecb71539cfc8d6558bd5b0059da\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b7c06bcc1b6b266c67e18c2c3666012\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b92cd65528308b4669229cbf0474f6d\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c53c380eaea52b7bc2de9562de59617\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b91c81bc0e195414fbd0a4af1565888a\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96bedf674c26d358e500e30de78381b6\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be93f9975e988ce5b48bb2d3f50fd819\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\182525b2536edb247b68eceb93bf0a6e\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6ec530a7abeff92b02400c647efafb3\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7ccc3f972f78556d0585b6ed29d65d8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b0b6876c46d57a1a9b066b85eecc75\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [io.bidmachine.util:app-intents:0.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50b4cfb838912397f3849c69ff5c9720\transformed\jetified-app-intents-0.13.0\AndroidManifest.xml:2:1-26:12
MERGED from [io.bidmachine.util:general-util:0.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a24cc9377ff182d6b5e2778f3fff8f5\transformed\jetified-general-util-0.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.bidmachine:analytics:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f8e8d71b3f5f765f7c4edcc886f6495\transformed\jetified-analytics-3.3.0\AndroidManifest.xml:2:1-10:12
MERGED from [androidx.room:room-ktx:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef5b3992ea26a08bb46c50f0ee2fab2e\transformed\jetified-room-ktx-2.5.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3196e15ffa88757cd68378f9ef3085e\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ab31b6f99418716c4d84fb2d81b2ee3\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc2bfd62d3443f09bff36862f8d32def\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36b9b6763060c90dcedfa9fcb47353f6\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d472d6fb44b0dd9f6736c692796df915\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d917819901ed96aa63fb2d90e8311fc3\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:2:1-32:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34a22c8205f551c33bf11728adb430b0\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\204b10abea0f2aa31b95309975c2abca\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96680c8c3d232e3f07387255610cfc69\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92e06faeeb860403525f13f65546d0b2\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\881f97646947a0d7e005d8309cd5f52d\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87a6a57114ab74d5692912af4bbf01b0\transformed\jetified-activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a0b4a9a7edca5edf41b6c9f4848a80e\transformed\jetified-activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:2:1-29:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2caebb15d44877ab6a0f988bc353a39f\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2645c67e521ce8d026db4473f840e4f\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\de834790fb5c56be4e6a30bfed65f273\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [me.tankery.lib:circularSeekBar:1.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c73f1f05c081f8209d2b94a6236b0fc0\transformed\jetified-circularSeekBar-1.4.2\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8fdbfcb9a59e663f6e18ca173a567aed\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21f7c7da82967c2fc579d2c6757c46d6\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.moloco.sdk.protobuf:moloco-sdk-protobuf:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4cc8f627e40e6a864f0aaea81b11080\transformed\jetified-moloco-sdk-protobuf-3.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.yandex.varioqub:appmetrica-adapter:0.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26822518e5da8747c115a65725e99919\transformed\jetified-appmetrica-adapter-0.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.yandex.varioqub:analytic-adapter:0.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd581c91a6dbe38fe3f9ea72047fc5b6\transformed\jetified-analytic-adapter-0.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.yandex.varioqub:proto:0.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55be198fd6fef9ec62080687a417f3c2\transformed\jetified-proto-0.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:17:1-31:12
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbc2283a442ddf4da4a7b59652d79671\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:2:1-48:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1db057b60ca878859754ff58f4b394d9\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-ad-revenue:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c79c739131295291589d7167ed438021\transformed\jetified-analytics-ad-revenue-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-location:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45d431add7cc928f6da0550595831d11\transformed\jetified-analytics-location-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-remote-permissions:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8de76db7b4e88ae78eab55e8d98bc320\transformed\jetified-analytics-remote-permissions-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-reporter-extension:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\645dda0939d76198162f97c5137bcab1\transformed\jetified-analytics-reporter-extension-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-screenshot:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f52e4c343dc02ddfc123cfe43b8f9b09\transformed\jetified-analytics-screenshot-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-billing-v6:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8d36735562b1b0f6ec6ca44f01ce844\transformed\jetified-analytics-billing-v6-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-billing-interface:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80a8830af44c9cfce4428928bdf180d4\transformed\jetified-analytics-billing-interface-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-ad-revenue-admob-v23:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d84a082b7603bc3ac69d326d24967a2f\transformed\jetified-analytics-ad-revenue-admob-v23-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-ad-revenue-applovin-v12:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea009868f53b25916fc853ab1ddcf7b8\transformed\jetified-analytics-ad-revenue-applovin-v12-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-ad-revenue-fyber-v3:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3952f870387e8b13662d39baa978bbd\transformed\jetified-analytics-ad-revenue-fyber-v3-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-ad-revenue-ironsource-v7:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0cbcb647b2630c7118fd9bd562de7d3\transformed\jetified-analytics-ad-revenue-ironsource-v7-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-modules-api:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c0e23b81e9d45ff05831cfb851bf813\transformed\jetified-analytics-modules-api-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-network-tasks:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a19ac5c7a5f5dba30e77286bdc1db70\transformed\jetified-analytics-network-tasks-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-network:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\774f1ea61a34b79813abecd3a61c459c\transformed\jetified-analytics-network-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-core-utils:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33600c5d24084ca22f4c27a5de4aa39e\transformed\jetified-analytics-core-utils-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-location-api:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3c00c66fda50d868f907eefce7e928\transformed\jetified-analytics-location-api-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-core-api:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ce4102c88edd58359cee1a597916a23\transformed\jetified-analytics-core-api-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-proto:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a14eb1ee30051537c13f0db0d3e20638\transformed\jetified-analytics-proto-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-ndkcrashes-api:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae97f6967f123ff0980fe83d0cf5232f\transformed\jetified-analytics-ndkcrashes-api-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-gpllibrary:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cbd144ffd8cfe4f12f016614d8694b2\transformed\jetified-analytics-gpllibrary-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.appmetrica.analytics:analytics-logger:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d10453138607451739ff10ae1ec529e\transformed\jetified-analytics-logger-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce84e3441e81c753232a4c4a9060ae2a\transformed\sqlite-framework-2.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63ec8fef2aa5aefd24a5161c6f321bde\transformed\sqlite-2.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [io.appmetrica.analytics:analytics-common-logger:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\831caa456edf722c1796f7de3de9d4d1\transformed\jetified-analytics-common-logger-7.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9e47b482c59ee6d8359c1b90f5e9e03\transformed\jetified-shimmer-0.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea75794504383849bed7ea47cecb5c7c\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6050882545392d1910227aa19f3ce3c\transformed\jetified-BlurView-version-2.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a664b93ad66e5c8134ba2d3befe84641\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\090df19a301a8bc86253b8269e331531\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b299bde7198fdc676494e96c5efbc47\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6d501cab03905534223b8d2eeb5c097\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\079ea97e3091935aed7b6122a995adf6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d075f442566b1188ed422cb41f677e6\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52a971bea2b61498d4613c3e10c1235d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3c7654772673e67543a121581e2b2f5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d56d3a7eea350f47dd3618bd0df6040\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da9563719b3a240d93e9ca03bf9c2974\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbb39180551b2e4c313d747fd05d3e5\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\155f118ca0e3f1eddbaa9ab706ba764a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dbc455ef1319aefacfd6fdce609a248\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e9e9101fda2fda8545890612cf8a5a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eec5187bebc0900b2d7b45d2760e2150\transformed\jetified-exoplayer-datasource-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7b103e93a896db8d4dc6e740af08cca\transformed\jetified-exoplayer-extractor-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d1e61c13759d3b8d83fe27d8537e843\transformed\jetified-exoplayer-decoder-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a85a860759a01610119ec5c5e2755a3\transformed\jetified-exoplayer-database-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3304f03c7f56dd8b018d3d426d52a1d2\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:17:1-26:12
MERGED from [com.intuit.sdp:sdp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0532fc82bad615631afcad7a74a4f7fe\transformed\jetified-sdp-android-1.1.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.intuit.ssp:ssp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81563d9f2c2af6acb3a72b8756bedd4d\transformed\jetified-ssp-android-1.1.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.applovin.mediation:bigoads-adapter:5.3.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad02605cde055cfc5f143fbbeb3af9e\transformed\jetified-bigoads-adapter-5.3.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1f9b8dcdbff0e36191b45702b08a8f1\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.dagger:dagger-lint-aar:2.56.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e7e9589bd069b809151ed94279c080b\transformed\jetified-dagger-lint-aar-2.56.1\AndroidManifest.xml:16:1-19:12
MERGED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:2:1-86:12
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:2:1-49:12
MERGED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:2:1-53:12
MERGED from [com.adjust.signature:adjust-android-signature:3.35.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5c8fa94b252e233896bffb033da0678\transformed\jetified-adjust-android-signature-3.35.2\AndroidManifest.xml:2:1-7:12
MERGED from [io.bidmachine.libs:omsdk:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77da15036d91f6da0c640a8d5e826ed1\transformed\jetified-omsdk-1.5.4\AndroidManifest.xml:2:1-12:12
MERGED from [io.bidmachine:ads.networks.gam:3.3.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd511c80e84af72750c7af79b9c916c1\transformed\jetified-ads.networks.gam-3.3.0.3\AndroidManifest.xml:2:1-7:12
MERGED from [io.bidmachine:ads.networks.gam_dynamic:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf25d24cf81b4604f91c3bf5f1aa130\transformed\jetified-ads.networks.gam_dynamic-3.3.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\608baf3e53004868af1266f6da91f5c1\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.fyber.omsdk:om-sdk:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\434b18b7753db8c3b04a0e05d8011db9\transformed\jetified-om-sdk-1.5.1\AndroidManifest.xml:2:1-12:12
MERGED from [com.inmobi.omsdk:inmobi-omsdk:1.5.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bce07eda1cdeed755f97335faaff331\transformed\jetified-inmobi-omsdk-1.5.2.0\AndroidManifest.xml:2:1-12:12
MERGED from [com.unity3d.ads-mediation:adquality-sdk:7.25.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b178af670e2c11c2d4a3c71d879b3625\transformed\jetified-adquality-sdk-7.25.1\AndroidManifest.xml:2:1-14:12
MERGED from [com.pangle.global:tiktok-business-android-sdk-comp:1.3.7-rc.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d55c01852f381793194acc917f2252b\transformed\jetified-tiktok-business-android-sdk-comp-1.3.7-rc.2\AndroidManifest.xml:9:1-21:12
MERGED from [io.bidmachine:ads.networks.gam.base:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea5d1f5a0b4a872688734357c46f795c\transformed\jetified-ads.networks.gam.base-3.3.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.bidmachine:ads.networks.gam.versions.v21_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca8423a7eb7e25f4d0d7bf0adbb95a6\transformed\jetified-ads.networks.gam.versions.v21_0_0-3.3.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.bidmachine:ads.networks.gam.versions.v22_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14f796a44abd6363584dd9f663c212e3\transformed\jetified-ads.networks.gam.versions.v22_0_0-3.3.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.bidmachine:ads.networks.gam.versions.v23_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\988fb14e6ffe332be3f4f6e7adedf615\transformed\jetified-ads.networks.gam.versions.v23_0_0-3.3.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.bidmachine:ads.networks.gam_dynamic.base:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd8b0f1aea2295f74fb04b2d87c413d9\transformed\jetified-ads.networks.gam_dynamic.base-3.3.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.bidmachine:ads.networks.gam_dynamic.versions.v21_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c478e145f7666ad964e653dc37c611c2\transformed\jetified-ads.networks.gam_dynamic.versions.v21_0_0-3.3.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.bidmachine:ads.networks.gam_dynamic.versions.v22_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aab3f98c31c3292f87aff1cab31618a\transformed\jetified-ads.networks.gam_dynamic.versions.v22_0_0-3.3.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.bidmachine:ads.networks.gam_dynamic.versions.v23_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aedecfd0004b5b67fb9887b23bb22d60\transformed\jetified-ads.networks.gam_dynamic.versions.v23_0_0-3.3.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.pangle.global:pag-gecko:2.0.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\565e7cc2ca115aa10a4860ff4b6809c1\transformed\jetified-pag-gecko-2.0.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [com.pangle.global:pag-apm:2.0.0.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1075a695635e5ca4185349e42edd59bf\transformed\jetified-pag-apm-2.0.0.7\AndroidManifest.xml:2:1-16:12
MERGED from [org.chromium.net:cronet-api:72.3626.96] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad5150bb915f8b45ef87f728e7096ccf\transformed\jetified-cronet-api-72.3626.96\AndroidManifest.xml:15:1-22:12
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0678fed54f2d0b0aadc368726d6267d2\transformed\jetified-androidsvg-aar-1.4\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:30:5-67
MERGED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:30:5-67
MERGED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:8:5-67
MERGED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:8:5-67
MERGED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:31:5-67
MERGED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:31:5-67
MERGED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:8:5-67
MERGED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:8:5-67
MERGED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:15:5-67
MERGED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:15:5-67
MERGED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:9:5-67
MERGED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:9:5-67
MERGED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:11:5-67
MERGED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:11:5-67
MERGED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:7:5-67
MERGED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:7:5-67
MERGED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:7:5-67
MERGED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:16:5-67
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:16:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:9:5-67
MERGED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:9:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [io.bidmachine:analytics:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f8e8d71b3f5f765f7c4edcc886f6495\transformed\jetified-analytics-3.3.0\AndroidManifest.xml:7:5-67
MERGED from [io.bidmachine:analytics:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f8e8d71b3f5f765f7c4edcc886f6495\transformed\jetified-analytics-3.3.0\AndroidManifest.xml:7:5-67
MERGED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:15:5-67
MERGED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:15:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:11:5-67
MERGED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:7:5-67
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:7:5-67
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:7:5-67
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:7:5-67
MERGED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:9:5-67
MERGED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:9:5-67
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:7:5-67
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:7:5-67
MERGED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:7:5-67
MERGED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:7:5-67
MERGED from [io.bidmachine.libs:omsdk:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77da15036d91f6da0c640a8d5e826ed1\transformed\jetified-omsdk-1.5.4\AndroidManifest.xml:9:5-67
MERGED from [io.bidmachine.libs:omsdk:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77da15036d91f6da0c640a8d5e826ed1\transformed\jetified-omsdk-1.5.4\AndroidManifest.xml:9:5-67
MERGED from [com.fyber.omsdk:om-sdk:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\434b18b7753db8c3b04a0e05d8011db9\transformed\jetified-om-sdk-1.5.1\AndroidManifest.xml:9:5-67
MERGED from [com.fyber.omsdk:om-sdk:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\434b18b7753db8c3b04a0e05d8011db9\transformed\jetified-om-sdk-1.5.1\AndroidManifest.xml:9:5-67
MERGED from [com.inmobi.omsdk:inmobi-omsdk:1.5.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bce07eda1cdeed755f97335faaff331\transformed\jetified-inmobi-omsdk-1.5.2.0\AndroidManifest.xml:9:5-67
MERGED from [com.inmobi.omsdk:inmobi-omsdk:1.5.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bce07eda1cdeed755f97335faaff331\transformed\jetified-inmobi-omsdk-1.5.2.0\AndroidManifest.xml:9:5-67
MERGED from [com.unity3d.ads-mediation:adquality-sdk:7.25.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b178af670e2c11c2d4a3c71d879b3625\transformed\jetified-adquality-sdk-7.25.1\AndroidManifest.xml:12:5-67
MERGED from [com.unity3d.ads-mediation:adquality-sdk:7.25.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b178af670e2c11c2d4a3c71d879b3625\transformed\jetified-adquality-sdk-7.25.1\AndroidManifest.xml:12:5-67
MERGED from [com.pangle.global:tiktok-business-android-sdk-comp:1.3.7-rc.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d55c01852f381793194acc917f2252b\transformed\jetified-tiktok-business-android-sdk-comp-1.3.7-rc.2\AndroidManifest.xml:16:5-67
MERGED from [com.pangle.global:tiktok-business-android-sdk-comp:1.3.7-rc.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d55c01852f381793194acc917f2252b\transformed\jetified-tiktok-business-android-sdk-comp-1.3.7-rc.2\AndroidManifest.xml:16:5-67
MERGED from [com.pangle.global:pag-apm:2.0.0.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1075a695635e5ca4185349e42edd59bf\transformed\jetified-pag-apm-2.0.0.7\AndroidManifest.xml:12:5-67
MERGED from [com.pangle.global:pag-apm:2.0.0.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1075a695635e5ca4185349e42edd59bf\transformed\jetified-pag-apm-2.0.0.7\AndroidManifest.xml:12:5-67
MERGED from [org.chromium.net:cronet-api:72.3626.96] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad5150bb915f8b45ef87f728e7096ccf\transformed\jetified-cronet-api-72.3626.96\AndroidManifest.xml:19:5-66
MERGED from [org.chromium.net:cronet-api:72.3626.96] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad5150bb915f8b45ef87f728e7096ccf\transformed\jetified-cronet-api-72.3626.96\AndroidManifest.xml:19:5-66
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:6:5-79
MERGED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:31:5-79
MERGED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:31:5-79
MERGED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:7:5-79
MERGED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:7:5-79
MERGED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:32:5-79
MERGED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:32:5-79
MERGED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:9:5-79
MERGED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:9:5-79
MERGED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:14:5-79
MERGED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:14:5-79
MERGED from [androidx.media3:media3-exoplayer:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\872bae3c38ca42cf14648f81ccb65b49\transformed\jetified-media3-exoplayer-1.6.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\872bae3c38ca42cf14648f81ccb65b49\transformed\jetified-media3-exoplayer-1.6.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05f75c4787b75960f57a7adac61c4aa1\transformed\jetified-media3-common-1.6.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05f75c4787b75960f57a7adac61c4aa1\transformed\jetified-media3-common-1.6.1\AndroidManifest.xml:22:5-79
MERGED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:10:5-79
MERGED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:10:5-79
MERGED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:12:5-79
MERGED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:12:5-79
MERGED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:8:5-79
MERGED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:8:5-79
MERGED from [io.bidmachine.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4dbf87090256f61108355beb9116dc24\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [io.bidmachine.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4dbf87090256f61108355beb9116dc24\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [io.bidmachine.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36b462227b4e5d7c6e407c1d2796bff9\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [io.bidmachine.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36b462227b4e5d7c6e407c1d2796bff9\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:17:5-79
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:17:5-79
MERGED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:14:5-79
MERGED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:14:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:10:5-79
MERGED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be221c00c7ce3e196e5e2fce895a3a70\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be221c00c7ce3e196e5e2fce895a3a70\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [io.bidmachine:analytics:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f8e8d71b3f5f765f7c4edcc886f6495\transformed\jetified-analytics-3.3.0\AndroidManifest.xml:8:5-79
MERGED from [io.bidmachine:analytics:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f8e8d71b3f5f765f7c4edcc886f6495\transformed\jetified-analytics-3.3.0\AndroidManifest.xml:8:5-79
MERGED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:16:5-79
MERGED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:16:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:12:5-79
MERGED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:12:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:8:5-79
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3304f03c7f56dd8b018d3d426d52a1d2\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3304f03c7f56dd8b018d3d426d52a1d2\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:24:5-79
MERGED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:10:5-79
MERGED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:10:5-79
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:8:5-79
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:8:5-79
MERGED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:8:5-79
MERGED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:8:5-79
MERGED from [io.bidmachine.libs:omsdk:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77da15036d91f6da0c640a8d5e826ed1\transformed\jetified-omsdk-1.5.4\AndroidManifest.xml:10:5-79
MERGED from [io.bidmachine.libs:omsdk:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77da15036d91f6da0c640a8d5e826ed1\transformed\jetified-omsdk-1.5.4\AndroidManifest.xml:10:5-79
MERGED from [com.fyber.omsdk:om-sdk:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\434b18b7753db8c3b04a0e05d8011db9\transformed\jetified-om-sdk-1.5.1\AndroidManifest.xml:10:5-79
MERGED from [com.fyber.omsdk:om-sdk:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\434b18b7753db8c3b04a0e05d8011db9\transformed\jetified-om-sdk-1.5.1\AndroidManifest.xml:10:5-79
MERGED from [com.inmobi.omsdk:inmobi-omsdk:1.5.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bce07eda1cdeed755f97335faaff331\transformed\jetified-inmobi-omsdk-1.5.2.0\AndroidManifest.xml:10:5-79
MERGED from [com.inmobi.omsdk:inmobi-omsdk:1.5.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bce07eda1cdeed755f97335faaff331\transformed\jetified-inmobi-omsdk-1.5.2.0\AndroidManifest.xml:10:5-79
MERGED from [com.unity3d.ads-mediation:adquality-sdk:7.25.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b178af670e2c11c2d4a3c71d879b3625\transformed\jetified-adquality-sdk-7.25.1\AndroidManifest.xml:11:5-79
MERGED from [com.unity3d.ads-mediation:adquality-sdk:7.25.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b178af670e2c11c2d4a3c71d879b3625\transformed\jetified-adquality-sdk-7.25.1\AndroidManifest.xml:11:5-79
MERGED from [com.pangle.global:pag-apm:2.0.0.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1075a695635e5ca4185349e42edd59bf\transformed\jetified-pag-apm-2.0.0.7\AndroidManifest.xml:11:5-79
MERGED from [com.pangle.global:pag-apm:2.0.0.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1075a695635e5ca4185349e42edd59bf\transformed\jetified-pag-apm-2.0.0.7\AndroidManifest.xml:11:5-79
MERGED from [org.chromium.net:cronet-api:72.3626.96] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad5150bb915f8b45ef87f728e7096ccf\transformed\jetified-cronet-api-72.3626.96\AndroidManifest.xml:20:5-78
MERGED from [org.chromium.net:cronet-api:72.3626.96] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad5150bb915f8b45ef87f728e7096ccf\transformed\jetified-cronet-api-72.3626.96\AndroidManifest.xml:20:5-78
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:7:5-77
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:7:22-74
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:8:5-95
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:8:22-92
uses-permission#android.permission.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:9:5-101
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:9:22-99
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:10:5-76
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:10:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:11:5-88
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:11:22-86
uses-permission#android.permission.VIBRATE
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:12:5-65
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:18:5-66
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:18:5-66
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:12:22-63
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:13:5-78
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:13:22-75
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:14:5-81
REJECTED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:14:22-78
uses-permission#android.permission.PACKAGE_USAGE_STATS
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:15:5-16:47
	tools:ignore
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:16:9-44
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:15:22-75
permission#com.tqhit.battery.one.permission.FINISH_OVERLAY
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:18:5-20:47
	android:protectionLevel
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:20:9-44
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:19:9-71
application
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:22:5-133:19
MERGED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:22:5-133:19
MERGED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:22:5-133:19
INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml:5:5-10:19
MERGED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:10:5-33:19
MERGED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:10:5-33:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9717de49df575d5db38658124185973\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9717de49df575d5db38658124185973\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\520991d59a821d447a15ee9584bb77d4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\520991d59a821d447a15ee9584bb77d4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a681cc97135fe2b710ac72b7f2a1e7\transformed\jetified-lottie-6.6.2\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a681cc97135fe2b710ac72b7f2a1e7\transformed\jetified-lottie-6.6.2\AndroidManifest.xml:7:5-20
MERGED from [com.tbuonomo:dotsindicator:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91f7115f29ef844ad3280fc681005d6c\transformed\jetified-dotsindicator-5.1.0\AndroidManifest.xml:7:5-47
MERGED from [com.tbuonomo:dotsindicator:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91f7115f29ef844ad3280fc681005d6c\transformed\jetified-dotsindicator-5.1.0\AndroidManifest.xml:7:5-47
MERGED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:34:5-75:19
MERGED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:34:5-75:19
MERGED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:9:5-19:19
MERGED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:9:5-19:19
MERGED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:13:5-42:19
MERGED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:13:5-42:19
MERGED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:20:5-63:19
MERGED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:20:5-63:19
MERGED from [com.applovin.mediation:inmobi-adapter:10.8.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8399b9610a3c9f1f00164a74c9cf489f\transformed\jetified-inmobi-adapter-10.8.3.1\AndroidManifest.xml:14:5-20
MERGED from [com.applovin.mediation:inmobi-adapter:10.8.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8399b9610a3c9f1f00164a74c9cf489f\transformed\jetified-inmobi-adapter-10.8.3.1\AndroidManifest.xml:14:5-20
MERGED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:38:5-45:19
MERGED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:38:5-45:19
MERGED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:25:5-101:19
MERGED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:25:5-101:19
MERGED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:17:5-27:19
MERGED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:17:5-27:19
MERGED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:28:5-44:19
MERGED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bc415f7c900b5ccf3ce30320edfbec\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bc415f7c900b5ccf3ce30320edfbec\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31d8f5382c2e9a6afd5cf9560ebc4af6\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31d8f5382c2e9a6afd5cf9560ebc4af6\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:25:5-115:19
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:25:5-115:19
MERGED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:25:5-54:19
MERGED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:25:5-54:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:12:5-35:19
MERGED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:12:5-35:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e255d8d77b59114a40f1aaa73e5ae5f\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e255d8d77b59114a40f1aaa73e5ae5f\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-cronet:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b5c1e35d4af0b0924e34cebaf332274\transformed\jetified-play-services-cronet-18.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-cronet:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b5c1e35d4af0b0924e34cebaf332274\transformed\jetified-play-services-cronet-18.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a694c913856ce98298f2bca61edc6d4b\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a694c913856ce98298f2bca61edc6d4b\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-appset:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af57918450edeadc1cb0515e366c0aab\transformed\jetified-play-services-appset-16.1.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-appset:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af57918450edeadc1cb0515e366c0aab\transformed\jetified-play-services-appset-16.1.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211659168c3565fdaf688939189fa6d6\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211659168c3565fdaf688939189fa6d6\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7ccc3f972f78556d0585b6ed29d65d8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7ccc3f972f78556d0585b6ed29d65d8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d917819901ed96aa63fb2d90e8311fc3\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d917819901ed96aa63fb2d90e8311fc3\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:19:5-30:19
MERGED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:19:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34a22c8205f551c33bf11728adb430b0\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34a22c8205f551c33bf11728adb430b0\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\204b10abea0f2aa31b95309975c2abca\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\204b10abea0f2aa31b95309975c2abca\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:15:5-27:19
MERGED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:15:5-27:19
MERGED from [me.tankery.lib:circularSeekBar:1.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c73f1f05c081f8209d2b94a6236b0fc0\transformed\jetified-circularSeekBar-1.4.2\AndroidManifest.xml:9:5-20
MERGED from [me.tankery.lib:circularSeekBar:1.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c73f1f05c081f8209d2b94a6236b0fc0\transformed\jetified-circularSeekBar-1.4.2\AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:23:5-29:19
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:7:5-12:19
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:7:5-12:19
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:11:5-46:19
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:11:5-46:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6050882545392d1910227aa19f3ce3c\transformed\jetified-BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6050882545392d1910227aa19f3ce3c\transformed\jetified-BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\079ea97e3091935aed7b6122a995adf6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\079ea97e3091935aed7b6122a995adf6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3c7654772673e67543a121581e2b2f5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3c7654772673e67543a121581e2b2f5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:10:5-15:19
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:10:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1f9b8dcdbff0e36191b45702b08a8f1\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1f9b8dcdbff0e36191b45702b08a8f1\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:19:5-84:19
MERGED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:19:5-84:19
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:20:5-47:19
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:20:5-47:19
MERGED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:20:5-51:19
MERGED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:20:5-51:19
MERGED from [com.pangle.global:tiktok-business-android-sdk-comp:1.3.7-rc.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d55c01852f381793194acc917f2252b\transformed\jetified-tiktok-business-android-sdk-comp-1.3.7-rc.2\AndroidManifest.xml:19:5-20
MERGED from [com.pangle.global:tiktok-business-android-sdk-comp:1.3.7-rc.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d55c01852f381793194acc917f2252b\transformed\jetified-tiktok-business-android-sdk-comp-1.3.7-rc.2\AndroidManifest.xml:19:5-20
MERGED from [com.pangle.global:pag-apm:2.0.0.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1075a695635e5ca4185349e42edd59bf\transformed\jetified-pag-apm-2.0.0.7\AndroidManifest.xml:14:5-20
MERGED from [com.pangle.global:pag-apm:2.0.0.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1075a695635e5ca4185349e42edd59bf\transformed\jetified-pag-apm-2.0.0.7\AndroidManifest.xml:14:5-20
	android:extractNativeLibs
		INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
	tools:ignore
		ADDED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:12:18-57
	android:roundIcon
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:29:9-54
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:29:9-54
	android:icon
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:27:9-43
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:27:9-43
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:30:9-45
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:30:9-45
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:31:9-35
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:31:9-35
	android:label
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:28:9-41
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:28:9-41
	android:hardwareAccelerated
		ADDED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:15:18-52
	android:fullBackupContent
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:26:9-54
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:26:9-54
	tools:targetApi
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:33:9-29
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:33:9-29
	android:allowBackup
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:24:9-35
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:24:9-35
	android:theme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:32:9-48
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:32:9-48
	android:dataExtractionRules
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:25:9-65
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:25:9-65
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:23:9-64
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:23:9-64
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:35:9-37:70
	android:value
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:37:13-67
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:36:13-69
activity#com.tqhit.battery.one.activity.splash.SplashActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:39:9-47:20
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:41:13-36
	android:theme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:42:13-59
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:40:13-80
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:43:13-46:29
action#android.intent.action.MAIN
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:44:17-69
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:44:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:45:17-77
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:45:27-74
activity#com.tqhit.battery.one.activity.starting.StartingActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:49:9-53:54
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:52:13-49
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:51:13-37
	android:theme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:53:13-52
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:50:13-84
activity#com.tqhit.battery.one.activity.onboarding.LanguageSelectionActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:55:9-59:54
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:58:13-49
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:57:13-37
	android:theme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:59:13-52
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:56:13-95
activity#com.tqhit.battery.one.activity.main.MainActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:61:9-65:51
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:65:13-49
	android:launchMode
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:64:13-44
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:63:13-36
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:62:13-76
activity#com.tqhit.battery.one.activity.animation.AnimationActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:67:9-71:51
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:71:13-49
	android:launchMode
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:70:13-44
	android:configChanges
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:69:13-59
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:68:13-86
activity#com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:73:9-79:51
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:79:13-49
	android:launchMode
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:78:13-44
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:75:13-37
	android:configChanges
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:77:13-59
	android:theme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:76:13-63
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:74:13-90
activity#com.tqhit.battery.one.activity.password.EnterPasswordActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:81:9-87:51
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:87:13-49
	android:launchMode
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:86:13-44
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:83:13-37
	android:configChanges
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:85:13-59
	android:theme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:84:13-63
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:82:13-89
activity#com.tqhit.battery.one.features.new_discharge.presentation.TestNewDischargeActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:9-100:20
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:93:13-49
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:13-36
	android:theme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:13-51
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:90:13-110
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:test_discharge+data:scheme:battery
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:94:13-99:29
action#android.intent.action.VIEW
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:95:17-69
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:95:25-66
category#android.intent.category.DEFAULT
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:96:17-76
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:96:27-73
category#android.intent.category.BROWSABLE
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:97:17-78
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:97:27-75
data
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:98:17-80
	android:host
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:98:48-77
	android:scheme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:98:23-47
service#com.tqhit.battery.one.service.BatteryMonitorService
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:106:9-110:59
	android:enabled
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:108:13-35
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:109:13-37
	android:foregroundServiceType
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:110:13-55
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:107:13-79
service#com.tqhit.battery.one.service.ChargingOverlayService
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:111:9-115:59
	android:enabled
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:113:13-35
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:114:13-37
	android:foregroundServiceType
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:115:13-55
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:112:13-80
service#com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:118:9-122:59
	android:enabled
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:120:13-35
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:121:13-37
	android:foregroundServiceType
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:122:13-55
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:119:13-112
service#com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:123:9-127:59
	android:enabled
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:125:13-35
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:126:13-37
	android:foregroundServiceType
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:127:13-55
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:124:13-108
service#com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:128:9-132:59
	android:enabled
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:130:13-35
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:131:13-37
	android:foregroundServiceType
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:132:13-55
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:129:13-112
activity#com.tqhit.battery.one.activity.debug.DebugActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml:7:9-9:35
	tools:node
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml:9:13-32
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml:8:13-78
uses-sdk
INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
MERGED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:28:5-44
MERGED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:28:5-44
MERGED from [androidx.databinding:databinding-adapters:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b7ff9644f9bcc389ba68543038801f1\transformed\databinding-adapters-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b7ff9644f9bcc389ba68543038801f1\transformed\databinding-adapters-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2678ee8bb276bfc54f76961656065b7\transformed\jetified-databinding-ktx-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2678ee8bb276bfc54f76961656065b7\transformed\jetified-databinding-ktx-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f20495e20024fda37c8f96aaa63601b2\transformed\databinding-runtime-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f20495e20024fda37c8f96aaa63601b2\transformed\databinding-runtime-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:hyprmx-adapter:6.4.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c8eebd14327ab0a36a0dc1e8dba9bdd\transformed\jetified-hyprmx-adapter-6.4.2.3\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:hyprmx-adapter:6.4.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c8eebd14327ab0a36a0dc1e8dba9bdd\transformed\jetified-hyprmx-adapter-6.4.2.3\AndroidManifest.xml:5:5-44
MERGED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a494ff65eb638f9f55cea72e0c3e1459\transformed\jetified-viewbinding-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a494ff65eb638f9f55cea72e0c3e1459\transformed\jetified-viewbinding-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e61ea0f6cce98a887ff0b34296c2f1\transformed\navigation-common-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e61ea0f6cce98a887ff0b34296c2f1\transformed\navigation-common-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\851c966c3a3a74d27be92daf16d2791c\transformed\navigation-common-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\851c966c3a3a74d27be92daf16d2791c\transformed\navigation-common-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af8efe70626cb8e9c0d6e052b6cf9531\transformed\navigation-runtime-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af8efe70626cb8e9c0d6e052b6cf9531\transformed\navigation-runtime-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07043f27e938bb6f52a2666836684d65\transformed\navigation-runtime-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07043f27e938bb6f52a2666836684d65\transformed\navigation-runtime-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93083ef1e6366bffd04033cf787c80e9\transformed\navigation-fragment-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93083ef1e6366bffd04033cf787c80e9\transformed\navigation-fragment-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\602e78263552bfcb9815dd2cc56e2e29\transformed\navigation-fragment-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\602e78263552bfcb9815dd2cc56e2e29\transformed\navigation-fragment-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0351e0abb9229ab6e1e70a2532f29db\transformed\navigation-ui-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0351e0abb9229ab6e1e70a2532f29db\transformed\navigation-ui-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a5d12d8d5558ab0105ad2ffa8804aca\transformed\navigation-ui-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a5d12d8d5558ab0105ad2ffa8804aca\transformed\navigation-ui-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9717de49df575d5db38658124185973\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9717de49df575d5db38658124185973\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\520991d59a821d447a15ee9584bb77d4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\520991d59a821d447a15ee9584bb77d4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a681cc97135fe2b710ac72b7f2a1e7\transformed\jetified-lottie-6.6.2\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a681cc97135fe2b710ac72b7f2a1e7\transformed\jetified-lottie-6.6.2\AndroidManifest.xml:5:5-44
MERGED from [com.tbuonomo:dotsindicator:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91f7115f29ef844ad3280fc681005d6c\transformed\jetified-dotsindicator-5.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.tbuonomo:dotsindicator:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91f7115f29ef844ad3280fc681005d6c\transformed\jetified-dotsindicator-5.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:moloco-adapter:3.11.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab80a7a7d0f93c9404249fb6f6ed9f66\transformed\jetified-moloco-adapter-3.11.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:moloco-adapter:3.11.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab80a7a7d0f93c9404249fb6f6ed9f66\transformed\jetified-moloco-adapter-3.11.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:6:5-44
MERGED from [io.coil-kt:coil-compose:2.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20efc1155011d1de6286ab59fbdb44e0\transformed\jetified-coil-compose-2.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [io.coil-kt:coil-compose:2.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20efc1155011d1de6286ab59fbdb44e0\transformed\jetified-coil-compose-2.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [io.coil-kt:coil-compose-base:2.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19e7a01abb8dbb71ea0d1d6450d274c6\transformed\jetified-coil-compose-base-2.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [io.coil-kt:coil-compose-base:2.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19e7a01abb8dbb71ea0d1d6450d274c6\transformed\jetified-coil-compose-base-2.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [io.coil-kt:coil:2.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\825d267d2f8c0bca9d798fe1391cda41\transformed\jetified-coil-2.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [io.coil-kt:coil:2.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\825d267d2f8c0bca9d798fe1391cda41\transformed\jetified-coil-2.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [io.coil-kt:coil-base:2.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c3f25d2f7f2bb1af05f599b9b81123f\transformed\jetified-coil-base-2.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [io.coil-kt:coil-base:2.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c3f25d2f7f2bb1af05f599b9b81123f\transformed\jetified-coil-base-2.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d51353db4733a732208d23b03395f158\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d51353db4733a732208d23b03395f158\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:5:5-7:41
MERGED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.applovin.mediation:yandex-adapter:7.14.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c5305312426893ebd5a390b6dfa151e\transformed\jetified-yandex-adapter-7.14.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:yandex-adapter:7.14.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c5305312426893ebd5a390b6dfa151e\transformed\jetified-yandex-adapter-7.14.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:6:5-44
MERGED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:6:5-44
MERGED from [com.yandex.div:div:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2cfb4b9f48d45f96c328421e469b00b\transformed\jetified-div-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2cfb4b9f48d45f96c328421e469b00b\transformed\jetified-div-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06e53aa7b654d8fb600b3cf6b112eca3\transformed\jetified-preference-ktx-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06e53aa7b654d8fb600b3cf6b112eca3\transformed\jetified-preference-ktx-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f209d101051ecb1c65632381d22aacb\transformed\preference-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f209d101051ecb1c65632381d22aacb\transformed\preference-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.yandex.div:beacon:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\675354ce1e55c14ae89a98b1306fa0ce\transformed\jetified-beacon-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:beacon:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\675354ce1e55c14ae89a98b1306fa0ce\transformed\jetified-beacon-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div-storage:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e087961eb177aae30ba405d855a3d0e2\transformed\jetified-div-storage-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div-storage:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e087961eb177aae30ba405d855a3d0e2\transformed\jetified-div-storage-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div-json:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99621e8555094ab88f3ea71375017117\transformed\jetified-div-json-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div-json:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99621e8555094ab88f3ea71375017117\transformed\jetified-div-json-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div-data:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6dfd260fcc4c9c905eda4c50f8b72b0f\transformed\jetified-div-data-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div-data:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6dfd260fcc4c9c905eda4c50f8b72b0f\transformed\jetified-div-data-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div-histogram:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5abcb0c3183cfcece71bfffd8fb915ee\transformed\jetified-div-histogram-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div-histogram:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5abcb0c3183cfcece71bfffd8fb915ee\transformed\jetified-div-histogram-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div-states:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a03307a70679f5c8d63dc5d4c0f9e08f\transformed\jetified-div-states-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div-states:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a03307a70679f5c8d63dc5d4c0f9e08f\transformed\jetified-div-states-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:utils:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a992c74f4f9b89d6cbf67eb315d2048a\transformed\jetified-utils-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:utils:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a992c74f4f9b89d6cbf67eb315d2048a\transformed\jetified-utils-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72a95338dd352f6906f9af3c130aa8a2\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72a95338dd352f6906f9af3c130aa8a2\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.56.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8fd753638b74007cf06349d765e36077\transformed\jetified-hilt-android-2.56.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.56.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8fd753638b74007cf06349d765e36077\transformed\jetified-hilt-android-2.56.1\AndroidManifest.xml:18:3-42
MERGED from [com.applovin.mediation:unityads-adapter:4.15.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cd1f147c79c58fca59a228b085a3e0f\transformed\jetified-unityads-adapter-4.15.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:unityads-adapter:4.15.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cd1f147c79c58fca59a228b085a3e0f\transformed\jetified-unityads-adapter-4.15.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:6:5-44
MERGED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:6:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbca92afabb0df51ea44b5c94da4bed2\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbca92afabb0df51ea44b5c94da4bed2\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.media3:media3-exoplayer:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\872bae3c38ca42cf14648f81ccb65b49\transformed\jetified-media3-exoplayer-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\872bae3c38ca42cf14648f81ccb65b49\transformed\jetified-media3-exoplayer-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0372eeb5bc1a1b07bf81f19912ff10e5\transformed\jetified-media3-extractor-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0372eeb5bc1a1b07bf81f19912ff10e5\transformed\jetified-media3-extractor-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb874e46aeaed2f00c1e00fc4df1211f\transformed\jetified-media3-container-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb874e46aeaed2f00c1e00fc4df1211f\transformed\jetified-media3-container-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e584669f7093e51e53ad9b0799f5f7b2\transformed\jetified-media3-datasource-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e584669f7093e51e53ad9b0799f5f7b2\transformed\jetified-media3-datasource-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf06e561e3202509b2d0e4246c414eba\transformed\jetified-media3-decoder-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf06e561e3202509b2d0e4246c414eba\transformed\jetified-media3-decoder-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c30c0d6ccfeb00bda739cdf5cc8f4b0\transformed\jetified-media3-database-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c30c0d6ccfeb00bda739cdf5cc8f4b0\transformed\jetified-media3-database-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05f75c4787b75960f57a7adac61c4aa1\transformed\jetified-media3-common-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05f75c4787b75960f57a7adac61c4aa1\transformed\jetified-media3-common-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7be73d2863fd2f56cbc8ece5355c1b16\transformed\jetified-media3-ui-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7be73d2863fd2f56cbc8ece5355c1b16\transformed\jetified-media3-ui-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.applovin.mediation:inmobi-adapter:10.8.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8399b9610a3c9f1f00164a74c9cf489f\transformed\jetified-inmobi-adapter-10.8.3.1\AndroidManifest.xml:9:5-44
MERGED from [com.applovin.mediation:inmobi-adapter:10.8.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8399b9610a3c9f1f00164a74c9cf489f\transformed\jetified-inmobi-adapter-10.8.3.1\AndroidManifest.xml:9:5-44
MERGED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:6:5-44
MERGED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d4ed03ab3978d697e580782f6ec8862\transformed\jetified-exoplayer-ui-2.18.2\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d4ed03ab3978d697e580782f6ec8862\transformed\jetified-exoplayer-ui-2.18.2\AndroidManifest.xml:20:5-22:41
MERGED from [com.applovin.mediation:bytedance-adapter:7.2.0.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5cb68ed3aafc97ea8f3f859f3c33992a\transformed\jetified-bytedance-adapter-7.2.0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:bytedance-adapter:7.2.0.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5cb68ed3aafc97ea8f3f859f3c33992a\transformed\jetified-bytedance-adapter-7.2.0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.pangle.global:pag-sdk:7.2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73db033df5aa5eeb280fcc2d541f2ff9\transformed\jetified-pag-sdk-7.2.0.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.pangle.global:pag-sdk:7.2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73db033df5aa5eeb280fcc2d541f2ff9\transformed\jetified-pag-sdk-7.2.0.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:7:5-9:41
MERGED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:7:5-9:41
MERGED from [com.applovin.mediation:bidmachine-adapter:3.3.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9220dc50da7019b23803500a6770262\transformed\jetified-bidmachine-adapter-3.3.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:bidmachine-adapter:3.3.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9220dc50da7019b23803500a6770262\transformed\jetified-bidmachine-adapter-3.3.0.1\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0434fd7afae5b54a7c71df69e81dc9d6\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0434fd7afae5b54a7c71df69e81dc9d6\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85fa1109f90e54854ce3d450994e0ab6\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85fa1109f90e54854ce3d450994e0ab6\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4dbf87090256f61108355beb9116dc24\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4dbf87090256f61108355beb9116dc24\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\098bc09b5962b6f0c09c21d2c1a2df84\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\098bc09b5962b6f0c09c21d2c1a2df84\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30a7ff715f1640b658699a88f406439f\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30a7ff715f1640b658699a88f406439f\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4719ddcb1c6ae13d354974751d20674\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4719ddcb1c6ae13d354974751d20674\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf8c4aa99908d6440ca1b18943a2bd3b\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf8c4aa99908d6440ca1b18943a2bd3b\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20dce44f6bb8650f38d51e68750c822c\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20dce44f6bb8650f38d51e68750c822c\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36b462227b4e5d7c6e407c1d2796bff9\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36b462227b4e5d7c6e407c1d2796bff9\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a21f2baa5561d54c8fa5b650f4a4da41\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a21f2baa5561d54c8fa5b650f4a4da41\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9c175028df0e95fd03879102b3fed9\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac9c175028df0e95fd03879102b3fed9\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c5a1dfd144670779ebc4c283f54a71\transformed\jetified-viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c5a1dfd144670779ebc4c283f54a71\transformed\jetified-viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:google-ad-manager-adapter:24.4.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1b7b2e330d8d1f8b64ad5762700892c\transformed\jetified-google-ad-manager-adapter-24.4.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:google-ad-manager-adapter:24.4.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1b7b2e330d8d1f8b64ad5762700892c\transformed\jetified-google-ad-manager-adapter-24.4.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:google-adapter:24.4.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6950a6070f00d8580e474fc6055a8467\transformed\jetified-google-adapter-24.4.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:google-adapter:24.4.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6950a6070f00d8580e474fc6055a8467\transformed\jetified-google-adapter-24.4.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4941c30c1ad49227b5c20962bf70d62\transformed\jetified-play-services-ads-24.4.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4941c30c1ad49227b5c20962bf70d62\transformed\jetified-play-services-ads-24.4.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbe03fbf86154f7f3be28d344a3b9966\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbe03fbf86154f7f3be28d344a3b9966\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bc415f7c900b5ccf3ce30320edfbec\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bc415f7c900b5ccf3ce30320edfbec\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31d8f5382c2e9a6afd5cf9560ebc4af6\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31d8f5382c2e9a6afd5cf9560ebc4af6\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.applovin.mediation:chartboost-adapter:9.8.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb2bd86024dfa08360b47ddf30fd7e70\transformed\jetified-chartboost-adapter-9.8.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:chartboost-adapter:9.8.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb2bd86024dfa08360b47ddf30fd7e70\transformed\jetified-chartboost-adapter-9.8.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:fyber-adapter:8.3.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b26160a15c757b4ee134ded7c104c19a\transformed\jetified-fyber-adapter-8.3.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:fyber-adapter:8.3.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b26160a15c757b4ee134ded7c104c19a\transformed\jetified-fyber-adapter-8.3.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:ironsource-adapter:8.9.1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c027bc0c53547b95a1186507cd76a2b\transformed\jetified-ironsource-adapter-8.9.1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:ironsource-adapter:8.9.1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c027bc0c53547b95a1186507cd76a2b\transformed\jetified-ironsource-adapter-8.9.1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:vungle-adapter:7.5.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\952f3a9025104d1d17578b9389d2fab1\transformed\jetified-vungle-adapter-7.5.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:vungle-adapter:7.5.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\952f3a9025104d1d17578b9389d2fab1\transformed\jetified-vungle-adapter-7.5.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:facebook-adapter:6.20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd73d3dfabf49af28deeb91bdcd1861a\transformed\jetified-facebook-adapter-6.20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:facebook-adapter:6.20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd73d3dfabf49af28deeb91bdcd1861a\transformed\jetified-facebook-adapter-6.20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:mintegral-adapter:16.9.71.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b5d64f445082364d6ab48802db3287d\transformed\jetified-mintegral-adapter-16.9.71.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:mintegral-adapter:16.9.71.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b5d64f445082364d6ab48802db3287d\transformed\jetified-mintegral-adapter-16.9.71.0\AndroidManifest.xml:5:5-44
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:5:5-44
MERGED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:6:5-44
MERGED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:6:5-44
MERGED from [io.appmetrica.analytics:analytics-identifiers:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d04ec4c2b2f3151dba7358ca13e4c61\transformed\jetified-analytics-identifiers-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-identifiers:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d04ec4c2b2f3151dba7358ca13e4c61\transformed\jetified-analytics-identifiers-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e255d8d77b59114a40f1aaa73e5ae5f\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e255d8d77b59114a40f1aaa73e5ae5f\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cronet:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b5c1e35d4af0b0924e34cebaf332274\transformed\jetified-play-services-cronet-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cronet:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b5c1e35d4af0b0924e34cebaf332274\transformed\jetified-play-services-cronet-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\482727c790f4fa36828f100f2e85d0b2\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\482727c790f4fa36828f100f2e85d0b2\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a79d0ee80f37b73e6e18409ef0430f1e\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a79d0ee80f37b73e6e18409ef0430f1e\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a694c913856ce98298f2bca61edc6d4b\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a694c913856ce98298f2bca61edc6d4b\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-appsetid:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18124c1db7b9def1bfc0200821a7e363\transformed\jetified-analytics-appsetid-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-appsetid:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18124c1db7b9def1bfc0200821a7e363\transformed\jetified-analytics-appsetid-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af57918450edeadc1cb0515e366c0aab\transformed\jetified-play-services-appset-16.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-appset:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af57918450edeadc1cb0515e366c0aab\transformed\jetified-play-services-appset-16.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e49b927a59fedf1bc868a7e7210b3864\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e49b927a59fedf1bc868a7e7210b3864\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02dfe3ab0f932f4f0e9ef2842779a125\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02dfe3ab0f932f4f0e9ef2842779a125\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b81c398e9455b04c7c579a154b9259d\transformed\jetified-material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b81c398e9455b04c7c579a154b9259d\transformed\jetified-material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a86a759daaa48920ee4dd21c9c95d0b\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a86a759daaa48920ee4dd21c9c95d0b\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cdb6ecbbdfc73b7f317dcb3f8c53b93\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cdb6ecbbdfc73b7f317dcb3f8c53b93\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7db2ee1439c5d3769f8da93e15f521aa\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7db2ee1439c5d3769f8da93e15f521aa\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\369187bcb5b4f8207444574e9a516d9c\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\369187bcb5b4f8207444574e9a516d9c\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f4d0fa4a686c23a8f4eb4f76a27791f\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f4d0fa4a686c23a8f4eb4f76a27791f\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c045bfa763bb62c5417c6b4a71ceb2b6\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c045bfa763bb62c5417c6b4a71ceb2b6\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7155000ab77f8d0dd05a583f4ae1dd7\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7155000ab77f8d0dd05a583f4ae1dd7\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd0a438922362988cccb7effef37f568\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd0a438922362988cccb7effef37f568\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0add04636e395b56e4b08c0e0a219f03\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0add04636e395b56e4b08c0e0a219f03\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddb673f31ce31038eef2591f89a81015\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddb673f31ce31038eef2591f89a81015\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9b19ece8daa5ba1ac609f37960aaeaf\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9b19ece8daa5ba1ac609f37960aaeaf\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\417e408e52c7a9c0d0eb3eff1b22d77c\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\417e408e52c7a9c0d0eb3eff1b22d77c\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.25.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b14d77f72be5547f29cf737780f433fc\transformed\jetified-accompanist-drawablepainter-0.25.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.25.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b14d77f72be5547f29cf737780f433fc\transformed\jetified-accompanist-drawablepainter-0.25.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material:material-icons-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d4dc131dd11f51515a8ef122210917\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d4dc131dd11f51515a8ef122210917\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50402698ac7a23eac4a4d32b68f3d8ff\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50402698ac7a23eac4a4d32b68f3d8ff\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a1ae21e7a3e151f984c500b8027704d\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a1ae21e7a3e151f984c500b8027704d\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50d36757b18b2c2b6f3eef970a8ccb63\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50d36757b18b2c2b6f3eef970a8ccb63\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57098a42ad5b0249685a13f5864ed3df\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57098a42ad5b0249685a13f5864ed3df\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211659168c3565fdaf688939189fa6d6\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211659168c3565fdaf688939189fa6d6\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\470c5fcfaaaf2826abb50f9e27be3471\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\470c5fcfaaaf2826abb50f9e27be3471\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c97eb39be37150a987f04ba73519036\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c97eb39be37150a987f04ba73519036\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1acb5be24c25eaef400aff20adc1dc8d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1acb5be24c25eaef400aff20adc1dc8d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76066564e51505754d88eda134219b01\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76066564e51505754d88eda134219b01\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\844640439d3094dd9757cc4dd4efdc58\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\844640439d3094dd9757cc4dd4efdc58\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001cb4152dcd13f0cac354cdd72d55de\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001cb4152dcd13f0cac354cdd72d55de\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4ef46b7837d44eefd2fdaf7c5649e53\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4ef46b7837d44eefd2fdaf7c5649e53\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c081f8f86e3b98a2aafe24a2c4948a42\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c081f8f86e3b98a2aafe24a2c4948a42\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be221c00c7ce3e196e5e2fce895a3a70\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be221c00c7ce3e196e5e2fce895a3a70\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2bddcf99854293f296b0d3ec5a62fcd\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2bddcf99854293f296b0d3ec5a62fcd\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da49ffbc74db9ed719ed21d8a1ff6f79\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da49ffbc74db9ed719ed21d8a1ff6f79\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.moloco.sdk.acm:moloco-android-client-metrics:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66fbe96fce09d74adbe23d3f33340b9f\transformed\jetified-moloco-android-client-metrics-3.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.moloco.sdk.acm:moloco-android-client-metrics:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66fbe96fce09d74adbe23d3f33340b9f\transformed\jetified-moloco-android-client-metrics-3.11.0\AndroidManifest.xml:6:5-44
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e2799ba8628e61f13a05a486e272f74\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e2799ba8628e61f13a05a486e272f74\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a95787e4d569d5c65431698cfea0a710\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a95787e4d569d5c65431698cfea0a710\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.yandex.div:assertion:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fa19596d38e54466a4cafe71c26ade0\transformed\jetified-assertion-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:assertion:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fa19596d38e54466a4cafe71c26ade0\transformed\jetified-assertion-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:logging:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9200a6cb07d25335d3c68943b2347744\transformed\jetified-logging-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:logging:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9200a6cb07d25335d3c68943b2347744\transformed\jetified-logging-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div-svg:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5670478a1f0dceed29d19b63b9e2e79\transformed\jetified-div-svg-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div-svg:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5670478a1f0dceed29d19b63b9e2e79\transformed\jetified-div-svg-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div-core:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce557585ca1aefa72321f36e4579fc82\transformed\jetified-div-core-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.div:div-core:31.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce557585ca1aefa72321f36e4579fc82\transformed\jetified-div-core-31.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9503b90828062b9442b0f92079897a6\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9503b90828062b9442b0f92079897a6\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\157d9cb1f378b2e49c3d8a5bd09cf673\transformed\jetified-graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\157d9cb1f378b2e49c3d8a5bd09cf673\transformed\jetified-graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e46f960dff35e735cf5d824e3bce25a5\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e46f960dff35e735cf5d824e3bce25a5\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\564190644e700145d329e0387c23df9e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\564190644e700145d329e0387c23df9e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62e94942f0c845f31cfee224a4ca802c\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62e94942f0c845f31cfee224a4ca802c\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bfd4ecb71539cfc8d6558bd5b0059da\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bfd4ecb71539cfc8d6558bd5b0059da\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b7c06bcc1b6b266c67e18c2c3666012\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b7c06bcc1b6b266c67e18c2c3666012\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b92cd65528308b4669229cbf0474f6d\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b92cd65528308b4669229cbf0474f6d\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c53c380eaea52b7bc2de9562de59617\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c53c380eaea52b7bc2de9562de59617\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b91c81bc0e195414fbd0a4af1565888a\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b91c81bc0e195414fbd0a4af1565888a\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96bedf674c26d358e500e30de78381b6\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96bedf674c26d358e500e30de78381b6\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be93f9975e988ce5b48bb2d3f50fd819\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be93f9975e988ce5b48bb2d3f50fd819\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\182525b2536edb247b68eceb93bf0a6e\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\182525b2536edb247b68eceb93bf0a6e\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6ec530a7abeff92b02400c647efafb3\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6ec530a7abeff92b02400c647efafb3\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7ccc3f972f78556d0585b6ed29d65d8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7ccc3f972f78556d0585b6ed29d65d8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b0b6876c46d57a1a9b066b85eecc75\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55b0b6876c46d57a1a9b066b85eecc75\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [io.bidmachine.util:app-intents:0.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50b4cfb838912397f3849c69ff5c9720\transformed\jetified-app-intents-0.13.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine.util:app-intents:0.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50b4cfb838912397f3849c69ff5c9720\transformed\jetified-app-intents-0.13.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine.util:general-util:0.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a24cc9377ff182d6b5e2778f3fff8f5\transformed\jetified-general-util-0.13.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine.util:general-util:0.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a24cc9377ff182d6b5e2778f3fff8f5\transformed\jetified-general-util-0.13.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:analytics:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f8e8d71b3f5f765f7c4edcc886f6495\transformed\jetified-analytics-3.3.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:analytics:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f8e8d71b3f5f765f7c4edcc886f6495\transformed\jetified-analytics-3.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-ktx:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef5b3992ea26a08bb46c50f0ee2fab2e\transformed\jetified-room-ktx-2.5.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef5b3992ea26a08bb46c50f0ee2fab2e\transformed\jetified-room-ktx-2.5.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3196e15ffa88757cd68378f9ef3085e\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c3196e15ffa88757cd68378f9ef3085e\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ab31b6f99418716c4d84fb2d81b2ee3\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ab31b6f99418716c4d84fb2d81b2ee3\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc2bfd62d3443f09bff36862f8d32def\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc2bfd62d3443f09bff36862f8d32def\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36b9b6763060c90dcedfa9fcb47353f6\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36b9b6763060c90dcedfa9fcb47353f6\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d472d6fb44b0dd9f6736c692796df915\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d472d6fb44b0dd9f6736c692796df915\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d917819901ed96aa63fb2d90e8311fc3\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d917819901ed96aa63fb2d90e8311fc3\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34a22c8205f551c33bf11728adb430b0\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34a22c8205f551c33bf11728adb430b0\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\204b10abea0f2aa31b95309975c2abca\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\204b10abea0f2aa31b95309975c2abca\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96680c8c3d232e3f07387255610cfc69\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96680c8c3d232e3f07387255610cfc69\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92e06faeeb860403525f13f65546d0b2\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92e06faeeb860403525f13f65546d0b2\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\881f97646947a0d7e005d8309cd5f52d\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\881f97646947a0d7e005d8309cd5f52d\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87a6a57114ab74d5692912af4bbf01b0\transformed\jetified-activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87a6a57114ab74d5692912af4bbf01b0\transformed\jetified-activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a0b4a9a7edca5edf41b6c9f4848a80e\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a0b4a9a7edca5edf41b6c9f4848a80e\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:6:5-9:63
MERGED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:6:5-9:63
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2caebb15d44877ab6a0f988bc353a39f\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2caebb15d44877ab6a0f988bc353a39f\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2645c67e521ce8d026db4473f840e4f\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2645c67e521ce8d026db4473f840e4f\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\de834790fb5c56be4e6a30bfed65f273\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\de834790fb5c56be4e6a30bfed65f273\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [me.tankery.lib:circularSeekBar:1.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c73f1f05c081f8209d2b94a6236b0fc0\transformed\jetified-circularSeekBar-1.4.2\AndroidManifest.xml:5:5-7:41
MERGED from [me.tankery.lib:circularSeekBar:1.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c73f1f05c081f8209d2b94a6236b0fc0\transformed\jetified-circularSeekBar-1.4.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8fdbfcb9a59e663f6e18ca173a567aed\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8fdbfcb9a59e663f6e18ca173a567aed\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21f7c7da82967c2fc579d2c6757c46d6\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21f7c7da82967c2fc579d2c6757c46d6\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.moloco.sdk.protobuf:moloco-sdk-protobuf:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4cc8f627e40e6a864f0aaea81b11080\transformed\jetified-moloco-sdk-protobuf-3.11.0\AndroidManifest.xml:5:5-44
MERGED from [com.moloco.sdk.protobuf:moloco-sdk-protobuf:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4cc8f627e40e6a864f0aaea81b11080\transformed\jetified-moloco-sdk-protobuf-3.11.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.varioqub:appmetrica-adapter:0.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26822518e5da8747c115a65725e99919\transformed\jetified-appmetrica-adapter-0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.varioqub:appmetrica-adapter:0.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26822518e5da8747c115a65725e99919\transformed\jetified-appmetrica-adapter-0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.varioqub:analytic-adapter:0.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd581c91a6dbe38fe3f9ea72047fc5b6\transformed\jetified-analytic-adapter-0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.varioqub:analytic-adapter:0.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd581c91a6dbe38fe3f9ea72047fc5b6\transformed\jetified-analytic-adapter-0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.varioqub:proto:0.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55be198fd6fef9ec62080687a417f3c2\transformed\jetified-proto-0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.yandex.varioqub:proto:0.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55be198fd6fef9ec62080687a417f3c2\transformed\jetified-proto-0.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:21:5-44
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbc2283a442ddf4da4a7b59652d79671\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbc2283a442ddf4da4a7b59652d79671\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:5:5-44
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1db057b60ca878859754ff58f4b394d9\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1db057b60ca878859754ff58f4b394d9\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-ad-revenue:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c79c739131295291589d7167ed438021\transformed\jetified-analytics-ad-revenue-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-ad-revenue:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c79c739131295291589d7167ed438021\transformed\jetified-analytics-ad-revenue-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-location:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45d431add7cc928f6da0550595831d11\transformed\jetified-analytics-location-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-location:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45d431add7cc928f6da0550595831d11\transformed\jetified-analytics-location-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-remote-permissions:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8de76db7b4e88ae78eab55e8d98bc320\transformed\jetified-analytics-remote-permissions-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-remote-permissions:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8de76db7b4e88ae78eab55e8d98bc320\transformed\jetified-analytics-remote-permissions-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-reporter-extension:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\645dda0939d76198162f97c5137bcab1\transformed\jetified-analytics-reporter-extension-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-reporter-extension:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\645dda0939d76198162f97c5137bcab1\transformed\jetified-analytics-reporter-extension-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-screenshot:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f52e4c343dc02ddfc123cfe43b8f9b09\transformed\jetified-analytics-screenshot-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-screenshot:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f52e4c343dc02ddfc123cfe43b8f9b09\transformed\jetified-analytics-screenshot-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-billing-v6:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8d36735562b1b0f6ec6ca44f01ce844\transformed\jetified-analytics-billing-v6-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-billing-v6:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8d36735562b1b0f6ec6ca44f01ce844\transformed\jetified-analytics-billing-v6-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-billing-interface:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80a8830af44c9cfce4428928bdf180d4\transformed\jetified-analytics-billing-interface-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-billing-interface:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80a8830af44c9cfce4428928bdf180d4\transformed\jetified-analytics-billing-interface-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-ad-revenue-admob-v23:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d84a082b7603bc3ac69d326d24967a2f\transformed\jetified-analytics-ad-revenue-admob-v23-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-ad-revenue-admob-v23:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d84a082b7603bc3ac69d326d24967a2f\transformed\jetified-analytics-ad-revenue-admob-v23-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-ad-revenue-applovin-v12:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea009868f53b25916fc853ab1ddcf7b8\transformed\jetified-analytics-ad-revenue-applovin-v12-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-ad-revenue-applovin-v12:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea009868f53b25916fc853ab1ddcf7b8\transformed\jetified-analytics-ad-revenue-applovin-v12-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-ad-revenue-fyber-v3:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3952f870387e8b13662d39baa978bbd\transformed\jetified-analytics-ad-revenue-fyber-v3-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-ad-revenue-fyber-v3:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3952f870387e8b13662d39baa978bbd\transformed\jetified-analytics-ad-revenue-fyber-v3-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-ad-revenue-ironsource-v7:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0cbcb647b2630c7118fd9bd562de7d3\transformed\jetified-analytics-ad-revenue-ironsource-v7-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-ad-revenue-ironsource-v7:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0cbcb647b2630c7118fd9bd562de7d3\transformed\jetified-analytics-ad-revenue-ironsource-v7-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-modules-api:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c0e23b81e9d45ff05831cfb851bf813\transformed\jetified-analytics-modules-api-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-modules-api:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c0e23b81e9d45ff05831cfb851bf813\transformed\jetified-analytics-modules-api-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-network-tasks:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a19ac5c7a5f5dba30e77286bdc1db70\transformed\jetified-analytics-network-tasks-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-network-tasks:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a19ac5c7a5f5dba30e77286bdc1db70\transformed\jetified-analytics-network-tasks-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-network:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\774f1ea61a34b79813abecd3a61c459c\transformed\jetified-analytics-network-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-network:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\774f1ea61a34b79813abecd3a61c459c\transformed\jetified-analytics-network-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-core-utils:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33600c5d24084ca22f4c27a5de4aa39e\transformed\jetified-analytics-core-utils-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-core-utils:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33600c5d24084ca22f4c27a5de4aa39e\transformed\jetified-analytics-core-utils-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-location-api:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3c00c66fda50d868f907eefce7e928\transformed\jetified-analytics-location-api-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-location-api:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf3c00c66fda50d868f907eefce7e928\transformed\jetified-analytics-location-api-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-core-api:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ce4102c88edd58359cee1a597916a23\transformed\jetified-analytics-core-api-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-core-api:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ce4102c88edd58359cee1a597916a23\transformed\jetified-analytics-core-api-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-proto:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a14eb1ee30051537c13f0db0d3e20638\transformed\jetified-analytics-proto-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-proto:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a14eb1ee30051537c13f0db0d3e20638\transformed\jetified-analytics-proto-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-ndkcrashes-api:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae97f6967f123ff0980fe83d0cf5232f\transformed\jetified-analytics-ndkcrashes-api-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-ndkcrashes-api:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae97f6967f123ff0980fe83d0cf5232f\transformed\jetified-analytics-ndkcrashes-api-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-gpllibrary:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cbd144ffd8cfe4f12f016614d8694b2\transformed\jetified-analytics-gpllibrary-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-gpllibrary:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cbd144ffd8cfe4f12f016614d8694b2\transformed\jetified-analytics-gpllibrary-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-logger:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d10453138607451739ff10ae1ec529e\transformed\jetified-analytics-logger-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-logger:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d10453138607451739ff10ae1ec529e\transformed\jetified-analytics-logger-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce84e3441e81c753232a4c4a9060ae2a\transformed\sqlite-framework-2.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce84e3441e81c753232a4c4a9060ae2a\transformed\sqlite-framework-2.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63ec8fef2aa5aefd24a5161c6f321bde\transformed\sqlite-2.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63ec8fef2aa5aefd24a5161c6f321bde\transformed\sqlite-2.3.1\AndroidManifest.xml:20:5-44
MERGED from [io.appmetrica.analytics:analytics-common-logger:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\831caa456edf722c1796f7de3de9d4d1\transformed\jetified-analytics-common-logger-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [io.appmetrica.analytics:analytics-common-logger:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\831caa456edf722c1796f7de3de9d4d1\transformed\jetified-analytics-common-logger-7.9.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9e47b482c59ee6d8359c1b90f5e9e03\transformed\jetified-shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9e47b482c59ee6d8359c1b90f5e9e03\transformed\jetified-shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea75794504383849bed7ea47cecb5c7c\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea75794504383849bed7ea47cecb5c7c\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6050882545392d1910227aa19f3ce3c\transformed\jetified-BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6050882545392d1910227aa19f3ce3c\transformed\jetified-BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a664b93ad66e5c8134ba2d3befe84641\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a664b93ad66e5c8134ba2d3befe84641\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\090df19a301a8bc86253b8269e331531\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\090df19a301a8bc86253b8269e331531\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b299bde7198fdc676494e96c5efbc47\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b299bde7198fdc676494e96c5efbc47\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6d501cab03905534223b8d2eeb5c097\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6d501cab03905534223b8d2eeb5c097\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\079ea97e3091935aed7b6122a995adf6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\079ea97e3091935aed7b6122a995adf6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d075f442566b1188ed422cb41f677e6\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d075f442566b1188ed422cb41f677e6\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52a971bea2b61498d4613c3e10c1235d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52a971bea2b61498d4613c3e10c1235d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3c7654772673e67543a121581e2b2f5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3c7654772673e67543a121581e2b2f5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d56d3a7eea350f47dd3618bd0df6040\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d56d3a7eea350f47dd3618bd0df6040\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da9563719b3a240d93e9ca03bf9c2974\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da9563719b3a240d93e9ca03bf9c2974\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbb39180551b2e4c313d747fd05d3e5\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbb39180551b2e4c313d747fd05d3e5\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\155f118ca0e3f1eddbaa9ab706ba764a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\155f118ca0e3f1eddbaa9ab706ba764a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dbc455ef1319aefacfd6fdce609a248\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dbc455ef1319aefacfd6fdce609a248\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e9e9101fda2fda8545890612cf8a5a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e9e9101fda2fda8545890612cf8a5a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eec5187bebc0900b2d7b45d2760e2150\transformed\jetified-exoplayer-datasource-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eec5187bebc0900b2d7b45d2760e2150\transformed\jetified-exoplayer-datasource-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7b103e93a896db8d4dc6e740af08cca\transformed\jetified-exoplayer-extractor-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7b103e93a896db8d4dc6e740af08cca\transformed\jetified-exoplayer-extractor-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d1e61c13759d3b8d83fe27d8537e843\transformed\jetified-exoplayer-decoder-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d1e61c13759d3b8d83fe27d8537e843\transformed\jetified-exoplayer-decoder-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a85a860759a01610119ec5c5e2755a3\transformed\jetified-exoplayer-database-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a85a860759a01610119ec5c5e2755a3\transformed\jetified-exoplayer-database-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3304f03c7f56dd8b018d3d426d52a1d2\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3304f03c7f56dd8b018d3d426d52a1d2\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.intuit.sdp:sdp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0532fc82bad615631afcad7a74a4f7fe\transformed\jetified-sdp-android-1.1.1\AndroidManifest.xml:7:5-44
MERGED from [com.intuit.sdp:sdp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0532fc82bad615631afcad7a74a4f7fe\transformed\jetified-sdp-android-1.1.1\AndroidManifest.xml:7:5-44
MERGED from [com.intuit.ssp:ssp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81563d9f2c2af6acb3a72b8756bedd4d\transformed\jetified-ssp-android-1.1.1\AndroidManifest.xml:7:5-44
MERGED from [com.intuit.ssp:ssp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81563d9f2c2af6acb3a72b8756bedd4d\transformed\jetified-ssp-android-1.1.1\AndroidManifest.xml:7:5-44
MERGED from [com.applovin.mediation:bigoads-adapter:5.3.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad02605cde055cfc5f143fbbeb3af9e\transformed\jetified-bigoads-adapter-5.3.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.applovin.mediation:bigoads-adapter:5.3.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad02605cde055cfc5f143fbbeb3af9e\transformed\jetified-bigoads-adapter-5.3.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1f9b8dcdbff0e36191b45702b08a8f1\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1f9b8dcdbff0e36191b45702b08a8f1\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.dagger:dagger-lint-aar:2.56.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e7e9589bd069b809151ed94279c080b\transformed\jetified-dagger-lint-aar-2.56.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.56.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e7e9589bd069b809151ed94279c080b\transformed\jetified-dagger-lint-aar-2.56.1\AndroidManifest.xml:18:3-42
MERGED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:7:5-44
MERGED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:7:5-44
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:5:5-44
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:5:5-44
MERGED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:5:5-44
MERGED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:5:5-44
MERGED from [com.adjust.signature:adjust-android-signature:3.35.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5c8fa94b252e233896bffb033da0678\transformed\jetified-adjust-android-signature-3.35.2\AndroidManifest.xml:5:5-44
MERGED from [com.adjust.signature:adjust-android-signature:3.35.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5c8fa94b252e233896bffb033da0678\transformed\jetified-adjust-android-signature-3.35.2\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine.libs:omsdk:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77da15036d91f6da0c640a8d5e826ed1\transformed\jetified-omsdk-1.5.4\AndroidManifest.xml:5:5-7:41
MERGED from [io.bidmachine.libs:omsdk:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77da15036d91f6da0c640a8d5e826ed1\transformed\jetified-omsdk-1.5.4\AndroidManifest.xml:5:5-7:41
MERGED from [io.bidmachine:ads.networks.gam:3.3.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd511c80e84af72750c7af79b9c916c1\transformed\jetified-ads.networks.gam-3.3.0.3\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam:3.3.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd511c80e84af72750c7af79b9c916c1\transformed\jetified-ads.networks.gam-3.3.0.3\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam_dynamic:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf25d24cf81b4604f91c3bf5f1aa130\transformed\jetified-ads.networks.gam_dynamic-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam_dynamic:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf25d24cf81b4604f91c3bf5f1aa130\transformed\jetified-ads.networks.gam_dynamic-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\608baf3e53004868af1266f6da91f5c1\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\608baf3e53004868af1266f6da91f5c1\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [com.fyber.omsdk:om-sdk:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\434b18b7753db8c3b04a0e05d8011db9\transformed\jetified-om-sdk-1.5.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.fyber.omsdk:om-sdk:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\434b18b7753db8c3b04a0e05d8011db9\transformed\jetified-om-sdk-1.5.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.inmobi.omsdk:inmobi-omsdk:1.5.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bce07eda1cdeed755f97335faaff331\transformed\jetified-inmobi-omsdk-1.5.2.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.inmobi.omsdk:inmobi-omsdk:1.5.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bce07eda1cdeed755f97335faaff331\transformed\jetified-inmobi-omsdk-1.5.2.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.unity3d.ads-mediation:adquality-sdk:7.25.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b178af670e2c11c2d4a3c71d879b3625\transformed\jetified-adquality-sdk-7.25.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.unity3d.ads-mediation:adquality-sdk:7.25.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b178af670e2c11c2d4a3c71d879b3625\transformed\jetified-adquality-sdk-7.25.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.pangle.global:tiktok-business-android-sdk-comp:1.3.7-rc.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d55c01852f381793194acc917f2252b\transformed\jetified-tiktok-business-android-sdk-comp-1.3.7-rc.2\AndroidManifest.xml:12:5-14:41
MERGED from [com.pangle.global:tiktok-business-android-sdk-comp:1.3.7-rc.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d55c01852f381793194acc917f2252b\transformed\jetified-tiktok-business-android-sdk-comp-1.3.7-rc.2\AndroidManifest.xml:12:5-14:41
MERGED from [io.bidmachine:ads.networks.gam.base:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea5d1f5a0b4a872688734357c46f795c\transformed\jetified-ads.networks.gam.base-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam.base:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea5d1f5a0b4a872688734357c46f795c\transformed\jetified-ads.networks.gam.base-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam.versions.v21_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca8423a7eb7e25f4d0d7bf0adbb95a6\transformed\jetified-ads.networks.gam.versions.v21_0_0-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam.versions.v21_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca8423a7eb7e25f4d0d7bf0adbb95a6\transformed\jetified-ads.networks.gam.versions.v21_0_0-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam.versions.v22_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14f796a44abd6363584dd9f663c212e3\transformed\jetified-ads.networks.gam.versions.v22_0_0-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam.versions.v22_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14f796a44abd6363584dd9f663c212e3\transformed\jetified-ads.networks.gam.versions.v22_0_0-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam.versions.v23_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\988fb14e6ffe332be3f4f6e7adedf615\transformed\jetified-ads.networks.gam.versions.v23_0_0-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam.versions.v23_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\988fb14e6ffe332be3f4f6e7adedf615\transformed\jetified-ads.networks.gam.versions.v23_0_0-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam_dynamic.base:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd8b0f1aea2295f74fb04b2d87c413d9\transformed\jetified-ads.networks.gam_dynamic.base-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam_dynamic.base:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd8b0f1aea2295f74fb04b2d87c413d9\transformed\jetified-ads.networks.gam_dynamic.base-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam_dynamic.versions.v21_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c478e145f7666ad964e653dc37c611c2\transformed\jetified-ads.networks.gam_dynamic.versions.v21_0_0-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam_dynamic.versions.v21_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c478e145f7666ad964e653dc37c611c2\transformed\jetified-ads.networks.gam_dynamic.versions.v21_0_0-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam_dynamic.versions.v22_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aab3f98c31c3292f87aff1cab31618a\transformed\jetified-ads.networks.gam_dynamic.versions.v22_0_0-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam_dynamic.versions.v22_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aab3f98c31c3292f87aff1cab31618a\transformed\jetified-ads.networks.gam_dynamic.versions.v22_0_0-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam_dynamic.versions.v23_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aedecfd0004b5b67fb9887b23bb22d60\transformed\jetified-ads.networks.gam_dynamic.versions.v23_0_0-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.bidmachine:ads.networks.gam_dynamic.versions.v23_0_0:3.3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aedecfd0004b5b67fb9887b23bb22d60\transformed\jetified-ads.networks.gam_dynamic.versions.v23_0_0-3.3.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.pangle.global:pag-gecko:2.0.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\565e7cc2ca115aa10a4860ff4b6809c1\transformed\jetified-pag-gecko-2.0.0.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.pangle.global:pag-gecko:2.0.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\565e7cc2ca115aa10a4860ff4b6809c1\transformed\jetified-pag-gecko-2.0.0.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.pangle.global:pag-apm:2.0.0.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1075a695635e5ca4185349e42edd59bf\transformed\jetified-pag-apm-2.0.0.7\AndroidManifest.xml:7:5-9:41
MERGED from [com.pangle.global:pag-apm:2.0.0.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1075a695635e5ca4185349e42edd59bf\transformed\jetified-pag-apm-2.0.0.7\AndroidManifest.xml:7:5-9:41
MERGED from [org.chromium.net:cronet-api:72.3626.96] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad5150bb915f8b45ef87f728e7096ccf\transformed\jetified-cronet-api-72.3626.96\AndroidManifest.xml:18:5-74
MERGED from [org.chromium.net:cronet-api:72.3626.96] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad5150bb915f8b45ef87f728e7096ccf\transformed\jetified-cronet-api-72.3626.96\AndroidManifest.xml:18:5-74
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0678fed54f2d0b0aadc368726d6267d2\transformed\jetified-androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0678fed54f2d0b0aadc368726d6267d2\transformed\jetified-androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4941c30c1ad49227b5c20962bf70d62\transformed\jetified-play-services-ads-24.4.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:32:5-76
MERGED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:11:5-76
MERGED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:11:5-76
MERGED from [com.applovin.mediation:inmobi-adapter:10.8.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8399b9610a3c9f1f00164a74c9cf489f\transformed\jetified-inmobi-adapter-10.8.3.1\AndroidManifest.xml:12:5-76
MERGED from [com.applovin.mediation:inmobi-adapter:10.8.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8399b9610a3c9f1f00164a74c9cf489f\transformed\jetified-inmobi-adapter-10.8.3.1\AndroidManifest.xml:12:5-76
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:23:5-76
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:23:5-76
	android:name
		ADDED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:32:22-73
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:33:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:33:22-65
activity#com.hyprmx.android.sdk.activity.HyprMXOfferViewerActivity
ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:13:9-19:59
	android:label
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:17:13-39
	android:launchMode
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:18:13-43
	android:hardwareAccelerated
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:16:13-47
	android:configChanges
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:15:13-113
	android:theme
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:19:13-56
	android:name
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:14:13-85
activity#com.hyprmx.android.sdk.activity.HyprMXRequiredInformationActivity
ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:20:9-23:63
	android:configChanges
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:22:13-113
	android:theme
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:23:13-60
	android:name
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:21:13-93
activity#com.hyprmx.android.sdk.activity.HyprMXNoOffersActivity
ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:24:9-27:59
	android:configChanges
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:26:13-113
	android:theme
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:27:13-56
	android:name
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:25:13-82
activity#com.hyprmx.android.sdk.overlay.HyprMXBrowserActivity
ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:28:9-32:59
	android:hardwareAccelerated
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:31:13-47
	android:configChanges
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:30:13-113
	android:theme
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:32:13-56
	android:name
		ADDED from [com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:29:13-80
provider#androidx.startup.InitializationProvider
ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:38:9-47:20
MERGED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:52:9-62:20
MERGED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:52:9-62:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7ccc3f972f78556d0585b6ed29d65d8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7ccc3f972f78556d0585b6ed29d65d8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\079ea97e3091935aed7b6122a995adf6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\079ea97e3091935aed7b6122a995adf6\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:43:13-31
	android:authorities
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:40:13-68
	android:exported
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:41:13-37
	android:initOrder
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:42:13-44
	android:name
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:39:13-67
meta-data#com.moloco.sdk.internal.android_context.StartupComponentInitialization
ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:44:13-46:52
	android:value
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:46:17-49
	android:name
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:45:17-102
activity#com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.templates.renderer.fullscreen.FullscreenWebviewActivity
ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:51:9-56:20
	android:exported
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:54:13-37
	android:configChanges
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:53:13-87
	android:theme
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:55:13-56
	android:name
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:52:13-143
activity#com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.vast.VastActivity
ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:57:9-62:59
	android:screenOrientation
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:61:13-51
	android:exported
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:60:13-37
	android:configChanges
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:59:13-87
	android:theme
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:62:13-56
	android:name
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:58:13-105
activity#com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.staticrenderer.StaticAdActivity
ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:63:9-68:59
	android:screenOrientation
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:67:13-51
	android:exported
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:66:13-37
	android:configChanges
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:65:13-87
	android:theme
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:68:13-56
	android:name
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:64:13-119
activity#com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.mraid.MraidActivity
ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:69:9-74:59
	android:screenOrientation
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:73:13-51
	android:exported
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:72:13-37
	android:configChanges
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:71:13-87
	android:theme
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:74:13-56
	android:name
		ADDED from [com.moloco.sdk:moloco-sdk:3.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\AndroidManifest.xml:70:13-107
activity#cat.ereza.customactivityoncrash.activity.DefaultErrorActivity
ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:10:9-12:49
	android:process
		ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:12:13-46
	android:name
		ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:11:13-89
provider#cat.ereza.customactivityoncrash.provider.CaocInitProvider
ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:14:9-18:39
	android:authorities
		ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:16:13-85
	android:exported
		ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:17:13-37
	android:initOrder
		ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:18:13-36
	android:name
		ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:15:13-85
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:10:5-79
MERGED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:16:5-79
MERGED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:16:5-79
MERGED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:11:5-79
MERGED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:11:5-79
MERGED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:13:5-79
MERGED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:13:5-79
MERGED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:9:5-79
MERGED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:19:5-79
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:19:5-79
MERGED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:15:5-79
MERGED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:15:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [io.appmetrica.analytics:analytics-identifiers:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d04ec4c2b2f3151dba7358ca13e4c61\transformed\jetified-analytics-identifiers-7.9.0\AndroidManifest.xml:8:5-79
MERGED from [io.appmetrica.analytics:analytics-identifiers:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d04ec4c2b2f3151dba7358ca13e4c61\transformed\jetified-analytics-identifiers-7.9.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e255d8d77b59114a40f1aaa73e5ae5f\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e255d8d77b59114a40f1aaa73e5ae5f\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:17:5-79
MERGED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:17:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:13:5-79
MERGED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:13:5-79
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:8:5-79
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:8:5-79
MERGED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:11:5-79
MERGED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:11:5-79
MERGED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:9:5-79
MERGED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:9:5-79
MERGED from [com.pangle.global:tiktok-business-android-sdk-comp:1.3.7-rc.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d55c01852f381793194acc917f2252b\transformed\jetified-tiktok-business-android-sdk-comp-1.3.7-rc.2\AndroidManifest.xml:17:5-79
MERGED from [com.pangle.global:tiktok-business-android-sdk-comp:1.3.7-rc.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d55c01852f381793194acc917f2252b\transformed\jetified-tiktok-business-android-sdk-comp-1.3.7-rc.2\AndroidManifest.xml:17:5-79
	android:name
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:10:22-76
activity#com.yandex.mobile.ads.common.AdActivity
ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:14:9-19:46
	tools:ignore
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:19:13-43
	android:configChanges
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:16:13-122
	android:theme
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:18:13-70
	android:enableOnBackInvokedCallback
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:17:13-56
	android:name
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:15:13-67
provider#com.yandex.mobile.ads.core.initializer.MobileAdsInitializeProvider
ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:21:9-24:40
	android:authorities
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:23:13-79
	android:exported
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:24:13-37
	android:name
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:22:13-94
activity#com.yandex.mobile.ads.features.debugpanel.ui.IntegrationInspectorActivity
ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:26:9-31:46
	android:exported
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:29:13-37
	tools:ignore
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:31:13-43
	android:theme
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:30:13-51
	android:enableOnBackInvokedCallback
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:28:13-56
	android:name
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:27:13-101
provider#com.yandex.mobile.ads.features.debugpanel.data.local.DebugPanelFileProvider
ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:33:9-41:20
	android:grantUriPermissions
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:37:13-47
	android:authorities
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:35:13-91
	android:exported
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:36:13-37
	android:name
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:34:13-103
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:38:13-40:66
	android:resource
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:40:17-63
	android:name
		ADDED from [com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:39:17-67
queries
ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:8:5-12:15
MERGED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:13:5-36:15
MERGED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:13:5-36:15
MERGED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:15:5-23:15
MERGED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:15:5-23:15
MERGED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:11:5-15:15
MERGED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:11:5-15:15
MERGED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:9:5-26:15
MERGED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:9:5-26:15
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:35:5-68:15
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:7:5-14:15
MERGED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:7:5-14:15
MERGED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:17:5-23:15
MERGED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:17:5-23:15
MERGED from [io.bidmachine.util:app-intents:0.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50b4cfb838912397f3849c69ff5c9720\transformed\jetified-app-intents-0.13.0\AndroidManifest.xml:7:5-24:15
MERGED from [io.bidmachine.util:app-intents:0.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50b4cfb838912397f3849c69ff5c9720\transformed\jetified-app-intents-0.13.0\AndroidManifest.xml:7:5-24:15
MERGED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:11:5-13:15
MERGED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:11:5-13:15
MERGED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:13:5-17:15
MERGED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:13:5-17:15
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:14:5-18:15
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:14:5-18:15
MERGED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:11:5-18:15
MERGED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:11:5-18:15
intent#action:name:com.attribution.REFERRAL_PROVIDER
ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:9:9-11:18
action#com.attribution.REFERRAL_PROVIDER
ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:10:13-72
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:10:21-69
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:17:5-83
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:29:5-83
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:29:5-83
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:12:5-83
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:12:5-83
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:17:22-80
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:18:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:9:5-88
MERGED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:9:5-88
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:10:5-88
MERGED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:10:5-88
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:18:22-85
activity#com.unity3d.services.ads.adunit.AdUnitActivity
ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:21:9-26:74
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:25:13-47
	android:exported
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:24:13-37
	android:configChanges
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:23:13-170
	android:theme
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:26:13-71
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:22:13-74
activity#com.unity3d.services.ads.adunit.AdUnitTransparentActivity
ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:27:9-32:86
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:31:13-47
	android:exported
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:30:13-37
	android:configChanges
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:29:13-170
	android:theme
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:32:13-83
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:28:13-85
activity#com.unity3d.services.ads.adunit.AdUnitTransparentSoftwareActivity
ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:33:9-38:86
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:37:13-48
	android:exported
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:36:13-37
	android:configChanges
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:35:13-170
	android:theme
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:38:13-83
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:34:13-93
activity#com.unity3d.services.ads.adunit.AdUnitSoftwareActivity
ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:39:9-44:74
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:43:13-48
	android:exported
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:42:13-37
	android:configChanges
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:41:13-170
	android:theme
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:44:13-71
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:40:13-82
activity#com.unity3d.ads.adplayer.FullScreenWebViewDisplay
ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:45:9-50:74
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:49:13-47
	android:exported
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:48:13-37
	android:configChanges
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:47:13-170
	android:theme
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:50:13-71
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:46:13-77
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:56:13-58:52
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7ccc3f972f78556d0585b6ed29d65d8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7ccc3f972f78556d0585b6ed29d65d8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:58:17-49
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:57:17-78
meta-data#com.unity3d.services.core.configuration.AdsSdkInitializer
ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:59:13-61:52
	android:value
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:61:17-49
	android:name
		ADDED from [com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:60:17-89
intent#action:name:androidx.browser.customtabs.CustomTabsService
ADDED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:14:9-16:18
action#androidx.browser.customtabs.CustomTabsService
ADDED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:15:13-84
	android:name
		ADDED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:15:21-81
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:17:9-23:18
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:http
ADDED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:24:9-30:18
intent#action:name:android.intent.action.VIEW+data:scheme:market
ADDED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:31:9-35:18
activity#com.inmobi.ads.rendering.InMobiAdActivity
ADDED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:39:9-44:46
	android:hardwareAccelerated
		ADDED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:42:13-47
	tools:ignore
		ADDED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:44:13-43
	android:configChanges
		ADDED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:41:13-139
	android:theme
		ADDED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:43:13-60
	android:name
		ADDED from [com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:40:13-69
meta-data#com.bytedance.sdk.pangle.version
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:26:9-28:39
	android:value
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:27:13-60
activity#com.bytedance.sdk.openadsdk.activity.TTCeilingLandingPageActivity
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:30:9-33:45
	android:launchMode
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:33:13-42
	android:configChanges
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:32:13-74
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:31:13-93
activity#com.bytedance.sdk.openadsdk.activity.TTLandingPageActivity
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:34:9-38:54
	android:launchMode
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:37:13-42
	android:configChanges
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:36:13-74
	android:theme
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:38:13-51
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:35:13-86
activity#com.bytedance.sdk.openadsdk.activity.TTPlayableLandingPageActivity
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:39:9-43:54
	android:launchMode
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:42:13-42
	android:configChanges
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:41:13-74
	android:theme
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:43:13-51
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:40:13-94
activity#com.bytedance.sdk.openadsdk.activity.TTVideoLandingPageLink2Activity
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:44:9-48:54
	android:launchMode
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:47:13-42
	android:configChanges
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:46:13-74
	android:theme
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:48:13-51
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:45:13-96
activity#com.bytedance.sdk.openadsdk.activity.TTDelegateActivity
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:49:9-52:75
	android:launchMode
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:51:13-42
	android:theme
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:50:13-83
activity#com.bytedance.sdk.openadsdk.activity.TTWebsiteActivity
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:53:9-57:62
	android:screenOrientation
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:56:13-49
	android:launchMode
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:55:13-42
	android:theme
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:57:13-59
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:54:13-82
service#com.bytedance.sdk.openadsdk.multipro.aidl.BinderPoolService
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:59:9-95
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:59:18-92
activity#com.bytedance.sdk.openadsdk.activity.TTAppOpenAdActivity
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:61:9-65:66
	android:launchMode
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:64:13-42
	android:configChanges
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:63:13-74
	android:theme
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:65:13-63
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:62:13-84
activity#com.bytedance.sdk.openadsdk.activity.TTRewardVideoActivity
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:66:9-70:57
	android:launchMode
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:69:13-42
	android:configChanges
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:68:13-74
	android:theme
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:70:13-54
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:67:13-86
activity#com.bytedance.sdk.openadsdk.activity.TTRewardExpressVideoActivity
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:71:9-75:57
	android:launchMode
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:74:13-42
	android:configChanges
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:73:13-74
	android:theme
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:75:13-54
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:72:13-93
activity#com.bytedance.sdk.openadsdk.activity.TTFullScreenVideoActivity
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:76:9-80:57
	android:launchMode
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:79:13-42
	android:configChanges
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:78:13-74
	android:theme
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:80:13-54
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:77:13-90
activity#com.bytedance.sdk.openadsdk.activity.TTFullScreenExpressVideoActivity
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:81:9-85:57
	android:launchMode
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:84:13-42
	android:configChanges
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:83:13-74
	android:theme
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:85:13-54
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:82:13-97
activity#com.bytedance.sdk.openadsdk.activity.TTInterstitialActivity
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:86:9-90:65
	android:launchMode
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:89:13-42
	android:configChanges
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:88:13-74
	android:theme
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:90:13-62
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:87:13-87
activity#com.bytedance.sdk.openadsdk.activity.TTInterstitialExpressActivity
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:91:9-95:65
	android:launchMode
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:94:13-42
	android:configChanges
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:93:13-74
	android:theme
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:95:13-62
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:92:13-94
activity#com.bytedance.sdk.openadsdk.activity.TTAdActivity
ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:96:9-100:57
	android:launchMode
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:99:13-42
	android:configChanges
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:98:13-74
	android:theme
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:100:13-54
	android:name
		ADDED from [com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:97:13-77
package#com.android.chrome
ADDED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:12:9-54
	android:name
		ADDED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:12:18-51
package#com.google.android.webview
ADDED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:13:9-62
	android:name
		ADDED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:13:18-59
package#com.android.webview
ADDED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:14:9-55
	android:name
		ADDED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:14:18-52
provider#io.bidmachine.BidMachineInitProvider
ADDED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:18:9-22:39
	android:authorities
		ADDED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:20:13-74
	android:exported
		ADDED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:21:13-37
	android:initOrder
		ADDED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:22:13-36
	android:name
		ADDED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:19:13-64
activity#io.bidmachine.nativead.view.VideoPlayerActivity
ADDED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:24:9-26:80
	android:theme
		ADDED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:26:13-77
	android:name
		ADDED from [io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:25:13-75
package#com.android.vending
ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:10:9-55
MERGED from [io.bidmachine.util:app-intents:0.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50b4cfb838912397f3849c69ff5c9720\transformed\jetified-app-intents-0.13.0\AndroidManifest.xml:8:9-55
MERGED from [io.bidmachine.util:app-intents:0.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50b4cfb838912397f3849c69ff5c9720\transformed\jetified-app-intents-0.13.0\AndroidManifest.xml:8:9-55
	android:name
		ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:10:18-52
activity#io.bidmachine.rendering.ad.fullscreen.FullScreenActivity
ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:29:9-33:74
	android:hardwareAccelerated
		ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:32:13-47
	android:configChanges
		ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:31:13-122
	android:theme
		ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:33:13-71
	android:name
		ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:30:13-84
activity#io.bidmachine.iab.mraid.MraidActivity
ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:34:9-38:74
	android:hardwareAccelerated
		ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:37:13-47
	android:configChanges
		ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:36:13-74
	android:theme
		ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:38:13-71
	android:name
		ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:35:13-65
activity#io.bidmachine.iab.vast.activity.VastActivity
ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:39:9-43:74
	android:hardwareAccelerated
		ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:42:13-47
	android:configChanges
		ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:41:13-74
	android:theme
		ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:43:13-71
	android:name
		ADDED from [io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:40:13-72
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deed93cbe32bc5dcd6b60b11dbd4cded\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:27:22-79
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:99:13-82
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:107:9-109:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:109:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:108:13-79
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION
ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:110:9-112:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:112:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:111:13-83
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b46c683c1e8f7166289ee1301d1a94f\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1f9b8dcdbff0e36191b45702b08a8f1\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:9:5-110
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1f9b8dcdbff0e36191b45702b08a8f1\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:32:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:31:13-84
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
intent#action:name:com.applovin.am.intent.action.APPHUB_SERVICE
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:8:9-10:18
action#com.applovin.am.intent.action.APPHUB_SERVICE
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:9:13-83
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:9:21-80
uses-permission#com.applovin.array.apphub.permission.BIND_APPHUB_SERVICE
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:20:5-96
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:20:22-93
provider#com.applovin.sdk.AppLovinInitProvider
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:26:9-30:39
	android:authorities
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:28:13-72
	android:exported
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:29:13-37
	android:initOrder
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:30:13-36
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:27:13-65
activity#com.applovin.adview.AppLovinFullscreenActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:32:9-39:74
	android:screenOrientation
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:38:13-47
	android:launchMode
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:37:13-43
	android:hardwareAccelerated
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:36:13-47
	android:exported
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:35:13-37
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:34:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:39:13-71
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:33:13-74
activity#com.applovin.sdk.AppLovinWebViewActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:40:9-42:142
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:42:13-139
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:41:13-68
activity#com.applovin.mediation.MaxDebuggerActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:45:9-48:87
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:47:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:48:13-84
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:46:13-70
activity#com.applovin.mediation.MaxDebuggerDetailActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:49:9-52:87
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:51:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:52:13-84
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:50:13-76
activity#com.applovin.mediation.MaxDebuggerMultiAdActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:53:9-56:87
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:55:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:56:13-84
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:54:13-77
activity#com.applovin.mediation.MaxDebuggerAdUnitsListActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:57:9-60:87
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:59:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:60:13-84
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:58:13-81
activity#com.applovin.mediation.MaxDebuggerAdUnitWaterfallsListActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:61:9-64:87
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:63:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:64:13-84
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:62:13-90
activity#com.applovin.mediation.MaxDebuggerAdUnitDetailActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:65:9-68:87
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:67:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:68:13-84
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:66:13-82
activity#com.applovin.mediation.MaxDebuggerCmpNetworksListActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:69:9-72:87
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:71:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:72:13-84
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:70:13-85
activity#com.applovin.mediation.MaxDebuggerTcfConsentStatusesListActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:73:9-76:87
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:75:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:76:13-84
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:74:13-92
activity#com.applovin.mediation.MaxDebuggerTcfInfoListActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:77:9-80:87
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:79:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:80:13-84
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:78:13-81
activity#com.applovin.mediation.MaxDebuggerTcfStringActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:81:9-84:87
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:83:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:84:13-84
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:82:13-79
activity#com.applovin.mediation.MaxDebuggerTestLiveNetworkActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:85:9-88:87
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:87:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:88:13-84
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:86:13-85
activity#com.applovin.mediation.MaxDebuggerTestModeNetworkActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:89:9-92:87
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:91:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:92:13-84
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:90:13-85
activity#com.applovin.mediation.MaxDebuggerUnifiedFlowActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:93:9-96:87
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:95:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:96:13-84
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:94:13-81
activity#com.applovin.mediation.MaxDebuggerWaterfallSegmentsActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:97:9-100:87
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:99:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:100:13-84
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:98:13-87
activity#com.applovin.creative.MaxCreativeDebuggerActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:101:9-104:91
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:103:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:104:13-88
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:102:13-77
activity#com.applovin.creative.MaxCreativeDebuggerDisplayedAdActivity
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:105:9-108:91
	android:configChanges
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:107:13-139
	android:theme
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:108:13-88
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:106:13-88
service#com.applovin.impl.adview.activity.FullscreenAdService
ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:111:9-114:44
	android:exported
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:113:13-37
	android:stopWithTask
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:114:13-41
	android:name
		ADDED from [com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:112:13-81
intent#action:name:android.intent.action.ACTION_VIEW+data:scheme:https
ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:18:9-22:18
action#android.intent.action.ACTION_VIEW
ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:19:13-72
	android:name
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:19:21-69
activity#com.chartboost.sdk.view.CBImpressionActivity
ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:26:9-33:80
	android:excludeFromRecents
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:30:13-46
	android:hardwareAccelerated
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:32:13-47
	android:exported
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:31:13-37
	android:configChanges
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:28:13-106
	android:theme
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:33:13-77
	android:enableOnBackInvokedCallback
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:29:13-55
	android:name
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:27:13-72
activity#com.chartboost.sdk.internal.clickthrough.EmbeddedBrowserActivity
ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:34:9-40:86
	android:excludeFromRecents
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:37:13-46
	android:hardwareAccelerated
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:39:13-47
	android:exported
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:36:13-74
	android:theme
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:40:13-83
	android:name
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:35:13-92
service#com.chartboost.sdk.internal.video.repository.exoplayer.VideoRepositoryDownloadService
ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:43:9-53:19
	android:exported
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:45:13-37
	android:name
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:44:13-113
intent-filter#action:name:com.google.android.exoplayer.downloadService.action.RESTART+category:name:android.intent.category.DEFAULT
ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:48:13-52:29
action#com.google.android.exoplayer.downloadService.action.RESTART
ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:49:17-102
	android:name
		ADDED from [com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:49:25-99
service#io.appmetrica.analytics.internal.AppMetricaService
ADDED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:15:9-26:19
	android:enabled
		ADDED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:17:13-35
	android:exported
		ADDED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:18:13-37
	android:name
		ADDED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:16:13-78
intent-filter#action:name:io.appmetrica.analytics.IAppMetricaService+category:name:android.intent.category.DEFAULT+data:scheme:appmetrica
ADDED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:19:13-25:29
action#io.appmetrica.analytics.IAppMetricaService
ADDED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:22:17-85
	android:name
		ADDED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:22:25-82
provider#io.appmetrica.analytics.internal.PreloadInfoContentProvider
ADDED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:29:9-34:54
	android:enabled
		ADDED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:32:13-35
	android:authorities
		ADDED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:31:13-81
	android:exported
		ADDED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:33:13-36
	tools:ignore
		ADDED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:34:13-51
	android:name
		ADDED from [io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:30:13-87
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:16:17-126
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:19:17-115
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar
ADDED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:30:17-128
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar
ADDED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:33:17-117
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.fc.p.tj.charginganimation.batterycharging.chargeeffect.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.fc.p.tj.charginganimation.batterycharging.chargeeffect.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
package#com.facebook.katana
ADDED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:12:9-55
	android:name
		ADDED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:12:18-52
activity#com.facebook.ads.AudienceNetworkActivity
ADDED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:20:9-24:75
	android:exported
		ADDED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:23:13-37
	android:configChanges
		ADDED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:22:13-106
	android:theme
		ADDED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:24:13-72
	android:name
		ADDED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:21:13-68
provider#com.facebook.ads.AudienceNetworkContentProvider
ADDED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:26:9-29:40
	android:authorities
		ADDED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:28:13-82
	android:exported
		ADDED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:29:13-37
	android:name
		ADDED from [com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:27:13-75
meta-data#com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar
ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
activity#com.vungle.ads.internal.ui.VungleActivity
ADDED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:16:9-20:46
	android:launchMode
		ADDED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:20:13-43
	android:hardwareAccelerated
		ADDED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:19:13-47
	android:configChanges
		ADDED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:18:13-113
	android:name
		ADDED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:17:13-69
provider#com.vungle.ads.VungleProvider
ADDED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:22:9-26:39
	android:authorities
		ADDED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:24:13-67
	android:exported
		ADDED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:25:13-37
	android:initOrder
		ADDED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:26:13-36
	android:name
		ADDED from [com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:23:13-57
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:25:13-74
provider#com.squareup.picasso.PicassoProvider
ADDED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:8:9-11:40
	android:authorities
		ADDED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:10:13-72
	android:exported
		ADDED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:9:13-64
activity#com.ironsource.sdk.controller.ControllerActivity
ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:12:9-16:63
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:15:13-47
	android:configChanges
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:14:13-59
	android:theme
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:16:13-60
	android:name
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:13:13-76
activity#com.ironsource.sdk.controller.InterstitialActivity
ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:17:9-21:75
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:20:13-47
	android:configChanges
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:19:13-59
	android:theme
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:21:13-72
	android:name
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:18:13-78
activity#com.ironsource.sdk.controller.OpenUrlActivity
ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:22:9-26:75
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:25:13-47
	android:configChanges
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:24:13-59
	android:theme
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:26:13-72
	android:name
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:23:13-73
activity#com.ironsource.mediationsdk.testSuite.TestSuiteActivity
ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:27:9-36:20
	android:hardwareAccelerated
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:31:13-47
	android:exported
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:30:13-37
	android:configChanges
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:29:13-59
	android:theme
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:32:13-60
	android:name
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:28:13-83
meta-data#android.webkit.WebView.EnableSafeBrowsing
ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:33:13-35:40
	android:value
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:35:17-37
	android:name
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:34:17-73
provider#com.ironsource.lifecycle.IronsourceLifecycleProvider
ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:38:9-41:40
	android:authorities
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:40:13-79
	android:exported
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:39:13-80
provider#com.ironsource.lifecycle.LevelPlayActivityLifecycleProvider
ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:42:9-45:40
	android:authorities
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:44:13-86
	android:exported
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:45:13-37
	android:name
		ADDED from [com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:43:13-87
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
provider#com.adjust.sdk.SystemLifecycleContentProvider
ADDED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:11:9-14:40
	android:authorities
		ADDED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:13:13-77
	android:exported
		ADDED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:14:13-37
	android:name
		ADDED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:12:13-73
activity#sg.bigo.ads.ad.splash.AdSplashActivity
ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:20:9-23:52
	android:screenOrientation
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:22:13-49
	android:theme
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:23:13-49
	android:name
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:21:13-66
activity#sg.bigo.ads.ad.splash.LandscapeAdSplashActivity
ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:24:9-27:52
	android:screenOrientation
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:26:13-50
	android:theme
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:27:13-49
	android:name
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:25:13-75
provider#sg.bigo.ads.controller.provider.BigoAdsProvider
ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:29:9-32:40
	android:authorities
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:31:13-67
	android:exported
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:30:13-75
activity#sg.bigo.ads.controller.form.AdFormActivity
ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:34:9-36:55
	android:windowSoftInputMode
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:36:13-52
	android:name
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:35:13-70
activity#sg.bigo.ads.api.AdActivity
ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:37:9-43:20
	android:screenOrientation
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:40:13-49
	android:windowSoftInputMode
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:42:13-60
	android:configChanges
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:39:13-59
	android:theme
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:41:13-72
	android:name
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:38:13-54
activity#sg.bigo.ads.api.PopupAdActivity
ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:44:9-50:20
	android:screenOrientation
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:47:13-49
	android:windowSoftInputMode
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:49:13-60
	android:configChanges
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:46:13-59
	android:theme
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:48:13-53
	android:name
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:45:13-59
activity#sg.bigo.ads.api.LandingStyleableActivity
ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:51:9-57:20
	android:screenOrientation
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:54:13-47
	android:windowSoftInputMode
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:56:13-60
	android:configChanges
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:53:13-59
	android:theme
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:55:13-79
	android:name
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:52:13-68
activity#sg.bigo.ads.api.LandscapeAdActivity
ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:58:9-64:20
	android:screenOrientation
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:61:13-50
	android:windowSoftInputMode
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:63:13-60
	android:configChanges
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:60:13-59
	android:theme
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:62:13-72
	android:name
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:59:13-63
activity#sg.bigo.ads.api.CompanionAdActivity
ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:65:9-71:20
	android:screenOrientation
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:68:13-49
	android:windowSoftInputMode
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:70:13-60
	android:configChanges
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:67:13-59
	android:theme
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:69:13-72
	android:name
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:66:13-63
activity#sg.bigo.ads.api.LandscapeCompanionAdActivity
ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:72:9-78:20
	android:screenOrientation
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:75:13-50
	android:windowSoftInputMode
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:77:13-60
	android:configChanges
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:74:13-59
	android:theme
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:76:13-72
	android:name
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:73:13-72
activity#sg.bigo.ads.core.mraid.MraidVideoActivity
ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:79:9-83:74
	android:screenOrientation
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:82:13-49
	android:configChanges
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:81:13-74
	android:theme
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:83:13-71
	android:name
		ADDED from [com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:80:13-69
intent#action:name:com.digitalturbine.ignite.cl.IgniteRemoteService
ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:15:9-17:18
action#com.digitalturbine.ignite.cl.IgniteRemoteService
ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:16:13-87
	android:name
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:16:21-84
activity#com.fyber.inneractive.sdk.activities.InneractiveInternalBrowserActivity
ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:21:9-25:52
	android:screenOrientation
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:25:13-49
	android:hardwareAccelerated
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:24:13-47
	android:configChanges
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:23:13-106
	android:name
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:22:13-99
activity#com.fyber.inneractive.sdk.activities.InneractiveFullscreenAdActivity
ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:26:9-30:74
	android:hardwareAccelerated
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:29:13-47
	android:configChanges
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:28:13-106
	android:theme
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:30:13-71
	android:name
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:27:13-96
activity#com.fyber.inneractive.sdk.activities.InneractiveRichMediaVideoPlayerActivityCore
ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:31:9-35:74
	android:hardwareAccelerated
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:34:13-47
	android:configChanges
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:33:13-106
	android:theme
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:35:13-71
	android:name
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:32:13-108
activity#com.fyber.inneractive.sdk.activities.InternalStoreWebpageActivity
ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:36:9-41:75
	android:screenOrientation
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:40:13-47
	android:excludeFromRecents
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:39:13-46
	android:configChanges
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:38:13-106
	android:theme
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:41:13-72
	android:name
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:37:13-93
activity#com.fyber.inneractive.sdk.activities.FyberReportAdActivity
ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:42:9-46:52
	android:screenOrientation
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:46:13-49
	android:hardwareAccelerated
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:45:13-47
	android:configChanges
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:44:13-106
	android:name
		ADDED from [com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:43:13-86
intent#action:name:android.intent.action.MAIN
ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:12:9-14:18
intent#action:name:android.intent.action.VIEW
ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:15:9-17:18
activity#com.mbridge.msdk.activity.MBCommonActivity
ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:23:9-28:80
	android:excludeFromRecents
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:26:13-46
	android:exported
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:27:13-37
	android:configChanges
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:25:13-57
	android:theme
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:28:13-77
	android:name
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:24:13-70
activity#com.mbridge.msdk.out.LoadingActivity
ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:31:9-73
	android:name
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:31:19-70
activity#com.mbridge.msdk.newreward.player.MBRewardVideoActivity
ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:33:9-37:75
	android:excludeFromRecents
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:36:13-46
	android:configChanges
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:35:13-74
	android:theme
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:37:13-72
	android:name
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:34:13-83
activity#com.mbridge.msdk.reward.player.MBRewardVideoActivity
ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:38:9-42:75
	android:excludeFromRecents
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:41:13-46
	android:configChanges
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:40:13-74
	android:theme
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:42:13-72
	android:name
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:39:13-80
receiver#com.mbridge.msdk.foundation.same.broadcast.NetWorkChangeReceiver
ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:44:9-50:20
	android:exported
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:46:13-36
	android:name
		ADDED from [com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:45:13-92
