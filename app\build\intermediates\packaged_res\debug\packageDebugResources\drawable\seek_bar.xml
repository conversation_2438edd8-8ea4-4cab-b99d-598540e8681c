<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:gravity="center_vertical">
        <shape android:shape="rectangle">
            <solid android:color="@color/empty"/>
            <size android:height="2dp"/>
            <corners android:radius="1dp"/>
        </shape>
    </item>
    <item android:gravity="center_vertical">
        <scale android:scaleWidth="100%">
            <selector>
                <item
                    android:state_enabled="false"
                    android:drawable="@android:color/transparent"/>
                <item>
                    <shape android:shape="rectangle">
                        <solid android:color="@color/empty"/>
                        <size android:height="10dp"/>
                        <corners android:radius="3dp"/>
                    </shape>
                </item>
            </selector>
        </scale>
    </item>
</layer-list>
