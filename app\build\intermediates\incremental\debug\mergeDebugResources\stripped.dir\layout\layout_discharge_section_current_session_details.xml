<?xml version="1.0" encoding="utf-8"?>
<!-- 
 * Current discharge session details section
 * This section contains:
 * - Section title with info button
 * - Total time and current rate
 * - Average speed and total consumed
 * - Screen on and screen off statistics
 */
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/currentSessionDetailsRoot"
    android:orientation="vertical"
    android:background="@drawable/white_block"
    android:paddingVertical="7dp"
    android:paddingHorizontal="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="14dp"
    android:layout_marginStart="9dp"
    android:layout_marginEnd="9dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/csd_tv_title"
            android:textSize="19sp"
            android:textColor="?attr/black"
            android:text="@string/current_session"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="2dp"
            android:layout_width="0dp"
            app:layout_constraintEnd_toStartOf="@+id/csd_iv_info_button"
            android:layout_height="wrap_content"/>
            <!-- Original st_text -->
        <ImageView
            android:id="@+id/csd_iv_info_button"
            android:layout_width="22sp"
            android:layout_height="0dp"
            android:src="@drawable/ic_note"
            android:scaleType="fitEnd"
            android:layout_marginStart="5dp"
            app:layout_constraintTop_toTopOf="@id/csd_tv_title"
            app:layout_constraintBottom_toBottomOf="@id/csd_tv_title"
            app:layout_constraintEnd_toEndOf="parent"/>
            <!-- Original discharge_session_info -->
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Row 1: Total Time, Current Rate -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp">
        <!-- Total Time Block -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/csd_cl_total_time"
            android:background="@drawable/grey_block_static"
            android:paddingVertical="7dp"
            android:paddingHorizontal="9dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/csd_cl_current_rate"
            android:layout_marginEnd="4dp">
            <TextView
                android:id="@+id/csd_tv_total_time_label"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/all_time_charge"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_total_time_value"
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:text="@string/zero_seconds"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_total_time_label"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_fulltime_dis_session -->
            <TextView
                android:id="@+id/csd_tv_session_start_time"
                android:textSize="14sp"
                android:textColor="?attr/colorr"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_total_time_value"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original time_dis_session_start -->
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Current Rate Block -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/csd_cl_current_rate"
            android:background="@drawable/grey_block_static"
             android:paddingVertical="7dp"
            android:paddingHorizontal="9dp"
            android:layout_width="0dp"
            android:layout_height="0dp" 
            app:layout_constraintTop_toTopOf="@id/csd_cl_total_time"
            app:layout_constraintBottom_toBottomOf="@id/csd_cl_total_time"
            app:layout_constraintStart_toEndOf="@id/csd_cl_total_time"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="4dp">
             <TextView
                android:id="@+id/csd_tv_current_rate_label"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/now"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_current_rate_value"
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_current_rate_label"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_now_dis_session -->
            <TextView
                android:textSize="11sp"
                android:textColor="?attr/black"
                android:text="@string/percent_in_hour"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_current_rate_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_current_rate_value"
                android:layout_width="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_height="wrap_content"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Row 2: Average Speed, Total Consumed -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp">
        <!-- Average Speed Block -->
         <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/csd_cl_avg_speed"
            android:background="@drawable/grey_block_static"
            android:paddingVertical="7dp"
            android:paddingHorizontal="9dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/csd_cl_total_consumed"
            android:layout_marginEnd="4dp">
            <TextView
                android:id="@+id/csd_tv_avg_speed_label"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/average_speed"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_avg_speed_percent_value"
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_avg_speed_label"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_percent_dis_all_session -->
            <TextView
                android:textSize="11sp"
                android:textColor="?attr/black"
                android:text="@string/percent_in_hour"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_avg_speed_percent_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_avg_speed_percent_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_avg_speed_mah_value"
                android:textSize="14sp"
                android:textColor="?attr/colorr"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_avg_speed_percent_value"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_speed_dis_all_session -->
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/ma_in_medium"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_avg_speed_mah_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_avg_speed_mah_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Total Consumed Block -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/csd_cl_total_consumed"
            android:background="@drawable/grey_block_static"
             android:paddingVertical="7dp"
            android:paddingHorizontal="9dp"
            android:layout_width="0dp"
            android:layout_height="0dp" 
            app:layout_constraintTop_toTopOf="@id/csd_cl_avg_speed"
            app:layout_constraintBottom_toBottomOf="@id/csd_cl_avg_speed"
            app:layout_constraintStart_toEndOf="@id/csd_cl_avg_speed"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="4dp">
            <TextView
                android:id="@+id/csd_tv_total_consumed_label"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/all"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_total_consumed_percent_value"
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_total_consumed_label"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_percent_dis_session_last -->
            <TextView
                android:id="@+id/csd_tv_total_consumed_percent_unit_and_range"
                android:textSize="11sp"
                android:textColor="?attr/black"
                android:text="@string/percent" 
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_total_consumed_percent_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_total_consumed_percent_value"
                android:layout_width="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_height="wrap_content"/>
                <!-- Combined original textView_percent and discharge_session_percent -->
            <TextView
                android:id="@+id/csd_tv_total_consumed_mah_value"
                android:textSize="14sp"
                android:textColor="?attr/colorr"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_total_consumed_percent_value"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_speed_dis_day_session (misused for total mAh) -->
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/ma"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_total_consumed_mah_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_total_consumed_mah_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Row 3: Screen Off Stats, Screen On Stats -->
     <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp">
        <!-- Screen Off Session Stats Block -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/csd_cl_screen_off_stats"
            android:background="@drawable/grey_block_static"
             android:paddingVertical="7dp"
            android:paddingHorizontal="9dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/csd_cl_screen_on_stats"
            android:layout_marginEnd="4dp">
            <TextView
                android:id="@+id/csd_tv_screen_off_stats_label"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/screen_off"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_screen_off_percent_value"
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_screen_off_stats_label"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_percent_dis_night_session -->
            <TextView
                android:textSize="11sp"
                android:textColor="?attr/black"
                android:text="@string/percent_in_hour"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_screen_off_percent_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_screen_off_percent_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_screen_off_mah_value"
                android:textSize="14sp"
                android:textColor="?attr/colorr"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_screen_off_percent_value"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_speed_dis_night_session -->
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/ma_in_medium"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_screen_off_mah_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_screen_off_mah_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Screen On Session Stats Block -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/csd_cl_screen_on_stats"
            android:background="@drawable/grey_block_static"
             android:paddingVertical="7dp"
            android:paddingHorizontal="9dp"
            android:layout_width="0dp"
            android:layout_height="0dp" 
            app:layout_constraintTop_toTopOf="@id/csd_cl_screen_off_stats"
            app:layout_constraintBottom_toBottomOf="@id/csd_cl_screen_off_stats"
            app:layout_constraintStart_toEndOf="@id/csd_cl_screen_off_stats"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="4dp">
             <TextView
                android:id="@+id/csd_tv_screen_on_stats_label"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/active_mode"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_screen_on_percent_value"
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_screen_on_stats_label"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_percent_dis_day_session -->
            <TextView
                android:textSize="11sp"
                android:textColor="?attr/black"
                android:text="@string/percent_in_hour"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_screen_on_percent_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_screen_on_percent_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_screen_on_mah_value"
                android:textSize="14sp"
                android:textColor="?attr/colorr"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_screen_on_percent_value"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_speed_dis_day_session2 -->
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/ma_in_medium"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_screen_on_mah_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_screen_on_mah_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout> 