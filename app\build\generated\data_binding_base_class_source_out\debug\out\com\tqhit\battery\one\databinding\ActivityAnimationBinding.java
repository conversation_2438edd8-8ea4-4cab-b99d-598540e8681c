// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.media3.ui.PlayerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAnimationBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout applyBlock;

  @NonNull
  public final Button applyButton;

  @NonNull
  public final Button backButton;

  @NonNull
  public final TextView batteryPercent;

  @NonNull
  public final LinearLayout dateTimeContainer;

  @NonNull
  public final AppCompatImageView iconAd;

  @NonNull
  public final PlayerView playerView;

  @NonNull
  public final TextView textBtn;

  @NonNull
  public final TextView textDate;

  @NonNull
  public final TextView textTime;

  @NonNull
  public final TextView timeRemaining;

  @NonNull
  public final TextView timeRemainingValue;

  private ActivityAnimationBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout applyBlock, @NonNull Button applyButton, @NonNull Button backButton,
      @NonNull TextView batteryPercent, @NonNull LinearLayout dateTimeContainer,
      @NonNull AppCompatImageView iconAd, @NonNull PlayerView playerView, @NonNull TextView textBtn,
      @NonNull TextView textDate, @NonNull TextView textTime, @NonNull TextView timeRemaining,
      @NonNull TextView timeRemainingValue) {
    this.rootView = rootView;
    this.applyBlock = applyBlock;
    this.applyButton = applyButton;
    this.backButton = backButton;
    this.batteryPercent = batteryPercent;
    this.dateTimeContainer = dateTimeContainer;
    this.iconAd = iconAd;
    this.playerView = playerView;
    this.textBtn = textBtn;
    this.textDate = textDate;
    this.textTime = textTime;
    this.timeRemaining = timeRemaining;
    this.timeRemainingValue = timeRemainingValue;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAnimationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAnimationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_animation, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAnimationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.apply_block;
      ConstraintLayout applyBlock = ViewBindings.findChildViewById(rootView, id);
      if (applyBlock == null) {
        break missingId;
      }

      id = R.id.applyButton;
      Button applyButton = ViewBindings.findChildViewById(rootView, id);
      if (applyButton == null) {
        break missingId;
      }

      id = R.id.back_button;
      Button backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.battery_percent;
      TextView batteryPercent = ViewBindings.findChildViewById(rootView, id);
      if (batteryPercent == null) {
        break missingId;
      }

      id = R.id.date_time_container;
      LinearLayout dateTimeContainer = ViewBindings.findChildViewById(rootView, id);
      if (dateTimeContainer == null) {
        break missingId;
      }

      id = R.id.icon_ad;
      AppCompatImageView iconAd = ViewBindings.findChildViewById(rootView, id);
      if (iconAd == null) {
        break missingId;
      }

      id = R.id.playerView;
      PlayerView playerView = ViewBindings.findChildViewById(rootView, id);
      if (playerView == null) {
        break missingId;
      }

      id = R.id.text_btn;
      TextView textBtn = ViewBindings.findChildViewById(rootView, id);
      if (textBtn == null) {
        break missingId;
      }

      id = R.id.text_date;
      TextView textDate = ViewBindings.findChildViewById(rootView, id);
      if (textDate == null) {
        break missingId;
      }

      id = R.id.text_time;
      TextView textTime = ViewBindings.findChildViewById(rootView, id);
      if (textTime == null) {
        break missingId;
      }

      id = R.id.time_remaining;
      TextView timeRemaining = ViewBindings.findChildViewById(rootView, id);
      if (timeRemaining == null) {
        break missingId;
      }

      id = R.id.time_remaining_value;
      TextView timeRemainingValue = ViewBindings.findChildViewById(rootView, id);
      if (timeRemainingValue == null) {
        break missingId;
      }

      return new ActivityAnimationBinding((ConstraintLayout) rootView, applyBlock, applyButton,
          backButton, batteryPercent, dateTimeContainer, iconAd, playerView, textBtn, textDate,
          textTime, timeRemaining, timeRemainingValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
