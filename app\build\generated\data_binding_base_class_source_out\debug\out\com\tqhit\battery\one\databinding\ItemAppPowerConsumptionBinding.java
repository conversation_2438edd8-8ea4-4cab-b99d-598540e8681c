// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAppPowerConsumptionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView ivAppIcon;

  @NonNull
  public final TextView tvAppName;

  @NonNull
  public final TextView tvPercentage;

  @NonNull
  public final TextView tvPowerConsumption;

  @NonNull
  public final TextView tvUsageTime;

  private ItemAppPowerConsumptionBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView ivAppIcon, @NonNull TextView tvAppName, @NonNull TextView tvPercentage,
      @NonNull TextView tvPowerConsumption, @NonNull TextView tvUsageTime) {
    this.rootView = rootView;
    this.ivAppIcon = ivAppIcon;
    this.tvAppName = tvAppName;
    this.tvPercentage = tvPercentage;
    this.tvPowerConsumption = tvPowerConsumption;
    this.tvUsageTime = tvUsageTime;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAppPowerConsumptionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAppPowerConsumptionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_app_power_consumption, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAppPowerConsumptionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_app_icon;
      ImageView ivAppIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivAppIcon == null) {
        break missingId;
      }

      id = R.id.tv_app_name;
      TextView tvAppName = ViewBindings.findChildViewById(rootView, id);
      if (tvAppName == null) {
        break missingId;
      }

      id = R.id.tv_percentage;
      TextView tvPercentage = ViewBindings.findChildViewById(rootView, id);
      if (tvPercentage == null) {
        break missingId;
      }

      id = R.id.tv_power_consumption;
      TextView tvPowerConsumption = ViewBindings.findChildViewById(rootView, id);
      if (tvPowerConsumption == null) {
        break missingId;
      }

      id = R.id.tv_usage_time;
      TextView tvUsageTime = ViewBindings.findChildViewById(rootView, id);
      if (tvUsageTime == null) {
        break missingId;
      }

      return new ItemAppPowerConsumptionBinding((LinearLayout) rootView, ivAppIcon, tvAppName,
          tvPercentage, tvPowerConsumption, tvUsageTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
