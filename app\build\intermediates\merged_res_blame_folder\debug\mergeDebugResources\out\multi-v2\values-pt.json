{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "159,257,258,259", "startColumns": "4,4,4,4", "startOffsets": "13383,23684,23783,23895", "endColumns": "114,98,111,105", "endOffsets": "13493,23778,23890,23996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "152,153,172,173,196,282,284,364,370,404,405,416,433,434,440,441,443", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12863,12958,15936,16033,19134,25714,25861,34069,34565,37491,37576,38472,40124,40203,40748,40824,40943", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,78,74,75,66,112", "endOffsets": "12953,13039,16028,16127,19215,25792,25953,34155,34647,37571,37661,38543,40198,40273,40819,40886,41051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-pt\\values-pt.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "362,363", "startColumns": "4,4", "startOffsets": "33837,33949", "endColumns": "111,119", "endOffsets": "33944,34064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "27221,27340,27461,27577,27693,27795,27892,28006,28140,28258,28410,28494,28595,28690,28790,28905,29035,29141,29280,29416,29547,29713,29840,29960,30084,30204,30300,30397,30517,30633,30733,30844,30953,31093,31238,31348,31451,31537,31631,31723,31839,31929,32018,32119,32199,32283,32384,32490,32582,32681,32769,32881,32982,33086,33205,33285,33385", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "27335,27456,27572,27688,27790,27887,28001,28135,28253,28405,28489,28590,28685,28785,28900,29030,29136,29275,29411,29542,29708,29835,29955,30079,30199,30295,30392,30512,30628,30728,30839,30948,31088,31233,31343,31446,31532,31626,31718,31834,31924,32013,32114,32194,32278,32379,32485,32577,32676,32764,32876,32977,33081,33200,33280,33380,33472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,11,15,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,236,237,238,239,240,241,242,243,244,245,246,249,250,251,252,253,254", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,19614,19695,19778,19851,19950,20046,20120,20186,20282,20377,20443,20512,20579,20650,20768,20885,21006,21073,21159,21235,21309,21407,21507,21571,22321,22374,22432,22480,22541,22606,22668,22734,22804,22868,22929,23126,23179,23243,23321,23399,23458", "endLines": "10,14,18,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,236,237,238,239,240,241,242,243,244,245,246,249,250,251,252,253,254", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,52,63,77,77,58,70", "endOffsets": "330,516,695,19690,19773,19846,19945,20041,20115,20181,20277,20372,20438,20507,20574,20645,20763,20880,21001,21068,21154,21230,21304,21402,21502,21566,21630,22369,22427,22475,22536,22601,22663,22729,22799,22863,22924,22990,23174,23238,23316,23394,23453,23524"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-pt\\values-pt.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "247,248", "startColumns": "4,4", "startOffsets": "22995,23060", "endColumns": "64,65", "endOffsets": "23055,23121"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-pt\\values-pt.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "41699,41760,41822,41868,41963,42003,42053,42110,42165,42226,42292,42358,42404,42502,42595,42679,42756,42796,42862,42910,43003,43077,43131,43183,43274,43323,43382,43436,43511,43557,43626,43721,43780,43850,43941,44000,44065,44138,44194,44245,44297,44357,44424,44488,44575,44677", "endColumns": "60,61,45,94,39,49,56,54,60,65,65,45,97,92,83,76,39,65,47,92,73,53,51,90,48,58,53,74,45,68,94,58,69,90,58,64,72,55,50,51,59,66,63,86,101,56", "endOffsets": "41755,41817,41863,41958,41998,42048,42105,42160,42221,42287,42353,42399,42497,42590,42674,42751,42791,42857,42905,42998,43072,43126,43178,43269,43318,43377,43431,43506,43552,43621,43716,43775,43845,43936,43995,44060,44133,44189,44240,44292,44352,44419,44483,44570,44672,44729"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-pt\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,268,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,269,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,270,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24758,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24815,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24869,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,55,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,52,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,83,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24809,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24863,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,24948,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,37,38,41,42,53,54,56,57,58,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,121,122,123,124,125,126,127,128,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,154,155,156,157,160,161,162,163,164,165,166,167,168,169,170,171,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,197,198,199,200,201,202,256,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,283,285,286,287,288,289,290,291,292,293,294,295,296,297,355,356,357,358,359,360,361,365,366,367,368,369,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,398,399,400,401,402,403,406,407,408,409,410,411,412,413,415,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,436,437,439,442,444,445,446,447,448,497,498,499,500,501,502,505,506,507,508,509,510,511,512,513,514,515,516", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "700,759,807,855,903,951,1002,1050,1098,1152,1219,1268,1315,1365,1412,1463,1778,1855,2134,2188,3176,3231,3419,3491,3569,3919,3989,4052,4104,4175,4213,4371,4427,4517,4559,4620,4710,4757,5043,5092,5426,5469,5545,5590,5632,5692,5738,5782,5872,5931,6013,6419,6820,6863,6928,6991,7719,7805,7887,7961,8036,8245,8566,10396,10449,10490,10550,10620,10674,10743,10812,11619,11663,11743,11824,11868,11950,12114,12190,12295,12380,12449,12542,12615,12687,12742,12798,13044,13098,13144,13213,13498,13548,13915,13970,14027,14640,15250,15323,15376,15522,15592,15883,16132,16629,16695,16835,16986,17147,17214,17262,17309,17384,17495,17587,17637,17792,17896,17952,18016,18082,18395,18668,18877,19036,19220,19303,19370,19424,19482,19521,23616,24001,24058,24284,24335,24416,24480,24545,24689,24751,24889,25108,25147,25205,25242,25279,25314,25349,25426,25467,25520,25573,25641,25797,25958,26025,26077,26133,26177,26217,26291,26739,26793,26868,27035,27110,27185,33477,33511,33551,33593,33649,33711,33779,34160,34238,34389,34424,34505,34731,34784,34877,34947,35021,35126,35244,35322,35366,35446,35741,35855,36037,36127,36227,36270,36309,36363,36413,36481,36562,36610,36676,36764,36813,37007,37051,37170,37242,37295,37392,37666,37716,37785,37993,38074,38133,38193,38271,38411,38548,38745,38829,38928,39011,39062,39151,39218,39293,39335,39392,39438,39554,39913,39997,40078,40379,40444,40693,40891,41056,41095,41153,41224,41440,44734,44796,44865,44917,44987,45054,45260,45320,45372,45434,45479,45520,45575,45631,45705,45754,45790,45825", "endColumns": "58,47,47,47,47,50,47,47,53,66,48,46,49,46,50,88,76,58,53,49,54,76,71,77,77,69,62,51,70,37,157,55,89,41,60,89,46,285,48,333,42,75,44,41,59,45,43,89,58,81,405,400,42,64,62,727,85,81,73,74,208,320,77,52,40,59,69,53,68,68,68,43,79,80,43,81,163,75,104,84,68,92,72,71,54,55,64,53,45,68,99,49,366,54,56,612,609,72,52,145,69,290,52,496,65,139,150,160,66,47,46,74,110,91,49,154,103,55,63,65,312,272,208,158,97,82,66,53,57,38,92,67,56,225,50,80,63,64,143,61,137,218,38,57,36,36,34,34,76,40,52,52,67,72,63,66,51,55,43,39,73,447,53,74,166,74,74,35,33,39,41,55,61,67,57,77,150,34,80,59,52,92,69,73,104,117,77,43,79,294,113,181,89,99,42,38,53,49,67,80,47,65,87,48,42,43,118,71,52,96,98,49,68,207,80,58,59,77,53,60,196,83,98,82,50,88,66,74,41,56,45,115,358,83,80,45,64,79,54,51,38,57,70,215,90,61,68,51,69,66,37,59,51,61,44,40,54,55,73,48,35,34,43", "endOffsets": "754,802,850,898,946,997,1045,1093,1147,1214,1263,1310,1360,1407,1458,1547,1850,1909,2183,2233,3226,3303,3486,3564,3642,3984,4047,4099,4170,4208,4366,4422,4512,4554,4615,4705,4752,5038,5087,5421,5464,5540,5585,5627,5687,5733,5777,5867,5926,6008,6414,6815,6858,6923,6986,7714,7800,7882,7956,8031,8240,8561,8639,10444,10485,10545,10615,10669,10738,10807,10876,11658,11738,11819,11863,11945,12109,12185,12290,12375,12444,12537,12610,12682,12737,12793,12858,13093,13139,13208,13308,13543,13910,13965,14022,14635,15245,15318,15371,15517,15587,15878,15931,16624,16690,16830,16981,17142,17209,17257,17304,17379,17490,17582,17632,17787,17891,17947,18011,18077,18390,18663,18872,19031,19129,19298,19365,19419,19477,19516,19609,23679,24053,24279,24330,24411,24475,24540,24684,24746,24884,25103,25142,25200,25237,25274,25309,25344,25421,25462,25515,25568,25636,25709,25856,26020,26072,26128,26172,26212,26286,26734,26788,26863,27030,27105,27180,27216,33506,33546,33588,33644,33706,33774,33832,34233,34384,34419,34500,34560,34779,34872,34942,35016,35121,35239,35317,35361,35441,35736,35850,36032,36122,36222,36265,36304,36358,36408,36476,36557,36605,36671,36759,36808,36851,37046,37165,37237,37290,37387,37486,37711,37780,37988,38069,38128,38188,38266,38320,38467,38740,38824,38923,39006,39057,39146,39213,39288,39330,39387,39433,39549,39908,39992,40073,40119,40439,40519,40743,40938,41090,41148,41219,41435,41526,44791,44860,44912,44982,45049,45087,45315,45367,45429,45474,45515,45570,45626,45700,45749,45785,45820,45864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "158,255,371,397,438,503,504", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "13313,23529,34652,36856,40524,45092,45179", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "13378,23611,34726,37002,40688,45174,45255"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "449,450", "startColumns": "4,4", "startOffsets": "41531,41614", "endColumns": "82,84", "endOffsets": "41609,41694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9b1d1c56b528abe32d69bef2c9ce868e\\transformed\\jetified-bigo-ads-5.3.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8644,8720,8795,8859,8917,8983,9062,9151,9224,9293,9349,9406,9740,9817,9903,9958,10033,10095,10149,10211,10259,10320", "endColumns": "75,74,63,57,65,78,88,72,68,55,56,333,76,85,54,74,61,53,61,47,60,75", "endOffsets": "8715,8790,8854,8912,8978,9057,9146,9219,9288,9344,9401,9735,9812,9898,9953,10028,10090,10144,10206,10254,10315,10391"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "129,130,131,132,133,134,135,435", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "10881,10978,11080,11179,11279,11389,11499,40278", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "10973,11075,11174,11274,11384,11494,11614,40374"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "227,228,229,230,231,232,233,234,235", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "21635,21705,21775,21847,21913,21990,22057,22158,22251", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "21700,21770,21842,21908,21985,22052,22153,22246,22316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "35,36,39,40,43,44,45,46,47,48,49,50,51,52,55,59,60,414", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1552,1672,1914,2015,2238,2329,2422,2517,2611,2711,2804,2899,2994,3085,3308,3647,3757,38325", "endColumns": "119,105,100,118,90,92,94,93,99,92,94,94,90,90,110,109,161,85", "endOffsets": "1667,1773,2010,2129,2324,2417,2512,2606,2706,2799,2894,2989,3080,3171,3414,3752,3914,38406"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tqhit.battery.one.app-mergeDebugResources-132:\\values-pt\\values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "159,257,258,259", "startColumns": "4,4,4,4", "startOffsets": "13383,23684,23783,23895", "endColumns": "114,98,111,105", "endOffsets": "13493,23778,23890,23996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,1011,1101,1253,1332,1407,1483,1550", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,78,74,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,1006,1096,1172,1327,1402,1478,1545,1658"}, "to": {"startLines": "152,153,172,173,196,282,284,363,369,402,403,414,430,431,437,438,440", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12863,12958,15936,16033,19134,25714,25861,34013,34509,37382,37467,38363,39931,40010,40555,40631,40750", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,78,74,75,66,112", "endOffsets": "12953,13039,16028,16127,19215,25792,25953,34099,34591,37462,37552,38434,40005,40080,40626,40693,40858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "361,362", "startColumns": "4,4", "startOffsets": "33781,33893", "endColumns": "111,119", "endOffsets": "33888,34008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4673,4763,4852,4953,5033,5117,5218,5324,5416,5515,5603,5715,5816,5920,6039,6119,6219", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4668,4758,4847,4948,5028,5112,5213,5319,5411,5510,5598,5710,5811,5915,6034,6114,6214,6306"}, "to": {"startLines": "297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "27165,27284,27405,27521,27637,27739,27836,27950,28084,28202,28354,28438,28539,28634,28734,28849,28979,29085,29224,29360,29491,29657,29784,29904,30028,30148,30244,30341,30461,30577,30677,30788,30897,31037,31182,31292,31395,31481,31575,31667,31783,31873,31962,32063,32143,32227,32328,32434,32526,32625,32713,32825,32926,33030,33149,33229,33329", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "27279,27400,27516,27632,27734,27831,27945,28079,28197,28349,28433,28534,28629,28729,28844,28974,29080,29219,29355,29486,29652,29779,29899,30023,30143,30239,30336,30456,30572,30672,30783,30892,31032,31177,31287,31390,31476,31570,31662,31778,31868,31957,32058,32138,32222,32323,32429,32521,32620,32708,32820,32921,33025,33144,33224,33324,33416"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,650,731,814,887,986,1082,1156,1222,1318,1413,1479,1548,1615,1686,1804,1921,2042,2109,2195,2271,2345,2443,2543,2607,2671,2724,2782,2830,2891,2956,3018,3084,3154,3218,3279,3345,3398,3462,3540,3618,3677", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,52,63,77,77,58,70", "endOffsets": "280,466,645,726,809,882,981,1077,1151,1217,1313,1408,1474,1543,1610,1681,1799,1916,2037,2104,2190,2266,2340,2438,2538,2602,2666,2719,2777,2825,2886,2951,3013,3079,3149,3213,3274,3340,3393,3457,3535,3613,3672,3743"}, "to": {"startLines": "2,11,15,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,236,237,238,239,240,241,242,243,244,245,246,249,250,251,252,253,254", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,19614,19695,19778,19851,19950,20046,20120,20186,20282,20377,20443,20512,20579,20650,20768,20885,21006,21073,21159,21235,21309,21407,21507,21571,22321,22374,22432,22480,22541,22606,22668,22734,22804,22868,22929,23126,23179,23243,23321,23399,23458", "endLines": "10,14,18,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,236,237,238,239,240,241,242,243,244,245,246,249,250,251,252,253,254", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,52,63,77,77,58,70", "endOffsets": "330,516,695,19690,19773,19846,19945,20041,20115,20181,20277,20372,20438,20507,20574,20645,20763,20880,21001,21068,21154,21230,21304,21402,21502,21566,21630,22369,22427,22475,22536,22601,22663,22729,22799,22863,22924,22990,23174,23238,23316,23394,23453,23524"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-pt\\values-pt.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3345,3410", "endColumns": "64,65", "endOffsets": "3405,3471"}, "to": {"startLines": "247,248", "startColumns": "4,4", "startOffsets": "22995,23060", "endColumns": "64,65", "endOffsets": "23055,23121"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,116,178,224,319,359,409,466,521,582,648,714,760,858,951,1035,1112,1152,1218,1266,1359,1433,1487,1539,1630,1679,1738,1792,1867,1913,1982,2077,2136,2206,2297,2356,2421,2494,2550,2601,2653,2713,2780,2844,2931,3033", "endColumns": "60,61,45,94,39,49,56,54,60,65,65,45,97,92,83,76,39,65,47,92,73,53,51,90,48,58,53,74,45,68,94,58,69,90,58,64,72,55,50,51,59,66,63,86,101,56", "endOffsets": "111,173,219,314,354,404,461,516,577,643,709,755,853,946,1030,1107,1147,1213,1261,1354,1428,1482,1534,1625,1674,1733,1787,1862,1908,1977,2072,2131,2201,2292,2351,2416,2489,2545,2596,2648,2708,2775,2839,2926,3028,3085"}, "to": {"startLines": "448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "41506,41567,41629,41675,41770,41810,41860,41917,41972,42033,42099,42165,42211,42309,42402,42486,42563,42603,42669,42717,42810,42884,42938,42990,43081,43130,43189,43243,43318,43364,43433,43528,43587,43657,43748,43807,43872,43945,44001,44052,44104,44164,44231,44295,44382,44484", "endColumns": "60,61,45,94,39,49,56,54,60,65,65,45,97,92,83,76,39,65,47,92,73,53,51,90,48,58,53,74,45,68,94,58,69,90,58,64,72,55,50,51,59,66,63,86,101,56", "endOffsets": "41562,41624,41670,41765,41805,41855,41912,41967,42028,42094,42160,42206,42304,42397,42481,42558,42598,42664,42712,42805,42879,42933,42985,43076,43125,43184,43238,43313,43359,43428,43523,43582,43652,43743,43802,43867,43940,43996,44047,44099,44159,44226,44290,44377,44479,44536"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-pt\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,265,30,31,32,33,34,35,36,241,251,254,256,37,2,264,244,262,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,239,57,58,243,59,60,61,62,63,64,65,66,67,68,249,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,259,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,260,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,242,168,169,170,171,172,173,174,175,176,177,178,247,246,245,253,257,179,180,181,182,183,184,255,185,186,187,240,188,261,189,190,191,192,193,194,195,196,248,197,198,258,199,200,201,202,203,252,204,205,206,207,208,209,210,211,212,213,214,215,216,263,217,218,219,220,221,222,223,224,225,226,250,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "134,194,243,292,341,390,442,491,540,595,663,713,761,812,860,22014,912,990,1050,1105,1156,1212,1290,1363,1442,1521,1592,1656,24639,1709,1748,1907,1964,2055,2098,2160,22278,23126,23582,23699,2251,57,24593,22489,24473,2295,2342,2387,2478,2538,2621,3028,3430,12457,12523,11728,11641,12587,3474,3549,3625,3835,4157,4236,4290,4332,4393,4464,4519,22163,4589,4659,22408,4704,4786,4831,4914,5079,5156,5262,5348,5418,5512,23000,5586,5642,5699,5765,5820,5867,5937,6038,6089,6457,6513,6571,7185,7796,7870,7924,8071,8142,8434,8488,8986,9053,9194,9346,9508,9576,9625,9673,9749,9861,9954,10005,10161,10266,10323,10388,10455,10769,11043,11253,11413,11512,12672,12740,24276,12795,12835,12929,12998,13056,13283,13335,13417,13482,13548,13693,13756,13895,14115,14155,14214,14252,14290,14326,14362,14440,14482,14536,14590,14659,24335,14733,14801,14854,14899,14940,15015,15464,15519,15595,15763,15839,15915,15952,15987,16028,16071,16128,16191,16260,16319,16398,16550,22326,16586,16647,16701,16795,16866,16941,17047,17166,17245,17290,17371,22806,22623,22532,23481,24034,17667,17707,17762,17813,17882,17964,23632,18013,18102,18152,22233,18196,24400,18316,18414,18514,18565,18635,18844,18926,18986,22921,19047,19102,24078,19164,19249,19349,19433,19485,23413,19575,19651,19694,19752,19799,19916,20276,20358,20405,20471,20552,20608,20661,24534,20701,20773,20990,21082,21145,21215,21268,21339,21407,21446,23073,21507,21570,21616,21658,21714,21771,21846,21896,21933,21969", "endColumns": "58,47,47,47,47,50,47,47,53,66,48,46,49,46,50,88,76,58,53,49,54,76,71,77,77,69,62,51,70,37,157,55,89,41,60,89,46,285,48,333,42,75,44,41,59,45,43,89,58,81,405,400,42,64,62,727,85,81,73,74,208,320,77,52,40,59,69,53,68,68,68,43,79,80,43,81,163,75,104,84,68,92,72,71,54,55,64,53,45,68,99,49,366,54,56,612,609,72,52,145,69,290,52,496,65,139,150,160,66,47,46,74,110,91,49,154,103,55,63,65,312,272,208,158,97,82,66,53,57,38,92,67,56,225,50,80,63,64,143,61,137,218,38,57,36,36,34,34,76,40,52,52,67,72,63,66,51,43,39,73,447,53,74,166,74,74,35,33,39,41,55,61,67,57,77,150,34,80,59,52,92,69,73,104,117,77,43,79,294,113,181,89,99,42,38,53,49,67,80,47,65,87,48,42,43,118,71,96,98,49,68,207,80,58,59,77,53,60,196,83,98,82,50,88,66,74,41,56,45,115,358,80,45,64,79,54,51,38,57,70,215,90,61,68,51,69,66,37,59,51,61,44,40,54,55,73,48,35,34,43", "endOffsets": "188,237,286,335,384,436,485,534,589,657,707,755,806,854,906,22098,984,1044,1099,1150,1206,1284,1357,1436,1515,1586,1650,1703,24705,1742,1901,1958,2049,2092,2154,2245,22320,23407,23626,24028,2289,128,24633,22526,24528,2336,2381,2472,2532,2615,3022,3424,3468,12517,12581,12451,11722,12664,3543,3619,3829,4151,4230,4284,4326,4387,4458,4513,4583,22227,4653,4698,22483,4780,4825,4908,5073,5150,5256,5342,5412,5506,5580,23067,5636,5693,5759,5814,5861,5931,6032,6083,6451,6507,6565,7179,7790,7864,7918,8065,8136,8428,8482,8980,9047,9188,9340,9502,9570,9619,9667,9743,9855,9948,9999,10155,10260,10317,10382,10449,10763,11037,11247,11407,11506,11590,12734,12789,24329,12829,12923,12992,13050,13277,13329,13411,13476,13542,13687,13750,13889,14109,14149,14208,14246,14284,14320,14356,14434,14476,14530,14584,14653,14727,24394,14795,14848,14893,14934,15009,15458,15513,15589,15757,15833,15909,15946,15981,16022,16065,16122,16185,16254,16313,16392,16544,16580,22402,16641,16695,16789,16860,16935,17041,17160,17239,17284,17365,17661,22915,22800,22617,23576,24072,17701,17756,17807,17876,17958,18007,23693,18096,18146,18190,22272,18310,24467,18408,18508,18559,18629,18838,18920,18980,19041,22994,19096,19158,24270,19243,19343,19427,19479,19569,23475,19645,19688,19746,19793,19910,20270,20352,20399,20465,20546,20602,20655,20695,24587,20767,20984,21076,21139,21209,21262,21333,21401,21440,21501,23120,21564,21610,21652,21708,21765,21840,21890,21927,21963,22008"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,37,38,41,42,53,54,56,57,58,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,121,122,123,124,125,126,127,128,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,154,155,156,157,160,161,162,163,164,165,166,167,168,169,170,171,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,197,198,199,200,201,202,256,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,283,285,286,287,288,289,290,291,292,293,294,295,296,354,355,356,357,358,359,360,364,365,366,367,368,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,397,398,399,400,401,404,405,406,407,408,409,410,411,413,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,433,434,436,439,441,442,443,444,445,494,495,496,497,498,499,502,503,504,505,506,507,508,509,510,511,512,513", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "700,759,807,855,903,951,1002,1050,1098,1152,1219,1268,1315,1365,1412,1463,1778,1855,2134,2188,3176,3231,3419,3491,3569,3919,3989,4052,4104,4175,4213,4371,4427,4517,4559,4620,4710,4757,5043,5092,5426,5469,5545,5590,5632,5692,5738,5782,5872,5931,6013,6419,6820,6863,6928,6991,7719,7805,7887,7961,8036,8245,8566,10396,10449,10490,10550,10620,10674,10743,10812,11619,11663,11743,11824,11868,11950,12114,12190,12295,12380,12449,12542,12615,12687,12742,12798,13044,13098,13144,13213,13498,13548,13915,13970,14027,14640,15250,15323,15376,15522,15592,15883,16132,16629,16695,16835,16986,17147,17214,17262,17309,17384,17495,17587,17637,17792,17896,17952,18016,18082,18395,18668,18877,19036,19220,19303,19370,19424,19482,19521,23616,24001,24058,24284,24335,24416,24480,24545,24689,24751,24889,25108,25147,25205,25242,25279,25314,25349,25426,25467,25520,25573,25641,25797,25958,26025,26077,26121,26161,26235,26683,26737,26812,26979,27054,27129,33421,33455,33495,33537,33593,33655,33723,34104,34182,34333,34368,34449,34675,34728,34821,34891,34965,35070,35188,35266,35310,35390,35685,35799,35981,36071,36171,36214,36253,36307,36357,36425,36506,36554,36620,36708,36757,36951,36995,37114,37186,37283,37557,37607,37676,37884,37965,38024,38084,38162,38302,38439,38636,38720,38819,38902,38953,39042,39109,39184,39226,39283,39329,39445,39804,39885,40186,40251,40500,40698,40863,40902,40960,41031,41247,44541,44603,44672,44724,44794,44861,45067,45127,45179,45241,45286,45327,45382,45438,45512,45561,45597,45632", "endColumns": "58,47,47,47,47,50,47,47,53,66,48,46,49,46,50,88,76,58,53,49,54,76,71,77,77,69,62,51,70,37,157,55,89,41,60,89,46,285,48,333,42,75,44,41,59,45,43,89,58,81,405,400,42,64,62,727,85,81,73,74,208,320,77,52,40,59,69,53,68,68,68,43,79,80,43,81,163,75,104,84,68,92,72,71,54,55,64,53,45,68,99,49,366,54,56,612,609,72,52,145,69,290,52,496,65,139,150,160,66,47,46,74,110,91,49,154,103,55,63,65,312,272,208,158,97,82,66,53,57,38,92,67,56,225,50,80,63,64,143,61,137,218,38,57,36,36,34,34,76,40,52,52,67,72,63,66,51,43,39,73,447,53,74,166,74,74,35,33,39,41,55,61,67,57,77,150,34,80,59,52,92,69,73,104,117,77,43,79,294,113,181,89,99,42,38,53,49,67,80,47,65,87,48,42,43,118,71,96,98,49,68,207,80,58,59,77,53,60,196,83,98,82,50,88,66,74,41,56,45,115,358,80,45,64,79,54,51,38,57,70,215,90,61,68,51,69,66,37,59,51,61,44,40,54,55,73,48,35,34,43", "endOffsets": "754,802,850,898,946,997,1045,1093,1147,1214,1263,1310,1360,1407,1458,1547,1850,1909,2183,2233,3226,3303,3486,3564,3642,3984,4047,4099,4170,4208,4366,4422,4512,4554,4615,4705,4752,5038,5087,5421,5464,5540,5585,5627,5687,5733,5777,5867,5926,6008,6414,6815,6858,6923,6986,7714,7800,7882,7956,8031,8240,8561,8639,10444,10485,10545,10615,10669,10738,10807,10876,11658,11738,11819,11863,11945,12109,12185,12290,12375,12444,12537,12610,12682,12737,12793,12858,13093,13139,13208,13308,13543,13910,13965,14022,14635,15245,15318,15371,15517,15587,15878,15931,16624,16690,16830,16981,17142,17209,17257,17304,17379,17490,17582,17632,17787,17891,17947,18011,18077,18390,18663,18872,19031,19129,19298,19365,19419,19477,19516,19609,23679,24053,24279,24330,24411,24475,24540,24684,24746,24884,25103,25142,25200,25237,25274,25309,25344,25421,25462,25515,25568,25636,25709,25856,26020,26072,26116,26156,26230,26678,26732,26807,26974,27049,27124,27160,33450,33490,33532,33588,33650,33718,33776,34177,34328,34363,34444,34504,34723,34816,34886,34960,35065,35183,35261,35305,35385,35680,35794,35976,36066,36166,36209,36248,36302,36352,36420,36501,36549,36615,36703,36752,36795,36990,37109,37181,37278,37377,37602,37671,37879,37960,38019,38079,38157,38211,38358,38631,38715,38814,38897,38948,39037,39104,39179,39221,39278,39324,39440,39799,39880,39926,40246,40326,40550,40745,40897,40955,41026,41242,41333,44598,44667,44719,44789,44856,44894,45122,45174,45236,45281,45322,45377,45433,45507,45556,45592,45627,45671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "158,255,370,396,435,500,501", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "13313,23529,34596,36800,40331,44899,44986", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "13378,23611,34670,36946,40495,44981,45062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "446,447", "startColumns": "4,4", "startOffsets": "41338,41421", "endColumns": "82,84", "endOffsets": "41416,41501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9b1d1c56b528abe32d69bef2c9ce868e\\transformed\\jetified-bigo-ads-5.3.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,206,270,328,394,473,562,635,704,760,817,1151,1228,1314,1369,1444,1506,1560,1622,1670,1731", "endColumns": "75,74,63,57,65,78,88,72,68,55,56,333,76,85,54,74,61,53,61,47,60,75", "endOffsets": "126,201,265,323,389,468,557,630,699,755,812,1146,1223,1309,1364,1439,1501,1555,1617,1665,1726,1802"}, "to": {"startLines": "99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8644,8720,8795,8859,8917,8983,9062,9151,9224,9293,9349,9406,9740,9817,9903,9958,10033,10095,10149,10211,10259,10320", "endColumns": "75,74,63,57,65,78,88,72,68,55,56,333,76,85,54,74,61,53,61,47,60,75", "endOffsets": "8715,8790,8854,8912,8978,9057,9146,9219,9288,9344,9401,9735,9812,9898,9953,10028,10090,10144,10206,10254,10315,10391"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,563,673,793", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "147,249,348,448,558,668,788,889"}, "to": {"startLines": "129,130,131,132,133,134,135,432", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "10881,10978,11080,11179,11279,11389,11499,40085", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "10973,11075,11174,11274,11384,11494,11614,40181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,195,267,333,410,477,578,671", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "120,190,262,328,405,472,573,666,736"}, "to": {"startLines": "227,228,229,230,231,232,233,234,235", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "21635,21705,21775,21847,21913,21990,22057,22158,22251", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "21700,21770,21842,21908,21985,22052,22153,22246,22316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,527,628,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,2042,2471,2581,2843", "endColumns": "119,105,100,118,90,92,94,93,99,92,94,94,90,90,110,109,161,85", "endOffsets": "220,326,623,742,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,2148,2576,2738,2924"}, "to": {"startLines": "35,36,39,40,43,44,45,46,47,48,49,50,51,52,55,59,60,412", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1552,1672,1914,2015,2238,2329,2422,2517,2611,2711,2804,2899,2994,3085,3308,3647,3757,38216", "endColumns": "119,105,100,118,90,92,94,93,99,92,94,94,90,90,110,109,161,85", "endOffsets": "1667,1773,2010,2129,2324,2417,2512,2606,2706,2799,2894,2989,3080,3171,3414,3752,3914,38297"}}]}]}