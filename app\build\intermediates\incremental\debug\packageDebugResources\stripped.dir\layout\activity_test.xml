<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />
        
    <!-- Service testing controls -->
    <LinearLayout
        android:id="@+id/test_controls_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="#EAEAEA">
        
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Service Testing"
            android:textStyle="bold"
            android:textSize="16sp"
            android:layout_marginBottom="8dp"/>
            
        <TextView
            android:id="@+id/service_status_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Service status: Unknown"
            android:textSize="14sp"
            android:layout_marginBottom="8dp"/>
            
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            
            <Button
                android:id="@+id/start_service_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Start Service"
                android:layout_marginEnd="4dp"/>
                
            <Button
                android:id="@+id/stop_service_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Stop Service"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"/>
                
            <Button
                android:id="@+id/check_service_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Check Status"
                android:layout_marginStart="4dp"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>