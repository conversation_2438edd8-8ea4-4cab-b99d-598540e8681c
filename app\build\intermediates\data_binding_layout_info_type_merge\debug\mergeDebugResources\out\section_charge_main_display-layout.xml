<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="section_charge_main_display" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\section_charge_main_display.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@+id/charge_main_display_root"><Targets><Target id="@+id/charge_main_display_root" tag="layout/section_charge_main_display_0" view="LinearLayout"><Expressions/><location startLine="8" startOffset="0" endLine="158" endOffset="14"/></Target><Target id="@+id/percent_layout" view="RelativeLayout"><Expressions/><location startLine="18" startOffset="4" endLine="56" endOffset="20"/></Target><Target id="@+id/percent_inner_layout" view="RelativeLayout"><Expressions/><location startLine="28" startOffset="8" endLine="55" endOffset="24"/></Target><Target id="@+id/text_percent" view="TextView"><Expressions/><location startLine="33" startOffset="12" endLine="40" endOffset="44"/></Target><Target id="@+id/charge_prog_bar_percent" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="41" startOffset="12" endLine="54" endOffset="41"/></Target><Target id="@+id/time_estimates_layout" view="LinearLayout"><Expressions/><location startLine="59" startOffset="4" endLine="157" endOffset="18"/></Target><Target id="@+id/full_charge_block" view="LinearLayout"><Expressions/><location startLine="71" startOffset="8" endLine="97" endOffset="22"/></Target><Target id="@+id/text_time_to_full" view="TextView"><Expressions/><location startLine="89" startOffset="12" endLine="96" endOffset="44"/></Target><Target id="@+id/target_charge_block" view="LinearLayout"><Expressions/><location startLine="100" startOffset="8" endLine="127" endOffset="22"/></Target><Target id="@+id/text_time_to_target" view="TextView"><Expressions/><location startLine="119" startOffset="12" endLine="126" endOffset="44"/></Target><Target id="@+id/current_rate_block" view="LinearLayout"><Expressions/><location startLine="130" startOffset="8" endLine="156" endOffset="22"/></Target><Target id="@+id/text_current_rate" view="TextView"><Expressions/><location startLine="148" startOffset="12" endLine="155" endOffset="44"/></Target></Targets></Layout>