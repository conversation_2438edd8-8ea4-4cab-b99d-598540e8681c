package com.tqhit.battery.one.fragment.onboarding;

import dagger.internal.DaggerGenerated;
import dagger.internal.IdentifierNameString;
import dagger.internal.KeepFieldType;
import javax.annotation.processing.Generated;

@IdentifierNameString
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class LanguageSelectionViewModel_HiltModules_BindsModule_Binds_LazyMapKey {
  @KeepFieldType
  static LanguageSelectionViewModel keepFieldType;

  public static String lazyClassKeyName = "com.tqhit.battery.one.fragment.onboarding.LanguageSelectionViewModel";
}
