// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class NewFragmentDischargeBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final TextView dischargeChargingMessage;

  @NonNull
  public final LayoutDischargeSectionActionsBinding includeActionsSection;

  @NonNull
  public final LayoutBackNavigationBinding includeBackNavigation;

  @NonNull
  public final LayoutDischargeSectionCurrentSessionDetailsBinding includeCurrentSessionDetails;

  @NonNull
  public final LayoutDischargeSectionLossOfChargeBinding includeLossOfCharge;

  @NonNull
  public final LayoutDischargeSectionStatusAndEstimatesBinding includeStatusAndEstimates;

  @NonNull
  public final ShimmerFrameLayout nativeAd;

  @NonNull
  public final NestedScrollView newDischargeScrollView;

  private NewFragmentDischargeBinding(@NonNull NestedScrollView rootView,
      @NonNull TextView dischargeChargingMessage,
      @NonNull LayoutDischargeSectionActionsBinding includeActionsSection,
      @NonNull LayoutBackNavigationBinding includeBackNavigation,
      @NonNull LayoutDischargeSectionCurrentSessionDetailsBinding includeCurrentSessionDetails,
      @NonNull LayoutDischargeSectionLossOfChargeBinding includeLossOfCharge,
      @NonNull LayoutDischargeSectionStatusAndEstimatesBinding includeStatusAndEstimates,
      @NonNull ShimmerFrameLayout nativeAd, @NonNull NestedScrollView newDischargeScrollView) {
    this.rootView = rootView;
    this.dischargeChargingMessage = dischargeChargingMessage;
    this.includeActionsSection = includeActionsSection;
    this.includeBackNavigation = includeBackNavigation;
    this.includeCurrentSessionDetails = includeCurrentSessionDetails;
    this.includeLossOfCharge = includeLossOfCharge;
    this.includeStatusAndEstimates = includeStatusAndEstimates;
    this.nativeAd = nativeAd;
    this.newDischargeScrollView = newDischargeScrollView;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static NewFragmentDischargeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static NewFragmentDischargeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.new_fragment_discharge, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static NewFragmentDischargeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.discharge_charging_message;
      TextView dischargeChargingMessage = ViewBindings.findChildViewById(rootView, id);
      if (dischargeChargingMessage == null) {
        break missingId;
      }

      id = R.id.include_actions_section;
      View includeActionsSection = ViewBindings.findChildViewById(rootView, id);
      if (includeActionsSection == null) {
        break missingId;
      }
      LayoutDischargeSectionActionsBinding binding_includeActionsSection = LayoutDischargeSectionActionsBinding.bind(includeActionsSection);

      id = R.id.include_back_navigation;
      View includeBackNavigation = ViewBindings.findChildViewById(rootView, id);
      if (includeBackNavigation == null) {
        break missingId;
      }
      LayoutBackNavigationBinding binding_includeBackNavigation = LayoutBackNavigationBinding.bind(includeBackNavigation);

      id = R.id.include_current_session_details;
      View includeCurrentSessionDetails = ViewBindings.findChildViewById(rootView, id);
      if (includeCurrentSessionDetails == null) {
        break missingId;
      }
      LayoutDischargeSectionCurrentSessionDetailsBinding binding_includeCurrentSessionDetails = LayoutDischargeSectionCurrentSessionDetailsBinding.bind(includeCurrentSessionDetails);

      id = R.id.include_loss_of_charge;
      View includeLossOfCharge = ViewBindings.findChildViewById(rootView, id);
      if (includeLossOfCharge == null) {
        break missingId;
      }
      LayoutDischargeSectionLossOfChargeBinding binding_includeLossOfCharge = LayoutDischargeSectionLossOfChargeBinding.bind(includeLossOfCharge);

      id = R.id.include_status_and_estimates;
      View includeStatusAndEstimates = ViewBindings.findChildViewById(rootView, id);
      if (includeStatusAndEstimates == null) {
        break missingId;
      }
      LayoutDischargeSectionStatusAndEstimatesBinding binding_includeStatusAndEstimates = LayoutDischargeSectionStatusAndEstimatesBinding.bind(includeStatusAndEstimates);

      id = R.id.nativeAd;
      ShimmerFrameLayout nativeAd = ViewBindings.findChildViewById(rootView, id);
      if (nativeAd == null) {
        break missingId;
      }

      NestedScrollView newDischargeScrollView = (NestedScrollView) rootView;

      return new NewFragmentDischargeBinding((NestedScrollView) rootView, dischargeChargingMessage,
          binding_includeActionsSection, binding_includeBackNavigation,
          binding_includeCurrentSessionDetails, binding_includeLossOfCharge,
          binding_includeStatusAndEstimates, nativeAd, newDischargeScrollView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
