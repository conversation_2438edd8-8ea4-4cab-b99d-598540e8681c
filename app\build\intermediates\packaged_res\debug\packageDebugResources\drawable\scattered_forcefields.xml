<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="1600dp"
    android:height="900dp"
    android:viewportWidth="1600"
    android:viewportHeight="900">
  <path
      android:pathData="M0,0h1600v900h-1600z"
      android:fillColor="?attr/white"/>
  <path
      android:pathData="M816.4,535.8 L833.2,475.5 876.6,519.7z"
      android:strokeWidth="50.8"
      android:fillColor="#FFF"
      android:fillAlpha="0">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="816.4"
          android:startY="535.8"
          android:endX="816.4"
          android:endY="475.5"
          android:type="linear">
        <item android:offset="0" android:color="?attr/white"/>
        <item android:offset="1" android:color="#FFCCFF66"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M214.1,252.8m-39.3,-6.9a39.9,39.9 132,1 1,78.6 13.9a39.9,39.9 132,1 1,-78.6 -13.9"
      android:strokeWidth="21.9"
      android:fillColor="#FFF"
      android:fillAlpha="0">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="174.2"
          android:startY="292.9"
          android:endX="174.2"
          android:endY="212.8"
          android:type="linear">
        <item android:offset="0" android:color="?attr/white"/>
        <item android:offset="1" android:color="#FFCCFF66"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M422.1,583.6l-53.7,-64c17.7,-14.8 44,-12.5 58.8,5.1S439.7,568.8 422.1,583.6z"
      android:strokeWidth="65.8"
      android:fillColor="#FFF"
      android:fillAlpha="0">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="368.4"
          android:startY="583.6"
          android:endX="368.4"
          android:endY="509.8"
          android:type="linear">
        <item android:offset="0" android:color="?attr/white"/>
        <item android:offset="1" android:color="#FFCCFF66"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M1348,361.9 L1320.9,408.6 1355.7,449.9 1382.8,403.2z"
      android:strokeWidth="19.8"
      android:fillColor="#FFF"
      android:fillAlpha="0">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="1320.9"
          android:startY="361.9"
          android:endX="1320.9"
          android:endY="449.9"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFCC00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M789,159h100v100h-100z"
      android:strokeWidth="44"
      android:fillColor="#FFF"
      android:fillAlpha="0">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="789"
          android:startY="159"
          android:endX="789"
          android:endY="259"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFCC00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M73.4,226.2 L62.8,244.4 41.8,244.4 31.3,226.2 41.8,208 62.8,208z"
      android:strokeWidth="10"
      android:fillColor="#FFF"
      android:fillAlpha="0">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="31.3"
          android:startY="208"
          android:endX="31.3"
          android:endY="244.4"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFCC00"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
