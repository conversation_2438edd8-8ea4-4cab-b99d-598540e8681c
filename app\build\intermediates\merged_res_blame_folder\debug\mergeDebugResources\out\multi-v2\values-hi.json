{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,179,225,275,333,387,451,512,573,670,778,862,910,994,1073,1130,1183,1286,1345,1415,1462,1524,1579,1670,1730,1805,1865,1917,1968,2029,2092,2156,2254,2362", "endColumns": "66,56,45,49,57,53,63,60,60,96,107,83,47,83,78,56,52,102,58,69,46,61,54,90,59,74,59,51,50,60,62,63,97,107,57", "endOffsets": "117,174,220,270,328,382,446,507,568,665,773,857,905,989,1068,1125,1178,1281,1340,1410,1457,1519,1574,1665,1725,1800,1860,1912,1963,2024,2087,2151,2249,2357,2415"}, "to": {"startLines": "339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "29603,29670,29727,29773,29823,29881,29935,29999,30060,30121,30218,30326,30410,30458,30542,30621,30678,30731,30834,30893,30963,31010,31072,31127,31218,31278,31353,31413,31465,31516,31577,31640,31704,31802,31910", "endColumns": "66,56,45,49,57,53,63,60,60,96,107,83,47,83,78,56,52,102,58,69,46,61,54,90,59,74,59,51,50,60,62,63,97,107,57", "endOffsets": "29665,29722,29768,29818,29876,29930,29994,30055,30116,30213,30321,30405,30453,30537,30616,30673,30726,30829,30888,30958,31005,31067,31122,31213,31273,31348,31408,31460,31511,31572,31635,31699,31797,31905,31963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,413,494,601,697,804,936,1019,1086,1151,1245,1314,1373,1458,1521,1584,1642,1707,1768,1829,1935,1993,2053,2112,2182,2298,2377,2468,2561,2659,2739,2873,2948,3024,3161,3258,3356,3413,3468,3534,3604,3681,3752,3837,3905,3981,4062,4140,4241,4327,4414,4511,4610,4684,4754,4858,4912,4999,5066,5156,5248,5310,5374,5437,5503,5608,5718,5819,5926,5987,6046,6125,6210,6290", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,80,106,95,106,131,82,66,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,90,92,97,79,133,74,75,136,96,97,56,54,65,69,76,70,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78,84,79,72", "endOffsets": "254,332,408,489,596,692,799,931,1014,1081,1146,1240,1309,1368,1453,1516,1579,1637,1702,1763,1824,1930,1988,2048,2107,2177,2293,2372,2463,2556,2654,2734,2868,2943,3019,3156,3253,3351,3408,3463,3529,3599,3676,3747,3832,3900,3976,4057,4135,4236,4322,4409,4506,4605,4679,4749,4853,4907,4994,5061,5151,5243,5305,5369,5432,5498,5603,5713,5814,5921,5982,6041,6120,6205,6285,6358"}, "to": {"startLines": "19,76,77,78,79,80,88,89,90,116,117,171,175,178,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,313,325,326,328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "794,5602,5680,5756,5837,5944,6793,6900,7032,9983,10050,14118,14529,14773,21129,21214,21277,21340,21398,21463,21524,21585,21691,21749,21809,21868,21938,22054,22133,22224,22317,22415,22495,22629,22704,22780,22917,23014,23112,23169,23224,23290,23360,23437,23508,23593,23661,23737,23818,23896,23997,24083,24170,24267,24366,24440,24510,24614,24668,24755,24822,24912,25004,25066,25130,25193,25259,25364,25474,25575,25682,25743,27478,28341,28426,28579", "endLines": "22,76,77,78,79,80,88,89,90,116,117,171,175,178,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,313,325,326,328", "endColumns": "12,77,75,80,106,95,106,131,82,66,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,90,92,97,79,133,74,75,136,96,97,56,54,65,69,76,70,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78,84,79,72", "endOffsets": "948,5675,5751,5832,5939,6035,6895,7027,7110,10045,10110,14207,14593,14827,21209,21272,21335,21393,21458,21519,21580,21686,21744,21804,21863,21933,22049,22128,22219,22312,22410,22490,22624,22699,22775,22912,23009,23107,23164,23219,23285,23355,23432,23503,23588,23656,23732,23813,23891,23992,24078,24165,24262,24361,24435,24505,24609,24663,24750,24817,24907,24999,25061,25125,25188,25254,25359,25469,25570,25677,25738,25797,27552,28421,28501,28647"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-hi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,248,296,350,418,488,589,652,783,920,1038,1092,1148,1268,1349,1390,1486,1525,1563,1610,1674,1715", "endColumns": "48,47,53,67,69,100,62,130,136,117,53,55,119,80,40,95,38,37,46,63,40,55", "endOffsets": "247,295,349,417,487,588,651,782,919,1037,1091,1147,1267,1348,1389,1485,1524,1562,1609,1673,1714,1770"}, "to": {"startLines": "294,295,296,302,303,304,305,306,307,308,309,310,311,312,317,318,319,320,321,322,323,376", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25802,25855,25907,26435,26507,26581,26686,26753,26888,27029,27151,27209,27269,27393,27865,27910,28010,28053,28095,28146,28214,32126", "endColumns": "52,51,57,71,73,104,66,134,140,121,57,59,123,84,44,99,42,41,50,67,44,59", "endOffsets": "25850,25902,25960,26502,26576,26681,26748,26883,27024,27146,27204,27264,27388,27473,27905,28005,28048,28090,28141,28209,28254,32181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,260,328,424,492,615,736", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "118,184,255,323,419,487,610,731,818"}, "to": {"startLines": "142,143,144,145,146,147,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "12054,12122,12188,12259,12327,12423,12491,12614,12735", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "12117,12183,12254,12322,12418,12486,12609,12730,12817"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,85", "endOffsets": "135,221"}, "to": {"startLines": "337,338", "startColumns": "4,4", "startOffsets": "29432,29517", "endColumns": "84,85", "endOffsets": "29512,29598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,575,683,830,956,1064,1172,1325,1430,1592,1718,1855,2004,2063,2126", "endColumns": "103,155,121,107,146,125,107,107,152,104,161,125,136,148,58,62,83", "endOffsets": "296,452,574,682,829,955,1063,1171,1324,1429,1591,1717,1854,2003,2062,2125,2209"}, "to": {"startLines": "93,94,95,96,97,98,99,100,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7293,7401,7561,7687,7799,7950,8080,8192,8450,8607,8716,8882,9012,9153,9306,9369,9436", "endColumns": "107,159,125,111,150,129,111,111,156,108,165,129,140,152,62,66,87", "endOffsets": "7396,7556,7682,7794,7945,8075,8187,8299,8602,8711,8877,9007,9148,9301,9364,9431,9519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,269,347,489,658,738", "endColumns": "72,90,77,141,168,79,77", "endOffsets": "173,264,342,484,653,733,811"}, "to": {"startLines": "111,170,301,314,333,374,375", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9524,14027,26357,27557,28996,31968,32048", "endColumns": "72,90,77,141,168,79,77", "endOffsets": "9592,14113,26430,27694,29160,32043,32121"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,415,531,626,722,835,973,1093,1243,1328,1431,1522,1619,1749,1869,1977,2122,2268,2398,2587,2714,2832,2954,3080,3172,3267,3395,3521,3620,3722,3834,3980,4132,4246,4346,4422,4522,4621,4731,4817,4907,5012,5092,5176,5276,5376,5471,5573,5659,5761,5859,5963,6078,6158,6258", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,109,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "168,286,410,526,621,717,830,968,1088,1238,1323,1426,1517,1614,1744,1864,1972,2117,2263,2393,2582,2709,2827,2949,3075,3167,3262,3390,3516,3615,3717,3829,3975,4127,4241,4341,4417,4517,4616,4726,4812,4902,5007,5087,5171,5271,5371,5466,5568,5654,5756,5854,5958,6073,6153,6253,6347"}, "to": {"startLines": "179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14832,14950,15068,15192,15308,15403,15499,15612,15750,15870,16020,16105,16208,16299,16396,16526,16646,16754,16899,17045,17175,17364,17491,17609,17731,17857,17949,18044,18172,18298,18397,18499,18611,18757,18909,19023,19123,19199,19299,19398,19508,19594,19684,19789,19869,19953,20053,20153,20248,20350,20436,20538,20636,20740,20855,20935,21035", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,109,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "14945,15063,15187,15303,15398,15494,15607,15745,15865,16015,16100,16203,16294,16391,16521,16641,16749,16894,17040,17170,17359,17486,17604,17726,17852,17944,18039,18167,18293,18392,18494,18606,18752,18904,19018,19118,19194,19294,19393,19503,19589,19679,19784,19864,19948,20048,20148,20243,20345,20431,20533,20631,20735,20850,20930,21030,21124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-hi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "101", "startColumns": "4", "startOffsets": "8304", "endColumns": "145", "endOffsets": "8445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-hi\\values-hi.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3317,3380", "endColumns": "62,66", "endOffsets": "3375,3442"}, "to": {"startLines": "162,163", "startColumns": "4,4", "startOffsets": "13501,13564", "endColumns": "62,66", "endOffsets": "13559,13626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,113", "endOffsets": "154,268"}, "to": {"startLines": "297,298", "startColumns": "4,4", "startOffsets": "25965,26069", "endColumns": "103,113", "endOffsets": "26064,26178"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,375", "endColumns": "105,101,111,102", "endOffsets": "156,258,370,473"}, "to": {"startLines": "112,172,173,174", "startColumns": "4,4,4,4", "startOffsets": "9597,14212,14314,14426", "endColumns": "105,101,111,102", "endOffsets": "9698,14309,14421,14524"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,993,1078,1151,1244,1319,1394,1475,1541", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,988,1073,1146,1239,1314,1389,1470,1536,1656"}, "to": {"startLines": "91,92,113,114,115,176,177,299,300,315,316,327,329,330,331,334,335,336", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7115,7210,9703,9796,9894,14598,14676,26183,26272,27699,27780,28506,28652,28745,28820,29165,29246,29312", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "7205,7288,9791,9889,9978,14671,14768,26267,26352,27775,27860,28574,28740,28815,28890,29241,29307,29427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,493,699,782,863,946,1041,1141,1210,1273,1359,1445,1510,1574,1638,1706,1819,1935,2047,2120,2204,2273,2342,2426,2508,2575,2638,2691,2753,2807,2868,2928,2995,3058,3128,3189,3251,3317,3377,3437,3511,3585,3638", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,112,115,111,72,83,68,68,83,81,66,62,52,61,53,60,59,66,62,69,60,61,65,59,59,73,73,52,74", "endOffsets": "281,488,694,777,858,941,1036,1136,1205,1268,1354,1440,1505,1569,1633,1701,1814,1930,2042,2115,2199,2268,2337,2421,2503,2570,2633,2686,2748,2802,2863,2923,2990,3053,3123,3184,3246,3312,3372,3432,3506,3580,3633,3708"}, "to": {"startLines": "2,11,15,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,151,152,153,154,155,156,157,158,159,160,161,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,588,10115,10198,10279,10362,10457,10557,10626,10689,10775,10861,10926,10990,11054,11122,11235,11351,11463,11536,11620,11689,11758,11842,11924,11991,12822,12875,12937,12991,13052,13112,13179,13242,13312,13373,13435,13631,13691,13751,13825,13899,13952", "endLines": "10,14,18,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,151,152,153,154,155,156,157,158,159,160,161,164,165,166,167,168,169", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,112,115,111,72,83,68,68,83,81,66,62,52,61,53,60,59,66,62,69,60,61,65,59,59,73,73,52,74", "endOffsets": "376,583,789,10193,10274,10357,10452,10552,10621,10684,10770,10856,10921,10985,11049,11117,11230,11346,11458,11531,11615,11684,11753,11837,11919,11986,12049,12870,12932,12986,13047,13107,13174,13237,13307,13368,13430,13496,13686,13746,13820,13894,13947,14022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,324", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "953,1059,1157,1267,1353,1455,1576,1654,1731,1822,1915,2010,2104,2204,2297,2392,2486,2577,2668,2749,2854,2956,3054,3164,3267,3376,3534,28259", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "1054,1152,1262,1348,1450,1571,1649,1726,1817,1910,2005,2099,2199,2292,2387,2481,2572,2663,2744,2849,2951,3049,3159,3262,3371,3529,3630,28336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9b1d1c56b528abe32d69bef2c9ce868e\\transformed\\jetified-bigo-ads-5.3.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,214,275,332,405,482,570,644,706,766,824,1147,1218,1302,1359,1424,1481,1536,1600,1647,1718,1779,1839,1887,1954", "endColumns": "71,86,60,56,72,76,87,73,61,59,57,322,70,83,56,64,56,54,63,46,70,60,59,47,66,67", "endOffsets": "122,209,270,327,400,477,565,639,701,761,819,1142,1213,1297,1354,1419,1476,1531,1595,1642,1713,1774,1834,1882,1949,2017"}, "to": {"startLines": "50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3635,3707,3794,3855,3912,3985,4062,4150,4224,4286,4346,4404,4727,4798,4882,4939,5004,5061,5116,5180,5227,5298,5359,5419,5467,5534", "endColumns": "71,86,60,56,72,76,87,73,61,59,57,322,70,83,56,64,56,54,63,46,70,60,59,47,66,67", "endOffsets": "3702,3789,3850,3907,3980,4057,4145,4219,4281,4341,4399,4722,4793,4877,4934,4999,5056,5111,5175,5222,5293,5354,5414,5462,5529,5597"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "81,82,83,84,85,86,87,332", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "6040,6138,6241,6346,6447,6560,6666,28895", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "6133,6236,6341,6442,6555,6661,6788,28991"}}]}]}