package com.tqhit.battery.one.features.navigation;

import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DynamicNavigationManager_Factory implements Factory<DynamicNavigationManager> {
  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  public DynamicNavigationManager_Factory(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider) {
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
  }

  @Override
  public DynamicNavigationManager get() {
    return newInstance(coreBatteryStatsProvider.get());
  }

  public static DynamicNavigationManager_Factory create(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider) {
    return new DynamicNavigationManager_Factory(coreBatteryStatsProvider);
  }

  public static DynamicNavigationManager newInstance(
      CoreBatteryStatsProvider coreBatteryStatsProvider) {
    return new DynamicNavigationManager(coreBatteryStatsProvider);
  }
}
