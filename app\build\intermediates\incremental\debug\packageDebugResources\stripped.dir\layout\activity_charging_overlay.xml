<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/grey">

    <androidx.media3.ui.PlayerView
        android:id="@+id/playerView"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        app:surface_type="surface_view"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>


    <!-- Date and Time at the top center -->
    <LinearLayout
        android:id="@+id/date_time_container"
        android:orientation="vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/_80sdp">

        <TextView
            android:id="@+id/text_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="19:00"
            android:textSize="@dimen/_36ssp"
            android:textColor="@android:color/white"
            android:textStyle="bold"/>

        <TextView
            android:id="@+id/text_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Fri, 13 May"
            android:textSize="@dimen/_16ssp"
            android:textColor="@android:color/white"/>
    </LinearLayout>

    <!-- Battery Percentage below the rocket -->
    <TextView
        android:id="@+id/battery_percent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="99%"
        android:textSize="28sp"
        android:textColor="@android:color/white"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="20dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>