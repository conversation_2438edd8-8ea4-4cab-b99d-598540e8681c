// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSlideLayout1Binding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final RelativeLayout button;

  @NonNull
  public final LinearLayout display;

  @NonNull
  public final LinearLayout lenghtLayout;

  @NonNull
  public final LinearLayout logo;

  @NonNull
  public final LinearLayout main;

  @NonNull
  public final Button nextPage;

  @NonNull
  public final RelativeLayout resetSessionChargeLayout;

  @NonNull
  public final LinearLayout text5;

  @NonNull
  public final TextView textView;

  @NonNull
  public final TextView textView14;

  @NonNull
  public final TextView textView2;

  @NonNull
  public final TextView textView4;

  @NonNull
  public final TextView textViewResetCharge;

  private ItemSlideLayout1Binding(@NonNull RelativeLayout rootView, @NonNull RelativeLayout button,
      @NonNull LinearLayout display, @NonNull LinearLayout lenghtLayout, @NonNull LinearLayout logo,
      @NonNull LinearLayout main, @NonNull Button nextPage,
      @NonNull RelativeLayout resetSessionChargeLayout, @NonNull LinearLayout text5,
      @NonNull TextView textView, @NonNull TextView textView14, @NonNull TextView textView2,
      @NonNull TextView textView4, @NonNull TextView textViewResetCharge) {
    this.rootView = rootView;
    this.button = button;
    this.display = display;
    this.lenghtLayout = lenghtLayout;
    this.logo = logo;
    this.main = main;
    this.nextPage = nextPage;
    this.resetSessionChargeLayout = resetSessionChargeLayout;
    this.text5 = text5;
    this.textView = textView;
    this.textView14 = textView14;
    this.textView2 = textView2;
    this.textView4 = textView4;
    this.textViewResetCharge = textViewResetCharge;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSlideLayout1Binding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSlideLayout1Binding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_slide_layout_1, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSlideLayout1Binding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button;
      RelativeLayout button = ViewBindings.findChildViewById(rootView, id);
      if (button == null) {
        break missingId;
      }

      id = R.id.display;
      LinearLayout display = ViewBindings.findChildViewById(rootView, id);
      if (display == null) {
        break missingId;
      }

      id = R.id.lenght_layout;
      LinearLayout lenghtLayout = ViewBindings.findChildViewById(rootView, id);
      if (lenghtLayout == null) {
        break missingId;
      }

      id = R.id.logo;
      LinearLayout logo = ViewBindings.findChildViewById(rootView, id);
      if (logo == null) {
        break missingId;
      }

      id = R.id.main;
      LinearLayout main = ViewBindings.findChildViewById(rootView, id);
      if (main == null) {
        break missingId;
      }

      id = R.id.next_page;
      Button nextPage = ViewBindings.findChildViewById(rootView, id);
      if (nextPage == null) {
        break missingId;
      }

      id = R.id.reset_session_charge_layout;
      RelativeLayout resetSessionChargeLayout = ViewBindings.findChildViewById(rootView, id);
      if (resetSessionChargeLayout == null) {
        break missingId;
      }

      id = R.id.text5;
      LinearLayout text5 = ViewBindings.findChildViewById(rootView, id);
      if (text5 == null) {
        break missingId;
      }

      id = R.id.textView;
      TextView textView = ViewBindings.findChildViewById(rootView, id);
      if (textView == null) {
        break missingId;
      }

      id = R.id.textView14;
      TextView textView14 = ViewBindings.findChildViewById(rootView, id);
      if (textView14 == null) {
        break missingId;
      }

      id = R.id.textView2;
      TextView textView2 = ViewBindings.findChildViewById(rootView, id);
      if (textView2 == null) {
        break missingId;
      }

      id = R.id.textView4;
      TextView textView4 = ViewBindings.findChildViewById(rootView, id);
      if (textView4 == null) {
        break missingId;
      }

      id = R.id.text_view_reset_charge;
      TextView textViewResetCharge = ViewBindings.findChildViewById(rootView, id);
      if (textViewResetCharge == null) {
        break missingId;
      }

      return new ItemSlideLayout1Binding((RelativeLayout) rootView, button, display, lenghtLayout,
          logo, main, nextPage, resetSessionChargeLayout, text5, textView, textView14, textView2,
          textView4, textViewResetCharge);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
