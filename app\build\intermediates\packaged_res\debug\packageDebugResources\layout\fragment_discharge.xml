<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scroll_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:background="@drawable/white_block"
            android:padding="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:background="@drawable/circle"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingStart="8dp"
                android:paddingEnd="8dp">
                <TextView
                    android:textSize="27dp"
                    android:textColor="?attr/colorr"
                    android:id="@+id/discharge_text_percent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/zero"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="27dp"
                    android:textColor="?attr/colorr"
                    android:visibility="invisible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="100%"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/discharge_text_percent3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/grey_block_line_up_static"
                    android:baselineAligned="false"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:gravity="center"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:paddingStart="10dp"
                    android:paddingTop="12dp"
                    android:paddingEnd="10dp"
                    android:paddingBottom="12dp"
                    android:singleLine="true"
                    android:text="@string/zero"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />

                <RelativeLayout
                    android:id="@+id/day_block"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:background="@drawable/grey_block_line"
                    android:clickable="true"
                    android:focusable="true">

                    <TextView
                        android:id="@+id/discharge_sun"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="10dp"
                        android:text="@string/zero"
                        android:textColor="?attr/black"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="5dp"
                        android:layout_marginBottom="12dp"
                        android:layout_toStartOf="@+id/discharge_sun"
                        android:ellipsize="marquee"
                        android:focusableInTouchMode="true"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:singleLine="true"
                        android:text="@string/active_mode"
                        android:textAlignment="viewStart"
                        android:textColor="?attr/black"
                        android:textSize="14sp" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/all_block"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:background="@drawable/grey_block_line"
                    android:clickable="true"
                    android:focusable="true">

                    <TextView
                        android:id="@+id/discharge_all"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="10dp"
                        android:text="@string/zero"
                        android:textColor="?attr/black"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="5dp"
                        android:layout_marginBottom="12dp"
                        android:layout_toStartOf="@+id/discharge_all"
                        android:ellipsize="marquee"
                        android:focusableInTouchMode="true"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:singleLine="true"
                        android:text="@string/complex_use"
                        android:textAlignment="viewStart"
                        android:textColor="?attr/black"
                        android:textSize="14sp" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/night_block"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:background="@drawable/grey_block_line_down"
                    android:clickable="true"
                    android:focusable="true">

                    <TextView
                        android:id="@+id/discharge_night"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="10dp"
                        android:text="@string/zero"
                        android:textColor="?attr/black"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="5dp"
                        android:layout_marginBottom="12dp"
                        android:layout_toStartOf="@+id/discharge_night"
                        android:ellipsize="marquee"
                        android:focusableInTouchMode="true"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:singleLine="true"
                        android:text="@string/screen_off"
                        android:textAlignment="viewStart"
                        android:textColor="?attr/black"
                        android:textSize="14sp" />
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/i_1"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginTop="8dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginEnd="4dp"
                app:layout_constraintEnd_toStartOf="@+id/i_2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/i_t">
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/time_day_session"
                    android:focusableInTouchMode="true"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/info_day_percent_session"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/ma"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:textAlignment="viewStart"
                    app:layout_constraintBaseline_toBaselineOf="@+id/info_day_speed_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/info_day_speed_session"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:id="@+id/iir"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/percent"
                    app:layout_constraintBaseline_toBaselineOf="@+id/info_day_percent_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/info_day_percent_session"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:ellipsize="marquee"
                    android:id="@+id/info_day_speed_session"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/time_day_session"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/info_day_percent_session"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintEnd_toStartOf="@+id/iir"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/i11"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/i11"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/active_mode"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/i_2"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginTop="8dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginStart="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/i_1"
                app:layout_constraintTop_toBottomOf="@+id/i_t">
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/percent"
                    app:layout_constraintBaseline_toBaselineOf="@+id/info_night_percent_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/info_night_percent_session"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/i332"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/ma"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:textAlignment="viewStart"
                    app:layout_constraintBaseline_toBaselineOf="@+id/info_night_speed_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/info_night_speed_session"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:ellipsize="marquee"
                    android:id="@+id/info_night_speed_session"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/time_night_session"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/time_night_session"
                    android:focusableInTouchMode="true"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/info_night_percent_session"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/info_night_percent_session"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/i93"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/i93"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/screen_off"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:id="@+id/i_t"
                android:focusableInTouchMode="true"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text="@string/info_in_current_session"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:textAlignment="viewStart"
                android:layout_marginStart="2dp"
                app:layout_constraintEnd_toStartOf="@+id/discharge_rate_info"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <ImageView
                android:id="@+id/discharge_rate_info"
                android:layout_width="22sp"
                android:layout_height="0dp"
                android:src="@drawable/ic_note"
                android:scaleType="fitEnd"
                android:layout_marginStart="5dp"
                android:layout_alignParentEnd="true"
                app:layout_constraintBottom_toBottomOf="@+id/i_t"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/i_t"
                app:layout_constraintTop_toTopOf="@+id/i_t"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/button_disscharge_using"
                android:background="@drawable/grey_block"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/using_energy"
                android:singleLine="true"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/i_1"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/battery_alarm_btn"
                android:background="@drawable/grey_block"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/low_battery_alarm"
                android:singleLine="true"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/button_disscharge_using"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="14dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:id="@+id/st_text"
                android:focusableInTouchMode="true"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/current_session"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:textAlignment="viewStart"
                android:layout_marginStart="2dp"
                app:layout_constraintEnd_toStartOf="@+id/discharge_session_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <ImageView
                android:id="@+id/discharge_session_info"
                android:layout_width="22sp"
                android:layout_height="0dp"
                android:src="@drawable/ic_note"
                android:scaleType="fitEnd"
                android:layout_marginStart="5dp"
                android:layout_alignParentEnd="true"
                app:layout_constraintBottom_toBottomOf="@+id/st_text"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/st_text"
                app:layout_constraintTop_toTopOf="@+id/st_text"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/s_6"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginStart="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/s_5"
                app:layout_constraintTop_toBottomOf="@+id/s_4">
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_percent_dis_day_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_percent_dis_day_session"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ma_in_medium"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_speed_dis_day_session2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_speed_dis_day_session2"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:id="@+id/text_speed_dis_day_session2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_percent_dis_day_session"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/text_percent_dis_day_session"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/te77"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/te77"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/active_mode"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/s_5"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginEnd="4dp"
                app:layout_constraintEnd_toStartOf="@+id/s_6"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/s_3">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ma_in_medium"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_speed_dis_night_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_speed_dis_night_session"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_percent_dis_night_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_percent_dis_night_session"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:id="@+id/text_speed_dis_night_session"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_percent_dis_night_session"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/text_percent_dis_night_session"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/te88"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/te88"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/screen_off"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/s_3"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginEnd="4dp"
                app:layout_constraintEnd_toStartOf="@+id/s_4"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/s_1">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/mA"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_speed_dis_all_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_speed_dis_all_session"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_percent_dis_all_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_percent_dis_all_session"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:ellipsize="marquee"
                    android:id="@+id/text_speed_dis_all_session"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_percent_dis_all_session"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/text_percent_dis_all_session"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/te99"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/te99"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/average_speed"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/s_4"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginStart="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/s_3"
                app:layout_constraintTop_toBottomOf="@+id/s_2">
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:id="@+id/textView_percent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/percent"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_percent_dis_session_last"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_percent_dis_session_last"/>
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/discharge_session_percent"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/zero"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:textAlignment="viewStart"
                    app:layout_constraintBaseline_toBaselineOf="@+id/textView_percent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/textView_percent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/textView9"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ma"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_speed_dis_day_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_speed_dis_day_session"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:ellipsize="marquee"
                    android:id="@+id/text_speed_dis_day_session"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_percent_dis_session_last"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/text_percent_dis_session_last"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/te0"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/te0"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/all"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/s_2"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginStart="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/s_1"
                app:layout_constraintTop_toBottomOf="@+id/st_text">
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:id="@+id/textView7"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    android:textAlignment="viewStart"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_now_dis_session"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_now_dis_session"/>
                <TextView
                    android:textSize="14sp"
                    android:visibility="invisible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_now_dis_session"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/text_now_dis_session"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text226"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/text226"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/now"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/s_1"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginEnd="4dp"
                app:layout_constraintEnd_toStartOf="@+id/s_2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/st_text">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:ellipsize="marquee"
                    android:id="@+id/time_dis_session_start"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_fulltime_dis_session"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/text_fulltime_dis_session"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero_seconds"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView6"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/textView6"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/all_time_charge"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/reset_session_discharge_button"
                android:background="@drawable/grey_block"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/reset_sessions"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/s_5"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="14dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:id="@+id/st_texeet"
                android:focusableInTouchMode="true"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/operating_time"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:textAlignment="viewStart"
                android:layout_marginStart="2dp"
                app:layout_constraintEnd_toStartOf="@+id/operation_session_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <ImageView
                android:id="@+id/operation_session_info"
                android:layout_width="22sp"
                android:layout_height="0dp"
                android:src="@drawable/ic_note"
                android:scaleType="fitEnd"
                android:layout_marginStart="5dp"
                android:layout_alignParentEnd="true"
                app:layout_constraintBottom_toBottomOf="@+id/st_texeet"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/st_texeet"
                app:layout_constraintTop_toTopOf="@+id/st_texeet"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/s_8"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginStart="4dp"
                app:layout_constraintBottom_toBottomOf="@+id/s_7"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/s_7"
                app:layout_constraintTop_toTopOf="@+id/s_7">
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/deep_percent"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/zero"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_percent_dis_day_session_deep"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_percent_dis_day_session_deep"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/text_percent_dis_day_session_deep"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero_seconds"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/te7722"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/te7722"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/deep_sleep"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/s_7"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginEnd="4dp"
                app:layout_constraintEnd_toStartOf="@+id/s_8"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/st_texeet">
                <TextView
                    android:textSize="13sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/awake_percent"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/zero"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_percent_dis_night_session_awake"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_percent_dis_night_session_awake"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ma_in_medium"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintBaseline_toBaselineOf="@+id/text_speed_dis_night_session_awake"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/text_speed_dis_night_session_awake"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:id="@+id/text_speed_dis_night_session_awake"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/text_percent_dis_night_session_awake"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/text_percent_dis_night_session_awake"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/zero_seconds"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/te88ww"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/te88ww"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/awake"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="14dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:id="@+id/i_text"
                android:focusableInTouchMode="true"
                android:visibility="visible"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/using_baterry_middle"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:textAlignment="viewStart"
                android:layout_marginStart="2dp"
                app:layout_constraintBottom_toTopOf="@+id/i1"
                app:layout_constraintEnd_toStartOf="@+id/average_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <ImageView
                android:id="@+id/average_info"
                android:layout_width="22sp"
                android:layout_height="0dp"
                android:src="@drawable/ic_note"
                android:scaleType="fitEnd"
                android:layout_marginStart="5dp"
                android:layout_alignParentEnd="true"
                app:layout_constraintBottom_toBottomOf="@+id/i_text"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/i_text"
                app:layout_constraintTop_toTopOf="@+id/i_text"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/i3"
                android:background="@drawable/grey_block_line_static"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintTop_toBottomOf="@+id/i1">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:id="@+id/mah_3"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/mA"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:gravity="end"
                    android:id="@+id/discharge_speed_mah_all"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_marginStart="6dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/mah_3"
                    app:layout_constraintStart_toEndOf="@+id/per_3"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:id="@+id/per_3"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    android:layout_weight="1"
                    app:layout_constraintBaseline_toBaselineOf="@+id/discharge_speed_percent_all"
                    app:layout_constraintStart_toEndOf="@+id/discharge_speed_percent_all"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/discharge_speed_percent_all"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:layout_marginBottom="9dp"
                    android:text="@string/zero"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:layout_marginBottom="6dp"
                    android:text="@string/complex_use"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="6dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/discharge_speed_percent_all"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/i2"
                android:background="@drawable/grey_block_line_down_static"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintTop_toBottomOf="@+id/i3">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:id="@+id/mah_2"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/mA"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:gravity="end"
                    android:id="@+id/discharge_speed_mah_night"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_marginStart="6dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/mah_2"
                    app:layout_constraintStart_toEndOf="@+id/per_2"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:id="@+id/per_2"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    android:layout_weight="1"
                    app:layout_constraintBaseline_toBaselineOf="@+id/discharge_speed_percent_night"
                    app:layout_constraintStart_toEndOf="@+id/discharge_speed_percent_night"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/discharge_speed_percent_night"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:layout_marginBottom="9dp"
                    android:text="@string/zero"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:layout_marginBottom="6dp"
                    android:text="@string/screen_off"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="6dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/discharge_speed_percent_night"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/i1"
                android:background="@drawable/grey_block_line_up_static"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                app:layout_constraintTop_toBottomOf="@+id/i_text">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:id="@+id/mah_1"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/mA"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:gravity="end"
                    android:id="@+id/discharge_speed_mah_day"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_marginStart="6dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/mah_1"
                    app:layout_constraintStart_toEndOf="@+id/per_1"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/black"
                    android:id="@+id/per_1"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/percent_in_hour"
                    android:layout_weight="1"
                    app:layout_constraintBaseline_toBaselineOf="@+id/discharge_speed_percent_day"
                    app:layout_constraintStart_toEndOf="@+id/discharge_speed_percent_day"/>
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/discharge_speed_percent_day"
                    android:visibility="visible"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:layout_marginBottom="9dp"
                    android:text="@string/zero"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:visibility="visible"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:layout_marginBottom="6dp"
                    android:text="@string/active_mode"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="6dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/discharge_speed_percent_day"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/indent_down"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="9dp"
            android:layout_marginTop="14dp"
            android:layout_marginEnd="9dp"
            android:layout_marginBottom="90dp"
            android:background="@drawable/white_block"
            android:orientation="vertical"
            android:paddingStart="8dp"
            android:paddingTop="7dp"
            android:paddingEnd="8dp"
            android:paddingBottom="8dp"
            android:visibility="visible">

            <TextView
                android:id="@+id/f_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:ellipsize="marquee"
                android:focusableInTouchMode="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:singleLine="true"
                android:text="@string/timework_on_fullbaterry"
                android:textAlignment="viewStart"
                android:textColor="?attr/black"
                android:textSize="19sp"
                android:visibility="visible"
                app:layout_constraintBottom_toTopOf="@+id/f1"
                app:layout_constraintEnd_toStartOf="@+id/full_percent_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/full_percent_info"
                android:layout_width="22sp"
                android:layout_height="0dp"
                android:layout_alignParentEnd="true"
                android:layout_marginStart="5dp"
                android:scaleType="fitEnd"
                android:src="@drawable/ic_note"
                app:layout_constraintBottom_toBottomOf="@+id/f_text"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/f_text"
                app:layout_constraintTop_toTopOf="@+id/f_text" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/f3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:background="@drawable/grey_block_line_static"
                android:visibility="visible"
                app:layout_constraintTop_toBottomOf="@+id/f1">

                <TextView
                    android:id="@+id/discharge_fulltime_remaining_all"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="6dp"
                    android:layout_marginEnd="10dp"
                    android:text="@string/zero"
                    android:textAlignment="viewEnd"
                    android:textColor="?attr/black"
                    android:textSize="14sp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/full2"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:layout_marginBottom="9dp"
                    android:text="@string/zero"
                    android:textColor="?attr/black"
                    android:textSize="19sp"
                    android:visibility="invisible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/full2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="6dp"
                    android:layout_marginEnd="6dp"
                    android:layout_marginBottom="6dp"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:text="@string/complex_use"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/black"
                    android:textSize="14sp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/discharge_fulltime_remaining_all"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/f2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:background="@drawable/grey_block_line_down_static"
                android:visibility="visible"
                app:layout_constraintTop_toBottomOf="@+id/f3">

                <TextView
                    android:id="@+id/discharge_fulltime_remaining_night"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="6dp"
                    android:layout_marginEnd="10dp"
                    android:text="@string/zero"
                    android:textAlignment="viewEnd"
                    android:textColor="?attr/black"
                    android:textSize="14sp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toEndOf="@+id/full3"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.48" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:layout_marginBottom="9dp"
                    android:text="@string/zero"
                    android:textColor="?attr/black"
                    android:textSize="19sp"
                    android:visibility="invisible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/full3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="6dp"
                    android:layout_marginEnd="6dp"
                    android:layout_marginBottom="6dp"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:text="@string/screen_off"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/black"
                    android:textSize="14sp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/discharge_fulltime_remaining_night"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/f1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:background="@drawable/grey_block_line_up_static"
                android:visibility="visible"
                app:layout_constraintTop_toBottomOf="@+id/f_text">

                <TextView
                    android:id="@+id/discharge_fulltime_remaining_day"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="6dp"
                    android:layout_marginEnd="10dp"
                    android:text="@string/zero"
                    android:textAlignment="viewEnd"
                    android:textColor="?attr/black"
                    android:textSize="14sp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/full1"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:layout_marginBottom="9dp"
                    android:text="@string/zero"
                    android:textColor="?attr/black"
                    android:textSize="19sp"
                    android:visibility="invisible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/full1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="6dp"
                    android:layout_marginEnd="6dp"
                    android:layout_marginBottom="6dp"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:text="@string/active_mode"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/black"
                    android:textSize="14sp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/discharge_fulltime_remaining_day"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:id="@+id/update_view"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/update_view_btn"
                android:background="@drawable/grey_block"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:visibility="invisible"
                android:clickable="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp"
                android:text=""
                android:singleLine="true"
                android:textAlignment="center"
                android:layout_marginEnd="4dp"/>
        </LinearLayout>
    </LinearLayout>
</androidx.core.widget.NestedScrollView>
