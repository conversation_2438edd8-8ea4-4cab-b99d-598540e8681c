// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SectionChargeCurrentSessionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnResetSession;

  @NonNull
  public final LinearLayout chargeCurrentSessionRoot;

  @NonNull
  public final ImageView chargeSessionInfoButton;

  @NonNull
  public final TextView valAvgSpeedMixed;

  @NonNull
  public final TextView valAvgSpeedScreenOff;

  @NonNull
  public final TextView valAvgSpeedScreenOn;

  @NonNull
  public final TextView valCurrentRate;

  @NonNull
  public final TextView valScreenOffTime;

  @NonNull
  public final TextView valScreenOnTime;

  @NonNull
  public final TextView valSessionDuration;

  @NonNull
  public final TextView valTotalChargedMah;

  @NonNull
  public final TextView valTotalChargedPercent;

  private SectionChargeCurrentSessionBinding(@NonNull LinearLayout rootView,
      @NonNull Button btnResetSession, @NonNull LinearLayout chargeCurrentSessionRoot,
      @NonNull ImageView chargeSessionInfoButton, @NonNull TextView valAvgSpeedMixed,
      @NonNull TextView valAvgSpeedScreenOff, @NonNull TextView valAvgSpeedScreenOn,
      @NonNull TextView valCurrentRate, @NonNull TextView valScreenOffTime,
      @NonNull TextView valScreenOnTime, @NonNull TextView valSessionDuration,
      @NonNull TextView valTotalChargedMah, @NonNull TextView valTotalChargedPercent) {
    this.rootView = rootView;
    this.btnResetSession = btnResetSession;
    this.chargeCurrentSessionRoot = chargeCurrentSessionRoot;
    this.chargeSessionInfoButton = chargeSessionInfoButton;
    this.valAvgSpeedMixed = valAvgSpeedMixed;
    this.valAvgSpeedScreenOff = valAvgSpeedScreenOff;
    this.valAvgSpeedScreenOn = valAvgSpeedScreenOn;
    this.valCurrentRate = valCurrentRate;
    this.valScreenOffTime = valScreenOffTime;
    this.valScreenOnTime = valScreenOnTime;
    this.valSessionDuration = valSessionDuration;
    this.valTotalChargedMah = valTotalChargedMah;
    this.valTotalChargedPercent = valTotalChargedPercent;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SectionChargeCurrentSessionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SectionChargeCurrentSessionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.section_charge_current_session, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SectionChargeCurrentSessionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_reset_session;
      Button btnResetSession = ViewBindings.findChildViewById(rootView, id);
      if (btnResetSession == null) {
        break missingId;
      }

      LinearLayout chargeCurrentSessionRoot = (LinearLayout) rootView;

      id = R.id.charge_session_info_button;
      ImageView chargeSessionInfoButton = ViewBindings.findChildViewById(rootView, id);
      if (chargeSessionInfoButton == null) {
        break missingId;
      }

      id = R.id.val_avg_speed_mixed;
      TextView valAvgSpeedMixed = ViewBindings.findChildViewById(rootView, id);
      if (valAvgSpeedMixed == null) {
        break missingId;
      }

      id = R.id.val_avg_speed_screen_off;
      TextView valAvgSpeedScreenOff = ViewBindings.findChildViewById(rootView, id);
      if (valAvgSpeedScreenOff == null) {
        break missingId;
      }

      id = R.id.val_avg_speed_screen_on;
      TextView valAvgSpeedScreenOn = ViewBindings.findChildViewById(rootView, id);
      if (valAvgSpeedScreenOn == null) {
        break missingId;
      }

      id = R.id.val_current_rate;
      TextView valCurrentRate = ViewBindings.findChildViewById(rootView, id);
      if (valCurrentRate == null) {
        break missingId;
      }

      id = R.id.val_screen_off_time;
      TextView valScreenOffTime = ViewBindings.findChildViewById(rootView, id);
      if (valScreenOffTime == null) {
        break missingId;
      }

      id = R.id.val_screen_on_time;
      TextView valScreenOnTime = ViewBindings.findChildViewById(rootView, id);
      if (valScreenOnTime == null) {
        break missingId;
      }

      id = R.id.val_session_duration;
      TextView valSessionDuration = ViewBindings.findChildViewById(rootView, id);
      if (valSessionDuration == null) {
        break missingId;
      }

      id = R.id.val_total_charged_mah;
      TextView valTotalChargedMah = ViewBindings.findChildViewById(rootView, id);
      if (valTotalChargedMah == null) {
        break missingId;
      }

      id = R.id.val_total_charged_percent;
      TextView valTotalChargedPercent = ViewBindings.findChildViewById(rootView, id);
      if (valTotalChargedPercent == null) {
        break missingId;
      }

      return new SectionChargeCurrentSessionBinding((LinearLayout) rootView, btnResetSession,
          chargeCurrentSessionRoot, chargeSessionInfoButton, valAvgSpeedMixed, valAvgSpeedScreenOff,
          valAvgSpeedScreenOn, valCurrentRate, valScreenOffTime, valScreenOnTime,
          valSessionDuration, valTotalChargedMah, valTotalChargedPercent);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
