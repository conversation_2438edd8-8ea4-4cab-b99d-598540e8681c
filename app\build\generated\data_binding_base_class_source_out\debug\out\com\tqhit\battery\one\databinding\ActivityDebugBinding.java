// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDebugBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnTestCharge;

  @NonNull
  public final Button btnTestDischarge;

  private ActivityDebugBinding(@NonNull ConstraintLayout rootView, @NonNull Button btnTestCharge,
      @NonNull Button btnTestDischarge) {
    this.rootView = rootView;
    this.btnTestCharge = btnTestCharge;
    this.btnTestDischarge = btnTestDischarge;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDebugBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDebugBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_debug, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDebugBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_test_charge;
      Button btnTestCharge = ViewBindings.findChildViewById(rootView, id);
      if (btnTestCharge == null) {
        break missingId;
      }

      id = R.id.btn_test_discharge;
      Button btnTestDischarge = ViewBindings.findChildViewById(rootView, id);
      if (btnTestDischarge == null) {
        break missingId;
      }

      return new ActivityDebugBinding((ConstraintLayout) rootView, btnTestCharge, btnTestDischarge);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
