// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityLanguageSelectionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView ar;

  @NonNull
  public final TextView de;

  @NonNull
  public final TextView en;

  @NonNull
  public final TextView es;

  @NonNull
  public final TextView fr;

  @NonNull
  public final TextView hu;

  @NonNull
  public final TextView it;

  @NonNull
  public final ShimmerFrameLayout nativeAd;

  @NonNull
  public final TextView nextButton;

  @NonNull
  public final TextView nl;

  @NonNull
  public final TextView pl;

  @NonNull
  public final TextView pt;

  @NonNull
  public final TextView ro;

  @NonNull
  public final TextView ru;

  @NonNull
  public final TextView subtitleText;

  @NonNull
  public final TextView titleText;

  @NonNull
  public final TextView tr;

  @NonNull
  public final TextView ua;

  @NonNull
  public final TextView zh;

  private ActivityLanguageSelectionBinding(@NonNull LinearLayout rootView, @NonNull TextView ar,
      @NonNull TextView de, @NonNull TextView en, @NonNull TextView es, @NonNull TextView fr,
      @NonNull TextView hu, @NonNull TextView it, @NonNull ShimmerFrameLayout nativeAd,
      @NonNull TextView nextButton, @NonNull TextView nl, @NonNull TextView pl,
      @NonNull TextView pt, @NonNull TextView ro, @NonNull TextView ru,
      @NonNull TextView subtitleText, @NonNull TextView titleText, @NonNull TextView tr,
      @NonNull TextView ua, @NonNull TextView zh) {
    this.rootView = rootView;
    this.ar = ar;
    this.de = de;
    this.en = en;
    this.es = es;
    this.fr = fr;
    this.hu = hu;
    this.it = it;
    this.nativeAd = nativeAd;
    this.nextButton = nextButton;
    this.nl = nl;
    this.pl = pl;
    this.pt = pt;
    this.ro = ro;
    this.ru = ru;
    this.subtitleText = subtitleText;
    this.titleText = titleText;
    this.tr = tr;
    this.ua = ua;
    this.zh = zh;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityLanguageSelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityLanguageSelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_language_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityLanguageSelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ar;
      TextView ar = ViewBindings.findChildViewById(rootView, id);
      if (ar == null) {
        break missingId;
      }

      id = R.id.de;
      TextView de = ViewBindings.findChildViewById(rootView, id);
      if (de == null) {
        break missingId;
      }

      id = R.id.en;
      TextView en = ViewBindings.findChildViewById(rootView, id);
      if (en == null) {
        break missingId;
      }

      id = R.id.es;
      TextView es = ViewBindings.findChildViewById(rootView, id);
      if (es == null) {
        break missingId;
      }

      id = R.id.fr;
      TextView fr = ViewBindings.findChildViewById(rootView, id);
      if (fr == null) {
        break missingId;
      }

      id = R.id.hu;
      TextView hu = ViewBindings.findChildViewById(rootView, id);
      if (hu == null) {
        break missingId;
      }

      id = R.id.it;
      TextView it = ViewBindings.findChildViewById(rootView, id);
      if (it == null) {
        break missingId;
      }

      id = R.id.nativeAd;
      ShimmerFrameLayout nativeAd = ViewBindings.findChildViewById(rootView, id);
      if (nativeAd == null) {
        break missingId;
      }

      id = R.id.next_button;
      TextView nextButton = ViewBindings.findChildViewById(rootView, id);
      if (nextButton == null) {
        break missingId;
      }

      id = R.id.nl;
      TextView nl = ViewBindings.findChildViewById(rootView, id);
      if (nl == null) {
        break missingId;
      }

      id = R.id.pl;
      TextView pl = ViewBindings.findChildViewById(rootView, id);
      if (pl == null) {
        break missingId;
      }

      id = R.id.pt;
      TextView pt = ViewBindings.findChildViewById(rootView, id);
      if (pt == null) {
        break missingId;
      }

      id = R.id.ro;
      TextView ro = ViewBindings.findChildViewById(rootView, id);
      if (ro == null) {
        break missingId;
      }

      id = R.id.ru;
      TextView ru = ViewBindings.findChildViewById(rootView, id);
      if (ru == null) {
        break missingId;
      }

      id = R.id.subtitleText;
      TextView subtitleText = ViewBindings.findChildViewById(rootView, id);
      if (subtitleText == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      id = R.id.tr;
      TextView tr = ViewBindings.findChildViewById(rootView, id);
      if (tr == null) {
        break missingId;
      }

      id = R.id.ua;
      TextView ua = ViewBindings.findChildViewById(rootView, id);
      if (ua == null) {
        break missingId;
      }

      id = R.id.zh;
      TextView zh = ViewBindings.findChildViewById(rootView, id);
      if (zh == null) {
        break missingId;
      }

      return new ActivityLanguageSelectionBinding((LinearLayout) rootView, ar, de, en, es, fr, hu,
          it, nativeAd, nextButton, nl, pl, pt, ro, ru, subtitleText, titleText, tr, ua, zh);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
