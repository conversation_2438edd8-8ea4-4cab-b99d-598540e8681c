{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,414,534,634,732,847,989,1104,1263,1347,1445,1543,1644,1761,1890,1993,2134,2274,2415,2581,2714,2831,2952,3081,3180,3277,3398,3543,3649,3762,3876,4015,4160,4269,4376,4462,4563,4664,4775,4861,4947,5058,5138,5222,5323,5431,5530,5634,5721,5834,5934,6041,6160,6240,6357", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,110,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "171,290,409,529,629,727,842,984,1099,1258,1342,1440,1538,1639,1756,1885,1988,2129,2269,2410,2576,2709,2826,2947,3076,3175,3272,3393,3538,3644,3757,3871,4010,4155,4264,4371,4457,4558,4659,4770,4856,4942,5053,5133,5217,5318,5426,5525,5629,5716,5829,5929,6036,6155,6235,6352,6459"}, "to": {"startLines": "153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12964,13085,13204,13323,13443,13543,13641,13756,13898,14013,14172,14256,14354,14452,14553,14670,14799,14902,15043,15183,15324,15490,15623,15740,15861,15990,16089,16186,16307,16452,16558,16671,16785,16924,17069,17178,17285,17371,17472,17573,17684,17770,17856,17967,18047,18131,18232,18340,18439,18543,18630,18743,18843,18950,19069,19149,19266", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,110,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "13080,13199,13318,13438,13538,13636,13751,13893,14008,14167,14251,14349,14447,14548,14665,14794,14897,15038,15178,15319,15485,15618,15735,15856,15985,16084,16181,16302,16447,16553,16666,16780,16919,17064,17173,17280,17366,17467,17568,17679,17765,17851,17962,18042,18126,18227,18335,18434,18538,18625,18738,18838,18945,19064,19144,19261,19368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,121", "endOffsets": "161,283"}, "to": {"startLines": "271,272", "startColumns": "4,4", "startOffsets": "24343,24454", "endColumns": "110,121", "endOffsets": "24449,24571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,390,493,582,660,751,842,928,1014,1105,1181,1256,1335,1410,1492,1563", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,74,78,74,81,70,119", "endOffsets": "197,281,385,488,577,655,746,837,923,1009,1100,1176,1251,1330,1405,1487,1558,1678"}, "to": {"startLines": "65,66,87,88,89,150,151,273,274,289,290,301,303,304,305,308,309,310", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5148,5245,7759,7863,7966,12730,12808,24576,24667,26103,26189,26952,27107,27182,27261,27606,27688,27759", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,74,78,74,81,70,119", "endOffsets": "5240,5324,7858,7961,8050,12803,12894,24662,24748,26184,26275,27023,27177,27256,27331,27683,27754,27874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,261,331,407,483,581,676", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "128,187,256,326,402,478,576,671,753"}, "to": {"startLines": "116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10236,10314,10373,10442,10512,10588,10664,10762,10857", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "10309,10368,10437,10507,10583,10659,10757,10852,10934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,101", "endOffsets": "148,250"}, "to": {"startLines": "311,312", "startColumns": "4,4", "startOffsets": "27879,27977", "endColumns": "97,101", "endOffsets": "27972,28074"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,523,626,718,819,947,1031,1092,1157,1254,1334,1399,1494,1558,1630,1692,1768,1831,1888,2009,2067,2128,2185,2265,2402,2489,2564,2657,2737,2821,2960,3038,3117,3269,3358,3434,3491,3547,3613,3691,3772,3843,3931,4009,4086,4160,4239,4349,4439,4531,4623,4724,4798,4880,4981,5031,5114,5180,5272,5359,5421,5485,5548,5621,5744,5857,5961,6069,6130,6190,6276,6362,6439", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,87,102,91,100,127,83,60,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,74,92,79,83,138,77,78,151,88,75,56,55,65,77,80,70,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85,85,76,78", "endOffsets": "269,350,430,518,621,713,814,942,1026,1087,1152,1249,1329,1394,1489,1553,1625,1687,1763,1826,1883,2004,2062,2123,2180,2260,2397,2484,2559,2652,2732,2816,2955,3033,3112,3264,3353,3429,3486,3542,3608,3686,3767,3838,3926,4004,4081,4155,4234,4344,4434,4526,4618,4719,4793,4875,4976,5026,5109,5175,5267,5354,5416,5480,5543,5616,5739,5852,5956,6064,6125,6185,6271,6357,6434,6513"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,90,91,145,149,152,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,287,299,300,302", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "761,3660,3741,3821,3909,4012,4835,4936,5064,8055,8116,12230,12650,12899,19373,19468,19532,19604,19666,19742,19805,19862,19983,20041,20102,20159,20239,20376,20463,20538,20631,20711,20795,20934,21012,21091,21243,21332,21408,21465,21521,21587,21665,21746,21817,21905,21983,22060,22134,22213,22323,22413,22505,22597,22698,22772,22854,22955,23005,23088,23154,23246,23333,23395,23459,23522,23595,23718,23831,23935,24043,24104,25874,26789,26875,27028", "endLines": "22,50,51,52,53,54,62,63,64,90,91,145,149,152,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,287,299,300,302", "endColumns": "12,80,79,87,102,91,100,127,83,60,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,74,92,79,83,138,77,78,151,88,75,56,55,65,77,80,70,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85,85,76,78", "endOffsets": "930,3736,3816,3904,4007,4099,4931,5059,5143,8111,8176,12322,12725,12959,19463,19527,19599,19661,19737,19800,19857,19978,20036,20097,20154,20234,20371,20458,20533,20626,20706,20790,20929,21007,21086,21238,21327,21403,21460,21516,21582,21660,21741,21812,21900,21978,22055,22129,22208,22318,22408,22500,22592,22693,22767,22849,22950,23000,23083,23149,23241,23328,23390,23454,23517,23590,23713,23826,23930,24038,24099,24159,25955,26870,26947,27102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,271,382", "endColumns": "112,102,110,108", "endOffsets": "163,266,377,486"}, "to": {"startLines": "86,146,147,148", "startColumns": "4,4,4,4", "startOffsets": "7646,12327,12430,12541", "endColumns": "112,102,110,108", "endOffsets": "7754,12425,12536,12645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-ca\\values-ca.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3399,3463", "endColumns": "63,65", "endOffsets": "3458,3524"}, "to": {"startLines": "136,137", "startColumns": "4,4", "startOffsets": "11617,11681", "endColumns": "63,65", "endOffsets": "11676,11742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,270,347,490,659,746", "endColumns": "68,95,76,142,168,86,80", "endOffsets": "169,265,342,485,654,741,822"}, "to": {"startLines": "85,144,275,288,307,313,314", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7577,12134,24753,25960,27437,28079,28166", "endColumns": "68,95,76,142,168,86,80", "endOffsets": "7641,12225,24825,26098,27601,28161,28242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-ca\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,239,285,366,430,499,609,672,805,926,1043,1099,1158,1276,1366,1408,1501,1536,1571,1632,1722,1765", "endColumns": "39,45,80,63,68,109,62,132,120,116,55,58,117,89,41,92,34,34,60,89,42,55", "endOffsets": "238,284,365,429,498,608,671,804,925,1042,1098,1157,1275,1365,1407,1500,1535,1570,1631,1721,1764,1820"}, "to": {"startLines": "268,269,270,276,277,278,279,280,281,282,283,284,285,286,291,292,293,294,295,296,297,315", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24164,24208,24258,24830,24898,24971,25085,25152,25289,25414,25535,25595,25658,25780,26280,26326,26423,26462,26501,26566,26660,28247", "endColumns": "43,49,84,67,72,113,66,136,124,120,59,62,121,93,45,96,38,38,64,93,46,59", "endOffsets": "24203,24253,24338,24893,24966,25080,25147,25284,25409,25530,25590,25653,25775,25869,26321,26418,26457,26496,26561,26655,26702,28302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,487,666,752,840,923,1022,1120,1201,1267,1380,1490,1563,1632,1698,1769,1879,1990,2099,2168,2256,2331,2413,2502,2593,2657,2721,2774,2832,2880,2941,3006,3075,3140,3212,3276,3333,3399,3452,3512,3586,3660,3717", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,87,82,98,97,80,65,112,109,72,68,65,70,109,110,108,68,87,74,81,88,90,63,63,52,57,47,60,64,68,64,71,63,56,65,52,59,73,73,56,68", "endOffsets": "280,482,661,747,835,918,1017,1115,1196,1262,1375,1485,1558,1627,1693,1764,1874,1985,2094,2163,2251,2326,2408,2497,2588,2652,2716,2769,2827,2875,2936,3001,3070,3135,3207,3271,3328,3394,3447,3507,3581,3655,3712,3781"}, "to": {"startLines": "2,11,15,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,125,126,127,128,129,130,131,132,133,134,135,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,582,8181,8267,8355,8438,8537,8635,8716,8782,8895,9005,9078,9147,9213,9284,9394,9505,9614,9683,9771,9846,9928,10017,10108,10172,10939,10992,11050,11098,11159,11224,11293,11358,11430,11494,11551,11747,11800,11860,11934,12008,12065", "endLines": "10,14,18,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,125,126,127,128,129,130,131,132,133,134,135,138,139,140,141,142,143", "endColumns": "17,12,12,85,87,82,98,97,80,65,112,109,72,68,65,70,109,110,108,68,87,74,81,88,90,63,63,52,57,47,60,64,68,64,71,63,56,65,52,59,73,73,56,68", "endOffsets": "375,577,756,8262,8350,8433,8532,8630,8711,8777,8890,9000,9073,9142,9208,9279,9389,9500,9609,9678,9766,9841,9923,10012,10103,10167,10231,10987,11045,11093,11154,11219,11288,11353,11425,11489,11546,11612,11795,11855,11929,12003,12060,12129"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,298", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "935,1058,1163,1270,1353,1459,1585,1669,1748,1839,1932,2025,2120,2218,2311,2404,2498,2589,2680,2761,2872,2980,3078,3188,3293,3401,3561,26707", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "1053,1158,1265,1348,1454,1580,1664,1743,1834,1927,2020,2115,2213,2306,2399,2493,2584,2675,2756,2867,2975,3073,3183,3288,3396,3556,3655,26784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ca\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "130", "endOffsets": "325"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6330", "endColumns": "134", "endOffsets": "6460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "55,56,57,58,59,60,61,306", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4104,4200,4302,4401,4498,4604,4709,27336", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "4195,4297,4396,4493,4599,4704,4830,27432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ca\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,670,816,939,1058,1162,1329,1434,1589,1716,1876,2030,2091,2155", "endColumns": "100,147,122,104,145,122,118,103,166,104,154,126,159,153,60,63,82", "endOffsets": "293,441,564,669,815,938,1057,1161,1328,1433,1588,1715,1875,2029,2090,2154,2237"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5329,5434,5586,5713,5822,5972,6099,6222,6465,6636,6745,6904,7035,7199,7357,7422,7490", "endColumns": "104,151,126,108,149,126,122,107,170,108,158,130,163,157,64,67,86", "endOffsets": "5429,5581,5708,5817,5967,6094,6217,6325,6631,6740,6899,7030,7194,7352,7417,7485,7572"}}]}]}