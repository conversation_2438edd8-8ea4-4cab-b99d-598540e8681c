package com.tqhit.battery.one.activity.onboarding;

import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager;
import com.tqhit.battery.one.base.LocaleAwareActivity_MembersInjector;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class LanguageSelectionActivity_MembersInjector implements MembersInjector<LanguageSelectionActivity> {
  private final Provider<AppRepository> appRepositoryProvider;

  private final Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider;

  public LanguageSelectionActivity_MembersInjector(Provider<AppRepository> appRepositoryProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider) {
    this.appRepositoryProvider = appRepositoryProvider;
    this.applovinNativeAdManagerProvider = applovinNativeAdManagerProvider;
  }

  public static MembersInjector<LanguageSelectionActivity> create(
      Provider<AppRepository> appRepositoryProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider) {
    return new LanguageSelectionActivity_MembersInjector(appRepositoryProvider, applovinNativeAdManagerProvider);
  }

  @Override
  public void injectMembers(LanguageSelectionActivity instance) {
    LocaleAwareActivity_MembersInjector.injectAppRepository(instance, appRepositoryProvider.get());
    injectApplovinNativeAdManager(instance, applovinNativeAdManagerProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.onboarding.LanguageSelectionActivity.applovinNativeAdManager")
  public static void injectApplovinNativeAdManager(LanguageSelectionActivity instance,
      ApplovinNativeAdManager applovinNativeAdManager) {
    instance.applovinNativeAdManager = applovinNativeAdManager;
  }
}
