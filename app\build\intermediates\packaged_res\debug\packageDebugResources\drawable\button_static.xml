<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android" android:exitFadeDuration="250">
    <item android:state_pressed="true">
        <shape android:tint="?attr/grey_pressed">
            <size
                android:height="50dp"
                android:width="50dp"/>
            <corners android:radius="100dp"/>
        </shape>
    </item>
    <item>
        <shape android:tint="?attr/grey">
            <size
                android:height="50dp"
                android:width="50dp"/>
            <corners android:radius="100dp"/>
        </shape>
    </item>
</selector>
