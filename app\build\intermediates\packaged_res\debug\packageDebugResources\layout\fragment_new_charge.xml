<?xml version="1.0" encoding="utf-8"?>
<!-- 
 * New charge fragment layout with modular section includes
 * This is the main container for the charge screen which includes multiple
 * section layouts for better organization and maintainability.
 */
-->
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/new_charge_scroll_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:animateLayoutChanges="true">

        <!-- Promo container (preserved from original layout) -->
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/promo_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"/>

        <!-- Main display section (percentage and time) -->
        <include
            android:id="@+id/chargeMainDisplaySection"
            layout="@layout/section_charge_main_display" />

        <com.facebook.shimmer.ShimmerFrameLayout
            android:id="@+id/nativeAd"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_220sdp"
            android:background="?attr/grey" />

        <!-- Not charging message -->
        <TextView
            android:id="@+id/newChargeNotChargingMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp"
            android:layout_marginBottom="14dp"
            android:background="@drawable/white_block"
            android:padding="16dp"
            android:text="@string/not_charging_message"
            android:textAlignment="center"
            android:textColor="?attr/colorr"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <!-- Battery wear section (target percentage) -->
        <include
            android:id="@+id/chargeBatteryWearSection"
            layout="@layout/section_charge_battery_wear" />

        <!-- Charging status details -->
        <include
            android:id="@+id/chargeStatusDetailsSection"
            layout="@layout/section_charge_status_details" />

        <!-- Remaining charging time -->
        <include
            android:id="@+id/chargeRemainingTimeSection"
            layout="@layout/section_charge_remaining_time" />

        <!-- Current charging session details -->
        <include
            android:id="@+id/chargeCurrentSessionSection"
            layout="@layout/section_charge_current_session" />
            
        <!-- Overall average charging speeds -->
        <include
            android:id="@+id/chargeOverallAverageSection"
            layout="@layout/section_charge_overall_average" />

        <!-- Bottom margin to avoid overlap with navigation bar -->
        <Space
            android:layout_width="match_parent"
            android:layout_height="90dp"/>

    </LinearLayout>
</androidx.core.widget.NestedScrollView>