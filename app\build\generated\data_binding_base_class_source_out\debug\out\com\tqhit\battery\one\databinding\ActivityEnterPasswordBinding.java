// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityEnterPasswordBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button confirmChangeCapacity;

  @NonNull
  public final TextInputEditText textInputEdit;

  @NonNull
  public final TextView textView20;

  @NonNull
  public final LinearLayout w22;

  private ActivityEnterPasswordBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button confirmChangeCapacity, @NonNull TextInputEditText textInputEdit,
      @NonNull TextView textView20, @NonNull LinearLayout w22) {
    this.rootView = rootView;
    this.confirmChangeCapacity = confirmChangeCapacity;
    this.textInputEdit = textInputEdit;
    this.textView20 = textView20;
    this.w22 = w22;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityEnterPasswordBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityEnterPasswordBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_enter_password, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityEnterPasswordBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.confirm_change_capacity;
      Button confirmChangeCapacity = ViewBindings.findChildViewById(rootView, id);
      if (confirmChangeCapacity == null) {
        break missingId;
      }

      id = R.id.textInputEdit;
      TextInputEditText textInputEdit = ViewBindings.findChildViewById(rootView, id);
      if (textInputEdit == null) {
        break missingId;
      }

      id = R.id.textView20;
      TextView textView20 = ViewBindings.findChildViewById(rootView, id);
      if (textView20 == null) {
        break missingId;
      }

      id = R.id.w22;
      LinearLayout w22 = ViewBindings.findChildViewById(rootView, id);
      if (w22 == null) {
        break missingId;
      }

      return new ActivityEnterPasswordBinding((ConstraintLayout) rootView, confirmChangeCapacity,
          textInputEdit, textView20, w22);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
