<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_discharge_section_status_and_estimates" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\layout_discharge_section_status_and_estimates.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@+id/statusAndEstimatesRoot"><Targets><Target id="@+id/statusAndEstimatesRoot" tag="layout/layout_discharge_section_status_and_estimates_0" view="LinearLayout"><Expressions/><location startLine="9" startOffset="0" endLine="208" endOffset="14"/></Target><Target id="@+id/sae_tv_percentage" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="44" endOffset="58"/></Target><Target id="@+id/sae_tv_formatted_status_text" view="TextView"><Expressions/><location startLine="68" startOffset="12" endLine="85" endOffset="41"/></Target><Target id="@+id/sae_rl_screen_on_estimate" view="RelativeLayout"><Expressions/><location startLine="88" startOffset="12" endLine="125" endOffset="28"/></Target><Target id="@+id/sae_tv_screen_on_time" view="TextView"><Expressions/><location startLine="97" startOffset="16" endLine="106" endOffset="45"/></Target><Target id="@+id/sae_rl_mixed_usage_estimate" view="RelativeLayout"><Expressions/><location startLine="128" startOffset="12" endLine="165" endOffset="28"/></Target><Target id="@+id/sae_tv_mixed_usage_time" view="TextView"><Expressions/><location startLine="137" startOffset="16" endLine="146" endOffset="45"/></Target><Target id="@+id/sae_rl_screen_off_estimate" view="RelativeLayout"><Expressions/><location startLine="168" startOffset="12" endLine="205" endOffset="28"/></Target><Target id="@+id/sae_tv_screen_off_time" view="TextView"><Expressions/><location startLine="177" startOffset="16" endLine="186" endOffset="45"/></Target></Targets></Layout>