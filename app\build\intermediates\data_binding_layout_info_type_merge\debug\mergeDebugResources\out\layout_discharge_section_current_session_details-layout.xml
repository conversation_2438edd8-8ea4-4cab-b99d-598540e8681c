<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_discharge_section_current_session_details" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\layout_discharge_section_current_session_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@+id/currentSessionDetailsRoot"><Targets><Target id="@+id/currentSessionDetailsRoot" tag="layout/layout_discharge_section_current_session_details_0" view="LinearLayout"><Expressions/><location startLine="10" startOffset="0" endLine="406" endOffset="14"/></Target><Target id="@+id/csd_tv_title" view="TextView"><Expressions/><location startLine="26" startOffset="8" endLine="36" endOffset="49"/></Target><Target id="@+id/csd_iv_info_button" view="ImageView"><Expressions/><location startLine="38" startOffset="8" endLine="47" endOffset="54"/></Target><Target id="@+id/csd_cl_total_time" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="57" startOffset="8" endLine="99" endOffset="59"/></Target><Target id="@+id/csd_tv_total_time_label" view="TextView"><Expressions/><location startLine="68" startOffset="12" endLine="76" endOffset="53"/></Target><Target id="@+id/csd_tv_total_time_value" view="TextView"><Expressions/><location startLine="77" startOffset="12" endLine="86" endOffset="53"/></Target><Target id="@+id/csd_tv_session_start_time" view="TextView"><Expressions/><location startLine="88" startOffset="12" endLine="97" endOffset="53"/></Target><Target id="@+id/csd_cl_current_rate" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="102" startOffset="8" endLine="143" endOffset="59"/></Target><Target id="@+id/csd_tv_current_rate_label" view="TextView"><Expressions/><location startLine="114" startOffset="13" endLine="122" endOffset="53"/></Target><Target id="@+id/csd_tv_current_rate_value" view="TextView"><Expressions/><location startLine="123" startOffset="12" endLine="132" endOffset="53"/></Target><Target id="@+id/csd_cl_avg_speed" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="152" startOffset="9" endLine="210" endOffset="59"/></Target><Target id="@+id/csd_tv_avg_speed_label" view="TextView"><Expressions/><location startLine="163" startOffset="12" endLine="171" endOffset="53"/></Target><Target id="@+id/csd_tv_avg_speed_percent_value" view="TextView"><Expressions/><location startLine="172" startOffset="12" endLine="181" endOffset="53"/></Target><Target id="@+id/csd_tv_avg_speed_mah_value" view="TextView"><Expressions/><location startLine="191" startOffset="12" endLine="200" endOffset="53"/></Target><Target id="@+id/csd_cl_total_consumed" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="213" startOffset="8" endLine="275" endOffset="59"/></Target><Target id="@+id/csd_tv_total_consumed_label" view="TextView"><Expressions/><location startLine="225" startOffset="12" endLine="233" endOffset="53"/></Target><Target id="@+id/csd_tv_total_consumed_percent_value" view="TextView"><Expressions/><location startLine="234" startOffset="12" endLine="243" endOffset="53"/></Target><Target id="@+id/csd_tv_total_consumed_percent_unit_and_range" view="TextView"><Expressions/><location startLine="245" startOffset="12" endLine="254" endOffset="53"/></Target><Target id="@+id/csd_tv_total_consumed_mah_value" view="TextView"><Expressions/><location startLine="256" startOffset="12" endLine="265" endOffset="53"/></Target><Target id="@+id/csd_cl_screen_off_stats" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="284" startOffset="8" endLine="342" endOffset="59"/></Target><Target id="@+id/csd_tv_screen_off_stats_label" view="TextView"><Expressions/><location startLine="295" startOffset="12" endLine="303" endOffset="53"/></Target><Target id="@+id/csd_tv_screen_off_percent_value" view="TextView"><Expressions/><location startLine="304" startOffset="12" endLine="313" endOffset="53"/></Target><Target id="@+id/csd_tv_screen_off_mah_value" view="TextView"><Expressions/><location startLine="323" startOffset="12" endLine="332" endOffset="53"/></Target><Target id="@+id/csd_cl_screen_on_stats" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="345" startOffset="8" endLine="404" endOffset="59"/></Target><Target id="@+id/csd_tv_screen_on_stats_label" view="TextView"><Expressions/><location startLine="357" startOffset="13" endLine="365" endOffset="53"/></Target><Target id="@+id/csd_tv_screen_on_percent_value" view="TextView"><Expressions/><location startLine="366" startOffset="12" endLine="375" endOffset="53"/></Target><Target id="@+id/csd_tv_screen_on_mah_value" view="TextView"><Expressions/><location startLine="385" startOffset="12" endLine="394" endOffset="53"/></Target></Targets></Layout>