{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tqhit.battery.one.app-mergeDebugResources-132:\\values-fr\\values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1010,1095,1246,1324,1398,1476,1545", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,77,73,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,1005,1090,1166,1319,1393,1471,1540,1662"}, "to": {"startLines": "176,177,214,215,238,328,330,471,477,519,520,540,557,558,564,565,567", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15325,15424,20964,21064,24266,31384,31535,44836,45323,49266,49347,50881,52623,52701,53244,53322,53443", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,77,73,77,68,121", "endOffsets": "15419,15507,21059,21159,24348,31458,31622,44923,45405,49342,49427,50952,52696,52770,53317,53386,53560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,158", "endColumns": "102,125", "endOffsets": "153,279"}, "to": {"startLines": "469,470", "startColumns": "4,4", "startOffsets": "44607,44710", "endColumns": "102,125", "endOffsets": "44705,44831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "178,179,180,181,182,183,184,185,187,188,189,190,191,192,193,194,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15512,15618,15798,15928,16037,16208,16341,16462,16740,16918,17030,17215,17351,17511,17690,17763,17830", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "15613,15793,15923,16032,16203,16336,16457,16570,16913,17025,17210,17346,17506,17685,17758,17825,17909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "150,151,152,153,154,155,156,559", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "13057,13155,13257,13356,13458,13562,13666,52775", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "13150,13252,13351,13453,13557,13661,13779,52871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,293,415,536,635,729,841,985,1104,1251,1335,1435,1536,1637,1758,1885,1990,2140,2286,2416,2608,2734,2852,2975,3108,3210,3315,3439,3564,3666,3773,3878,4023,4175,4284,4393,4480,4573,4668,4788,4879,4965,5072,5152,5237,5339,5451,5549,5649,5737,5853,5954,6057,6189,6269,6379", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,119,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "170,288,410,531,630,724,836,980,1099,1246,1330,1430,1531,1632,1753,1880,1985,2135,2281,2411,2603,2729,2847,2970,3103,3205,3310,3434,3559,3661,3768,3873,4018,4170,4279,4388,4475,4568,4663,4783,4874,4960,5067,5147,5232,5334,5446,5544,5644,5732,5848,5949,6052,6184,6264,6374,6472"}, "to": {"startLines": "344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "32960,33080,33198,33320,33441,33540,33634,33746,33890,34009,34156,34240,34340,34441,34542,34663,34790,34895,35045,35191,35321,35513,35639,35757,35880,36013,36115,36220,36344,36469,36571,36678,36783,36928,37080,37189,37298,37385,37478,37573,37693,37784,37870,37977,38057,38142,38244,38356,38454,38554,38642,38758,38859,38962,39094,39174,39284", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,119,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "33075,33193,33315,33436,33535,33629,33741,33885,34004,34151,34235,34335,34436,34537,34658,34785,34890,35040,35186,35316,35508,35634,35752,35875,36008,36110,36215,36339,36464,36566,36673,36778,36923,37075,37184,37293,37380,37473,37568,37688,37779,37865,37972,38052,38137,38239,38351,38449,38549,38637,38753,38854,38957,39089,39169,39279,39377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "201,302,303,304", "startColumns": "4,4,4,4", "startOffsets": "18274,29151,29253,29372", "endColumns": "106,101,118,104", "endOffsets": "18376,29248,29367,29472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3465,3530", "endColumns": "64,65", "endOffsets": "3525,3591"}, "to": {"startLines": "291,292", "startColumns": "4,4", "startOffsets": "28374,28439", "endColumns": "64,65", "endOffsets": "28434,28500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,483,692,781,872,951,1049,1146,1225,1291,1397,1504,1569,1635,1699,1771,1891,2014,2136,2211,2299,2372,2452,2543,2636,2702,2766,2819,2879,2927,2988,3059,3130,3197,3275,3340,3399,3465,3517,3577,3651,3725,3779", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,51,59,73,73,53,65", "endOffsets": "286,478,687,776,867,946,1044,1141,1220,1286,1392,1499,1564,1630,1694,1766,1886,2009,2131,2206,2294,2367,2447,2538,2631,2697,2761,2814,2874,2922,2983,3054,3125,3192,3270,3335,3394,3460,3512,3572,3646,3720,3774,3840"}, "to": {"startLines": "2,11,15,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,280,281,282,283,284,285,286,287,288,289,290,293,294,295,296,297,298", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,386,578,24888,24977,25068,25147,25245,25342,25421,25487,25593,25700,25765,25831,25895,25967,26087,26210,26332,26407,26495,26568,26648,26739,26832,26898,27675,27728,27788,27836,27897,27968,28039,28106,28184,28249,28308,28505,28557,28617,28691,28765,28819", "endLines": "10,14,18,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,280,281,282,283,284,285,286,287,288,289,290,293,294,295,296,297,298", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,51,59,73,73,53,65", "endOffsets": "381,573,782,24972,25063,25142,25240,25337,25416,25482,25588,25695,25760,25826,25890,25962,26082,26205,26327,26402,26490,26563,26643,26734,26827,26893,26957,27723,27783,27831,27892,27963,28034,28101,28179,28244,28303,28369,28552,28612,28686,28760,28814,28880"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-fr\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,265,30,31,32,33,34,35,36,241,251,254,256,37,2,264,244,262,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,239,57,58,243,59,60,61,62,63,64,65,66,67,68,249,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,259,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,260,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,242,168,169,170,171,172,173,174,175,176,177,178,247,246,245,253,257,179,180,181,182,183,184,255,185,186,187,240,188,261,189,190,191,192,193,194,195,196,248,197,198,258,199,200,201,202,203,252,204,205,206,207,208,209,210,211,212,213,214,215,216,263,217,218,219,220,221,222,223,224,225,226,250,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "130,201,253,303,353,404,457,505,555,610,683,734,782,833,880,23015,932,1014,1072,1124,1175,1232,1313,1387,1471,1550,1620,1693,25780,1747,1786,1969,2026,2120,2163,2223,23265,24156,24647,24774,2322,57,25734,23468,25609,2367,2416,2455,2551,2610,2686,3117,3555,12954,13021,12178,12089,13085,3598,3679,3757,3993,4346,4437,4489,4531,4590,4663,4711,23151,4778,4850,23390,4894,4975,5019,5091,5270,5342,5441,5524,5594,5687,24029,5757,5818,5877,5948,6010,6057,6129,6242,6292,6678,6733,6795,7440,8105,8180,8235,8391,8465,8781,8837,9379,9442,9585,9750,9916,9981,10028,10076,10153,10253,10346,10398,10546,10651,10711,10779,10854,11179,11471,11699,11857,11961,13172,13245,25395,13301,13343,13432,13505,13563,13819,13872,13956,14027,14096,14247,14312,14460,14710,14749,14805,14843,14881,14917,14953,15039,15081,15144,15202,15275,25462,15351,15420,15480,15525,15566,15639,16107,16164,16243,16426,16510,16593,16630,16665,16706,16749,16806,16873,16943,17000,17072,17230,23314,17266,17324,17379,17478,17550,17629,17749,17891,17972,18016,18102,23818,23605,23513,24537,25140,18449,18489,18544,18595,18666,18750,24696,18801,18884,18933,23220,18978,25535,19105,19200,19297,19355,19429,19665,19740,19803,23937,19872,19925,25191,19993,20081,20190,20282,20331,24460,20401,20487,20534,20590,20638,20760,21178,21266,21312,21370,21455,21514,21567,25676,21608,21684,21926,22019,22085,22167,22221,22306,22390,22429,24097,22496,22563,22608,22650,22704,22769,22844,22897,22934,22970", "endColumns": "69,50,48,48,49,51,46,48,53,71,49,46,49,45,50,75,80,56,50,49,55,79,72,82,77,68,71,52,72,37,181,55,92,41,58,97,47,302,47,364,43,71,44,43,65,47,37,94,57,74,429,436,41,65,62,774,87,83,79,76,234,351,89,50,40,57,71,46,65,67,70,42,76,79,42,70,177,70,97,81,68,91,68,66,59,57,69,60,45,70,111,48,384,53,60,643,663,73,53,154,72,314,54,540,61,141,163,164,63,45,46,75,98,91,50,146,103,58,66,73,323,290,226,156,102,81,71,54,65,40,87,71,56,254,51,82,69,67,149,63,146,248,37,54,36,36,34,34,84,40,61,56,71,74,71,67,58,43,39,71,466,55,77,181,82,81,35,33,39,41,55,65,68,55,70,156,34,74,56,53,97,70,77,118,140,79,42,84,345,117,211,90,108,49,38,53,49,69,82,49,76,81,47,43,43,125,72,93,95,56,72,234,73,61,67,90,51,66,202,86,107,90,47,68,75,84,45,54,46,120,416,86,44,56,83,57,51,39,56,74,240,91,64,80,52,83,82,37,65,57,65,43,40,52,63,73,51,35,34,43", "endOffsets": "195,247,297,347,398,451,499,549,604,677,728,776,827,874,926,23086,1008,1066,1118,1169,1226,1307,1381,1465,1544,1614,1687,1741,25848,1780,1963,2020,2114,2157,2217,2316,23308,24454,24690,25134,2361,124,25774,23507,25670,2410,2449,2545,2604,2680,3111,3549,3592,13015,13079,12948,12172,13164,3673,3751,3987,4340,4431,4483,4525,4584,4657,4705,4772,23214,4844,4888,23462,4969,5013,5085,5264,5336,5435,5518,5588,5681,5751,24091,5812,5871,5942,6004,6051,6123,6236,6286,6672,6727,6789,7434,8099,8174,8229,8385,8459,8775,8831,9373,9436,9579,9744,9910,9975,10022,10070,10147,10247,10340,10392,10540,10645,10705,10773,10848,11173,11465,11693,11851,11955,12038,13239,13295,25456,13337,13426,13499,13557,13813,13866,13950,14021,14090,14241,14306,14454,14704,14743,14799,14837,14875,14911,14947,15033,15075,15138,15196,15269,15345,25529,15414,15474,15519,15560,15633,16101,16158,16237,16420,16504,16587,16624,16659,16700,16743,16800,16867,16937,16994,17066,17224,17260,23384,17318,17373,17472,17544,17623,17743,17885,17966,18010,18096,18443,23931,23812,23599,24641,25185,18483,18538,18589,18660,18744,18795,24768,18878,18927,18972,23259,19099,25603,19194,19291,19349,19423,19659,19734,19797,19866,24023,19919,19987,25389,20075,20184,20276,20325,20395,24531,20481,20528,20584,20632,20754,21172,21260,21306,21364,21449,21508,21561,21602,25728,21678,21920,22013,22079,22161,22215,22300,22384,22423,22490,24150,22557,22602,22644,22698,22763,22838,22891,22928,22964,23009"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,45,46,57,58,60,61,62,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,137,138,139,145,146,147,148,149,157,158,159,162,163,164,165,166,167,168,169,170,171,172,173,174,196,197,198,199,202,203,204,205,206,207,208,209,210,211,212,213,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,239,240,241,242,243,245,300,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,326,327,329,331,332,334,335,336,337,338,339,340,341,342,343,401,402,403,404,418,419,465,472,473,474,475,476,479,480,481,482,483,484,485,486,495,496,497,498,499,500,501,503,504,505,506,507,508,509,510,511,512,514,515,516,517,518,521,522,523,524,525,526,534,535,539,541,542,543,544,545,546,547,548,550,551,552,553,554,555,556,560,561,563,566,568,569,570,571,572,621,622,623,624,625,626,629,630,631,632,634,635,636,637,638,639,640,641", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "970,1040,1091,1140,1189,1239,1291,1338,1387,1441,1513,1563,1610,1660,1706,1757,2059,2140,2433,2484,3474,3530,3721,3794,3877,4221,4290,4362,4415,4488,4526,4708,4764,4857,4899,4958,5056,5104,5407,5455,5820,5864,5936,6772,6816,6882,6930,6968,7063,7121,7196,7626,8063,8105,8171,8234,9009,9097,9181,9261,9338,9573,9925,12135,12186,12227,12733,12805,12852,12918,12986,13784,13827,13904,14212,14255,14326,14504,14575,14673,14755,14824,14916,14985,15052,15112,15170,17914,17975,18021,18092,18381,18430,18815,18869,18930,19574,20238,20312,20366,20521,20594,20909,21164,21705,21767,21909,22073,22238,22302,22348,22395,22471,22570,22662,22713,22860,22964,23023,23090,23164,23488,23779,24006,24163,24353,24435,24507,24562,24628,24734,28982,29477,29534,29789,29841,29924,29994,30062,30212,30276,30423,30672,30710,30765,30802,30839,30874,30909,30994,31035,31097,31237,31309,31463,31627,31695,31820,31864,31904,31976,32443,32499,32577,32759,32842,32924,39382,39416,39456,39498,40559,40625,44394,44928,44999,45156,45191,45266,45488,45542,45640,45711,45789,45908,46049,46129,46994,47079,47425,47543,47755,47846,47955,48094,48133,48187,48237,48307,48390,48440,48517,48599,48647,48833,48877,49003,49076,49170,49432,49489,49562,49797,49871,49933,50420,50511,50814,50957,51160,51247,51355,51446,51494,51563,51639,51805,51851,51906,51953,52074,52491,52578,52876,52933,53186,53391,53565,53605,53662,53737,53978,57448,57513,57594,57647,57731,57814,58018,58084,58142,58208,58312,58353,58406,58470,58544,58596,58632,58667", "endColumns": "69,50,48,48,49,51,46,48,53,71,49,46,49,45,50,75,80,56,50,49,55,79,72,82,77,68,71,52,72,37,181,55,92,41,58,97,47,302,47,364,43,71,44,43,65,47,37,94,57,74,429,436,41,65,62,774,87,83,79,76,234,351,89,50,40,57,71,46,65,67,70,42,76,79,42,70,177,70,97,81,68,91,68,66,59,57,69,60,45,70,111,48,384,53,60,643,663,73,53,154,72,314,54,540,61,141,163,164,63,45,46,75,98,91,50,146,103,58,66,73,323,290,226,156,102,81,71,54,65,40,87,71,56,254,51,82,69,67,149,63,146,248,37,54,36,36,34,34,84,40,61,56,71,74,71,67,58,43,39,71,466,55,77,181,82,81,35,33,39,41,55,65,68,55,70,156,34,74,56,53,97,70,77,118,140,79,42,84,345,117,211,90,108,49,38,53,49,69,82,49,76,81,47,43,43,125,72,93,95,56,72,234,73,61,67,90,51,66,202,86,107,90,47,68,75,84,45,54,46,120,416,86,44,56,83,57,51,39,56,74,240,91,64,80,52,83,82,37,65,57,65,43,40,52,63,73,51,35,34,43", "endOffsets": "1035,1086,1135,1184,1234,1286,1333,1382,1436,1508,1558,1605,1655,1701,1752,1828,2135,2192,2479,2529,3525,3605,3789,3872,3950,4285,4357,4410,4483,4521,4703,4759,4852,4894,4953,5051,5099,5402,5450,5815,5859,5931,5976,6811,6877,6925,6963,7058,7116,7191,7621,8058,8100,8166,8229,9004,9092,9176,9256,9333,9568,9920,10010,12181,12222,12280,12800,12847,12913,12981,13052,13822,13899,13979,14250,14321,14499,14570,14668,14750,14819,14911,14980,15047,15107,15165,15235,17970,18016,18087,18199,18425,18810,18864,18925,19569,20233,20307,20361,20516,20589,20904,20959,21700,21762,21904,22068,22233,22297,22343,22390,22466,22565,22657,22708,22855,22959,23018,23085,23159,23483,23774,24001,24158,24261,24430,24502,24557,24623,24664,24817,29049,29529,29784,29836,29919,29989,30057,30207,30271,30418,30667,30705,30760,30797,30834,30869,30904,30989,31030,31092,31149,31304,31379,31530,31690,31749,31859,31899,31971,32438,32494,32572,32754,32837,32919,32955,39411,39451,39493,39549,40620,40689,44445,44994,45151,45186,45261,45318,45537,45635,45706,45784,45903,46044,46124,46167,47074,47420,47538,47750,47841,47950,48000,48128,48182,48232,48302,48385,48435,48512,48594,48642,48686,48872,48998,49071,49165,49261,49484,49557,49792,49866,49928,49996,50506,50558,50876,51155,51242,51350,51441,51489,51558,51634,51719,51846,51901,51948,52069,52486,52573,52618,52928,53012,53239,53438,53600,53657,53732,53973,54065,57508,57589,57642,57726,57809,57847,58079,58137,58203,58247,58348,58401,58465,58539,58591,58627,58662,58706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,178,224,302,341,391,451,505,566,634,699,743,848,969,1050,1144,1185,1256,1305,1397,1472,1529,1580,1680,1734,1792,1848,1924,1970,2044,2174,2234,2309,2420,2480,2558,2633,2690,2748,2802,2858,2920,2984,3081,3192", "endColumns": "61,60,45,77,38,49,59,53,60,67,64,43,104,120,80,93,40,70,48,91,74,56,50,99,53,57,55,75,45,73,129,59,74,110,59,77,74,56,57,53,55,61,63,96,110,56", "endOffsets": "112,173,219,297,336,386,446,500,561,629,694,738,843,964,1045,1139,1180,1251,1300,1392,1467,1524,1575,1675,1729,1787,1843,1919,1965,2039,2169,2229,2304,2415,2475,2553,2628,2685,2743,2797,2853,2915,2979,3076,3187,3244"}, "to": {"startLines": "575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "54254,54316,54377,54423,54501,54540,54590,54650,54704,54765,54833,54898,54942,55047,55168,55249,55343,55384,55455,55504,55596,55671,55728,55779,55879,55933,55991,56047,56123,56169,56243,56373,56433,56508,56619,56679,56757,56832,56889,56947,57001,57057,57119,57183,57280,57391", "endColumns": "61,60,45,77,38,49,59,53,60,67,64,43,104,120,80,93,40,70,48,91,74,56,50,99,53,57,55,75,45,73,129,59,74,110,59,77,74,56,57,53,55,61,63,96,110,56", "endOffsets": "54311,54372,54418,54496,54535,54585,54645,54699,54760,54828,54893,54937,55042,55163,55244,55338,55379,55450,55499,55591,55666,55723,55774,55874,55928,55986,56042,56118,56164,56238,56368,56428,56503,56614,56674,56752,56827,56884,56942,56996,57052,57114,57178,57275,57386,57443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,350,492,661,747", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "170,267,345,487,656,742,822"}, "to": {"startLines": "200,299,478,513,562,627,628", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "18204,28885,45410,48691,53017,57852,57938", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "18269,28977,45483,48828,53181,57933,58013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,287,594,657,802,920,1042,1092,1150,1282,1384,1432,1527,1563,1598,1654,1735,1775", "endColumns": "41,45,56,62,144,117,121,49,57,131,101,47,94,35,34,55,80,39,55", "endOffsets": "240,286,343,656,801,919,1041,1091,1149,1281,1383,1431,1526,1562,1597,1653,1734,1774,1830"}, "to": {"startLines": "466,467,468,487,488,489,490,491,492,493,494,527,528,529,530,531,532,533,633", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "44450,44496,44546,46172,46239,46388,46510,46636,46690,46752,46888,50001,50053,50152,50192,50231,50291,50376,58252", "endColumns": "45,49,60,66,148,121,125,53,61,135,105,51,98,39,38,59,84,43,59", "endOffsets": "44491,44541,44602,46234,46383,46505,46631,46685,46747,46883,46989,50048,50147,50187,50226,50286,50371,50415,58307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1109,1175,1272,1355,1421,1523,1588,1663,1719,1798,1858,1912,2034,2093,2155,2209,2291,2426,2518,2593,2688,2769,2853,2997,3076,3157,3298,3391,3470,3525,3576,3642,3722,3803,3874,3954,4027,4105,4178,4250,4362,4455,4527,4619,4711,4785,4869,4961,5018,5102,5168,5251,5338,5400,5464,5527,5605,5707,5811,5908,6012,6071,6126,6215,6302,6379", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "278,358,439,522,631,726,824,954,1039,1104,1170,1267,1350,1416,1518,1583,1658,1714,1793,1853,1907,2029,2088,2150,2204,2286,2421,2513,2588,2683,2764,2848,2992,3071,3152,3293,3386,3465,3520,3571,3637,3717,3798,3869,3949,4022,4100,4173,4245,4357,4450,4522,4614,4706,4780,4864,4956,5013,5097,5163,5246,5333,5395,5459,5522,5600,5702,5806,5903,6007,6066,6121,6210,6297,6374,6455"}, "to": {"startLines": "19,140,141,142,143,144,160,161,175,244,246,301,325,333,405,406,407,408,409,410,411,412,413,414,415,416,417,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,502,537,538,549", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,12285,12365,12446,12529,12638,13984,14082,15240,24669,24822,29054,31154,31754,39554,39656,39721,39796,39852,39931,39991,40045,40167,40226,40288,40342,40424,40694,40786,40861,40956,41037,41121,41265,41344,41425,41566,41659,41738,41793,41844,41910,41990,42071,42142,42222,42295,42373,42446,42518,42630,42723,42795,42887,42979,43053,43137,43229,43286,43370,43436,43519,43606,43668,43732,43795,43873,43975,44079,44176,44280,44339,48005,50650,50737,51724", "endLines": "22,140,141,142,143,144,160,161,175,244,246,301,325,333,405,406,407,408,409,410,411,412,413,414,415,416,417,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,502,537,538,549", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "965,12360,12441,12524,12633,12728,14077,14207,15320,24729,24883,29146,31232,31815,39651,39716,39791,39847,39926,39986,40040,40162,40221,40283,40337,40419,40554,40781,40856,40951,41032,41116,41260,41339,41420,41561,41654,41733,41788,41839,41905,41985,42066,42137,42217,42290,42368,42441,42513,42625,42718,42790,42882,42974,43048,43132,43224,43281,43365,43431,43514,43601,43663,43727,43790,43868,43970,44074,44171,44275,44334,44389,48089,50732,50809,51800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "186", "startColumns": "4", "startOffsets": "16575", "endColumns": "164", "endOffsets": "16735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d148dadd36a9aeec86630cf6fa0b2687\\transformed\\jetified-applovin-sdk-13.3.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,168,222,290,464,604,690,770", "endColumns": "112,53,67,173,139,85,79,75", "endOffsets": "163,217,285,459,599,685,765,841"}, "to": {"startLines": "83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5981,6094,6148,6216,6390,6530,6616,6696", "endColumns": "112,53,67,173,139,85,79,75", "endOffsets": "6089,6143,6211,6385,6525,6611,6691,6767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9b1d1c56b528abe32d69bef2c9ce868e\\transformed\\jetified-bigo-ads-5.3.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,214,280,342,409,499,593,674,744,801,857,1248,1328,1409,1467,1540,1603,1665,1728,1780,1871,1932,1985,2033,2098", "endColumns": "83,74,65,61,66,89,93,80,69,56,55,390,79,80,57,72,62,61,62,51,90,60,52,47,64,76", "endOffsets": "134,209,275,337,404,494,588,669,739,796,852,1243,1323,1404,1462,1535,1598,1660,1723,1775,1866,1927,1980,2028,2093,2170"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10015,10099,10174,10240,10302,10369,10459,10553,10634,10704,10761,10817,11208,11288,11369,11427,11500,11563,11625,11688,11740,11831,11892,11945,11993,12058", "endColumns": "83,74,65,61,66,89,93,80,69,56,55,390,79,80,57,72,62,61,62,51,90,60,52,47,64,76", "endOffsets": "10094,10169,10235,10297,10364,10454,10548,10629,10699,10756,10812,11203,11283,11364,11422,11495,11558,11620,11683,11735,11826,11887,11940,11988,12053,12130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,94", "endOffsets": "139,234"}, "to": {"startLines": "573,574", "startColumns": "4,4", "startOffsets": "54070,54159", "endColumns": "88,94", "endOffsets": "54154,54249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,263,335,418,495,592,685", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "124,189,258,330,413,490,587,680,763"}, "to": {"startLines": "271,272,273,274,275,276,277,278,279", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "26962,27036,27101,27170,27242,27325,27402,27499,27592", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "27031,27096,27165,27237,27320,27397,27494,27587,27670"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,523,629,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,2049,2482,2589,2847", "endColumns": "110,114,105,129,90,92,97,94,99,92,92,94,90,90,110,106,158,86", "endOffsets": "211,326,624,754,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,2155,2584,2743,2929"}, "to": {"startLines": "39,40,43,44,47,48,49,50,51,52,53,54,55,56,59,63,64,536", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1833,1944,2197,2303,2534,2625,2718,2816,2911,3011,3104,3197,3292,3383,3610,3955,4062,50563", "endColumns": "110,114,105,129,90,92,97,94,99,92,92,94,90,90,110,106,158,86", "endOffsets": "1939,2054,2298,2428,2620,2713,2811,2906,3006,3099,3192,3287,3378,3469,3716,4057,4216,50645"}}]}, {"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "176,177,214,215,238,328,330,472,478,521,522,542,560,561,567,568,570", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15325,15424,20964,21064,24266,31384,31535,44893,45380,49376,49457,50991,52824,52902,53445,53523,53644", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,77,73,77,68,121", "endOffsets": "15419,15507,21059,21159,24348,31458,31622,44980,45462,49452,49537,51062,52897,52971,53518,53587,53761"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "470,471", "startColumns": "4,4", "startOffsets": "44664,44767", "endColumns": "102,125", "endOffsets": "44762,44888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fr\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "178,179,180,181,182,183,184,185,187,188,189,190,191,192,193,194,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15512,15618,15798,15928,16037,16208,16341,16462,16740,16918,17030,17215,17351,17511,17690,17763,17830", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "15613,15793,15923,16032,16203,16336,16457,16570,16913,17025,17210,17346,17506,17685,17758,17825,17909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "150,151,152,153,154,155,156,562", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "13057,13155,13257,13356,13458,13562,13666,52976", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "13150,13252,13351,13453,13557,13661,13779,53072"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "33017,33137,33255,33377,33498,33597,33691,33803,33947,34066,34213,34297,34397,34498,34599,34720,34847,34952,35102,35248,35378,35570,35696,35814,35937,36070,36172,36277,36401,36526,36628,36735,36840,36985,37137,37246,37355,37442,37535,37630,37750,37841,37927,38034,38114,38199,38301,38413,38511,38611,38699,38815,38916,39019,39151,39231,39341", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,119,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "33132,33250,33372,33493,33592,33686,33798,33942,34061,34208,34292,34392,34493,34594,34715,34842,34947,35097,35243,35373,35565,35691,35809,35932,36065,36167,36272,36396,36521,36623,36730,36835,36980,37132,37241,37350,37437,37530,37625,37745,37836,37922,38029,38109,38194,38296,38408,38506,38606,38694,38810,38911,39014,39146,39226,39336,39434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "201,302,303,304", "startColumns": "4,4,4,4", "startOffsets": "18274,29151,29253,29372", "endColumns": "106,101,118,104", "endOffsets": "18376,29248,29367,29472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "291,292", "startColumns": "4,4", "startOffsets": "28374,28439", "endColumns": "64,65", "endOffsets": "28434,28500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,11,15,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,280,281,282,283,284,285,286,287,288,289,290,293,294,295,296,297,298", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,386,578,24888,24977,25068,25147,25245,25342,25421,25487,25593,25700,25765,25831,25895,25967,26087,26210,26332,26407,26495,26568,26648,26739,26832,26898,27675,27728,27788,27836,27897,27968,28039,28106,28184,28249,28308,28505,28557,28617,28691,28765,28819", "endLines": "10,14,18,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,280,281,282,283,284,285,286,287,288,289,290,293,294,295,296,297,298", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,51,59,73,73,53,65", "endOffsets": "381,573,782,24972,25063,25142,25240,25337,25416,25482,25588,25695,25760,25826,25890,25962,26082,26205,26327,26402,26490,26563,26643,26734,26827,26893,26957,27723,27783,27831,27892,27963,28034,28101,28179,28244,28303,28369,28552,28612,28686,28760,28814,28880"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-fr\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,268,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,269,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,270,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,25901,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,25959,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,26013,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,56,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,52,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,90,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,25953,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,26007,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,26099,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,45,46,57,58,60,61,62,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,137,138,139,145,146,147,148,149,157,158,159,162,163,164,165,166,167,168,169,170,171,172,173,174,196,197,198,199,202,203,204,205,206,207,208,209,210,211,212,213,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,239,240,241,242,243,245,300,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,326,327,329,331,332,333,335,336,337,338,339,340,341,342,343,344,402,403,404,405,419,420,466,473,474,475,476,477,480,481,482,483,484,485,486,487,496,497,498,499,500,501,502,504,505,506,507,508,509,510,511,512,513,515,516,517,518,519,520,523,524,525,526,527,528,536,537,541,543,544,545,546,547,548,549,550,552,553,554,555,556,557,558,559,563,564,566,569,571,572,573,574,575,624,625,626,627,628,629,632,633,634,635,637,638,639,640,641,642,643,644", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "970,1040,1091,1140,1189,1239,1291,1338,1387,1441,1513,1563,1610,1660,1706,1757,2059,2140,2433,2484,3474,3530,3721,3794,3877,4221,4290,4362,4415,4488,4526,4708,4764,4857,4899,4958,5056,5104,5407,5455,5820,5864,5936,6772,6816,6882,6930,6968,7063,7121,7196,7626,8063,8105,8171,8234,9009,9097,9181,9261,9338,9573,9925,12135,12186,12227,12733,12805,12852,12918,12986,13784,13827,13904,14212,14255,14326,14504,14575,14673,14755,14824,14916,14985,15052,15112,15170,17914,17975,18021,18092,18381,18430,18815,18869,18930,19574,20238,20312,20366,20521,20594,20909,21164,21705,21767,21909,22073,22238,22302,22348,22395,22471,22570,22662,22713,22860,22964,23023,23090,23164,23488,23779,24006,24163,24353,24435,24507,24562,24628,24734,28982,29477,29534,29789,29841,29924,29994,30062,30212,30276,30423,30672,30710,30765,30802,30839,30874,30909,30994,31035,31097,31237,31309,31463,31627,31695,31754,31877,31921,31961,32033,32500,32556,32634,32816,32899,32981,39439,39473,39513,39555,40616,40682,44451,44985,45056,45213,45248,45323,45545,45599,45697,45768,45846,45965,46106,46186,47051,47136,47482,47600,47812,47903,48012,48151,48190,48244,48294,48364,48447,48497,48574,48656,48704,48890,48934,49060,49133,49186,49280,49542,49599,49672,49907,49981,50043,50530,50621,50924,51067,51270,51357,51465,51556,51604,51673,51749,51915,51961,52016,52063,52184,52601,52692,52779,53077,53134,53387,53592,53766,53806,53863,53938,54179,57649,57714,57795,57848,57932,58015,58219,58285,58343,58409,58513,58554,58607,58671,58745,58797,58833,58868", "endColumns": "69,50,48,48,49,51,46,48,53,71,49,46,49,45,50,75,80,56,50,49,55,79,72,82,77,68,71,52,72,37,181,55,92,41,58,97,47,302,47,364,43,71,44,43,65,47,37,94,57,74,429,436,41,65,62,774,87,83,79,76,234,351,89,50,40,57,71,46,65,67,70,42,76,79,42,70,177,70,97,81,68,91,68,66,59,57,69,60,45,70,111,48,384,53,60,643,663,73,53,154,72,314,54,540,61,141,163,164,63,45,46,75,98,91,50,146,103,58,66,73,323,290,226,156,102,81,71,54,65,40,87,71,56,254,51,82,69,67,149,63,146,248,37,54,36,36,34,34,84,40,61,56,71,74,71,67,58,56,43,39,71,466,55,77,181,82,81,35,33,39,41,55,65,68,55,70,156,34,74,56,53,97,70,77,118,140,79,42,84,345,117,211,90,108,49,38,53,49,69,82,49,76,81,47,43,43,125,72,52,93,95,56,72,234,73,61,67,90,51,66,202,86,107,90,47,68,75,84,45,54,46,120,416,90,86,44,56,83,57,51,39,56,74,240,91,64,80,52,83,82,37,65,57,65,43,40,52,63,73,51,35,34,43", "endOffsets": "1035,1086,1135,1184,1234,1286,1333,1382,1436,1508,1558,1605,1655,1701,1752,1828,2135,2192,2479,2529,3525,3605,3789,3872,3950,4285,4357,4410,4483,4521,4703,4759,4852,4894,4953,5051,5099,5402,5450,5815,5859,5931,5976,6811,6877,6925,6963,7058,7116,7191,7621,8058,8100,8166,8229,9004,9092,9176,9256,9333,9568,9920,10010,12181,12222,12280,12800,12847,12913,12981,13052,13822,13899,13979,14250,14321,14499,14570,14668,14750,14819,14911,14980,15047,15107,15165,15235,17970,18016,18087,18199,18425,18810,18864,18925,19569,20233,20307,20361,20516,20589,20904,20959,21700,21762,21904,22068,22233,22297,22343,22390,22466,22565,22657,22708,22855,22959,23018,23085,23159,23483,23774,24001,24158,24261,24430,24502,24557,24623,24664,24817,29049,29529,29784,29836,29919,29989,30057,30207,30271,30418,30667,30705,30760,30797,30834,30869,30904,30989,31030,31092,31149,31304,31379,31530,31690,31749,31806,31916,31956,32028,32495,32551,32629,32811,32894,32976,33012,39468,39508,39550,39606,40677,40746,44502,45051,45208,45243,45318,45375,45594,45692,45763,45841,45960,46101,46181,46224,47131,47477,47595,47807,47898,48007,48057,48185,48239,48289,48359,48442,48492,48569,48651,48699,48743,48929,49055,49128,49181,49275,49371,49594,49667,49902,49976,50038,50106,50616,50668,50986,51265,51352,51460,51551,51599,51668,51744,51829,51956,52011,52058,52179,52596,52687,52774,52819,53129,53213,53440,53639,53801,53858,53933,54174,54266,57709,57790,57843,57927,58010,58048,58280,58338,58404,58448,58549,58602,58666,58740,58792,58828,58863,58907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "54455,54517,54578,54624,54702,54741,54791,54851,54905,54966,55034,55099,55143,55248,55369,55450,55544,55585,55656,55705,55797,55872,55929,55980,56080,56134,56192,56248,56324,56370,56444,56574,56634,56709,56820,56880,56958,57033,57090,57148,57202,57258,57320,57384,57481,57592", "endColumns": "61,60,45,77,38,49,59,53,60,67,64,43,104,120,80,93,40,70,48,91,74,56,50,99,53,57,55,75,45,73,129,59,74,110,59,77,74,56,57,53,55,61,63,96,110,56", "endOffsets": "54512,54573,54619,54697,54736,54786,54846,54900,54961,55029,55094,55138,55243,55364,55445,55539,55580,55651,55700,55792,55867,55924,55975,56075,56129,56187,56243,56319,56365,56439,56569,56629,56704,56815,56875,56953,57028,57085,57143,57197,57253,57315,57379,57476,57587,57644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "200,299,479,514,565,630,631", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "18204,28885,45467,48748,53218,58053,58139", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "18269,28977,45540,48885,53382,58134,58214"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-fr\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "467,468,469,488,489,490,491,492,493,494,495,529,530,531,532,533,534,535,636", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "44507,44553,44603,46229,46296,46445,46567,46693,46747,46809,46945,50111,50163,50262,50302,50341,50401,50486,58453", "endColumns": "45,49,60,66,148,121,125,53,61,135,105,51,98,39,38,59,84,43,59", "endOffsets": "44548,44598,44659,46291,46440,46562,46688,46742,46804,46940,47046,50158,50257,50297,50336,50396,50481,50525,58508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "19,140,141,142,143,144,160,161,175,244,246,301,325,334,406,407,408,409,410,411,412,413,414,415,416,417,418,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,503,539,540,551", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,12285,12365,12446,12529,12638,13984,14082,15240,24669,24822,29054,31154,31811,39611,39713,39778,39853,39909,39988,40048,40102,40224,40283,40345,40399,40481,40751,40843,40918,41013,41094,41178,41322,41401,41482,41623,41716,41795,41850,41901,41967,42047,42128,42199,42279,42352,42430,42503,42575,42687,42780,42852,42944,43036,43110,43194,43286,43343,43427,43493,43576,43663,43725,43789,43852,43930,44032,44136,44233,44337,44396,48062,50760,50847,51834", "endLines": "22,140,141,142,143,144,160,161,175,244,246,301,325,334,406,407,408,409,410,411,412,413,414,415,416,417,418,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,503,539,540,551", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "965,12360,12441,12524,12633,12728,14077,14207,15320,24729,24883,29146,31232,31872,39708,39773,39848,39904,39983,40043,40097,40219,40278,40340,40394,40476,40611,40838,40913,41008,41089,41173,41317,41396,41477,41618,41711,41790,41845,41896,41962,42042,42123,42194,42274,42347,42425,42498,42570,42682,42775,42847,42939,43031,43105,43189,43281,43338,43422,43488,43571,43658,43720,43784,43847,43925,44027,44131,44228,44332,44391,44446,48146,50842,50919,51910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-fr\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "186", "startColumns": "4", "startOffsets": "16575", "endColumns": "164", "endOffsets": "16735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d148dadd36a9aeec86630cf6fa0b2687\\transformed\\jetified-applovin-sdk-13.3.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5981,6094,6148,6216,6390,6530,6616,6696", "endColumns": "112,53,67,173,139,85,79,75", "endOffsets": "6089,6143,6211,6385,6525,6611,6691,6767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9b1d1c56b528abe32d69bef2c9ce868e\\transformed\\jetified-bigo-ads-5.3.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10015,10099,10174,10240,10302,10369,10459,10553,10634,10704,10761,10817,11208,11288,11369,11427,11500,11563,11625,11688,11740,11831,11892,11945,11993,12058", "endColumns": "83,74,65,61,66,89,93,80,69,56,55,390,79,80,57,72,62,61,62,51,90,60,52,47,64,76", "endOffsets": "10094,10169,10235,10297,10364,10454,10548,10629,10699,10756,10812,11203,11283,11364,11422,11495,11558,11620,11683,11735,11826,11887,11940,11988,12053,12130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "576,577", "startColumns": "4,4", "startOffsets": "54271,54360", "endColumns": "88,94", "endOffsets": "54355,54450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "271,272,273,274,275,276,277,278,279", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "26962,27036,27101,27170,27242,27325,27402,27499,27592", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "27031,27096,27165,27237,27320,27397,27494,27587,27670"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "39,40,43,44,47,48,49,50,51,52,53,54,55,56,59,63,64,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1833,1944,2197,2303,2534,2625,2718,2816,2911,3011,3104,3197,3292,3383,3610,3955,4062,50673", "endColumns": "110,114,105,129,90,92,97,94,99,92,92,94,90,90,110,106,158,86", "endOffsets": "1939,2054,2298,2428,2620,2713,2811,2906,3006,3099,3192,3287,3378,3469,3716,4057,4216,50755"}}]}]}