// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogNotificationBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final Button cancelBtn;

  @NonNull
  public final RelativeLayout cancelView;

  @NonNull
  public final Button confirmChangeCapacityError;

  @NonNull
  public final Button exit;

  @NonNull
  public final TextView mainText;

  @NonNull
  public final RelativeLayout margin;

  @NonNull
  public final ShimmerFrameLayout nativeAd;

  @NonNull
  public final LinearLayout sdfgsdfg1;

  @NonNull
  public final LinearLayout sdg1;

  @NonNull
  public final RelativeLayout strelka;

  @NonNull
  public final TextView textBtn;

  @NonNull
  public final TextView textBtnCancel;

  @NonNull
  public final TextView upText;

  private DialogNotificationBinding(@NonNull RelativeLayout rootView, @NonNull Button cancelBtn,
      @NonNull RelativeLayout cancelView, @NonNull Button confirmChangeCapacityError,
      @NonNull Button exit, @NonNull TextView mainText, @NonNull RelativeLayout margin,
      @NonNull ShimmerFrameLayout nativeAd, @NonNull LinearLayout sdfgsdfg1,
      @NonNull LinearLayout sdg1, @NonNull RelativeLayout strelka, @NonNull TextView textBtn,
      @NonNull TextView textBtnCancel, @NonNull TextView upText) {
    this.rootView = rootView;
    this.cancelBtn = cancelBtn;
    this.cancelView = cancelView;
    this.confirmChangeCapacityError = confirmChangeCapacityError;
    this.exit = exit;
    this.mainText = mainText;
    this.margin = margin;
    this.nativeAd = nativeAd;
    this.sdfgsdfg1 = sdfgsdfg1;
    this.sdg1 = sdg1;
    this.strelka = strelka;
    this.textBtn = textBtn;
    this.textBtnCancel = textBtnCancel;
    this.upText = upText;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogNotificationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogNotificationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_notification, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogNotificationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cancel_btn;
      Button cancelBtn = ViewBindings.findChildViewById(rootView, id);
      if (cancelBtn == null) {
        break missingId;
      }

      id = R.id.cancel_view;
      RelativeLayout cancelView = ViewBindings.findChildViewById(rootView, id);
      if (cancelView == null) {
        break missingId;
      }

      id = R.id.confirm_change_capacity_error;
      Button confirmChangeCapacityError = ViewBindings.findChildViewById(rootView, id);
      if (confirmChangeCapacityError == null) {
        break missingId;
      }

      id = R.id.exit;
      Button exit = ViewBindings.findChildViewById(rootView, id);
      if (exit == null) {
        break missingId;
      }

      id = R.id.main_text;
      TextView mainText = ViewBindings.findChildViewById(rootView, id);
      if (mainText == null) {
        break missingId;
      }

      id = R.id.margin;
      RelativeLayout margin = ViewBindings.findChildViewById(rootView, id);
      if (margin == null) {
        break missingId;
      }

      id = R.id.nativeAd;
      ShimmerFrameLayout nativeAd = ViewBindings.findChildViewById(rootView, id);
      if (nativeAd == null) {
        break missingId;
      }

      id = R.id.sdfgsdfg1;
      LinearLayout sdfgsdfg1 = ViewBindings.findChildViewById(rootView, id);
      if (sdfgsdfg1 == null) {
        break missingId;
      }

      id = R.id.sdg1;
      LinearLayout sdg1 = ViewBindings.findChildViewById(rootView, id);
      if (sdg1 == null) {
        break missingId;
      }

      id = R.id.strelka;
      RelativeLayout strelka = ViewBindings.findChildViewById(rootView, id);
      if (strelka == null) {
        break missingId;
      }

      id = R.id.text_btn;
      TextView textBtn = ViewBindings.findChildViewById(rootView, id);
      if (textBtn == null) {
        break missingId;
      }

      id = R.id.text_btn_cancel;
      TextView textBtnCancel = ViewBindings.findChildViewById(rootView, id);
      if (textBtnCancel == null) {
        break missingId;
      }

      id = R.id.up_text;
      TextView upText = ViewBindings.findChildViewById(rootView, id);
      if (upText == null) {
        break missingId;
      }

      return new DialogNotificationBinding((RelativeLayout) rootView, cancelBtn, cancelView,
          confirmChangeCapacityError, exit, mainText, margin, nativeAd, sdfgsdfg1, sdg1, strelka,
          textBtn, textBtnCancel, upText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
