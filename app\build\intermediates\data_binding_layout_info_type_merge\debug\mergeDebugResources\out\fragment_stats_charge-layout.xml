<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_stats_charge" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_stats_charge.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView" rootNodeViewId="@+id/stats_charge_scroll_view"><Targets><Target id="@+id/stats_charge_scroll_view" tag="layout/fragment_stats_charge_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="6" startOffset="0" endLine="503" endOffset="39"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="13" startOffset="4" endLine="502" endOffset="18"/></Target><Target id="@+id/include_back_navigation" tag="binding_1" include="layout_back_navigation"><Expressions/><location startLine="20" startOffset="8" endLine="22" endOffset="53"/></Target><Target id="@+id/stats_charge_main_display_root" view="LinearLayout"><Expressions/><location startLine="25" startOffset="8" endLine="174" endOffset="22"/></Target><Target id="@+id/percent_layout" view="RelativeLayout"><Expressions/><location startLine="34" startOffset="12" endLine="72" endOffset="28"/></Target><Target id="@+id/percent_inner_layout" view="RelativeLayout"><Expressions/><location startLine="44" startOffset="16" endLine="71" endOffset="32"/></Target><Target id="@+id/tv_percentage" view="TextView"><Expressions/><location startLine="49" startOffset="20" endLine="56" endOffset="52"/></Target><Target id="@+id/charge_prog_bar_percent" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="57" startOffset="20" endLine="70" endOffset="49"/></Target><Target id="@+id/time_estimates_layout" view="LinearLayout"><Expressions/><location startLine="75" startOffset="12" endLine="173" endOffset="26"/></Target><Target id="@+id/full_charge_block" view="LinearLayout"><Expressions/><location startLine="87" startOffset="16" endLine="113" endOffset="30"/></Target><Target id="@+id/tv_time_to_full" view="TextView"><Expressions/><location startLine="105" startOffset="20" endLine="112" endOffset="52"/></Target><Target id="@+id/target_charge_block" view="LinearLayout"><Expressions/><location startLine="116" startOffset="16" endLine="143" endOffset="30"/></Target><Target id="@+id/tv_time_to_target" view="TextView"><Expressions/><location startLine="135" startOffset="20" endLine="142" endOffset="52"/></Target><Target id="@+id/charging_status_block" view="LinearLayout"><Expressions/><location startLine="146" startOffset="16" endLine="172" endOffset="30"/></Target><Target id="@+id/tv_charging_status" view="TextView"><Expressions/><location startLine="164" startOffset="20" endLine="171" endOffset="52"/></Target><Target id="@+id/nativeAd" view="com.facebook.shimmer.ShimmerFrameLayout"><Expressions/><location startLine="176" startOffset="8" endLine="180" endOffset="45"/></Target><Target id="@+id/target_percentage_root" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="183" startOffset="8" endLine="276" endOffset="59"/></Target><Target id="@+id/target_percentage_title" view="TextView"><Expressions/><location startLine="198" startOffset="12" endLine="207" endOffset="59"/></Target><Target id="@+id/divider_target" view="View"><Expressions/><location startLine="209" startOffset="12" endLine="217" endOffset="84"/></Target><Target id="@+id/battery_alarm_btn" view="TextView"><Expressions/><location startLine="220" startOffset="12" endLine="238" endOffset="75"/></Target><Target id="@+id/target_percent_label" view="TextView"><Expressions/><location startLine="241" startOffset="12" endLine="250" endOffset="78"/></Target><Target id="@+id/tv_target_percentage" view="TextView"><Expressions/><location startLine="252" startOffset="12" endLine="263" endOffset="78"/></Target><Target id="@+id/seekbar_target" view="SeekBar"><Expressions/><location startLine="266" startOffset="12" endLine="275" endOffset="81"/></Target><Target id="@+id/charge_current_session_root" view="LinearLayout"><Expressions/><location startLine="279" startOffset="8" endLine="495" endOffset="22"/></Target><Target id="@+id/tv_session_start_time" view="TextView"><Expressions/><location startLine="325" startOffset="16" endLine="331" endOffset="45"/></Target><Target id="@+id/tv_session_duration" view="TextView"><Expressions/><location startLine="347" startOffset="16" endLine="353" endOffset="45"/></Target><Target id="@+id/tv_session_percentage_charged" view="TextView"><Expressions/><location startLine="369" startOffset="16" endLine="375" endOffset="45"/></Target><Target id="@+id/tv_session_total_charge_mah" view="TextView"><Expressions/><location startLine="391" startOffset="16" endLine="397" endOffset="45"/></Target><Target id="@+id/tv_current" view="TextView"><Expressions/><location startLine="413" startOffset="16" endLine="419" endOffset="45"/></Target><Target id="@+id/tv_voltage" view="TextView"><Expressions/><location startLine="435" startOffset="16" endLine="441" endOffset="45"/></Target><Target id="@+id/tv_temperature" view="TextView"><Expressions/><location startLine="457" startOffset="16" endLine="463" endOffset="45"/></Target><Target id="@+id/tv_power" view="TextView"><Expressions/><location startLine="478" startOffset="16" endLine="484" endOffset="45"/></Target><Target id="@+id/btn_reset_session" view="Button"><Expressions/><location startLine="488" startOffset="12" endLine="494" endOffset="45"/></Target></Targets></Layout>