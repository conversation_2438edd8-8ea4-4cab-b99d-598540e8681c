<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_app_power_consumption" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\dialog_app_power_consumption.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_app_power_consumption_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="207" endOffset="14"/></Target><Target id="@+id/tv_dialog_title" view="TextView"><Expressions/><location startLine="17" startOffset="8" endLine="25" endOffset="45"/></Target><Target id="@+id/iv_close_dialog" view="ImageView"><Expressions/><location startLine="27" startOffset="8" endLine="33" endOffset="56"/></Target><Target id="@+id/tv_session_duration" view="TextView"><Expressions/><location startLine="68" startOffset="12" endLine="75" endOffset="49"/></Target><Target id="@+id/tv_total_consumption" view="TextView"><Expressions/><location startLine="92" startOffset="12" endLine="99" endOffset="49"/></Target><Target id="@+id/progress_loading" view="ProgressBar"><Expressions/><location startLine="106" startOffset="4" endLine="112" endOffset="35"/></Target><Target id="@+id/tv_error_message" view="TextView"><Expressions/><location startLine="114" startOffset="4" endLine="123" endOffset="35"/></Target><Target id="@+id/tv_no_data_message" view="TextView"><Expressions/><location startLine="125" startOffset="4" endLine="134" endOffset="35"/></Target><Target id="@+id/rv_apps_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="137" startOffset="4" endLine="144" endOffset="35"/></Target><Target id="@+id/ll_permission_request" view="LinearLayout"><Expressions/><location startLine="147" startOffset="4" endLine="205" endOffset="18"/></Target><Target id="@+id/tv_permission_status" view="TextView"><Expressions/><location startLine="174" startOffset="8" endLine="183" endOffset="39"/></Target><Target id="@+id/btn_grant_permission" view="Button"><Expressions/><location startLine="185" startOffset="8" endLine="192" endOffset="47"/></Target><Target id="@+id/tv_skip_permission" view="TextView"><Expressions/><location startLine="194" startOffset="8" endLine="203" endOffset="65"/></Target></Targets></Layout>