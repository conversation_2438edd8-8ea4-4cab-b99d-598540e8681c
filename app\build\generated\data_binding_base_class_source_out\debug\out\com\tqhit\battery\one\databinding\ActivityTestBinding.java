// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentContainerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTestBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button checkServiceButton;

  @NonNull
  public final FragmentContainerView fragmentContainer;

  @NonNull
  public final TextView serviceStatusText;

  @NonNull
  public final Button startServiceButton;

  @NonNull
  public final Button stopServiceButton;

  @NonNull
  public final LinearLayout testControlsLayout;

  private ActivityTestBinding(@NonNull LinearLayout rootView, @NonNull Button checkServiceButton,
      @NonNull FragmentContainerView fragmentContainer, @NonNull TextView serviceStatusText,
      @NonNull Button startServiceButton, @NonNull Button stopServiceButton,
      @NonNull LinearLayout testControlsLayout) {
    this.rootView = rootView;
    this.checkServiceButton = checkServiceButton;
    this.fragmentContainer = fragmentContainer;
    this.serviceStatusText = serviceStatusText;
    this.startServiceButton = startServiceButton;
    this.stopServiceButton = stopServiceButton;
    this.testControlsLayout = testControlsLayout;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTestBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_test, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.check_service_button;
      Button checkServiceButton = ViewBindings.findChildViewById(rootView, id);
      if (checkServiceButton == null) {
        break missingId;
      }

      id = R.id.fragment_container;
      FragmentContainerView fragmentContainer = ViewBindings.findChildViewById(rootView, id);
      if (fragmentContainer == null) {
        break missingId;
      }

      id = R.id.service_status_text;
      TextView serviceStatusText = ViewBindings.findChildViewById(rootView, id);
      if (serviceStatusText == null) {
        break missingId;
      }

      id = R.id.start_service_button;
      Button startServiceButton = ViewBindings.findChildViewById(rootView, id);
      if (startServiceButton == null) {
        break missingId;
      }

      id = R.id.stop_service_button;
      Button stopServiceButton = ViewBindings.findChildViewById(rootView, id);
      if (stopServiceButton == null) {
        break missingId;
      }

      id = R.id.test_controls_layout;
      LinearLayout testControlsLayout = ViewBindings.findChildViewById(rootView, id);
      if (testControlsLayout == null) {
        break missingId;
      }

      return new ActivityTestBinding((LinearLayout) rootView, checkServiceButton, fragmentContainer,
          serviceStatusText, startServiceButton, stopServiceButton, testControlsLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
