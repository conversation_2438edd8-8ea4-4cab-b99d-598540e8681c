package com.tqhit.battery.one.initialization

import android.content.Context
import android.util.Log
import com.tqhit.battery.one.BatteryApplication
import com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.delay
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Helper class for coordinating service initialization during app startup.
 * Provides status checking and initialization coordination for critical services.
 * 
 * Following clean architecture principles:
 * - Single Responsibility: Only handles service initialization coordination
 * - Dependency Inversion: Depends on service abstractions
 */
@Singleton
class ServiceInitializationHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val coreBatteryServiceHelper: CoreBatteryServiceHelper
) {
    
    companion object {
        private const val TAG = "ServiceInitHelper"
        private const val SERVICE_CHECK_INTERVAL_MS = 100L
        private const val MAX_SERVICE_WAIT_TIME_MS = 2000L
    }
    
    /**
     * Initialize and verify critical battery services
     */
    suspend fun initializeBatteryServices(): Boolean {
        Log.d(TAG, "STARTUP_TIMING: Starting battery services initialization")
        
        return try {
            // Get BatteryApplication instance
            val batteryApp = context.applicationContext as? BatteryApplication
            if (batteryApp == null) {
                Log.e(TAG, "Could not get BatteryApplication instance")
                return false
            }
            
            // Services are already started in BatteryApplication.initializeAsyncComponents()
            // We need to verify they are running properly
            
            // Check CoreBatteryStatsService status
            val coreServiceReady = waitForCoreServiceReady()
            if (coreServiceReady) {
                Log.d(TAG, "STARTUP_TIMING: CoreBatteryStatsService is ready")
            } else {
                Log.w(TAG, "STARTUP_TIMING: CoreBatteryStatsService not ready within timeout")
            }
            
            // Additional service checks can be added here
            
            Log.d(TAG, "STARTUP_TIMING: Battery services initialization completed")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "STARTUP_TIMING: Error during battery services initialization", e)
            false
        }
    }
    
    /**
     * Wait for CoreBatteryStatsService to be ready
     */
    private suspend fun waitForCoreServiceReady(): Boolean {
        var waitTime = 0L
        
        while (waitTime < MAX_SERVICE_WAIT_TIME_MS) {
            try {
                val serviceStatus = coreBatteryServiceHelper.getServiceStatus()
                Log.d(TAG, "CoreBatteryStatsService status: $serviceStatus")
                
                // Check if service is running (you may need to adjust this based on actual status values)
                if (serviceStatus.contains("running") || serviceStatus.contains("active") ||
                    serviceStatus.contains("Running") || serviceStatus.contains("Active")) {
                    return true
                }
                
            } catch (e: Exception) {
                Log.w(TAG, "Error checking service status: ${e.message}")
            }
            
            delay(SERVICE_CHECK_INTERVAL_MS)
            waitTime += SERVICE_CHECK_INTERVAL_MS
        }
        
        return false
    }
    
    /**
     * Initialize charging overlay service
     */
    suspend fun initializeChargingOverlayService(): Boolean {
        Log.d(TAG, "STARTUP_TIMING: Initializing charging overlay service")
        
        return try {
            // Charging overlay service is already started in BatteryApplication
            // Just verify it's ready
            delay(200) // Allow service to initialize
            
            Log.d(TAG, "STARTUP_TIMING: Charging overlay service ready")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "STARTUP_TIMING: Error initializing charging overlay service", e)
            false
        }
    }
    
    /**
     * Get overall service initialization status
     */
    fun getServiceInitializationStatus(): String {
        return try {
            val coreServiceStatus = coreBatteryServiceHelper.getServiceStatus()
            "Core: $coreServiceStatus"
        } catch (e: Exception) {
            "Error: ${e.message}"
        }
    }
}
