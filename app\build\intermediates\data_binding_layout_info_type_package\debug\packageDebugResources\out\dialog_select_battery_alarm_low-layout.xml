<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_select_battery_alarm_low" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\dialog_select_battery_alarm_low.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/dialog_select_battery_alarm_low_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="471" endOffset="16"/></Target><Target id="@+id/strelka" view="RelativeLayout"><Expressions/><location startLine="26" startOffset="16" endLine="58" endOffset="32"/></Target><Target id="@+id/exit" view="Button"><Expressions/><location startLine="33" startOffset="20" endLine="44" endOffset="74"/></Target><Target id="@+id/textView20" view="TextView"><Expressions/><location startLine="59" startOffset="16" endLine="76" endOffset="59"/></Target><Target id="@+id/relativ33" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="78" startOffset="12" endLine="227" endOffset="63"/></Target><Target id="@+id/charge_l" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="84" startOffset="16" endLine="130" endOffset="67"/></Target><Target id="@+id/p192" view="TextView"><Expressions/><location startLine="95" startOffset="20" endLine="114" endOffset="66"/></Target><Target id="@+id/switch_low_alarm" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="115" startOffset="20" endLine="129" endOffset="59"/></Target><Target id="@+id/relat223" view="LinearLayout"><Expressions/><location startLine="131" startOffset="16" endLine="155" endOffset="30"/></Target><Target id="@+id/text_buy_access2" view="TextView"><Expressions/><location startLine="143" startOffset="20" endLine="154" endOffset="55"/></Target><Target id="@+id/linearLayout2" view="LinearLayout"><Expressions/><location startLine="156" startOffset="16" endLine="226" endOffset="30"/></Target><Target id="@+id/progressbar_low_alarm" view="ProgressBar"><Expressions/><location startLine="173" startOffset="24" endLine="189" endOffset="77"/></Target><Target id="@+id/seekBar_low_alarm" view="SeekBar"><Expressions/><location startLine="190" startOffset="24" endLine="205" endOffset="70"/></Target><Target id="@+id/low_alarm_percent" view="TextView"><Expressions/><location startLine="218" startOffset="24" endLine="224" endOffset="47"/></Target><Target id="@+id/p6" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="228" startOffset="12" endLine="272" endOffset="63"/></Target><Target id="@+id/p_112" view="TextView"><Expressions/><location startLine="237" startOffset="16" endLine="256" endOffset="62"/></Target><Target id="@+id/switch_vibration" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="257" startOffset="16" endLine="271" endOffset="55"/></Target><Target id="@+id/p5" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="273" startOffset="12" endLine="328" endOffset="63"/></Target><Target id="@+id/dont_disturb_from_layout" view="LinearLayout"><Expressions/><location startLine="282" startOffset="16" endLine="292" endOffset="62"/></Target><Target id="@+id/p_11" view="TextView"><Expressions/><location startLine="293" startOffset="16" endLine="312" endOffset="62"/></Target><Target id="@+id/switch_dont_disturb" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="313" startOffset="16" endLine="327" endOffset="55"/></Target><Target id="@+id/dont_disturb_until_layout" view="LinearLayout"><Expressions/><location startLine="329" startOffset="12" endLine="430" endOffset="26"/></Target><Target id="@+id/dont_disturb_from" view="Button"><Expressions/><location startLine="344" startOffset="20" endLine="353" endOffset="74"/></Target><Target id="@+id/p_12er" view="TextView"><Expressions/><location startLine="354" startOffset="20" endLine="370" endOffset="66"/></Target><Target id="@+id/p_12er1" view="TextView"><Expressions/><location startLine="371" startOffset="20" endLine="386" endOffset="66"/></Target><Target id="@+id/dont_disturb_until" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="388" startOffset="16" endLine="429" endOffset="67"/></Target><Target id="@+id/p_1221" view="TextView"><Expressions/><location startLine="397" startOffset="20" endLine="413" endOffset="66"/></Target><Target id="@+id/p_12e2" view="TextView"><Expressions/><location startLine="414" startOffset="20" endLine="428" endOffset="66"/></Target><Target id="@+id/button_charge_alarm" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="431" startOffset="12" endLine="468" endOffset="63"/></Target><Target id="@+id/battery_4" view="Button"><Expressions/><location startLine="439" startOffset="16" endLine="450" endOffset="70"/></Target></Targets></Layout>