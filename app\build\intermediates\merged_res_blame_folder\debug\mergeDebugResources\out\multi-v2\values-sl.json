{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "61,62,63,64,65,66,67,312", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4515,4612,4714,4812,4916,5019,5121,27404", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "4607,4709,4807,4911,5014,5116,5233,27500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,581,684,827,953,1063,1163,1317,1420,1583,1709,1857,2005,2071,2129", "endColumns": "101,160,124,102,142,125,109,99,153,102,162,125,147,147,65,57,79", "endOffsets": "294,455,580,683,826,952,1062,1162,1316,1419,1582,1708,1856,2004,2070,2128,2208"}, "to": {"startLines": "73,74,75,76,77,78,79,80,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5730,5836,6001,6130,6237,6384,6514,6628,6872,7030,7137,7304,7434,7586,7738,7808,7870", "endColumns": "105,164,128,106,146,129,113,103,157,106,166,129,151,151,69,61,83", "endOffsets": "5831,5996,6125,6232,6379,6509,6623,6727,7025,7132,7299,7429,7581,7733,7803,7865,7949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,313,431,560,670,766,879,1019,1145,1288,1373,1472,1565,1662,1779,1901,2005,2142,2276,2407,2591,2718,2841,2966,3088,3182,3280,3400,3524,3624,3733,3839,3982,4129,4238,4340,4424,4519,4615,4723,4811,4897,5000,5082,5165,5260,5360,5451,5548,5636,5740,5837,5939,6081,6163,6269", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,107,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "179,308,426,555,665,761,874,1014,1140,1283,1368,1467,1560,1657,1774,1896,2000,2137,2271,2402,2586,2713,2836,2961,3083,3177,3275,3395,3519,3619,3728,3834,3977,4124,4233,4335,4419,4514,4610,4718,4806,4892,4995,5077,5160,5255,5355,5446,5543,5631,5735,5832,5934,6076,6158,6264,6363"}, "to": {"startLines": "159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13331,13460,13589,13707,13836,13946,14042,14155,14295,14421,14564,14649,14748,14841,14938,15055,15177,15281,15418,15552,15683,15867,15994,16117,16242,16364,16458,16556,16676,16800,16900,17009,17115,17258,17405,17514,17616,17700,17795,17891,17999,18087,18173,18276,18358,18441,18536,18636,18727,18824,18912,19016,19113,19215,19357,19439,19545", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,107,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "13455,13584,13702,13831,13941,14037,14150,14290,14416,14559,14644,14743,14836,14933,15050,15172,15276,15413,15547,15678,15862,15989,16112,16237,16359,16453,16551,16671,16795,16895,17004,17110,17253,17400,17509,17611,17695,17790,17886,17994,18082,18168,18271,18353,18436,18531,18631,18722,18819,18907,19011,19108,19210,19352,19434,19540,19639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,120", "endOffsets": "158,279"}, "to": {"startLines": "277,278", "startColumns": "4,4", "startOffsets": "24425,24533", "endColumns": "107,120", "endOffsets": "24528,24649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,101", "endOffsets": "156,260,372,474"}, "to": {"startLines": "92,152,153,154", "startColumns": "4,4,4,4", "startOffsets": "8025,12710,12814,12926", "endColumns": "105,103,111,101", "endOffsets": "8126,12809,12921,13023"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,304", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1320,1432,1534,1642,1729,1832,1951,2032,2110,2202,2296,2391,2485,2580,2674,2770,2870,2962,3054,3138,3246,3354,3454,3567,3675,3780,3960,26772", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "1427,1529,1637,1724,1827,1946,2027,2105,2197,2291,2386,2480,2575,2669,2765,2865,2957,3049,3133,3241,3349,3449,3562,3670,3775,3955,4055,26851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-sl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,287,349,413,480,589,652,787,900,1055,1109,1166,1281,1377,1423,1503,1538,1572,1632,1710,1754", "endColumns": "40,46,61,63,66,108,62,134,112,154,53,56,114,95,45,79,34,33,59,77,43,55", "endOffsets": "239,286,348,412,479,588,651,786,899,1054,1108,1165,1280,1376,1422,1502,1537,1571,1631,1709,1753,1809"}, "to": {"startLines": "274,275,276,282,283,284,285,286,287,288,289,290,291,292,297,298,299,300,301,302,303,321", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24263,24308,24359,24909,24977,25048,25161,25228,25367,25484,25643,25701,25762,25881,26367,26417,26501,26540,26578,26642,26724,28296", "endColumns": "44,50,65,67,70,112,66,138,116,158,57,60,118,99,49,83,38,37,63,81,47,59", "endOffsets": "24303,24354,24420,24972,25043,25156,25223,25362,25479,25638,25696,25757,25876,25976,26412,26496,26535,26573,26637,26719,26767,28351"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,281,377,475,560,637,724,816,898,980,1066,1138,1215,1295,1373,1451,1521", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,76,79,77,77,69,120", "endOffsets": "195,276,372,470,555,632,719,811,893,975,1061,1133,1210,1290,1368,1446,1516,1637"}, "to": {"startLines": "71,72,93,94,95,156,157,279,280,295,296,307,309,310,311,314,315,316", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5554,5649,8131,8227,8325,13104,13181,24654,24746,26199,26281,27019,27169,27246,27326,27674,27752,27822", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,76,79,77,77,69,120", "endOffsets": "5644,5725,8222,8320,8405,13176,13263,24741,24823,26276,26362,27086,27241,27321,27399,27747,27817,27938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,183,247,311,386,467,566,657", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "118,178,242,306,381,462,561,652,726"}, "to": {"startLines": "122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10639,10707,10767,10831,10895,10970,11051,11150,11241", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "10702,10762,10826,10890,10965,11046,11145,11236,11310"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,90", "endOffsets": "140,231"}, "to": {"startLines": "317,318", "startColumns": "4,4", "startOffsets": "27943,28033", "endColumns": "89,90", "endOffsets": "28028,28119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,262,343,484,653,741", "endColumns": "70,85,80,140,168,87,83", "endOffsets": "171,257,338,479,648,736,820"}, "to": {"startLines": "91,150,281,294,313,319,320", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7954,12530,24828,26058,27505,28124,28212", "endColumns": "70,85,80,140,168,87,83", "endOffsets": "8020,12611,24904,26194,27669,28207,28291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-sl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "81", "startColumns": "4", "startOffsets": "6732", "endColumns": "139", "endOffsets": "6867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-sl\\values-sl.xml", "from": {"startLines": "58,59", "startColumns": "4,4", "startOffsets": "3740,3809", "endColumns": "68,73", "endOffsets": "3804,3878"}, "to": {"startLines": "142,143", "startColumns": "4,4", "startOffsets": "11998,12067", "endColumns": "68,73", "endOffsets": "12062,12136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,645,958,1045,1133,1216,1314,1415,1498,1563,1660,1754,1825,1895,1959,2027,2149,2277,2399,2476,2556,2629,2709,2816,2924,2992,3057,3110,3168,3216,3277,3347,3416,3479,3544,3607,3664,3740,3792,3855,3932,4009,4063", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,121,127,121,76,79,72,79,106,107,67,64,52,57,47,60,69,68,62,64,62,56,75,51,62,76,76,53,65", "endOffsets": "318,640,953,1040,1128,1211,1309,1410,1493,1558,1655,1749,1820,1890,1954,2022,2144,2272,2394,2471,2551,2624,2704,2811,2919,2987,3052,3105,3163,3211,3272,3342,3411,3474,3539,3602,3659,3735,3787,3850,3927,4004,4058,4124"}, "to": {"startLines": "2,11,17,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,131,132,133,134,135,136,137,138,139,140,141,144,145,146,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,418,740,8540,8627,8715,8798,8896,8997,9080,9145,9242,9336,9407,9477,9541,9609,9731,9859,9981,10058,10138,10211,10291,10398,10506,10574,11315,11368,11426,11474,11535,11605,11674,11737,11802,11865,11922,12141,12193,12256,12333,12410,12464", "endLines": "10,16,22,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,131,132,133,134,135,136,137,138,139,140,141,144,145,146,147,148,149", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,121,127,121,76,79,72,79,106,107,67,64,52,57,47,60,69,68,62,64,62,56,75,51,62,76,76,53,65", "endOffsets": "413,735,1048,8622,8710,8793,8891,8992,9075,9140,9237,9331,9402,9472,9536,9604,9726,9854,9976,10053,10133,10206,10286,10393,10501,10569,10634,11363,11421,11469,11530,11600,11669,11732,11797,11860,11917,11993,12188,12251,12328,12405,12459,12525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,367,456,545,633,731,822,928,1054,1138,1202,1268,1362,1438,1501,1613,1673,1738,1792,1862,1922,1978,2090,2147,2209,2265,2338,2472,2557,2634,2723,2804,2889,3032,3116,3199,3333,3422,3499,3555,3610,3676,3749,3826,3897,3976,4050,4126,4201,4274,4379,4467,4540,4630,4721,4793,4867,4958,5010,5092,5159,5243,5330,5392,5456,5519,5588,5691,5799,5897,6001,6061,6120,6197,6284,6360", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "362,451,540,628,726,817,923,1049,1133,1197,1263,1357,1433,1496,1608,1668,1733,1787,1857,1917,1973,2085,2142,2204,2260,2333,2467,2552,2629,2718,2799,2884,3027,3111,3194,3328,3417,3494,3550,3605,3671,3744,3821,3892,3971,4045,4121,4196,4269,4374,4462,4535,4625,4716,4788,4862,4953,5005,5087,5154,5238,5325,5387,5451,5514,5583,5686,5794,5892,5996,6056,6115,6192,6279,6355,6433"}, "to": {"startLines": "23,56,57,58,59,60,68,69,70,96,97,151,155,158,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,293,305,306,308", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1053,4060,4149,4238,4326,4424,5238,5344,5470,8410,8474,12616,13028,13268,19644,19756,19816,19881,19935,20005,20065,20121,20233,20290,20352,20408,20481,20615,20700,20777,20866,20947,21032,21175,21259,21342,21476,21565,21642,21698,21753,21819,21892,21969,22040,22119,22193,22269,22344,22417,22522,22610,22683,22773,22864,22936,23010,23101,23153,23235,23302,23386,23473,23535,23599,23662,23731,23834,23942,24040,24144,24204,25981,26856,26943,27091", "endLines": "28,56,57,58,59,60,68,69,70,96,97,151,155,158,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,293,305,306,308", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "1315,4144,4233,4321,4419,4510,5339,5465,5549,8469,8535,12705,13099,13326,19751,19811,19876,19930,20000,20060,20116,20228,20285,20347,20403,20476,20610,20695,20772,20861,20942,21027,21170,21254,21337,21471,21560,21637,21693,21748,21814,21887,21964,22035,22114,22188,22264,22339,22412,22517,22605,22678,22768,22859,22931,23005,23096,23148,23230,23297,23381,23468,23530,23594,23657,23726,23829,23937,24035,24139,24199,24258,26053,26938,27014,27164"}}]}]}