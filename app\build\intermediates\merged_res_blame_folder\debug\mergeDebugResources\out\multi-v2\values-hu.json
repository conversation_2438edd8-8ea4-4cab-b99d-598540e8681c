{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,480,614,719,883,1017,1135,1241,1407,1511,1692,1825,1993,2161,2228,2292", "endColumns": "106,179,133,104,163,133,117,105,165,103,180,132,167,167,66,63,83", "endOffsets": "299,479,613,718,882,1016,1134,1240,1406,1510,1691,1824,1992,2160,2227,2291,2375"}, "to": {"startLines": "144,145,146,147,148,149,150,151,153,154,155,156,157,158,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11746,11857,12041,12179,12288,12456,12594,12716,13003,13173,13281,13466,13603,13775,13947,14018,14086", "endColumns": "110,183,137,108,167,137,121,109,169,107,184,136,171,171,70,67,87", "endOffsets": "11852,12036,12174,12283,12451,12589,12711,12821,13168,13276,13461,13598,13770,13942,14013,14081,14169"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-hu\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,265,30,31,32,33,34,35,36,241,251,254,256,37,2,264,244,262,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,239,57,58,243,59,60,61,62,63,64,65,66,67,68,249,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,259,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,260,146,147,268,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,242,168,169,170,171,172,173,174,175,176,177,178,247,246,245,253,257,179,180,181,182,183,184,255,185,186,187,240,188,261,269,189,190,191,192,193,194,195,196,248,197,198,258,199,200,201,202,203,252,204,205,206,207,208,209,270,210,211,212,213,214,215,216,263,217,218,219,220,221,222,223,224,225,226,250,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "127,196,244,292,341,389,440,486,535,589,656,705,753,803,851,20801,899,985,1040,1088,1135,1189,1267,1338,1419,1499,1573,1635,23277,1697,1739,1906,1962,2047,2090,2152,21060,21879,22282,22411,2245,57,23229,21259,23108,2289,2335,2381,2465,2522,2596,2985,3379,11443,11513,10892,10809,11578,3420,3495,3570,3785,4047,4122,4181,4222,4282,4362,4410,20949,4479,4551,21183,4593,4676,4719,4793,4942,5009,5094,5167,5240,5327,21758,5393,5449,5505,5567,5628,5677,5750,5837,5887,6177,6235,6298,6839,7395,7467,7516,7664,7733,8005,8055,8505,8570,8704,8849,8993,9059,9103,9150,9221,9310,9386,9436,9540,9644,9698,9763,9829,10066,10282,10449,10606,10689,11665,11738,22919,11791,11831,11919,11986,12044,12251,12301,12381,12445,12507,12660,12721,12871,13094,13135,13195,13233,13271,13307,13343,13424,13468,13527,13577,13644,22979,13714,13783,23396,13843,13887,13930,14007,14402,14464,14542,14706,14787,14860,14897,14932,14968,15006,15058,15126,15193,15248,15329,15479,21108,15515,15575,15627,15723,15796,15873,15988,16104,16176,16214,16294,21580,21385,21303,22181,22683,16536,16576,16626,16677,16743,16827,22333,16878,16964,17013,21015,17061,23041,23453,17189,17275,17363,17419,17490,17688,17761,17825,21686,17892,17953,22728,18012,18089,18187,18269,18319,22106,18399,18481,18525,18583,18628,18749,23507,19102,19178,19224,19283,19357,19418,19471,23171,19511,19580,19787,19872,19933,20002,20056,20135,20207,20241,21823,20299,20364,20412,20449,20502,20555,20619,20681,20719,20755", "endColumns": "67,46,46,47,46,49,44,47,52,65,47,46,48,46,46,87,84,53,46,45,52,76,69,79,78,72,60,60,70,40,165,54,83,41,60,91,46,225,49,270,42,68,46,42,61,44,44,82,55,72,387,392,39,68,63,549,81,83,73,73,213,260,73,57,39,58,78,46,67,64,70,40,74,81,41,72,147,65,83,71,71,85,64,63,54,54,60,59,47,71,85,48,288,56,61,539,554,70,47,146,67,270,48,448,63,132,143,142,64,42,45,69,87,74,48,102,102,52,63,64,235,214,165,155,81,73,71,51,58,38,86,65,56,205,48,78,62,60,151,59,148,221,39,58,36,36,34,34,79,42,57,48,65,68,60,67,58,55,42,41,75,393,60,76,162,79,71,35,33,34,36,50,66,65,53,79,148,34,73,58,50,94,71,75,113,114,70,36,78,240,104,193,80,99,43,38,48,49,64,82,49,76,84,47,46,43,126,65,52,84,86,54,69,196,71,62,65,70,59,57,189,75,96,80,48,78,73,80,42,56,43,119,351,82,74,44,57,72,59,51,38,56,67,205,83,59,67,52,77,70,32,56,54,63,46,35,51,51,62,60,36,34,44", "endOffsets": "190,238,286,335,383,434,480,529,583,650,699,747,797,845,893,20884,979,1034,1082,1129,1183,1261,1332,1413,1493,1567,1629,1691,23343,1733,1900,1956,2041,2084,2146,2239,21102,22100,22327,22677,2283,121,23271,21297,23165,2329,2375,2459,2516,2590,2979,3373,3414,11507,11572,11437,10886,11657,3489,3564,3779,4041,4116,4175,4216,4276,4356,4404,4473,21009,4545,4587,21253,4670,4713,4787,4936,5003,5088,5161,5234,5321,5387,21817,5443,5499,5561,5622,5671,5744,5831,5881,6171,6229,6292,6833,7389,7461,7510,7658,7727,7999,8049,8499,8564,8698,8843,8987,9053,9097,9144,9215,9304,9380,9430,9534,9638,9692,9757,9823,10060,10276,10443,10600,10683,10758,11732,11785,22973,11825,11913,11980,12038,12245,12295,12375,12439,12501,12654,12715,12865,13088,13129,13189,13227,13265,13301,13337,13418,13462,13521,13571,13638,13708,23035,13777,13837,23447,13881,13924,14001,14396,14458,14536,14700,14781,14854,14891,14926,14962,15000,15052,15120,15187,15242,15323,15473,15509,21177,15569,15621,15717,15790,15867,15982,16098,16170,16208,16288,16530,21680,21574,21379,22276,22722,16570,16620,16671,16737,16821,16872,22405,16958,17007,17055,21054,17183,23102,23501,17269,17357,17413,17484,17682,17755,17819,17886,21752,17947,18006,22913,18083,18181,18263,18313,18393,22175,18475,18519,18577,18622,18743,19096,23585,19172,19218,19277,19351,19412,19465,19505,23223,19574,19781,19866,19927,19996,20050,20129,20201,20235,20293,21873,20358,20406,20443,20496,20549,20613,20675,20713,20749,20795"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,45,46,57,58,60,61,62,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,111,112,113,114,115,123,124,125,128,129,130,131,132,133,134,135,136,137,138,139,140,162,163,164,165,168,169,170,171,172,173,174,175,176,177,178,179,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,205,206,207,208,209,211,266,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,292,293,295,297,298,299,301,302,303,304,305,306,307,308,309,310,368,369,370,371,385,386,432,439,440,441,442,443,446,447,448,449,450,451,452,453,462,463,464,465,466,467,468,470,471,472,473,474,475,476,477,478,479,481,482,483,484,485,486,489,490,491,492,493,494,502,503,507,509,510,511,512,513,514,515,516,518,519,520,521,522,523,524,525,529,530,532,535,537,538,539,540,541,544,545,546,547,548,549,552,553,554,555,557,558,559,560,561,562,563,564", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "950,1018,1065,1112,1160,1207,1257,1302,1350,1403,1469,1517,1564,1613,1660,1707,1995,2080,2372,2419,3403,3456,3643,3713,3793,4173,4246,4307,4368,4439,4480,4646,4701,4785,4827,4888,4980,5027,5253,5303,5574,5617,5686,5733,5776,5838,5883,5928,6011,6067,6140,6528,6921,6961,7030,7094,7644,7726,7810,7884,7958,8172,8433,8507,8565,8605,9068,9147,9194,9262,9327,10120,10161,10236,10537,10579,10652,10800,10866,10950,11022,11094,11180,11245,11309,11364,11419,14174,14234,14282,14354,14607,14656,14945,15002,15064,15604,16159,16230,16278,16425,16493,16764,17009,17458,17522,17655,17799,17942,18007,18050,18096,18166,18254,18329,18378,18481,18584,18637,18701,18766,19002,19217,19383,19539,19708,19782,19854,19906,19965,20067,24220,24705,24762,24968,25017,25096,25159,25220,25372,25432,25581,25803,25843,25902,25939,25976,26011,26046,26126,26169,26227,26351,26417,26568,26725,26793,26852,26967,27010,27052,27128,27522,27583,27660,27823,27903,27975,34341,34375,34410,34447,35461,35528,39302,39831,39911,40060,40095,40169,40397,40448,40543,40615,40691,40805,40920,40991,41826,41905,42146,42251,42445,42526,42626,42755,42794,42843,42893,42958,43041,43091,43168,43253,43301,43485,43529,43656,43722,43775,43860,44118,44173,44243,44440,44512,44575,45054,45125,45431,45563,45753,45829,45926,46007,46056,46135,46209,46363,46406,46463,46507,46627,46979,47062,47137,47424,47482,47724,47929,48102,48141,48198,48266,48472,48742,48802,48870,48923,49001,49072,49260,49317,49372,49436,49543,49579,49631,49683,49746,49807,49844,49879", "endColumns": "67,46,46,47,46,49,44,47,52,65,47,46,48,46,46,87,84,53,46,45,52,76,69,79,78,72,60,60,70,40,165,54,83,41,60,91,46,225,49,270,42,68,46,42,61,44,44,82,55,72,387,392,39,68,63,549,81,83,73,73,213,260,73,57,39,58,78,46,67,64,70,40,74,81,41,72,147,65,83,71,71,85,64,63,54,54,60,59,47,71,85,48,288,56,61,539,554,70,47,146,67,270,48,448,63,132,143,142,64,42,45,69,87,74,48,102,102,52,63,64,235,214,165,155,81,73,71,51,58,38,86,65,56,205,48,78,62,60,151,59,148,221,39,58,36,36,34,34,79,42,57,48,65,68,60,67,58,55,42,41,75,393,60,76,162,79,71,35,33,34,36,50,66,65,53,79,148,34,73,58,50,94,71,75,113,114,70,36,78,240,104,193,80,99,43,38,48,49,64,82,49,76,84,47,46,43,126,65,52,84,86,54,69,196,71,62,65,70,59,57,189,75,96,80,48,78,73,80,42,56,43,119,351,82,74,44,57,72,59,51,38,56,67,205,83,59,67,52,77,70,32,56,54,63,46,35,51,51,62,60,36,34,44", "endOffsets": "1013,1060,1107,1155,1202,1252,1297,1345,1398,1464,1512,1559,1608,1655,1702,1790,2075,2129,2414,2460,3451,3528,3708,3788,3867,4241,4302,4363,4434,4475,4641,4696,4780,4822,4883,4975,5022,5248,5298,5569,5612,5681,5728,5771,5833,5878,5923,6006,6062,6135,6523,6916,6956,7025,7089,7639,7721,7805,7879,7953,8167,8428,8502,8560,8600,8659,9142,9189,9257,9322,9393,10156,10231,10313,10574,10647,10795,10861,10945,11017,11089,11175,11240,11304,11359,11414,11475,14229,14277,14349,14435,14651,14940,14997,15059,15599,16154,16225,16273,16420,16488,16759,16808,17453,17517,17650,17794,17937,18002,18045,18091,18161,18249,18324,18373,18476,18579,18632,18696,18761,18997,19212,19378,19534,19616,19777,19849,19901,19960,19999,20149,24281,24757,24963,25012,25091,25154,25215,25367,25427,25576,25798,25838,25897,25934,25971,26006,26041,26121,26164,26222,26271,26412,26481,26624,26788,26847,26903,27005,27047,27123,27517,27578,27655,27818,27898,27970,28006,34370,34405,34442,34493,35523,35589,39351,39906,40055,40090,40164,40223,40443,40538,40610,40686,40800,40915,40986,41023,41900,42141,42246,42440,42521,42621,42665,42789,42838,42888,42953,43036,43086,43163,43248,43296,43343,43524,43651,43717,43770,43855,43942,44168,44238,44435,44507,44570,44636,45120,45180,45484,45748,45824,45921,46002,46051,46130,46204,46285,46401,46458,46502,46622,46974,47057,47132,47177,47477,47550,47779,47976,48136,48193,48261,48467,48551,48797,48865,48918,48996,49067,49100,49312,49367,49431,49478,49574,49626,49678,49741,49802,49839,49874,49919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,252,367", "endColumns": "95,100,114,103", "endOffsets": "146,247,362,466"}, "to": {"startLines": "167,268,269,270", "startColumns": "4,4,4,4", "startOffsets": "14511,24385,24486,24601", "endColumns": "95,100,114,103", "endOffsets": "14602,24481,24596,24700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,346,483,652,731", "endColumns": "70,87,81,136,168,78,75", "endOffsets": "171,259,341,478,647,726,802"}, "to": {"startLines": "166,265,445,480,531,550,551", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14440,24132,40315,43348,47555,49105,49184", "endColumns": "70,87,81,136,168,78,75", "endOffsets": "14506,24215,40392,43480,47719,49179,49255"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,96", "endOffsets": "139,236"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "48556,48645", "endColumns": "88,96", "endOffsets": "48640,48737"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,1008,1096,1245,1316,1386,1465,1531", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,70,69,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,1003,1091,1165,1311,1381,1460,1526,1647"}, "to": {"startLines": "142,143,180,181,204,294,296,438,444,487,488,508,526,527,533,534,536", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11563,11658,16813,16910,19621,26486,26629,39742,40228,43947,44030,45489,47182,47253,47784,47863,47981", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,70,69,78,65,120", "endOffsets": "11653,11741,16905,17004,19703,26563,26720,39826,40310,44025,44113,45558,47248,47318,47858,47924,48097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,192,266,338,416,489,583,673", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "125,187,261,333,411,484,578,668,749"}, "to": {"startLines": "237,238,239,240,241,242,243,244,245", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "22221,22296,22358,22432,22504,22582,22655,22749,22839", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "22291,22353,22427,22499,22577,22650,22744,22834,22915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,288,591,653,778,894,1035,1085,1136,1255,1357,1396,1487,1524,1562,1615,1701,1742", "endColumns": "41,46,52,61,124,115,140,49,50,118,101,38,90,36,37,52,85,40,55", "endOffsets": "240,287,340,652,777,893,1034,1084,1135,1254,1356,1395,1486,1523,1561,1614,1700,1741,1797"}, "to": {"startLines": "433,434,435,454,455,456,457,458,459,460,461,495,496,497,498,499,500,501,556", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39356,39402,39453,41028,41094,41223,41343,41488,41542,41597,41720,44641,44684,44779,44820,44862,44919,45009,49483", "endColumns": "45,50,56,65,128,119,144,53,54,122,105,42,94,40,41,56,89,44,59", "endOffsets": "39397,39448,39505,41089,41218,41338,41483,41537,41592,41715,41821,44679,44774,44815,44857,44914,45004,45049,49538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,504,619,894,985,1078,1173,1267,1367,1460,1555,1650,1741,2025,2455,2574,2859", "endColumns": "107,91,114,122,90,92,94,93,99,92,94,94,90,90,109,118,181,83", "endOffsets": "208,300,614,737,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,2130,2569,2751,2938"}, "to": {"startLines": "39,40,43,44,47,48,49,50,51,52,53,54,55,56,59,63,64,504", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1795,1903,2134,2249,2465,2556,2649,2744,2838,2938,3031,3126,3221,3312,3533,3872,3991,45185", "endColumns": "107,91,114,122,90,92,94,93,99,92,94,94,90,90,109,118,181,83", "endOffsets": "1898,1990,2244,2367,2551,2644,2739,2833,2933,3026,3121,3216,3307,3398,3638,3986,4168,45264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,292,400,516,611,708,822,962,1085,1232,1317,1417,1515,1617,1739,1876,1981,2121,2259,2385,2581,2704,2826,2948,3074,3173,3268,3387,3524,3626,3737,3841,3986,4133,4240,4347,4431,4529,4623,4731,4819,4906,5007,5088,5171,5270,5376,5471,5574,5660,5769,5867,5973,6094,6175,6287", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "171,287,395,511,606,703,817,957,1080,1227,1312,1412,1510,1612,1734,1871,1976,2116,2254,2380,2576,2699,2821,2943,3069,3168,3263,3382,3519,3621,3732,3836,3981,4128,4235,4342,4426,4524,4618,4726,4814,4901,5002,5083,5166,5265,5371,5466,5569,5655,5764,5862,5968,6089,6170,6282,6380"}, "to": {"startLines": "311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28011,28132,28248,28356,28472,28567,28664,28778,28918,29041,29188,29273,29373,29471,29573,29695,29832,29937,30077,30215,30341,30537,30660,30782,30904,31030,31129,31224,31343,31480,31582,31693,31797,31942,32089,32196,32303,32387,32485,32579,32687,32775,32862,32963,33044,33127,33226,33332,33427,33530,33616,33725,33823,33929,34050,34131,34243", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "28127,28243,28351,28467,28562,28659,28773,28913,29036,29183,29268,29368,29466,29568,29690,29827,29932,30072,30210,30336,30532,30655,30777,30899,31025,31124,31219,31338,31475,31577,31688,31792,31937,32084,32191,32298,32382,32480,32574,32682,32770,32857,32958,33039,33122,33221,33327,33422,33525,33611,33720,33818,33924,34045,34126,34238,34336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "116,117,118,119,120,121,122,528", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9398,9495,9597,9699,9800,9903,10010,47323", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "9490,9592,9694,9795,9898,10005,10115,47419"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-hu\\values-hu.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3367,3433", "endColumns": "65,65", "endOffsets": "3428,3494"}, "to": {"startLines": "257,258", "startColumns": "4,4", "startOffsets": "23592,23658", "endColumns": "65,65", "endOffsets": "23653,23719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "436,437", "startColumns": "4,4", "startOffsets": "39510,39622", "endColumns": "111,119", "endOffsets": "39617,39737"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1032,1096,1195,1270,1329,1439,1501,1570,1628,1700,1761,1816,1919,1976,2036,2091,2172,2292,2375,2453,2549,2635,2723,2858,2941,3021,3161,3255,3337,3390,3441,3507,3583,3665,3736,3820,3897,3972,4051,4128,4233,4329,4406,4498,4595,4669,4754,4851,4903,4986,5053,5141,5228,5290,5354,5417,5483,5581,5687,5781,5888,5945,6000,6085,6170,6247", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "258,339,415,492,582,662,761,881,964,1027,1091,1190,1265,1324,1434,1496,1565,1623,1695,1756,1811,1914,1971,2031,2086,2167,2287,2370,2448,2544,2630,2718,2853,2936,3016,3156,3250,3332,3385,3436,3502,3578,3660,3731,3815,3892,3967,4046,4123,4228,4324,4401,4493,4590,4664,4749,4846,4898,4981,5048,5136,5223,5285,5349,5412,5478,5576,5682,5776,5883,5940,5995,6080,6165,6242,6315"}, "to": {"startLines": "19,106,107,108,109,110,126,127,141,210,212,267,291,300,372,373,374,375,376,377,378,379,380,381,382,383,384,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,469,505,506,517", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,8664,8745,8821,8898,8988,10318,10417,11480,20004,20154,24286,26276,26908,34498,34608,34670,34739,34797,34869,34930,34985,35088,35145,35205,35260,35341,35594,35677,35755,35851,35937,36025,36160,36243,36323,36463,36557,36639,36692,36743,36809,36885,36967,37038,37122,37199,37274,37353,37430,37535,37631,37708,37800,37897,37971,38056,38153,38205,38288,38355,38443,38530,38592,38656,38719,38785,38883,38989,39083,39190,39247,42670,45269,45354,46290", "endLines": "22,106,107,108,109,110,126,127,141,210,212,267,291,300,372,373,374,375,376,377,378,379,380,381,382,383,384,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,469,505,506,517", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "945,8740,8816,8893,8983,9063,10412,10532,11558,20062,20213,24380,26346,26962,34603,34665,34734,34792,34864,34925,34980,35083,35140,35200,35255,35336,35456,35672,35750,35846,35932,36020,36155,36238,36318,36458,36552,36634,36687,36738,36804,36880,36962,37033,37117,37194,37269,37348,37425,37530,37626,37703,37795,37892,37966,38051,38148,38200,38283,38350,38438,38525,38587,38651,38714,38780,38878,38984,39078,39185,39242,39297,42750,45349,45426,46358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-hu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "172", "endOffsets": "367"}, "to": {"startLines": "152", "startColumns": "4", "startOffsets": "12826", "endColumns": "176", "endOffsets": "12998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,490,692,780,867,945,1031,1134,1208,1276,1373,1474,1547,1615,1680,1748,1861,1972,2082,2156,2238,2312,2385,2475,2564,2632,2695,2748,2806,2854,2915,2979,3046,3110,3178,3243,3302,3367,3420,3485,3567,3649,3706", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,112,110,109,73,81,73,72,89,88,67,62,52,57,47,60,63,66,63,67,64,58,64,52,64,81,81,56,68", "endOffsets": "280,485,687,775,862,940,1026,1129,1203,1271,1368,1469,1542,1610,1675,1743,1856,1967,2077,2151,2233,2307,2380,2470,2559,2627,2690,2743,2801,2849,2910,2974,3041,3105,3173,3238,3297,3362,3415,3480,3562,3644,3701,3770"}, "to": {"startLines": "2,11,15,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,246,247,248,249,250,251,252,253,254,255,256,259,260,261,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,585,20218,20306,20393,20471,20557,20660,20734,20802,20899,21000,21073,21141,21206,21274,21387,21498,21608,21682,21764,21838,21911,22001,22090,22158,22920,22973,23031,23079,23140,23204,23271,23335,23403,23468,23527,23724,23777,23842,23924,24006,24063", "endLines": "10,14,18,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,246,247,248,249,250,251,252,253,254,255,256,259,260,261,262,263,264", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,112,110,109,73,81,73,72,89,88,67,62,52,57,47,60,63,66,63,67,64,58,64,52,64,81,81,56,68", "endOffsets": "375,580,782,20301,20388,20466,20552,20655,20729,20797,20894,20995,21068,21136,21201,21269,21382,21493,21603,21677,21759,21833,21906,21996,22085,22153,22216,22968,23026,23074,23135,23199,23266,23330,23398,23463,23522,23587,23772,23837,23919,24001,24058,24127"}}]}]}