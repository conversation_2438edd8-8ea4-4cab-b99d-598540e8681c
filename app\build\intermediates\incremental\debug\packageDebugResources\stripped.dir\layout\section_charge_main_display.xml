<?xml version="1.0" encoding="utf-8"?>
<!-- 
 * Main charge display section
 * This section contains:
 * - Battery percentage in a circular progress indicator
 * - Charging time estimates (to target and to full)
 */
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/charge_main_display_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:baselineAligned="false"
    android:orientation="horizontal">

    <!-- Battery percentage with circular progress -->
    <RelativeLayout
        android:id="@+id/percent_layout"
        android:background="@drawable/white_block"
        android:padding="8dp"
        android:layout_width="0dp"
        android:layout_height="235dp"
        android:layout_marginBottom="14dp"
        android:layout_weight="1"
        android:layout_marginStart="9dp"
        android:layout_marginEnd="14dp">
        <RelativeLayout
            android:id="@+id/percent_inner_layout"
            android:background="@drawable/grey_block_static"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <TextView
                android:textSize="50sp"
                android:textColor="?attr/colorr"
                android:gravity="center"
                android:id="@+id/text_percent"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="@string/zero"/>
            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/charge_prog_bar_percent"
                android:background="@drawable/circle_back"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:max="100"
                android:progress="0"
                android:layout_centerInParent="true"
                app:indicatorColor="?attr/colorr"
                app:indicatorInset="2dp"
                app:indicatorSize="160dp"
                app:trackColor="@color/empty"
                app:trackCornerRadius="10dp"
                app:trackThickness="3dp"/>
        </RelativeLayout>
    </RelativeLayout>

    <!-- Time estimates -->
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/time_estimates_layout"
        android:background="@drawable/white_block"
        android:padding="8dp"
        android:visibility="visible"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginBottom="14dp"
        android:layout_marginEnd="9dp">

        <!-- Time to full charge -->
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:id="@+id/full_charge_block"
            android:background="@drawable/grey_block"
            android:focusable="true"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="5dp"
            android:layout_weight="1"
            android:paddingStart="7dp"
            android:paddingEnd="7dp">
            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:contentDescription="@string/zero"
                app:srcCompat="@drawable/ic_day"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/text_time_to_full"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="@string/zero"/>
        </LinearLayout>

        <!-- Time to target charge -->
        <LinearLayout
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:id="@+id/target_charge_block"
            android:background="@drawable/grey_block"
            android:focusable="true"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="3dp"
            android:layout_marginBottom="3dp"
            android:layout_weight="1"
            android:paddingStart="7dp"
            android:paddingEnd="7dp">
            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:contentDescription="@string/zero"
                app:srcCompat="@drawable/ic_day_night"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/text_time_to_target"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="@string/zero"/>
        </LinearLayout>

        <!-- Current charging rate -->
        <LinearLayout
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:id="@+id/current_rate_block"
            android:background="@drawable/grey_block"
            android:focusable="true"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="5dp"
            android:layout_weight="1"
            android:paddingStart="7dp"
            android:paddingEnd="7dp">
            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:contentDescription="@string/zero"
                app:srcCompat="@drawable/ic_night"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/text_current_rate"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="@string/zero"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>