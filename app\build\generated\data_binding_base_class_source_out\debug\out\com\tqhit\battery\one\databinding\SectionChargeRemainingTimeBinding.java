// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SectionChargeRemainingTimeBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout chargeRemainingTimeRoot;

  @NonNull
  public final TextView labelTimeToTarget;

  @NonNull
  public final TextView valTimeToFull;

  @NonNull
  public final TextView valTimeToTarget;

  private SectionChargeRemainingTimeBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout chargeRemainingTimeRoot, @NonNull TextView labelTimeToTarget,
      @NonNull TextView valTimeToFull, @NonNull TextView valTimeToTarget) {
    this.rootView = rootView;
    this.chargeRemainingTimeRoot = chargeRemainingTimeRoot;
    this.labelTimeToTarget = labelTimeToTarget;
    this.valTimeToFull = valTimeToFull;
    this.valTimeToTarget = valTimeToTarget;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SectionChargeRemainingTimeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SectionChargeRemainingTimeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.section_charge_remaining_time, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SectionChargeRemainingTimeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      LinearLayout chargeRemainingTimeRoot = (LinearLayout) rootView;

      id = R.id.label_time_to_target;
      TextView labelTimeToTarget = ViewBindings.findChildViewById(rootView, id);
      if (labelTimeToTarget == null) {
        break missingId;
      }

      id = R.id.val_time_to_full;
      TextView valTimeToFull = ViewBindings.findChildViewById(rootView, id);
      if (valTimeToFull == null) {
        break missingId;
      }

      id = R.id.val_time_to_target;
      TextView valTimeToTarget = ViewBindings.findChildViewById(rootView, id);
      if (valTimeToTarget == null) {
        break missingId;
      }

      return new SectionChargeRemainingTimeBinding((LinearLayout) rootView, chargeRemainingTimeRoot,
          labelTimeToTarget, valTimeToFull, valTimeToTarget);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
