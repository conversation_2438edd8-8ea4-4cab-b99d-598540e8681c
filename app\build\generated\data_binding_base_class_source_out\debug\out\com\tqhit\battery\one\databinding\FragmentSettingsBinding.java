// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSettingsBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final TextView aT;

  @NonNull
  public final TextView aboutTranslations;

  @NonNull
  public final TextView animationTitleBlock;

  @NonNull
  public final ConstraintLayout antiThiefBlock;

  @NonNull
  public final ImageView antiThiefInfo;

  @NonNull
  public final TextView antiThiefTitleBlock;

  @NonNull
  public final TextView autoStabDatabase;

  @NonNull
  public final TextView batteryInfo;

  @NonNull
  public final TextView buttonChangeFrequency;

  @NonNull
  public final TextView buttonChangeNotify;

  @NonNull
  public final TextView buttonChangeNotifyIcon;

  @NonNull
  public final TextView buttonSettingsNotify;

  @NonNull
  public final TextView buyAdvanceAccess;

  @NonNull
  public final ConstraintLayout changeBLMButton;

  @NonNull
  public final TextView changeCapacity;

  @NonNull
  public final TextView changeDualBattery;

  @NonNull
  public final TextView changeIcon;

  @NonNull
  public final TextView changeLang;

  @NonNull
  public final TextView changeSecondColorTheme;

  @NonNull
  public final TextView changeTemp;

  @NonNull
  public final TextView changeTheme;

  @NonNull
  public final TextView clearDatabase;

  @NonNull
  public final ConstraintLayout currentSessionAmperageButton;

  @NonNull
  public final SwitchCompat currentSessionAmperageSession;

  @NonNull
  public final TextView d1;

  @NonNull
  public final TextView debugTitle;

  @NonNull
  public final TextView debuggerButton;

  @NonNull
  public final LinearLayout ertretr;

  @NonNull
  public final TextView exportDatabase;

  @NonNull
  public final ConstraintLayout handResetButton;

  @NonNull
  public final TextView heandStabDatabase;

  @NonNull
  public final TextView importDatabase;

  @NonNull
  public final LayoutBackNavigationBinding includeBackNavigation;

  @NonNull
  public final ConstraintLayout indentDown;

  @NonNull
  public final TextView mediationDebuggerButton;

  @NonNull
  public final ConstraintLayout n11;

  @NonNull
  public final TextView n111;

  @NonNull
  public final ConstraintLayout n12;

  @NonNull
  public final TextView n121;

  @NonNull
  public final ConstraintLayout n2;

  @NonNull
  public final TextView n21;

  @NonNull
  public final TextView n3;

  @NonNull
  public final TextView nT;

  @NonNull
  public final TextView p11;

  @NonNull
  public final TextView p12334;

  @NonNull
  public final ConstraintLayout p5;

  @NonNull
  public final ConstraintLayout p6;

  @NonNull
  public final TextView pT;

  @NonNull
  public final TextView privacyButton;

  @NonNull
  public final TextView privacySettingButton;

  @NonNull
  public final TextView rateButton;

  @NonNull
  public final TextView removeAds;

  @NonNull
  public final TextView resetPurchasesButton;

  @NonNull
  public final NestedScrollView scrollView;

  @NonNull
  public final TextView supportMeButton;

  @NonNull
  public final ConstraintLayout switchAnimationBlock;

  @NonNull
  public final ConstraintLayout switchAnimationTimeBlock;

  @NonNull
  public final TextView switchAnimationTimeTitle;

  @NonNull
  public final TextView switchAnimationTitle;

  @NonNull
  public final ConstraintLayout switchAntiThiefBlock;

  @NonNull
  public final ConstraintLayout switchAntiThiefSoundBlock;

  @NonNull
  public final TextView switchAntiThiefSoundTitle;

  @NonNull
  public final TextView switchAntiThiefTitle;

  @NonNull
  public final SwitchCompat switchBLM;

  @NonNull
  public final SwitchCompat switchEnableAnimation;

  @NonNull
  public final SwitchCompat switchEnableAnimationTime;

  @NonNull
  public final SwitchCompat switchEnableAntiThief;

  @NonNull
  public final SwitchCompat switchEnableAntiThiefSound;

  @NonNull
  public final SwitchCompat switchHandResetSession;

  @NonNull
  public final SwitchCompat switchInfo;

  @NonNull
  public final SwitchCompat switchIsChargeNotify;

  @NonNull
  public final SwitchCompat switchIsDischargeNotify;

  @NonNull
  public final SwitchCompat switchIsShowedOnLockscreen;

  @NonNull
  public final SwitchCompat switchVibration;

  @NonNull
  public final TextView testNewDischarge;

  @NonNull
  public final TextView versionApp;

  @NonNull
  public final ConstraintLayout versionAppButton;

  @NonNull
  public final TextView workInBackgoundButton;

  @NonNull
  public final TextView writeMe;

  private FragmentSettingsBinding(@NonNull NestedScrollView rootView, @NonNull TextView aT,
      @NonNull TextView aboutTranslations, @NonNull TextView animationTitleBlock,
      @NonNull ConstraintLayout antiThiefBlock, @NonNull ImageView antiThiefInfo,
      @NonNull TextView antiThiefTitleBlock, @NonNull TextView autoStabDatabase,
      @NonNull TextView batteryInfo, @NonNull TextView buttonChangeFrequency,
      @NonNull TextView buttonChangeNotify, @NonNull TextView buttonChangeNotifyIcon,
      @NonNull TextView buttonSettingsNotify, @NonNull TextView buyAdvanceAccess,
      @NonNull ConstraintLayout changeBLMButton, @NonNull TextView changeCapacity,
      @NonNull TextView changeDualBattery, @NonNull TextView changeIcon,
      @NonNull TextView changeLang, @NonNull TextView changeSecondColorTheme,
      @NonNull TextView changeTemp, @NonNull TextView changeTheme, @NonNull TextView clearDatabase,
      @NonNull ConstraintLayout currentSessionAmperageButton,
      @NonNull SwitchCompat currentSessionAmperageSession, @NonNull TextView d1,
      @NonNull TextView debugTitle, @NonNull TextView debuggerButton, @NonNull LinearLayout ertretr,
      @NonNull TextView exportDatabase, @NonNull ConstraintLayout handResetButton,
      @NonNull TextView heandStabDatabase, @NonNull TextView importDatabase,
      @NonNull LayoutBackNavigationBinding includeBackNavigation,
      @NonNull ConstraintLayout indentDown, @NonNull TextView mediationDebuggerButton,
      @NonNull ConstraintLayout n11, @NonNull TextView n111, @NonNull ConstraintLayout n12,
      @NonNull TextView n121, @NonNull ConstraintLayout n2, @NonNull TextView n21,
      @NonNull TextView n3, @NonNull TextView nT, @NonNull TextView p11, @NonNull TextView p12334,
      @NonNull ConstraintLayout p5, @NonNull ConstraintLayout p6, @NonNull TextView pT,
      @NonNull TextView privacyButton, @NonNull TextView privacySettingButton,
      @NonNull TextView rateButton, @NonNull TextView removeAds,
      @NonNull TextView resetPurchasesButton, @NonNull NestedScrollView scrollView,
      @NonNull TextView supportMeButton, @NonNull ConstraintLayout switchAnimationBlock,
      @NonNull ConstraintLayout switchAnimationTimeBlock,
      @NonNull TextView switchAnimationTimeTitle, @NonNull TextView switchAnimationTitle,
      @NonNull ConstraintLayout switchAntiThiefBlock,
      @NonNull ConstraintLayout switchAntiThiefSoundBlock,
      @NonNull TextView switchAntiThiefSoundTitle, @NonNull TextView switchAntiThiefTitle,
      @NonNull SwitchCompat switchBLM, @NonNull SwitchCompat switchEnableAnimation,
      @NonNull SwitchCompat switchEnableAnimationTime, @NonNull SwitchCompat switchEnableAntiThief,
      @NonNull SwitchCompat switchEnableAntiThiefSound,
      @NonNull SwitchCompat switchHandResetSession, @NonNull SwitchCompat switchInfo,
      @NonNull SwitchCompat switchIsChargeNotify, @NonNull SwitchCompat switchIsDischargeNotify,
      @NonNull SwitchCompat switchIsShowedOnLockscreen, @NonNull SwitchCompat switchVibration,
      @NonNull TextView testNewDischarge, @NonNull TextView versionApp,
      @NonNull ConstraintLayout versionAppButton, @NonNull TextView workInBackgoundButton,
      @NonNull TextView writeMe) {
    this.rootView = rootView;
    this.aT = aT;
    this.aboutTranslations = aboutTranslations;
    this.animationTitleBlock = animationTitleBlock;
    this.antiThiefBlock = antiThiefBlock;
    this.antiThiefInfo = antiThiefInfo;
    this.antiThiefTitleBlock = antiThiefTitleBlock;
    this.autoStabDatabase = autoStabDatabase;
    this.batteryInfo = batteryInfo;
    this.buttonChangeFrequency = buttonChangeFrequency;
    this.buttonChangeNotify = buttonChangeNotify;
    this.buttonChangeNotifyIcon = buttonChangeNotifyIcon;
    this.buttonSettingsNotify = buttonSettingsNotify;
    this.buyAdvanceAccess = buyAdvanceAccess;
    this.changeBLMButton = changeBLMButton;
    this.changeCapacity = changeCapacity;
    this.changeDualBattery = changeDualBattery;
    this.changeIcon = changeIcon;
    this.changeLang = changeLang;
    this.changeSecondColorTheme = changeSecondColorTheme;
    this.changeTemp = changeTemp;
    this.changeTheme = changeTheme;
    this.clearDatabase = clearDatabase;
    this.currentSessionAmperageButton = currentSessionAmperageButton;
    this.currentSessionAmperageSession = currentSessionAmperageSession;
    this.d1 = d1;
    this.debugTitle = debugTitle;
    this.debuggerButton = debuggerButton;
    this.ertretr = ertretr;
    this.exportDatabase = exportDatabase;
    this.handResetButton = handResetButton;
    this.heandStabDatabase = heandStabDatabase;
    this.importDatabase = importDatabase;
    this.includeBackNavigation = includeBackNavigation;
    this.indentDown = indentDown;
    this.mediationDebuggerButton = mediationDebuggerButton;
    this.n11 = n11;
    this.n111 = n111;
    this.n12 = n12;
    this.n121 = n121;
    this.n2 = n2;
    this.n21 = n21;
    this.n3 = n3;
    this.nT = nT;
    this.p11 = p11;
    this.p12334 = p12334;
    this.p5 = p5;
    this.p6 = p6;
    this.pT = pT;
    this.privacyButton = privacyButton;
    this.privacySettingButton = privacySettingButton;
    this.rateButton = rateButton;
    this.removeAds = removeAds;
    this.resetPurchasesButton = resetPurchasesButton;
    this.scrollView = scrollView;
    this.supportMeButton = supportMeButton;
    this.switchAnimationBlock = switchAnimationBlock;
    this.switchAnimationTimeBlock = switchAnimationTimeBlock;
    this.switchAnimationTimeTitle = switchAnimationTimeTitle;
    this.switchAnimationTitle = switchAnimationTitle;
    this.switchAntiThiefBlock = switchAntiThiefBlock;
    this.switchAntiThiefSoundBlock = switchAntiThiefSoundBlock;
    this.switchAntiThiefSoundTitle = switchAntiThiefSoundTitle;
    this.switchAntiThiefTitle = switchAntiThiefTitle;
    this.switchBLM = switchBLM;
    this.switchEnableAnimation = switchEnableAnimation;
    this.switchEnableAnimationTime = switchEnableAnimationTime;
    this.switchEnableAntiThief = switchEnableAntiThief;
    this.switchEnableAntiThiefSound = switchEnableAntiThiefSound;
    this.switchHandResetSession = switchHandResetSession;
    this.switchInfo = switchInfo;
    this.switchIsChargeNotify = switchIsChargeNotify;
    this.switchIsDischargeNotify = switchIsDischargeNotify;
    this.switchIsShowedOnLockscreen = switchIsShowedOnLockscreen;
    this.switchVibration = switchVibration;
    this.testNewDischarge = testNewDischarge;
    this.versionApp = versionApp;
    this.versionAppButton = versionAppButton;
    this.workInBackgoundButton = workInBackgoundButton;
    this.writeMe = writeMe;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.a_t;
      TextView aT = ViewBindings.findChildViewById(rootView, id);
      if (aT == null) {
        break missingId;
      }

      id = R.id.about_translations;
      TextView aboutTranslations = ViewBindings.findChildViewById(rootView, id);
      if (aboutTranslations == null) {
        break missingId;
      }

      id = R.id.animation_title_block;
      TextView animationTitleBlock = ViewBindings.findChildViewById(rootView, id);
      if (animationTitleBlock == null) {
        break missingId;
      }

      id = R.id.anti_thief_block;
      ConstraintLayout antiThiefBlock = ViewBindings.findChildViewById(rootView, id);
      if (antiThiefBlock == null) {
        break missingId;
      }

      id = R.id.anti_thief_info;
      ImageView antiThiefInfo = ViewBindings.findChildViewById(rootView, id);
      if (antiThiefInfo == null) {
        break missingId;
      }

      id = R.id.anti_thief_title_block;
      TextView antiThiefTitleBlock = ViewBindings.findChildViewById(rootView, id);
      if (antiThiefTitleBlock == null) {
        break missingId;
      }

      id = R.id.auto_stab_database;
      TextView autoStabDatabase = ViewBindings.findChildViewById(rootView, id);
      if (autoStabDatabase == null) {
        break missingId;
      }

      id = R.id.battery_info;
      TextView batteryInfo = ViewBindings.findChildViewById(rootView, id);
      if (batteryInfo == null) {
        break missingId;
      }

      id = R.id.button_change_frequency;
      TextView buttonChangeFrequency = ViewBindings.findChildViewById(rootView, id);
      if (buttonChangeFrequency == null) {
        break missingId;
      }

      id = R.id.button_change_notify;
      TextView buttonChangeNotify = ViewBindings.findChildViewById(rootView, id);
      if (buttonChangeNotify == null) {
        break missingId;
      }

      id = R.id.button_change_notify_icon;
      TextView buttonChangeNotifyIcon = ViewBindings.findChildViewById(rootView, id);
      if (buttonChangeNotifyIcon == null) {
        break missingId;
      }

      id = R.id.button_settings_notify;
      TextView buttonSettingsNotify = ViewBindings.findChildViewById(rootView, id);
      if (buttonSettingsNotify == null) {
        break missingId;
      }

      id = R.id.buy_advance_access;
      TextView buyAdvanceAccess = ViewBindings.findChildViewById(rootView, id);
      if (buyAdvanceAccess == null) {
        break missingId;
      }

      id = R.id.change_BLM_button;
      ConstraintLayout changeBLMButton = ViewBindings.findChildViewById(rootView, id);
      if (changeBLMButton == null) {
        break missingId;
      }

      id = R.id.change_capacity;
      TextView changeCapacity = ViewBindings.findChildViewById(rootView, id);
      if (changeCapacity == null) {
        break missingId;
      }

      id = R.id.change_dual_battery;
      TextView changeDualBattery = ViewBindings.findChildViewById(rootView, id);
      if (changeDualBattery == null) {
        break missingId;
      }

      id = R.id.change_icon;
      TextView changeIcon = ViewBindings.findChildViewById(rootView, id);
      if (changeIcon == null) {
        break missingId;
      }

      id = R.id.change_lang;
      TextView changeLang = ViewBindings.findChildViewById(rootView, id);
      if (changeLang == null) {
        break missingId;
      }

      id = R.id.change_second_color_theme;
      TextView changeSecondColorTheme = ViewBindings.findChildViewById(rootView, id);
      if (changeSecondColorTheme == null) {
        break missingId;
      }

      id = R.id.change_temp;
      TextView changeTemp = ViewBindings.findChildViewById(rootView, id);
      if (changeTemp == null) {
        break missingId;
      }

      id = R.id.change_theme;
      TextView changeTheme = ViewBindings.findChildViewById(rootView, id);
      if (changeTheme == null) {
        break missingId;
      }

      id = R.id.clear_database;
      TextView clearDatabase = ViewBindings.findChildViewById(rootView, id);
      if (clearDatabase == null) {
        break missingId;
      }

      id = R.id.current_session_amperage_button;
      ConstraintLayout currentSessionAmperageButton = ViewBindings.findChildViewById(rootView, id);
      if (currentSessionAmperageButton == null) {
        break missingId;
      }

      id = R.id.current_session_amperage_session;
      SwitchCompat currentSessionAmperageSession = ViewBindings.findChildViewById(rootView, id);
      if (currentSessionAmperageSession == null) {
        break missingId;
      }

      id = R.id.d1;
      TextView d1 = ViewBindings.findChildViewById(rootView, id);
      if (d1 == null) {
        break missingId;
      }

      id = R.id.debug_title;
      TextView debugTitle = ViewBindings.findChildViewById(rootView, id);
      if (debugTitle == null) {
        break missingId;
      }

      id = R.id.debugger_button;
      TextView debuggerButton = ViewBindings.findChildViewById(rootView, id);
      if (debuggerButton == null) {
        break missingId;
      }

      id = R.id.ertretr;
      LinearLayout ertretr = ViewBindings.findChildViewById(rootView, id);
      if (ertretr == null) {
        break missingId;
      }

      id = R.id.export_database;
      TextView exportDatabase = ViewBindings.findChildViewById(rootView, id);
      if (exportDatabase == null) {
        break missingId;
      }

      id = R.id.hand_reset_button;
      ConstraintLayout handResetButton = ViewBindings.findChildViewById(rootView, id);
      if (handResetButton == null) {
        break missingId;
      }

      id = R.id.heand_stab_database;
      TextView heandStabDatabase = ViewBindings.findChildViewById(rootView, id);
      if (heandStabDatabase == null) {
        break missingId;
      }

      id = R.id.import_database;
      TextView importDatabase = ViewBindings.findChildViewById(rootView, id);
      if (importDatabase == null) {
        break missingId;
      }

      id = R.id.include_back_navigation;
      View includeBackNavigation = ViewBindings.findChildViewById(rootView, id);
      if (includeBackNavigation == null) {
        break missingId;
      }
      LayoutBackNavigationBinding binding_includeBackNavigation = LayoutBackNavigationBinding.bind(includeBackNavigation);

      id = R.id.indent_down;
      ConstraintLayout indentDown = ViewBindings.findChildViewById(rootView, id);
      if (indentDown == null) {
        break missingId;
      }

      id = R.id.mediation_debugger_button;
      TextView mediationDebuggerButton = ViewBindings.findChildViewById(rootView, id);
      if (mediationDebuggerButton == null) {
        break missingId;
      }

      id = R.id.n_1_1;
      ConstraintLayout n11 = ViewBindings.findChildViewById(rootView, id);
      if (n11 == null) {
        break missingId;
      }

      id = R.id.n1_1;
      TextView n111 = ViewBindings.findChildViewById(rootView, id);
      if (n111 == null) {
        break missingId;
      }

      id = R.id.n_1_2;
      ConstraintLayout n12 = ViewBindings.findChildViewById(rootView, id);
      if (n12 == null) {
        break missingId;
      }

      id = R.id.n1_2;
      TextView n121 = ViewBindings.findChildViewById(rootView, id);
      if (n121 == null) {
        break missingId;
      }

      id = R.id.n_2;
      ConstraintLayout n2 = ViewBindings.findChildViewById(rootView, id);
      if (n2 == null) {
        break missingId;
      }

      id = R.id.n2;
      TextView n21 = ViewBindings.findChildViewById(rootView, id);
      if (n21 == null) {
        break missingId;
      }

      id = R.id.n_3;
      TextView n3 = ViewBindings.findChildViewById(rootView, id);
      if (n3 == null) {
        break missingId;
      }

      id = R.id.n_t;
      TextView nT = ViewBindings.findChildViewById(rootView, id);
      if (nT == null) {
        break missingId;
      }

      id = R.id.p_11;
      TextView p11 = ViewBindings.findChildViewById(rootView, id);
      if (p11 == null) {
        break missingId;
      }

      id = R.id.p_12334;
      TextView p12334 = ViewBindings.findChildViewById(rootView, id);
      if (p12334 == null) {
        break missingId;
      }

      id = R.id.p5;
      ConstraintLayout p5 = ViewBindings.findChildViewById(rootView, id);
      if (p5 == null) {
        break missingId;
      }

      id = R.id.p6;
      ConstraintLayout p6 = ViewBindings.findChildViewById(rootView, id);
      if (p6 == null) {
        break missingId;
      }

      id = R.id.p_t;
      TextView pT = ViewBindings.findChildViewById(rootView, id);
      if (pT == null) {
        break missingId;
      }

      id = R.id.privacy_button;
      TextView privacyButton = ViewBindings.findChildViewById(rootView, id);
      if (privacyButton == null) {
        break missingId;
      }

      id = R.id.privacy_setting_button;
      TextView privacySettingButton = ViewBindings.findChildViewById(rootView, id);
      if (privacySettingButton == null) {
        break missingId;
      }

      id = R.id.rate_button;
      TextView rateButton = ViewBindings.findChildViewById(rootView, id);
      if (rateButton == null) {
        break missingId;
      }

      id = R.id.remove_ads;
      TextView removeAds = ViewBindings.findChildViewById(rootView, id);
      if (removeAds == null) {
        break missingId;
      }

      id = R.id.reset_purchases_button;
      TextView resetPurchasesButton = ViewBindings.findChildViewById(rootView, id);
      if (resetPurchasesButton == null) {
        break missingId;
      }

      NestedScrollView scrollView = (NestedScrollView) rootView;

      id = R.id.support_me_button;
      TextView supportMeButton = ViewBindings.findChildViewById(rootView, id);
      if (supportMeButton == null) {
        break missingId;
      }

      id = R.id.switch_animation_block;
      ConstraintLayout switchAnimationBlock = ViewBindings.findChildViewById(rootView, id);
      if (switchAnimationBlock == null) {
        break missingId;
      }

      id = R.id.switch_animation_time_block;
      ConstraintLayout switchAnimationTimeBlock = ViewBindings.findChildViewById(rootView, id);
      if (switchAnimationTimeBlock == null) {
        break missingId;
      }

      id = R.id.switch_animation_time_title;
      TextView switchAnimationTimeTitle = ViewBindings.findChildViewById(rootView, id);
      if (switchAnimationTimeTitle == null) {
        break missingId;
      }

      id = R.id.switch_animation_title;
      TextView switchAnimationTitle = ViewBindings.findChildViewById(rootView, id);
      if (switchAnimationTitle == null) {
        break missingId;
      }

      id = R.id.switch_anti_thief_block;
      ConstraintLayout switchAntiThiefBlock = ViewBindings.findChildViewById(rootView, id);
      if (switchAntiThiefBlock == null) {
        break missingId;
      }

      id = R.id.switch_anti_thief_sound_block;
      ConstraintLayout switchAntiThiefSoundBlock = ViewBindings.findChildViewById(rootView, id);
      if (switchAntiThiefSoundBlock == null) {
        break missingId;
      }

      id = R.id.switch_anti_thief_sound_title;
      TextView switchAntiThiefSoundTitle = ViewBindings.findChildViewById(rootView, id);
      if (switchAntiThiefSoundTitle == null) {
        break missingId;
      }

      id = R.id.switch_anti_thief_title;
      TextView switchAntiThiefTitle = ViewBindings.findChildViewById(rootView, id);
      if (switchAntiThiefTitle == null) {
        break missingId;
      }

      id = R.id.switch_BLM;
      SwitchCompat switchBLM = ViewBindings.findChildViewById(rootView, id);
      if (switchBLM == null) {
        break missingId;
      }

      id = R.id.switch_enable_animation;
      SwitchCompat switchEnableAnimation = ViewBindings.findChildViewById(rootView, id);
      if (switchEnableAnimation == null) {
        break missingId;
      }

      id = R.id.switch_enable_animation_time;
      SwitchCompat switchEnableAnimationTime = ViewBindings.findChildViewById(rootView, id);
      if (switchEnableAnimationTime == null) {
        break missingId;
      }

      id = R.id.switch_enable_anti_thief;
      SwitchCompat switchEnableAntiThief = ViewBindings.findChildViewById(rootView, id);
      if (switchEnableAntiThief == null) {
        break missingId;
      }

      id = R.id.switch_enable_anti_thief_sound;
      SwitchCompat switchEnableAntiThiefSound = ViewBindings.findChildViewById(rootView, id);
      if (switchEnableAntiThiefSound == null) {
        break missingId;
      }

      id = R.id.switch_hand_reset_session;
      SwitchCompat switchHandResetSession = ViewBindings.findChildViewById(rootView, id);
      if (switchHandResetSession == null) {
        break missingId;
      }

      id = R.id.switch_info;
      SwitchCompat switchInfo = ViewBindings.findChildViewById(rootView, id);
      if (switchInfo == null) {
        break missingId;
      }

      id = R.id.switch_is_charge_notify;
      SwitchCompat switchIsChargeNotify = ViewBindings.findChildViewById(rootView, id);
      if (switchIsChargeNotify == null) {
        break missingId;
      }

      id = R.id.switch_is_discharge_notify;
      SwitchCompat switchIsDischargeNotify = ViewBindings.findChildViewById(rootView, id);
      if (switchIsDischargeNotify == null) {
        break missingId;
      }

      id = R.id.switch_is_showed_on_lockscreen;
      SwitchCompat switchIsShowedOnLockscreen = ViewBindings.findChildViewById(rootView, id);
      if (switchIsShowedOnLockscreen == null) {
        break missingId;
      }

      id = R.id.switch_vibration;
      SwitchCompat switchVibration = ViewBindings.findChildViewById(rootView, id);
      if (switchVibration == null) {
        break missingId;
      }

      id = R.id.test_new_discharge;
      TextView testNewDischarge = ViewBindings.findChildViewById(rootView, id);
      if (testNewDischarge == null) {
        break missingId;
      }

      id = R.id.version_app;
      TextView versionApp = ViewBindings.findChildViewById(rootView, id);
      if (versionApp == null) {
        break missingId;
      }

      id = R.id.version_app_button;
      ConstraintLayout versionAppButton = ViewBindings.findChildViewById(rootView, id);
      if (versionAppButton == null) {
        break missingId;
      }

      id = R.id.work_in_backgound_button;
      TextView workInBackgoundButton = ViewBindings.findChildViewById(rootView, id);
      if (workInBackgoundButton == null) {
        break missingId;
      }

      id = R.id.write_me;
      TextView writeMe = ViewBindings.findChildViewById(rootView, id);
      if (writeMe == null) {
        break missingId;
      }

      return new FragmentSettingsBinding((NestedScrollView) rootView, aT, aboutTranslations,
          animationTitleBlock, antiThiefBlock, antiThiefInfo, antiThiefTitleBlock, autoStabDatabase,
          batteryInfo, buttonChangeFrequency, buttonChangeNotify, buttonChangeNotifyIcon,
          buttonSettingsNotify, buyAdvanceAccess, changeBLMButton, changeCapacity,
          changeDualBattery, changeIcon, changeLang, changeSecondColorTheme, changeTemp,
          changeTheme, clearDatabase, currentSessionAmperageButton, currentSessionAmperageSession,
          d1, debugTitle, debuggerButton, ertretr, exportDatabase, handResetButton,
          heandStabDatabase, importDatabase, binding_includeBackNavigation, indentDown,
          mediationDebuggerButton, n11, n111, n12, n121, n2, n21, n3, nT, p11, p12334, p5, p6, pT,
          privacyButton, privacySettingButton, rateButton, removeAds, resetPurchasesButton,
          scrollView, supportMeButton, switchAnimationBlock, switchAnimationTimeBlock,
          switchAnimationTimeTitle, switchAnimationTitle, switchAntiThiefBlock,
          switchAntiThiefSoundBlock, switchAntiThiefSoundTitle, switchAntiThiefTitle, switchBLM,
          switchEnableAnimation, switchEnableAnimationTime, switchEnableAntiThief,
          switchEnableAntiThiefSound, switchHandResetSession, switchInfo, switchIsChargeNotify,
          switchIsDischargeNotify, switchIsShowedOnLockscreen, switchVibration, testNewDischarge,
          versionApp, versionAppButton, workInBackgoundButton, writeMe);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
