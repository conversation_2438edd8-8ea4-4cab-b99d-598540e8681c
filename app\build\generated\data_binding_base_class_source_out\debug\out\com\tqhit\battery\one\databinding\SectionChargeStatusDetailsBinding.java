// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SectionChargeStatusDetailsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout chargeStatusDetailsRoot;

  @NonNull
  public final TextView valAmperage;

  @NonNull
  public final TextView valChargingSpeed;

  @NonNull
  public final TextView valPower;

  @NonNull
  public final TextView valTemperature;

  @NonNull
  public final TextView valVoltage;

  private SectionChargeStatusDetailsBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout chargeStatusDetailsRoot, @NonNull TextView valAmperage,
      @NonNull TextView valChargingSpeed, @NonNull TextView valPower,
      @NonNull TextView valTemperature, @NonNull TextView valVoltage) {
    this.rootView = rootView;
    this.chargeStatusDetailsRoot = chargeStatusDetailsRoot;
    this.valAmperage = valAmperage;
    this.valChargingSpeed = valChargingSpeed;
    this.valPower = valPower;
    this.valTemperature = valTemperature;
    this.valVoltage = valVoltage;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SectionChargeStatusDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SectionChargeStatusDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.section_charge_status_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SectionChargeStatusDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      LinearLayout chargeStatusDetailsRoot = (LinearLayout) rootView;

      id = R.id.val_amperage;
      TextView valAmperage = ViewBindings.findChildViewById(rootView, id);
      if (valAmperage == null) {
        break missingId;
      }

      id = R.id.val_charging_speed;
      TextView valChargingSpeed = ViewBindings.findChildViewById(rootView, id);
      if (valChargingSpeed == null) {
        break missingId;
      }

      id = R.id.val_power;
      TextView valPower = ViewBindings.findChildViewById(rootView, id);
      if (valPower == null) {
        break missingId;
      }

      id = R.id.val_temperature;
      TextView valTemperature = ViewBindings.findChildViewById(rootView, id);
      if (valTemperature == null) {
        break missingId;
      }

      id = R.id.val_voltage;
      TextView valVoltage = ViewBindings.findChildViewById(rootView, id);
      if (valVoltage == null) {
        break missingId;
      }

      return new SectionChargeStatusDetailsBinding((LinearLayout) rootView, chargeStatusDetailsRoot,
          valAmperage, valChargingSpeed, valPower, valTemperature, valVoltage);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
