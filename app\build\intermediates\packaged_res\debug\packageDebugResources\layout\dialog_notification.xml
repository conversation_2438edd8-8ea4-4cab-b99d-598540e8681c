<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_centerInParent="false">
    <RelativeLayout
        android:background="@drawable/white_block"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:elevation="25dp">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <RelativeLayout
                    android:id="@+id/strelka"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignTop="@+id/up_text"
                    android:layout_alignBottom="@+id/up_text"
                    android:layout_alignParentEnd="true">
                    <Button
                        android:id="@+id/exit"
                        android:background="@drawable/grey_block_line_up"
                        android:layout_width="45sp"
                        android:layout_height="match_parent"
                        android:layout_centerInParent="true"
                        android:layout_marginStart="5dp"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:orientation="vertical"
                        android:background="@drawable/ic_strelka"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4sp"
                        android:layout_marginBottom="4sp"
                        android:layout_centerInParent="true"
                        android:layout_marginStart="15sp"
                        android:layout_marginEnd="15sp"
                        android:layout_alignStart="@+id/exit"
                        android:layout_alignEnd="@+id/exit"/>
                </RelativeLayout>
                <TextView
                    android:textSize="18sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:gravity="start"
                    android:id="@+id/up_text"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/error"
                    android:singleLine="true"
                    android:layout_centerVertical="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="2dp"
                    android:layout_toStartOf="@+id/strelka"
                    android:layout_alignParentStart="true"/>
            </RelativeLayout>
            <LinearLayout
                android:orientation="vertical"
                android:background="@drawable/grey_block"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="7dp"
                android:layout_marginBottom="8dp"
                android:paddingStart="9dp"
                android:paddingEnd="7dp">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:id="@+id/main_text"
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/design_capacity_text_error"
                    android:textAlignment="viewStart"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:baselineAligned="false">
                <RelativeLayout
                    android:id="@+id/cancel_view"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1">
                    <Button
                        android:id="@+id/cancel_btn"
                        android:background="@drawable/grey_block"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_alignTop="@+id/sdg1"
                        android:layout_alignBottom="@+id/sdg1"
                        android:layout_alignParentTop="false"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="vertical"
                        android:id="@+id/sdg1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:id="@+id/text_btn_cancel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/cancel"/>
                    </LinearLayout>
                </RelativeLayout>
                <RelativeLayout
                    android:id="@+id/margin"
                    android:visibility="gone"
                    android:layout_width="8dp"
                    android:layout_height="match_parent"/>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1">
                    <Button
                        android:id="@+id/confirm_change_capacity_error"
                        android:background="@drawable/grey_block"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_alignTop="@+id/sdfgsdfg1"
                        android:layout_alignBottom="@+id/sdfgsdfg1"
                        android:layout_alignParentTop="false"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="vertical"
                        android:id="@+id/sdfgsdfg1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:id="@+id/text_btn"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/confirm"/>
                    </LinearLayout>
                </RelativeLayout>
            </LinearLayout>

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/nativeAd"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/grey" />

        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>
