package com.tqhit.battery.one.features.stats.discharge.service;

import com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager;
import com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService;
import com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class EnhancedDischargeTimerService_MembersInjector implements MembersInjector<EnhancedDischargeTimerService> {
  private final Provider<DischargeSessionRepository> dischargeSessionRepositoryProvider;

  private final Provider<ScreenTimeValidationService> screenTimeValidationServiceProvider;

  private final Provider<AppLifecycleManager> appLifecycleManagerProvider;

  public EnhancedDischargeTimerService_MembersInjector(
      Provider<DischargeSessionRepository> dischargeSessionRepositoryProvider,
      Provider<ScreenTimeValidationService> screenTimeValidationServiceProvider,
      Provider<AppLifecycleManager> appLifecycleManagerProvider) {
    this.dischargeSessionRepositoryProvider = dischargeSessionRepositoryProvider;
    this.screenTimeValidationServiceProvider = screenTimeValidationServiceProvider;
    this.appLifecycleManagerProvider = appLifecycleManagerProvider;
  }

  public static MembersInjector<EnhancedDischargeTimerService> create(
      Provider<DischargeSessionRepository> dischargeSessionRepositoryProvider,
      Provider<ScreenTimeValidationService> screenTimeValidationServiceProvider,
      Provider<AppLifecycleManager> appLifecycleManagerProvider) {
    return new EnhancedDischargeTimerService_MembersInjector(dischargeSessionRepositoryProvider, screenTimeValidationServiceProvider, appLifecycleManagerProvider);
  }

  @Override
  public void injectMembers(EnhancedDischargeTimerService instance) {
    injectDischargeSessionRepository(instance, dischargeSessionRepositoryProvider.get());
    injectScreenTimeValidationService(instance, screenTimeValidationServiceProvider.get());
    injectAppLifecycleManager(instance, appLifecycleManagerProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.dischargeSessionRepository")
  public static void injectDischargeSessionRepository(EnhancedDischargeTimerService instance,
      DischargeSessionRepository dischargeSessionRepository) {
    instance.dischargeSessionRepository = dischargeSessionRepository;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.screenTimeValidationService")
  public static void injectScreenTimeValidationService(EnhancedDischargeTimerService instance,
      ScreenTimeValidationService screenTimeValidationService) {
    instance.screenTimeValidationService = screenTimeValidationService;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.appLifecycleManager")
  public static void injectAppLifecycleManager(EnhancedDischargeTimerService instance,
      AppLifecycleManager appLifecycleManager) {
    instance.appLifecycleManager = appLifecycleManager;
  }
}
