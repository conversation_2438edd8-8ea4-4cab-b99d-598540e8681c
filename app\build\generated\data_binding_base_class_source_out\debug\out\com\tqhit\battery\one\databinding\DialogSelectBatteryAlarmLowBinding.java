// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogSelectBatteryAlarmLowBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final Button battery4;

  @NonNull
  public final ConstraintLayout buttonChargeAlarm;

  @NonNull
  public final ConstraintLayout chargeL;

  @NonNull
  public final Button dontDisturbFrom;

  @NonNull
  public final LinearLayout dontDisturbFromLayout;

  @NonNull
  public final ConstraintLayout dontDisturbUntil;

  @NonNull
  public final LinearLayout dontDisturbUntilLayout;

  @NonNull
  public final Button exit;

  @NonNull
  public final LinearLayout linearLayout2;

  @NonNull
  public final TextView lowAlarmPercent;

  @NonNull
  public final TextView p11;

  @NonNull
  public final TextView p112;

  @NonNull
  public final TextView p1221;

  @NonNull
  public final TextView p12e2;

  @NonNull
  public final TextView p12er;

  @NonNull
  public final TextView p12er1;

  @NonNull
  public final TextView p192;

  @NonNull
  public final ConstraintLayout p5;

  @NonNull
  public final ConstraintLayout p6;

  @NonNull
  public final ProgressBar progressbarLowAlarm;

  @NonNull
  public final LinearLayout relat223;

  @NonNull
  public final ConstraintLayout relativ33;

  @NonNull
  public final SeekBar seekBarLowAlarm;

  @NonNull
  public final RelativeLayout strelka;

  @NonNull
  public final SwitchCompat switchDontDisturb;

  @NonNull
  public final SwitchCompat switchLowAlarm;

  @NonNull
  public final SwitchCompat switchVibration;

  @NonNull
  public final TextView textBuyAccess2;

  @NonNull
  public final TextView textView20;

  private DialogSelectBatteryAlarmLowBinding(@NonNull RelativeLayout rootView,
      @NonNull Button battery4, @NonNull ConstraintLayout buttonChargeAlarm,
      @NonNull ConstraintLayout chargeL, @NonNull Button dontDisturbFrom,
      @NonNull LinearLayout dontDisturbFromLayout, @NonNull ConstraintLayout dontDisturbUntil,
      @NonNull LinearLayout dontDisturbUntilLayout, @NonNull Button exit,
      @NonNull LinearLayout linearLayout2, @NonNull TextView lowAlarmPercent, @NonNull TextView p11,
      @NonNull TextView p112, @NonNull TextView p1221, @NonNull TextView p12e2,
      @NonNull TextView p12er, @NonNull TextView p12er1, @NonNull TextView p192,
      @NonNull ConstraintLayout p5, @NonNull ConstraintLayout p6,
      @NonNull ProgressBar progressbarLowAlarm, @NonNull LinearLayout relat223,
      @NonNull ConstraintLayout relativ33, @NonNull SeekBar seekBarLowAlarm,
      @NonNull RelativeLayout strelka, @NonNull SwitchCompat switchDontDisturb,
      @NonNull SwitchCompat switchLowAlarm, @NonNull SwitchCompat switchVibration,
      @NonNull TextView textBuyAccess2, @NonNull TextView textView20) {
    this.rootView = rootView;
    this.battery4 = battery4;
    this.buttonChargeAlarm = buttonChargeAlarm;
    this.chargeL = chargeL;
    this.dontDisturbFrom = dontDisturbFrom;
    this.dontDisturbFromLayout = dontDisturbFromLayout;
    this.dontDisturbUntil = dontDisturbUntil;
    this.dontDisturbUntilLayout = dontDisturbUntilLayout;
    this.exit = exit;
    this.linearLayout2 = linearLayout2;
    this.lowAlarmPercent = lowAlarmPercent;
    this.p11 = p11;
    this.p112 = p112;
    this.p1221 = p1221;
    this.p12e2 = p12e2;
    this.p12er = p12er;
    this.p12er1 = p12er1;
    this.p192 = p192;
    this.p5 = p5;
    this.p6 = p6;
    this.progressbarLowAlarm = progressbarLowAlarm;
    this.relat223 = relat223;
    this.relativ33 = relativ33;
    this.seekBarLowAlarm = seekBarLowAlarm;
    this.strelka = strelka;
    this.switchDontDisturb = switchDontDisturb;
    this.switchLowAlarm = switchLowAlarm;
    this.switchVibration = switchVibration;
    this.textBuyAccess2 = textBuyAccess2;
    this.textView20 = textView20;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogSelectBatteryAlarmLowBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogSelectBatteryAlarmLowBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_select_battery_alarm_low, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogSelectBatteryAlarmLowBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.battery_4;
      Button battery4 = ViewBindings.findChildViewById(rootView, id);
      if (battery4 == null) {
        break missingId;
      }

      id = R.id.button_charge_alarm;
      ConstraintLayout buttonChargeAlarm = ViewBindings.findChildViewById(rootView, id);
      if (buttonChargeAlarm == null) {
        break missingId;
      }

      id = R.id.charge_l;
      ConstraintLayout chargeL = ViewBindings.findChildViewById(rootView, id);
      if (chargeL == null) {
        break missingId;
      }

      id = R.id.dont_disturb_from;
      Button dontDisturbFrom = ViewBindings.findChildViewById(rootView, id);
      if (dontDisturbFrom == null) {
        break missingId;
      }

      id = R.id.dont_disturb_from_layout;
      LinearLayout dontDisturbFromLayout = ViewBindings.findChildViewById(rootView, id);
      if (dontDisturbFromLayout == null) {
        break missingId;
      }

      id = R.id.dont_disturb_until;
      ConstraintLayout dontDisturbUntil = ViewBindings.findChildViewById(rootView, id);
      if (dontDisturbUntil == null) {
        break missingId;
      }

      id = R.id.dont_disturb_until_layout;
      LinearLayout dontDisturbUntilLayout = ViewBindings.findChildViewById(rootView, id);
      if (dontDisturbUntilLayout == null) {
        break missingId;
      }

      id = R.id.exit;
      Button exit = ViewBindings.findChildViewById(rootView, id);
      if (exit == null) {
        break missingId;
      }

      id = R.id.linearLayout2;
      LinearLayout linearLayout2 = ViewBindings.findChildViewById(rootView, id);
      if (linearLayout2 == null) {
        break missingId;
      }

      id = R.id.low_alarm_percent;
      TextView lowAlarmPercent = ViewBindings.findChildViewById(rootView, id);
      if (lowAlarmPercent == null) {
        break missingId;
      }

      id = R.id.p_11;
      TextView p11 = ViewBindings.findChildViewById(rootView, id);
      if (p11 == null) {
        break missingId;
      }

      id = R.id.p_112;
      TextView p112 = ViewBindings.findChildViewById(rootView, id);
      if (p112 == null) {
        break missingId;
      }

      id = R.id.p_1221;
      TextView p1221 = ViewBindings.findChildViewById(rootView, id);
      if (p1221 == null) {
        break missingId;
      }

      id = R.id.p_12e2;
      TextView p12e2 = ViewBindings.findChildViewById(rootView, id);
      if (p12e2 == null) {
        break missingId;
      }

      id = R.id.p_12er;
      TextView p12er = ViewBindings.findChildViewById(rootView, id);
      if (p12er == null) {
        break missingId;
      }

      id = R.id.p_12er1;
      TextView p12er1 = ViewBindings.findChildViewById(rootView, id);
      if (p12er1 == null) {
        break missingId;
      }

      id = R.id.p192;
      TextView p192 = ViewBindings.findChildViewById(rootView, id);
      if (p192 == null) {
        break missingId;
      }

      id = R.id.p5;
      ConstraintLayout p5 = ViewBindings.findChildViewById(rootView, id);
      if (p5 == null) {
        break missingId;
      }

      id = R.id.p6;
      ConstraintLayout p6 = ViewBindings.findChildViewById(rootView, id);
      if (p6 == null) {
        break missingId;
      }

      id = R.id.progressbar_low_alarm;
      ProgressBar progressbarLowAlarm = ViewBindings.findChildViewById(rootView, id);
      if (progressbarLowAlarm == null) {
        break missingId;
      }

      id = R.id.relat223;
      LinearLayout relat223 = ViewBindings.findChildViewById(rootView, id);
      if (relat223 == null) {
        break missingId;
      }

      id = R.id.relativ33;
      ConstraintLayout relativ33 = ViewBindings.findChildViewById(rootView, id);
      if (relativ33 == null) {
        break missingId;
      }

      id = R.id.seekBar_low_alarm;
      SeekBar seekBarLowAlarm = ViewBindings.findChildViewById(rootView, id);
      if (seekBarLowAlarm == null) {
        break missingId;
      }

      id = R.id.strelka;
      RelativeLayout strelka = ViewBindings.findChildViewById(rootView, id);
      if (strelka == null) {
        break missingId;
      }

      id = R.id.switch_dont_disturb;
      SwitchCompat switchDontDisturb = ViewBindings.findChildViewById(rootView, id);
      if (switchDontDisturb == null) {
        break missingId;
      }

      id = R.id.switch_low_alarm;
      SwitchCompat switchLowAlarm = ViewBindings.findChildViewById(rootView, id);
      if (switchLowAlarm == null) {
        break missingId;
      }

      id = R.id.switch_vibration;
      SwitchCompat switchVibration = ViewBindings.findChildViewById(rootView, id);
      if (switchVibration == null) {
        break missingId;
      }

      id = R.id.text_buy_access2;
      TextView textBuyAccess2 = ViewBindings.findChildViewById(rootView, id);
      if (textBuyAccess2 == null) {
        break missingId;
      }

      id = R.id.textView20;
      TextView textView20 = ViewBindings.findChildViewById(rootView, id);
      if (textView20 == null) {
        break missingId;
      }

      return new DialogSelectBatteryAlarmLowBinding((RelativeLayout) rootView, battery4,
          buttonChargeAlarm, chargeL, dontDisturbFrom, dontDisturbFromLayout, dontDisturbUntil,
          dontDisturbUntilLayout, exit, linearLayout2, lowAlarmPercent, p11, p112, p1221, p12e2,
          p12er, p12er1, p192, p5, p6, progressbarLowAlarm, relat223, relativ33, seekBarLowAlarm,
          strelka, switchDontDisturb, switchLowAlarm, switchVibration, textBuyAccess2, textView20);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
