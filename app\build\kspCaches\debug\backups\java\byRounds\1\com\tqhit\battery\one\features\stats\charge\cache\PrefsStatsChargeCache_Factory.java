package com.tqhit.battery.one.features.stats.charge.cache;

import com.google.gson.Gson;
import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class PrefsStatsChargeCache_Factory implements Factory<PrefsStatsChargeCache> {
  private final Provider<PreferencesHelper> preferencesHelperProvider;

  private final Provider<Gson> gsonProvider;

  public PrefsStatsChargeCache_Factory(Provider<PreferencesHelper> preferencesHelperProvider,
      Provider<Gson> gsonProvider) {
    this.preferencesHelperProvider = preferencesHelperProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public PrefsStatsChargeCache get() {
    return newInstance(preferencesHelperProvider.get(), gsonProvider.get());
  }

  public static PrefsStatsChargeCache_Factory create(
      Provider<PreferencesHelper> preferencesHelperProvider, Provider<Gson> gsonProvider) {
    return new PrefsStatsChargeCache_Factory(preferencesHelperProvider, gsonProvider);
  }

  public static PrefsStatsChargeCache newInstance(PreferencesHelper preferencesHelper, Gson gson) {
    return new PrefsStatsChargeCache(preferencesHelper, gson);
  }
}
