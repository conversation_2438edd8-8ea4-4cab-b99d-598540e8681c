// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentStatsChargeBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final TextView batteryAlarmBtn;

  @NonNull
  public final Button btnResetSession;

  @NonNull
  public final LinearLayout chargeCurrentSessionRoot;

  @NonNull
  public final CircularProgressIndicator chargeProgBarPercent;

  @NonNull
  public final LinearLayout chargingStatusBlock;

  @NonNull
  public final View dividerTarget;

  @NonNull
  public final LinearLayout fullChargeBlock;

  @NonNull
  public final LayoutBackNavigationBinding includeBackNavigation;

  @NonNull
  public final ShimmerFrameLayout nativeAd;

  @NonNull
  public final RelativeLayout percentInnerLayout;

  @NonNull
  public final RelativeLayout percentLayout;

  @NonNull
  public final SeekBar seekbarTarget;

  @NonNull
  public final LinearLayout statsChargeMainDisplayRoot;

  @NonNull
  public final NestedScrollView statsChargeScrollView;

  @NonNull
  public final LinearLayout targetChargeBlock;

  @NonNull
  public final TextView targetPercentLabel;

  @NonNull
  public final ConstraintLayout targetPercentageRoot;

  @NonNull
  public final TextView targetPercentageTitle;

  @NonNull
  public final LinearLayout timeEstimatesLayout;

  @NonNull
  public final TextView tvChargingStatus;

  @NonNull
  public final TextView tvCurrent;

  @NonNull
  public final TextView tvPercentage;

  @NonNull
  public final TextView tvPower;

  @NonNull
  public final TextView tvSessionDuration;

  @NonNull
  public final TextView tvSessionPercentageCharged;

  @NonNull
  public final TextView tvSessionStartTime;

  @NonNull
  public final TextView tvSessionTotalChargeMah;

  @NonNull
  public final TextView tvTargetPercentage;

  @NonNull
  public final TextView tvTemperature;

  @NonNull
  public final TextView tvTimeToFull;

  @NonNull
  public final TextView tvTimeToTarget;

  @NonNull
  public final TextView tvVoltage;

  private FragmentStatsChargeBinding(@NonNull NestedScrollView rootView,
      @NonNull TextView batteryAlarmBtn, @NonNull Button btnResetSession,
      @NonNull LinearLayout chargeCurrentSessionRoot,
      @NonNull CircularProgressIndicator chargeProgBarPercent,
      @NonNull LinearLayout chargingStatusBlock, @NonNull View dividerTarget,
      @NonNull LinearLayout fullChargeBlock,
      @NonNull LayoutBackNavigationBinding includeBackNavigation,
      @NonNull ShimmerFrameLayout nativeAd, @NonNull RelativeLayout percentInnerLayout,
      @NonNull RelativeLayout percentLayout, @NonNull SeekBar seekbarTarget,
      @NonNull LinearLayout statsChargeMainDisplayRoot,
      @NonNull NestedScrollView statsChargeScrollView, @NonNull LinearLayout targetChargeBlock,
      @NonNull TextView targetPercentLabel, @NonNull ConstraintLayout targetPercentageRoot,
      @NonNull TextView targetPercentageTitle, @NonNull LinearLayout timeEstimatesLayout,
      @NonNull TextView tvChargingStatus, @NonNull TextView tvCurrent,
      @NonNull TextView tvPercentage, @NonNull TextView tvPower,
      @NonNull TextView tvSessionDuration, @NonNull TextView tvSessionPercentageCharged,
      @NonNull TextView tvSessionStartTime, @NonNull TextView tvSessionTotalChargeMah,
      @NonNull TextView tvTargetPercentage, @NonNull TextView tvTemperature,
      @NonNull TextView tvTimeToFull, @NonNull TextView tvTimeToTarget,
      @NonNull TextView tvVoltage) {
    this.rootView = rootView;
    this.batteryAlarmBtn = batteryAlarmBtn;
    this.btnResetSession = btnResetSession;
    this.chargeCurrentSessionRoot = chargeCurrentSessionRoot;
    this.chargeProgBarPercent = chargeProgBarPercent;
    this.chargingStatusBlock = chargingStatusBlock;
    this.dividerTarget = dividerTarget;
    this.fullChargeBlock = fullChargeBlock;
    this.includeBackNavigation = includeBackNavigation;
    this.nativeAd = nativeAd;
    this.percentInnerLayout = percentInnerLayout;
    this.percentLayout = percentLayout;
    this.seekbarTarget = seekbarTarget;
    this.statsChargeMainDisplayRoot = statsChargeMainDisplayRoot;
    this.statsChargeScrollView = statsChargeScrollView;
    this.targetChargeBlock = targetChargeBlock;
    this.targetPercentLabel = targetPercentLabel;
    this.targetPercentageRoot = targetPercentageRoot;
    this.targetPercentageTitle = targetPercentageTitle;
    this.timeEstimatesLayout = timeEstimatesLayout;
    this.tvChargingStatus = tvChargingStatus;
    this.tvCurrent = tvCurrent;
    this.tvPercentage = tvPercentage;
    this.tvPower = tvPower;
    this.tvSessionDuration = tvSessionDuration;
    this.tvSessionPercentageCharged = tvSessionPercentageCharged;
    this.tvSessionStartTime = tvSessionStartTime;
    this.tvSessionTotalChargeMah = tvSessionTotalChargeMah;
    this.tvTargetPercentage = tvTargetPercentage;
    this.tvTemperature = tvTemperature;
    this.tvTimeToFull = tvTimeToFull;
    this.tvTimeToTarget = tvTimeToTarget;
    this.tvVoltage = tvVoltage;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentStatsChargeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentStatsChargeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_stats_charge, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentStatsChargeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.battery_alarm_btn;
      TextView batteryAlarmBtn = ViewBindings.findChildViewById(rootView, id);
      if (batteryAlarmBtn == null) {
        break missingId;
      }

      id = R.id.btn_reset_session;
      Button btnResetSession = ViewBindings.findChildViewById(rootView, id);
      if (btnResetSession == null) {
        break missingId;
      }

      id = R.id.charge_current_session_root;
      LinearLayout chargeCurrentSessionRoot = ViewBindings.findChildViewById(rootView, id);
      if (chargeCurrentSessionRoot == null) {
        break missingId;
      }

      id = R.id.charge_prog_bar_percent;
      CircularProgressIndicator chargeProgBarPercent = ViewBindings.findChildViewById(rootView, id);
      if (chargeProgBarPercent == null) {
        break missingId;
      }

      id = R.id.charging_status_block;
      LinearLayout chargingStatusBlock = ViewBindings.findChildViewById(rootView, id);
      if (chargingStatusBlock == null) {
        break missingId;
      }

      id = R.id.divider_target;
      View dividerTarget = ViewBindings.findChildViewById(rootView, id);
      if (dividerTarget == null) {
        break missingId;
      }

      id = R.id.full_charge_block;
      LinearLayout fullChargeBlock = ViewBindings.findChildViewById(rootView, id);
      if (fullChargeBlock == null) {
        break missingId;
      }

      id = R.id.include_back_navigation;
      View includeBackNavigation = ViewBindings.findChildViewById(rootView, id);
      if (includeBackNavigation == null) {
        break missingId;
      }
      LayoutBackNavigationBinding binding_includeBackNavigation = LayoutBackNavigationBinding.bind(includeBackNavigation);

      id = R.id.nativeAd;
      ShimmerFrameLayout nativeAd = ViewBindings.findChildViewById(rootView, id);
      if (nativeAd == null) {
        break missingId;
      }

      id = R.id.percent_inner_layout;
      RelativeLayout percentInnerLayout = ViewBindings.findChildViewById(rootView, id);
      if (percentInnerLayout == null) {
        break missingId;
      }

      id = R.id.percent_layout;
      RelativeLayout percentLayout = ViewBindings.findChildViewById(rootView, id);
      if (percentLayout == null) {
        break missingId;
      }

      id = R.id.seekbar_target;
      SeekBar seekbarTarget = ViewBindings.findChildViewById(rootView, id);
      if (seekbarTarget == null) {
        break missingId;
      }

      id = R.id.stats_charge_main_display_root;
      LinearLayout statsChargeMainDisplayRoot = ViewBindings.findChildViewById(rootView, id);
      if (statsChargeMainDisplayRoot == null) {
        break missingId;
      }

      NestedScrollView statsChargeScrollView = (NestedScrollView) rootView;

      id = R.id.target_charge_block;
      LinearLayout targetChargeBlock = ViewBindings.findChildViewById(rootView, id);
      if (targetChargeBlock == null) {
        break missingId;
      }

      id = R.id.target_percent_label;
      TextView targetPercentLabel = ViewBindings.findChildViewById(rootView, id);
      if (targetPercentLabel == null) {
        break missingId;
      }

      id = R.id.target_percentage_root;
      ConstraintLayout targetPercentageRoot = ViewBindings.findChildViewById(rootView, id);
      if (targetPercentageRoot == null) {
        break missingId;
      }

      id = R.id.target_percentage_title;
      TextView targetPercentageTitle = ViewBindings.findChildViewById(rootView, id);
      if (targetPercentageTitle == null) {
        break missingId;
      }

      id = R.id.time_estimates_layout;
      LinearLayout timeEstimatesLayout = ViewBindings.findChildViewById(rootView, id);
      if (timeEstimatesLayout == null) {
        break missingId;
      }

      id = R.id.tv_charging_status;
      TextView tvChargingStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvChargingStatus == null) {
        break missingId;
      }

      id = R.id.tv_current;
      TextView tvCurrent = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrent == null) {
        break missingId;
      }

      id = R.id.tv_percentage;
      TextView tvPercentage = ViewBindings.findChildViewById(rootView, id);
      if (tvPercentage == null) {
        break missingId;
      }

      id = R.id.tv_power;
      TextView tvPower = ViewBindings.findChildViewById(rootView, id);
      if (tvPower == null) {
        break missingId;
      }

      id = R.id.tv_session_duration;
      TextView tvSessionDuration = ViewBindings.findChildViewById(rootView, id);
      if (tvSessionDuration == null) {
        break missingId;
      }

      id = R.id.tv_session_percentage_charged;
      TextView tvSessionPercentageCharged = ViewBindings.findChildViewById(rootView, id);
      if (tvSessionPercentageCharged == null) {
        break missingId;
      }

      id = R.id.tv_session_start_time;
      TextView tvSessionStartTime = ViewBindings.findChildViewById(rootView, id);
      if (tvSessionStartTime == null) {
        break missingId;
      }

      id = R.id.tv_session_total_charge_mah;
      TextView tvSessionTotalChargeMah = ViewBindings.findChildViewById(rootView, id);
      if (tvSessionTotalChargeMah == null) {
        break missingId;
      }

      id = R.id.tv_target_percentage;
      TextView tvTargetPercentage = ViewBindings.findChildViewById(rootView, id);
      if (tvTargetPercentage == null) {
        break missingId;
      }

      id = R.id.tv_temperature;
      TextView tvTemperature = ViewBindings.findChildViewById(rootView, id);
      if (tvTemperature == null) {
        break missingId;
      }

      id = R.id.tv_time_to_full;
      TextView tvTimeToFull = ViewBindings.findChildViewById(rootView, id);
      if (tvTimeToFull == null) {
        break missingId;
      }

      id = R.id.tv_time_to_target;
      TextView tvTimeToTarget = ViewBindings.findChildViewById(rootView, id);
      if (tvTimeToTarget == null) {
        break missingId;
      }

      id = R.id.tv_voltage;
      TextView tvVoltage = ViewBindings.findChildViewById(rootView, id);
      if (tvVoltage == null) {
        break missingId;
      }

      return new FragmentStatsChargeBinding((NestedScrollView) rootView, batteryAlarmBtn,
          btnResetSession, chargeCurrentSessionRoot, chargeProgBarPercent, chargingStatusBlock,
          dividerTarget, fullChargeBlock, binding_includeBackNavigation, nativeAd,
          percentInnerLayout, percentLayout, seekbarTarget, statsChargeMainDisplayRoot,
          statsChargeScrollView, targetChargeBlock, targetPercentLabel, targetPercentageRoot,
          targetPercentageTitle, timeEstimatesLayout, tvChargingStatus, tvCurrent, tvPercentage,
          tvPower, tvSessionDuration, tvSessionPercentageCharged, tvSessionStartTime,
          tvSessionTotalChargeMah, tvTargetPercentage, tvTemperature, tvTimeToFull, tvTimeToTarget,
          tvVoltage);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
