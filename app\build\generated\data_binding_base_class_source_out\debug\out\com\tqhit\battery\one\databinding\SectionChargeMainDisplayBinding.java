// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SectionChargeMainDisplayBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout chargeMainDisplayRoot;

  @NonNull
  public final CircularProgressIndicator chargeProgBarPercent;

  @NonNull
  public final LinearLayout currentRateBlock;

  @NonNull
  public final LinearLayout fullChargeBlock;

  @NonNull
  public final RelativeLayout percentInnerLayout;

  @NonNull
  public final RelativeLayout percentLayout;

  @NonNull
  public final LinearLayout targetChargeBlock;

  @NonNull
  public final TextView textCurrentRate;

  @NonNull
  public final TextView textPercent;

  @NonNull
  public final TextView textTimeToFull;

  @NonNull
  public final TextView textTimeToTarget;

  @NonNull
  public final LinearLayout timeEstimatesLayout;

  private SectionChargeMainDisplayBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout chargeMainDisplayRoot,
      @NonNull CircularProgressIndicator chargeProgBarPercent,
      @NonNull LinearLayout currentRateBlock, @NonNull LinearLayout fullChargeBlock,
      @NonNull RelativeLayout percentInnerLayout, @NonNull RelativeLayout percentLayout,
      @NonNull LinearLayout targetChargeBlock, @NonNull TextView textCurrentRate,
      @NonNull TextView textPercent, @NonNull TextView textTimeToFull,
      @NonNull TextView textTimeToTarget, @NonNull LinearLayout timeEstimatesLayout) {
    this.rootView = rootView;
    this.chargeMainDisplayRoot = chargeMainDisplayRoot;
    this.chargeProgBarPercent = chargeProgBarPercent;
    this.currentRateBlock = currentRateBlock;
    this.fullChargeBlock = fullChargeBlock;
    this.percentInnerLayout = percentInnerLayout;
    this.percentLayout = percentLayout;
    this.targetChargeBlock = targetChargeBlock;
    this.textCurrentRate = textCurrentRate;
    this.textPercent = textPercent;
    this.textTimeToFull = textTimeToFull;
    this.textTimeToTarget = textTimeToTarget;
    this.timeEstimatesLayout = timeEstimatesLayout;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SectionChargeMainDisplayBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SectionChargeMainDisplayBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.section_charge_main_display, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SectionChargeMainDisplayBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      LinearLayout chargeMainDisplayRoot = (LinearLayout) rootView;

      id = R.id.charge_prog_bar_percent;
      CircularProgressIndicator chargeProgBarPercent = ViewBindings.findChildViewById(rootView, id);
      if (chargeProgBarPercent == null) {
        break missingId;
      }

      id = R.id.current_rate_block;
      LinearLayout currentRateBlock = ViewBindings.findChildViewById(rootView, id);
      if (currentRateBlock == null) {
        break missingId;
      }

      id = R.id.full_charge_block;
      LinearLayout fullChargeBlock = ViewBindings.findChildViewById(rootView, id);
      if (fullChargeBlock == null) {
        break missingId;
      }

      id = R.id.percent_inner_layout;
      RelativeLayout percentInnerLayout = ViewBindings.findChildViewById(rootView, id);
      if (percentInnerLayout == null) {
        break missingId;
      }

      id = R.id.percent_layout;
      RelativeLayout percentLayout = ViewBindings.findChildViewById(rootView, id);
      if (percentLayout == null) {
        break missingId;
      }

      id = R.id.target_charge_block;
      LinearLayout targetChargeBlock = ViewBindings.findChildViewById(rootView, id);
      if (targetChargeBlock == null) {
        break missingId;
      }

      id = R.id.text_current_rate;
      TextView textCurrentRate = ViewBindings.findChildViewById(rootView, id);
      if (textCurrentRate == null) {
        break missingId;
      }

      id = R.id.text_percent;
      TextView textPercent = ViewBindings.findChildViewById(rootView, id);
      if (textPercent == null) {
        break missingId;
      }

      id = R.id.text_time_to_full;
      TextView textTimeToFull = ViewBindings.findChildViewById(rootView, id);
      if (textTimeToFull == null) {
        break missingId;
      }

      id = R.id.text_time_to_target;
      TextView textTimeToTarget = ViewBindings.findChildViewById(rootView, id);
      if (textTimeToTarget == null) {
        break missingId;
      }

      id = R.id.time_estimates_layout;
      LinearLayout timeEstimatesLayout = ViewBindings.findChildViewById(rootView, id);
      if (timeEstimatesLayout == null) {
        break missingId;
      }

      return new SectionChargeMainDisplayBinding((LinearLayout) rootView, chargeMainDisplayRoot,
          chargeProgBarPercent, currentRateBlock, fullChargeBlock, percentInnerLayout,
          percentLayout, targetChargeBlock, textCurrentRate, textPercent, textTimeToFull,
          textTimeToTarget, timeEstimatesLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
