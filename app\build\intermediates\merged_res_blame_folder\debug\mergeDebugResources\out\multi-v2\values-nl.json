{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "167,268,269,270", "startColumns": "4,4,4,4", "startOffsets": "14753,25035,25136,25247", "endColumns": "102,100,110,98", "endOffsets": "14851,25131,25242,25341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "152", "startColumns": "4", "startOffsets": "13121", "endColumns": "142", "endOffsets": "13259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,108", "endOffsets": "161,270"}, "to": {"startLines": "436,437", "startColumns": "4,4", "startOffsets": "40179,40290", "endColumns": "110,108", "endOffsets": "40285,40394"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,444,568,675,838,961,1080,1182,1356,1458,1623,1745,1904,2082,2146,2205", "endColumns": "103,146,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,443,567,674,837,960,1079,1181,1355,1457,1622,1744,1903,2081,2145,2204,2279"}, "to": {"startLines": "144,145,146,147,148,149,150,151,153,154,155,156,157,158,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12100,12208,12359,12487,12598,12765,12892,13015,13264,13442,13548,13717,13843,14006,14188,14256,14319", "endColumns": "107,150,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "12203,12354,12482,12593,12760,12887,13010,13116,13437,13543,13712,13838,14001,14183,14251,14314,14393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,415,533,633,731,846,998,1119,1261,1346,1445,1541,1644,1762,1883,1987,2118,2246,2382,2560,2691,2811,2932,3067,3164,3264,3384,3513,3613,3720,3823,3960,4100,4206,4310,4394,4494,4591,4702,4789,4876,4981,5061,5144,5243,5347,5442,5541,5629,5739,5840,5945,6065,6145,6246", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,110,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "172,289,410,528,628,726,841,993,1114,1256,1341,1440,1536,1639,1757,1878,1982,2113,2241,2377,2555,2686,2806,2927,3062,3159,3259,3379,3508,3608,3715,3818,3955,4095,4201,4305,4389,4489,4586,4697,4784,4871,4976,5056,5139,5238,5342,5437,5536,5624,5734,5835,5940,6060,6140,6241,6336"}, "to": {"startLines": "311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28724,28846,28963,29084,29202,29302,29400,29515,29667,29788,29930,30015,30114,30210,30313,30431,30552,30656,30787,30915,31051,31229,31360,31480,31601,31736,31833,31933,32053,32182,32282,32389,32492,32629,32769,32875,32979,33063,33163,33260,33371,33458,33545,33650,33730,33813,33912,34016,34111,34210,34298,34408,34509,34614,34734,34814,34915", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,110,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "28841,28958,29079,29197,29297,29395,29510,29662,29783,29925,30010,30109,30205,30308,30426,30547,30651,30782,30910,31046,31224,31355,31475,31596,31731,31828,31928,32048,32177,32277,32384,32487,32624,32764,32870,32974,33058,33158,33255,33366,33453,33540,33645,33725,33808,33907,34011,34106,34205,34293,34403,34504,34609,34729,34809,34910,35005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,85", "endOffsets": "136,222"}, "to": {"startLines": "542,543", "startColumns": "4,4", "startOffsets": "49197,49283", "endColumns": "85,85", "endOffsets": "49278,49364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,990,1073,1225,1297,1368,1452,1522", "endColumns": "91,82,96,98,84,75,95,86,88,80,82,76,71,70,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,985,1068,1145,1292,1363,1447,1517,1637"}, "to": {"startLines": "142,143,180,181,204,294,296,438,444,487,488,508,526,527,533,534,536", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11925,12017,17222,17319,20347,27184,27327,40399,40875,44502,44583,46044,47808,47880,48405,48489,48611", "endColumns": "91,82,96,98,84,75,95,86,88,80,82,76,71,70,83,69,119", "endOffsets": "12012,12095,17314,17413,20427,27255,27418,40481,40959,44578,44661,46116,47875,47946,48484,48554,48726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,267,348,494,663,743", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "172,262,343,489,658,738,815"}, "to": {"startLines": "166,265,445,480,531,550,551", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14681,24792,40964,43919,48179,49722,49802", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "14748,24877,41040,44060,48343,49797,49874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,254,321,398,467,556,639", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "121,185,249,316,393,462,551,634,706"}, "to": {"startLines": "237,238,239,240,241,242,243,244,245", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "22955,23026,23090,23154,23221,23298,23367,23456,23539", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "23021,23085,23149,23216,23293,23362,23451,23534,23606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-nl\\values-nl.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3336,3401", "endColumns": "64,65", "endOffsets": "3396,3462"}, "to": {"startLines": "257,258", "startColumns": "4,4", "startOffsets": "24273,24338", "endColumns": "64,65", "endOffsets": "24333,24399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1066,1130,1219,1298,1361,1454,1516,1582,1640,1713,1777,1833,1955,2012,2074,2130,2206,2340,2425,2504,2602,2688,2774,2912,2993,3072,3196,3286,3363,3420,3471,3537,3615,3698,3769,3845,3920,3999,4072,4143,4252,4346,4424,4513,4603,4677,4758,4845,4898,4977,5044,5125,5209,5271,5335,5398,5469,5577,5689,5791,5902,5963,6018,6099,6182,6258", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "264,350,432,509,607,701,798,920,1001,1061,1125,1214,1293,1356,1449,1511,1577,1635,1708,1772,1828,1950,2007,2069,2125,2201,2335,2420,2499,2597,2683,2769,2907,2988,3067,3191,3281,3358,3415,3466,3532,3610,3693,3764,3840,3915,3994,4067,4138,4247,4341,4419,4508,4598,4672,4753,4840,4893,4972,5039,5120,5204,5266,5330,5393,5464,5572,5684,5786,5897,5958,6013,6094,6177,6253,6325"}, "to": {"startLines": "19,106,107,108,109,110,126,127,141,210,212,267,291,300,372,373,374,375,376,377,378,379,380,381,382,383,384,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,469,505,506,517", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "772,8951,9037,9119,9196,9294,10642,10739,11844,20738,20894,24946,26967,27598,35171,35264,35326,35392,35450,35523,35587,35643,35765,35822,35884,35940,36016,36283,36368,36447,36545,36631,36717,36855,36936,37015,37139,37229,37306,37363,37414,37480,37558,37641,37712,37788,37863,37942,38015,38086,38195,38289,38367,38456,38546,38620,38701,38788,38841,38920,38987,39068,39152,39214,39278,39341,39412,39520,39632,39734,39845,39906,43259,45826,45909,46854", "endLines": "22,106,107,108,109,110,126,127,141,210,212,267,291,300,372,373,374,375,376,377,378,379,380,381,382,383,384,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,469,505,506,517", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "936,9032,9114,9191,9289,9383,10734,10856,11920,20793,20953,25030,27041,27656,35259,35321,35387,35445,35518,35582,35638,35760,35817,35879,35935,36011,36145,36363,36442,36540,36626,36712,36850,36931,37010,37134,37224,37301,37358,37409,37475,37553,37636,37707,37783,37858,37937,38010,38081,38190,38284,38362,38451,38541,38615,38696,38783,38836,38915,38982,39063,39147,39209,39273,39336,39407,39515,39627,39729,39840,39901,39956,43335,45904,45980,46921"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,520,624,898,990,1084,1179,1273,1373,1467,1563,1658,1750,2035,2466,2569,2827", "endColumns": "117,104,103,119,91,93,94,93,99,93,95,94,91,91,102,102,154,82", "endOffsets": "218,323,619,739,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,2133,2564,2719,2905"}, "to": {"startLines": "39,40,43,44,47,48,49,50,51,52,53,54,55,56,59,63,64,504", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1794,1912,2150,2254,2469,2561,2655,2750,2844,2944,3038,3134,3229,3321,3546,3878,3981,45743", "endColumns": "117,104,103,119,91,93,94,93,99,93,95,94,91,91,102,102,154,82", "endOffsets": "1907,2012,2249,2369,2556,2650,2745,2839,2939,3033,3129,3224,3316,3408,3644,3976,4131,45821"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "116,117,118,119,120,121,122,528", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9718,9820,9922,10022,10122,10229,10333,47951", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "9815,9917,10017,10117,10224,10328,10447,48047"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,486,677,766,854,934,1027,1120,1193,1260,1362,1460,1528,1595,1660,1729,1848,1967,2082,2155,2234,2309,2378,2461,2543,2609,2674,2727,2785,2833,2894,2959,3021,3086,3154,3212,3270,3336,3388,3450,3526,3602,3657", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,118,118,114,72,78,74,68,82,81,65,64,52,57,47,60,64,61,64,67,57,57,65,51,61,75,75,54,66", "endOffsets": "281,481,672,761,849,929,1022,1115,1188,1255,1357,1455,1523,1590,1655,1724,1843,1962,2077,2150,2229,2304,2373,2456,2538,2604,2669,2722,2780,2828,2889,2954,3016,3081,3149,3207,3265,3331,3383,3445,3521,3597,3652,3719"}, "to": {"startLines": "2,11,15,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,246,247,248,249,250,251,252,253,254,255,256,259,260,261,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,581,20958,21047,21135,21215,21308,21401,21474,21541,21643,21741,21809,21876,21941,22010,22129,22248,22363,22436,22515,22590,22659,22742,22824,22890,23611,23664,23722,23770,23831,23896,23958,24023,24091,24149,24207,24404,24456,24518,24594,24670,24725", "endLines": "10,14,18,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,246,247,248,249,250,251,252,253,254,255,256,259,260,261,262,263,264", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,118,118,114,72,78,74,68,82,81,65,64,52,57,47,60,64,61,64,67,57,57,65,51,61,75,75,54,66", "endOffsets": "376,576,767,21042,21130,21210,21303,21396,21469,21536,21638,21736,21804,21871,21936,22005,22124,22243,22358,22431,22510,22585,22654,22737,22819,22885,22950,23659,23717,23765,23826,23891,23953,24018,24086,24144,24202,24268,24451,24513,24589,24665,24720,24787"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-nl\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,267,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,268,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,269,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "123,190,241,290,337,385,438,488,535,590,648,700,751,800,848,21631,900,978,1035,1084,1132,1185,1267,1337,1423,1499,1573,1638,24174,1690,1730,1884,1941,2027,2070,2132,21837,22644,23086,23217,2218,57,24127,22029,24000,2262,2312,2351,2438,2501,2575,2985,3402,12196,12262,11515,11427,12327,3444,3516,3593,3799,4120,4195,4257,4299,4360,4436,4489,21723,4559,4626,21961,4672,4751,4795,4866,5042,5105,5197,5279,5349,5436,22522,5510,5566,5623,5683,5745,5793,5866,5970,6021,6395,6450,6510,7069,7649,7720,7768,7902,7971,8296,8348,8880,8943,9082,9250,9396,9460,9506,9554,9621,9717,9807,9858,10015,10110,10166,10231,10297,10593,10857,11047,11200,11299,12414,12485,23805,12539,12579,12676,12741,12799,13029,13078,13158,13222,13282,13442,13501,13655,13907,13947,14006,14044,14082,14118,14154,14226,14273,14331,14382,14447,23868,14522,14594,24298,14643,14686,14727,14802,15217,15272,15345,15526,15600,15679,15716,15751,15787,15825,15881,15946,16016,16074,16155,16291,21885,16327,16392,16442,16531,16602,16678,16774,16883,16954,16990,17062,22336,22156,22074,22991,23543,17351,17391,17441,17492,17556,17640,23140,17689,17769,17819,21792,17863,23936,24355,17969,18055,18143,18200,18273,18489,18560,18625,22441,18685,18736,23592,18796,18872,18968,19042,19093,22924,19179,19257,19303,19362,19410,19537,24409,19942,20018,20064,20120,20193,20251,20304,24067,20345,20411,20627,20715,20779,20846,20902,20978,21040,21074,22586,21131,21190,21236,21273,21330,21387,21460,21514,21550,21586", "endColumns": "65,49,47,45,46,51,48,45,53,56,50,49,47,46,50,90,76,55,47,46,51,80,68,84,74,72,63,50,75,38,152,55,84,41,60,84,46,278,52,324,42,64,45,43,65,48,37,85,61,72,408,415,40,64,63,679,86,83,70,75,204,319,73,60,40,59,74,51,68,67,65,44,66,77,42,69,174,61,90,80,68,85,72,62,54,55,58,60,46,71,102,49,372,53,58,557,578,69,46,132,67,323,50,530,61,137,166,144,62,44,46,65,94,88,49,155,93,54,63,64,294,262,188,151,97,81,69,52,61,38,95,63,56,228,47,78,62,58,158,57,152,250,38,57,36,36,34,34,70,45,56,49,63,73,66,70,47,55,41,39,73,413,53,71,179,72,77,35,33,34,36,54,63,68,56,79,134,34,74,63,48,87,69,74,94,107,69,34,70,287,103,178,80,93,47,38,48,49,62,82,47,75,78,48,42,43,104,62,52,84,86,55,71,214,69,63,58,79,49,58,211,74,94,72,49,84,65,76,44,57,46,125,403,81,74,44,54,71,56,51,39,58,64,214,86,62,65,54,74,60,32,55,56,57,44,35,55,55,71,52,34,34,43", "endOffsets": "184,235,284,331,379,432,482,529,584,642,694,745,794,842,894,21717,972,1029,1078,1126,1179,1261,1331,1417,1493,1567,1632,1684,24245,1724,1878,1935,2021,2064,2126,2212,21879,22918,23134,23537,2256,117,24168,22068,24061,2306,2345,2432,2495,2569,2979,3396,3438,12256,12321,12190,11509,12406,3510,3587,3793,4114,4189,4251,4293,4354,4430,4483,4553,21786,4620,4666,22023,4745,4789,4860,5036,5099,5191,5273,5343,5430,5504,22580,5560,5617,5677,5739,5787,5860,5964,6015,6389,6444,6504,7063,7643,7714,7762,7896,7965,8290,8342,8874,8937,9076,9244,9390,9454,9500,9548,9615,9711,9801,9852,10009,10104,10160,10225,10291,10587,10851,11041,11194,11293,11376,12479,12533,23862,12573,12670,12735,12793,13023,13072,13152,13216,13276,13436,13495,13649,13901,13941,14000,14038,14076,14112,14148,14220,14267,14325,14376,14441,14516,23930,14588,14637,24349,14680,14721,14796,15211,15266,15339,15520,15594,15673,15710,15745,15781,15819,15875,15940,16010,16068,16149,16285,16321,21955,16386,16436,16525,16596,16672,16768,16877,16948,16984,17056,17345,22435,22330,22150,23080,23586,17385,17435,17486,17550,17634,17683,23211,17763,17813,17857,21831,17963,23994,24403,18049,18137,18194,18267,18483,18554,18619,18679,22516,18730,18790,23799,18866,18962,19036,19087,19173,22985,19251,19297,19356,19404,19531,19936,24486,20012,20058,20114,20187,20245,20298,20339,24121,20405,20621,20709,20773,20840,20896,20972,21034,21068,21125,22638,21184,21230,21267,21324,21381,21454,21508,21544,21580,21625"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,45,46,57,58,60,61,62,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,111,112,113,114,115,123,124,125,128,129,130,131,132,133,134,135,136,137,138,139,140,162,163,164,165,168,169,170,171,172,173,174,175,176,177,178,179,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,205,206,207,208,209,211,266,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,292,293,295,297,298,299,301,302,303,304,305,306,307,308,309,310,368,369,370,371,385,386,432,439,440,441,442,443,446,447,448,449,450,451,452,453,462,463,464,465,466,467,468,470,471,472,473,474,475,476,477,478,479,481,482,483,484,485,486,489,490,491,492,493,494,502,503,507,509,510,511,512,513,514,515,516,518,519,520,521,522,523,524,525,529,530,532,535,537,538,539,540,541,544,545,546,547,548,549,552,553,554,555,557,558,559,560,561,562,563,564", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "941,1007,1057,1105,1151,1198,1250,1299,1345,1399,1456,1507,1557,1605,1652,1703,2017,2094,2374,2422,3413,3465,3649,3718,3803,4136,4209,4273,4324,4400,4439,4592,4648,4733,4775,4836,4921,4968,5247,5300,5625,5668,5733,5779,5823,5889,5938,5976,6062,6124,6197,6606,7022,7063,7128,7192,7872,7959,8043,8114,8190,8395,8715,8789,8850,8891,9388,9463,9515,9584,9652,10452,10497,10564,10861,10904,10974,11149,11211,11302,11383,11452,11538,11611,11674,11729,11785,14398,14459,14506,14578,14856,14906,15279,15333,15392,15950,16529,16599,16646,16779,16847,17171,17418,17949,18011,18149,18316,18461,18524,18569,18616,18682,18777,18866,18916,19072,19166,19221,19285,19350,19645,19908,20097,20249,20432,20514,20584,20637,20699,20798,24882,25346,25403,25632,25680,25759,25822,25881,26040,26098,26251,26502,26541,26599,26636,26673,26708,26743,26814,26860,26917,27046,27110,27260,27423,27494,27542,27661,27703,27743,27817,28231,28285,28357,28537,28610,28688,35010,35044,35079,35116,36150,36214,39961,40486,40566,40701,40736,40811,41045,41094,41182,41252,41327,41422,41530,41600,42394,42465,42753,42857,43036,43117,43211,43340,43379,43428,43478,43541,43624,43672,43748,43827,43876,44065,44109,44214,44277,44330,44415,44666,44722,44794,45009,45079,45143,45613,45693,45985,46121,46333,46408,46503,46576,46626,46711,46777,46926,46971,47029,47076,47202,47606,47688,47763,48052,48107,48348,48559,48731,48771,48830,48895,49110,49369,49432,49498,49553,49628,49689,49879,49935,49992,50050,50155,50191,50247,50303,50375,50428,50463,50498", "endColumns": "65,49,47,45,46,51,48,45,53,56,50,49,47,46,50,90,76,55,47,46,51,80,68,84,74,72,63,50,75,38,152,55,84,41,60,84,46,278,52,324,42,64,45,43,65,48,37,85,61,72,408,415,40,64,63,679,86,83,70,75,204,319,73,60,40,59,74,51,68,67,65,44,66,77,42,69,174,61,90,80,68,85,72,62,54,55,58,60,46,71,102,49,372,53,58,557,578,69,46,132,67,323,50,530,61,137,166,144,62,44,46,65,94,88,49,155,93,54,63,64,294,262,188,151,97,81,69,52,61,38,95,63,56,228,47,78,62,58,158,57,152,250,38,57,36,36,34,34,70,45,56,49,63,73,66,70,47,55,41,39,73,413,53,71,179,72,77,35,33,34,36,54,63,68,56,79,134,34,74,63,48,87,69,74,94,107,69,34,70,287,103,178,80,93,47,38,48,49,62,82,47,75,78,48,42,43,104,62,52,84,86,55,71,214,69,63,58,79,49,58,211,74,94,72,49,84,65,76,44,57,46,125,403,81,74,44,54,71,56,51,39,58,64,214,86,62,65,54,74,60,32,55,56,57,44,35,55,55,71,52,34,34,43", "endOffsets": "1002,1052,1100,1146,1193,1245,1294,1340,1394,1451,1502,1552,1600,1647,1698,1789,2089,2145,2417,2464,3460,3541,3713,3798,3873,4204,4268,4319,4395,4434,4587,4643,4728,4770,4831,4916,4963,5242,5295,5620,5663,5728,5774,5818,5884,5933,5971,6057,6119,6192,6601,7017,7058,7123,7187,7867,7954,8038,8109,8185,8390,8710,8784,8845,8886,8946,9458,9510,9579,9647,9713,10492,10559,10637,10899,10969,11144,11206,11297,11378,11447,11533,11606,11669,11724,11780,11839,14454,14501,14573,14676,14901,15274,15328,15387,15945,16524,16594,16641,16774,16842,17166,17217,17944,18006,18144,18311,18456,18519,18564,18611,18677,18772,18861,18911,19067,19161,19216,19280,19345,19640,19903,20092,20244,20342,20509,20579,20632,20694,20733,20889,24941,25398,25627,25675,25754,25817,25876,26035,26093,26246,26497,26536,26594,26631,26668,26703,26738,26809,26855,26912,26962,27105,27179,27322,27489,27537,27593,27698,27738,27812,28226,28280,28352,28532,28605,28683,28719,35039,35074,35111,35166,36209,36278,40013,40561,40696,40731,40806,40870,41089,41177,41247,41322,41417,41525,41595,41630,42460,42748,42852,43031,43112,43206,43254,43374,43423,43473,43536,43619,43667,43743,43822,43871,43914,44104,44209,44272,44325,44410,44497,44717,44789,45004,45074,45138,45197,45688,45738,46039,46328,46403,46498,46571,46621,46706,46772,46849,46966,47024,47071,47197,47601,47683,47758,47803,48102,48174,48400,48606,48766,48825,48890,49105,49192,49427,49493,49548,49623,49684,49717,49930,49987,50045,50090,50186,50242,50298,50370,50423,50458,50493,50537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,249,293,580,643,756,873,990,1040,1100,1219,1307,1353,1443,1481,1517,1566,1647,1690", "endColumns": "49,43,54,62,112,116,116,49,59,118,87,45,89,37,35,48,80,42,55", "endOffsets": "248,292,347,642,755,872,989,1039,1099,1218,1306,1352,1442,1480,1516,1565,1646,1689,1745"}, "to": {"startLines": "433,434,435,454,455,456,457,458,459,460,461,495,496,497,498,499,500,501,556", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "40018,40072,40120,41635,41702,41819,41940,42061,42115,42179,42302,45202,45252,45346,45388,45428,45481,45566,50095", "endColumns": "53,47,58,66,116,120,120,53,63,122,91,49,93,41,39,52,84,46,59", "endOffsets": "40067,40115,40174,41697,41814,41935,42056,42110,42174,42297,42389,45247,45341,45383,45423,45476,45561,45608,50150"}}]}]}