// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSlideLayout2Binding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RelativeLayout button;

  @NonNull
  public final Button confirmAndContinueButton;

  @NonNull
  public final Button nextPage;

  @NonNull
  public final Button privacyPolicyButton;

  @NonNull
  public final TextView textView4;

  @NonNull
  public final TextView underText;

  private ItemSlideLayout2Binding(@NonNull LinearLayout rootView, @NonNull RelativeLayout button,
      @NonNull Button confirmAndContinueButton, @NonNull Button nextPage,
      @NonNull Button privacyPolicyButton, @NonNull TextView textView4,
      @NonNull TextView underText) {
    this.rootView = rootView;
    this.button = button;
    this.confirmAndContinueButton = confirmAndContinueButton;
    this.nextPage = nextPage;
    this.privacyPolicyButton = privacyPolicyButton;
    this.textView4 = textView4;
    this.underText = underText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSlideLayout2Binding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSlideLayout2Binding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_slide_layout_2, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSlideLayout2Binding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button;
      RelativeLayout button = ViewBindings.findChildViewById(rootView, id);
      if (button == null) {
        break missingId;
      }

      id = R.id.confirm_and_continue_button;
      Button confirmAndContinueButton = ViewBindings.findChildViewById(rootView, id);
      if (confirmAndContinueButton == null) {
        break missingId;
      }

      id = R.id.next_page;
      Button nextPage = ViewBindings.findChildViewById(rootView, id);
      if (nextPage == null) {
        break missingId;
      }

      id = R.id.privacy_policy_button;
      Button privacyPolicyButton = ViewBindings.findChildViewById(rootView, id);
      if (privacyPolicyButton == null) {
        break missingId;
      }

      id = R.id.textView4;
      TextView textView4 = ViewBindings.findChildViewById(rootView, id);
      if (textView4 == null) {
        break missingId;
      }

      id = R.id.under_text;
      TextView underText = ViewBindings.findChildViewById(rootView, id);
      if (underText == null) {
        break missingId;
      }

      return new ItemSlideLayout2Binding((LinearLayout) rootView, button, confirmAndContinueButton,
          nextPage, privacyPolicyButton, textView4, underText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
