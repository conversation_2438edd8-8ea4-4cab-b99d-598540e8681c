<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_centerInParent="false">
    <RelativeLayout
        android:background="@drawable/white_block"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:animateLayoutChanges="true"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:elevation="25dp">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true">
            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <RelativeLayout
                    android:id="@+id/strelka"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignTop="@+id/textView20"
                    android:layout_alignBottom="@+id/textView20"
                    android:layout_alignParentEnd="true">
                    <Button
                        android:id="@+id/exit"
                        android:background="@drawable/grey_block_line_up"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="45sp"
                        android:layout_height="match_parent"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_centerInParent="true"
                        android:layout_marginStart="5dp"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:orientation="vertical"
                        android:background="@drawable/ic_strelka"
                        android:visibility="visible"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4sp"
                        android:layout_marginBottom="4sp"
                        android:layout_centerInParent="true"
                        android:layout_marginStart="15sp"
                        android:layout_marginEnd="15sp"
                        android:layout_alignStart="@+id/exit"
                        android:layout_alignEnd="@+id/exit"/>
                </RelativeLayout>
                <TextView
                    android:textSize="18sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:gravity="start"
                    android:id="@+id/textView20"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/battery_level_alert"
                    android:singleLine="true"
                    android:layout_centerVertical="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:onClick="OnClick"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="2dp"
                    android:layout_toStartOf="@+id/strelka"
                    android:layout_alignParentStart="true"/>
            </RelativeLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/relativ33"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="7dp"
                android:animateLayoutChanges="true">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/charge_l"
                    android:background="@drawable/grey_block_line_up"
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:animateLayoutChanges="true"
                    app:layout_constraintBottom_toTopOf="@+id/linearLayout2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:ellipsize="marquee"
                        android:id="@+id/p192"
                        android:focusableInTouchMode="true"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12.5dp"
                        android:layout_marginBottom="12.5dp"
                        android:text="@string/low_battery_alarm"
                        android:singleLine="true"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:onClick="OnClick"
                        android:textAlignment="viewStart"
                        android:layout_marginStart="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/switch_low_alarm"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>
                    <androidx.appcompat.widget.SwitchCompat
                        android:layout_gravity="center_vertical"
                        android:id="@+id/switch_low_alarm"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:checked="false"
                        android:thumb="@drawable/switch_thumb"
                        android:layout_marginStart="5dp"
                        android:layout_marginEnd="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/p192"
                        app:layout_constraintTop_toTopOf="parent"
                        app:splitTrack="false"
                        app:track="@drawable/switch_track"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
                <LinearLayout
                    android:orientation="vertical"
                    android:id="@+id/relat223"
                    android:background="@drawable/grey_block_line_down"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/linearLayout2">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/text_buy_access2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp"
                        android:layout_marginBottom="7dp"
                        android:text="@string/low_battery_alarm_text"
                        android:textAlignment="viewStart"
                        android:layout_marginStart="9dp"
                        android:layout_marginEnd="7dp"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/linearLayout2"
                    android:visibility="gone"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    app:layout_constraintBottom_toTopOf="@+id/relat223"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/charge_l">
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:background="@drawable/grey_block"
                        android:paddingTop="5dp"
                        android:paddingBottom="5dp"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1">
                        <ProgressBar
                            android:id="@+id/progressbar_low_alarm"
                            android:visibility="visible"
                            android:layout_width="0dp"
                            android:layout_height="6dp"
                            android:max="40"
                            android:progress="30"
                            android:progressDrawable="@drawable/progress_bar"
                            android:layout_centerHorizontal="true"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="8dp"
                            android:layout_marginEnd="8dp"
                            app:layout_constraintBottom_toBottomOf="@+id/seekBar_low_alarm"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/seekBar_low_alarm"
                            style="?android:attr/progressBarStyleHorizontal"/>
                        <SeekBar
                            android:id="@+id/seekBar_low_alarm"
                            android:focusable="auto"
                            android:visibility="visible"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:max="40"
                            android:progress="30"
                            android:progressDrawable="@drawable/seek_bar"
                            android:thumb="@drawable/thumb"
                            android:paddingStart="8dp"
                            android:paddingEnd="8dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"/>
                    </androidx.constraintlayout.widget.ConstraintLayout>
                    <LinearLayout
                        android:gravity="center"
                        android:background="@drawable/circle"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp"
                        android:visibility="visible"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:paddingStart="12dp"
                        android:paddingEnd="12dp"
                        android:layout_marginStart="5dp">
                        <TextView
                            android:textSize="20sp"
                            android:textColor="?attr/colorr"
                            android:id="@+id/low_alarm_percent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="70%"/>
                    </LinearLayout>
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/p6"
                android:background="@drawable/grey_block"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="7dp"
                android:animateLayoutChanges="true"
                app:layout_constraintStart_toStartOf="parent">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/p_112"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/enable_vibration"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:onClick="OnClick"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switch_vibration"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:layout_gravity="center_vertical"
                    android:id="@+id/switch_vibration"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/p_112"
                    app:layout_constraintTop_toTopOf="parent"
                    app:splitTrack="false"
                    app:track="@drawable/switch_track"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/p5"
                android:background="@drawable/grey_block"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="7dp"
                android:animateLayoutChanges="true"
                app:layout_constraintStart_toStartOf="parent">
                <LinearLayout
                    android:orientation="vertical"
                    android:id="@+id/dont_disturb_from_layout"
                    android:background="@drawable/grey_block_line_up"
                    android:visibility="gone"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/p_11"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/do_not_disturb"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:onClick="OnClick"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switch_dont_disturb"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:layout_gravity="center_vertical"
                    android:id="@+id/switch_dont_disturb"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/p_11"
                    app:layout_constraintTop_toTopOf="parent"
                    app:splitTrack="false"
                    app:track="@drawable/switch_track"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <LinearLayout
                android:orientation="vertical"
                android:id="@+id/dont_disturb_until_layout"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:focusable="true"
                    android:visibility="visible"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp"
                    android:animateLayoutChanges="true"
                    app:layout_constraintStart_toStartOf="parent">
                    <Button
                        android:id="@+id/dont_disturb_from"
                        android:background="@drawable/grey_block_line"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/p_12er"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12.5dp"
                        android:layout_marginBottom="12.5dp"
                        android:text="@string/do_not_disturb_from"
                        android:singleLine="true"
                        android:textAlignment="viewStart"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/p_12er1"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/colorr"
                        android:gravity="end"
                        android:id="@+id/p_12er1"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:layout_marginTop="12.5dp"
                        android:layout_marginBottom="12.5dp"
                        android:text="22:00 PM"
                        android:singleLine="true"
                        android:layout_marginEnd="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/p_12er"
                        app:layout_constraintTop_toTopOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/dont_disturb_until"
                    android:background="@drawable/grey_block_line_down"
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp"
                    android:animateLayoutChanges="true"
                    app:layout_constraintStart_toStartOf="parent">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/p_1221"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12.5dp"
                        android:layout_marginBottom="12.5dp"
                        android:text="@string/do_not_disturb_until"
                        android:singleLine="true"
                        android:textAlignment="viewStart"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/p_12e2"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/colorr"
                        android:id="@+id/p_12e2"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:layout_marginTop="12.5dp"
                        android:layout_marginBottom="12.5dp"
                        android:text="10:30"
                        android:singleLine="true"
                        android:layout_marginEnd="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/p_1221"
                        app:layout_constraintTop_toTopOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/button_charge_alarm"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="8dp"
                android:animateLayoutChanges="true"
                app:layout_constraintStart_toStartOf="parent">
                <Button
                    android:id="@+id/battery_4"
                    android:background="@drawable/grey_block"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0"
                    style="@style/Widget.AppCompat.Button.Borderless"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:gravity="center"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/bla"
                    android:singleLine="true"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>
