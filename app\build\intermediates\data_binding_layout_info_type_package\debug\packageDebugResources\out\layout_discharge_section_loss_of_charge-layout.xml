<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_discharge_section_loss_of_charge" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\layout_discharge_section_loss_of_charge.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/lossOfChargeRoot"><Targets><Target id="@+id/lossOfChargeRoot" tag="layout/layout_discharge_section_loss_of_charge_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="10" startOffset="0" endLine="243" endOffset="51"/></Target><Target id="@+id/loc_tv_title" view="TextView"><Expressions/><location startLine="26" startOffset="4" endLine="36" endOffset="50"/></Target><Target id="@+id/loc_iv_info_button" view="ImageView"><Expressions/><location startLine="39" startOffset="4" endLine="48" endOffset="50"/></Target><Target id="@+id/loc_cl_screen_on_consumption" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="52" startOffset="4" endLine="135" endOffset="55"/></Target><Target id="@+id/loc_tv_screen_on_title" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="73" endOffset="49"/></Target><Target id="@+id/loc_tv_screen_on_percentage_dropped" view="TextView"><Expressions/><location startLine="75" startOffset="8" endLine="84" endOffset="49"/></Target><Target id="@+id/loc_tv_screen_on_percentage_unit" view="TextView"><Expressions/><location startLine="87" startOffset="8" endLine="95" endOffset="49"/></Target><Target id="@+id/loc_tv_screen_on_time" view="TextView"><Expressions/><location startLine="97" startOffset="8" endLine="111" endOffset="91"/></Target><Target id="@+id/loc_tv_screen_on_mah_consumed" view="TextView"><Expressions/><location startLine="114" startOffset="8" endLine="123" endOffset="49"/></Target><Target id="@+id/loc_tv_screen_on_mah_unit" view="TextView"><Expressions/><location startLine="126" startOffset="8" endLine="134" endOffset="49"/></Target><Target id="@+id/loc_cl_screen_off_consumption" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="138" startOffset="4" endLine="221" endOffset="55"/></Target><Target id="@+id/loc_tv_screen_off_title" view="TextView"><Expressions/><location startLine="151" startOffset="8" endLine="159" endOffset="49"/></Target><Target id="@+id/loc_tv_screen_off_percentage_dropped" view="TextView"><Expressions/><location startLine="161" startOffset="8" endLine="170" endOffset="49"/></Target><Target id="@+id/loc_tv_screen_off_percentage_unit" view="TextView"><Expressions/><location startLine="173" startOffset="8" endLine="181" endOffset="49"/></Target><Target id="@+id/loc_tv_screen_off_time" view="TextView"><Expressions/><location startLine="183" startOffset="8" endLine="197" endOffset="92"/></Target><Target id="@+id/loc_tv_screen_off_mah_consumed" view="TextView"><Expressions/><location startLine="200" startOffset="8" endLine="209" endOffset="49"/></Target><Target id="@+id/loc_tv_screen_off_mah_unit" view="TextView"><Expressions/><location startLine="212" startOffset="8" endLine="220" endOffset="49"/></Target><Target id="@+id/loc_btn_app_power_consumption" view="TextView"><Expressions/><location startLine="224" startOffset="4" endLine="241" endOffset="80"/></Target></Targets></Layout>