package com.tqhit.battery.one.features.stats.health.domain;

import com.tqhit.battery.one.repository.BatteryRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class GetHealthHistoryUseCase_Factory implements Factory<GetHealthHistoryUseCase> {
  private final Provider<BatteryRepository> batteryRepositoryProvider;

  public GetHealthHistoryUseCase_Factory(Provider<BatteryRepository> batteryRepositoryProvider) {
    this.batteryRepositoryProvider = batteryRepositoryProvider;
  }

  @Override
  public GetHealthHistoryUseCase get() {
    return newInstance(batteryRepositoryProvider.get());
  }

  public static GetHealthHistoryUseCase_Factory create(
      Provider<BatteryRepository> batteryRepositoryProvider) {
    return new GetHealthHistoryUseCase_Factory(batteryRepositoryProvider);
  }

  public static GetHealthHistoryUseCase newInstance(BatteryRepository batteryRepository) {
    return new GetHealthHistoryUseCase(batteryRepository);
  }
}
