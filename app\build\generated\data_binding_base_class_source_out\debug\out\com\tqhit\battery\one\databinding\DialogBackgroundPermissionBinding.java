// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogBackgroundPermissionBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final Button btnAllow;

  @NonNull
  public final Button btnClose;

  @NonNull
  public final TextView dialogMessage;

  @NonNull
  public final TextView dialogTitle;

  @NonNull
  public final TextView dontKillMyAppLink;

  private DialogBackgroundPermissionBinding(@NonNull RelativeLayout rootView,
      @NonNull Button btnAllow, @NonNull Button btnClose, @NonNull TextView dialogMessage,
      @NonNull TextView dialogTitle, @NonNull TextView dontKillMyAppLink) {
    this.rootView = rootView;
    this.btnAllow = btnAllow;
    this.btnClose = btnClose;
    this.dialogMessage = dialogMessage;
    this.dialogTitle = dialogTitle;
    this.dontKillMyAppLink = dontKillMyAppLink;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogBackgroundPermissionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogBackgroundPermissionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_background_permission, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogBackgroundPermissionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_allow;
      Button btnAllow = ViewBindings.findChildViewById(rootView, id);
      if (btnAllow == null) {
        break missingId;
      }

      id = R.id.btn_close;
      Button btnClose = ViewBindings.findChildViewById(rootView, id);
      if (btnClose == null) {
        break missingId;
      }

      id = R.id.dialog_message;
      TextView dialogMessage = ViewBindings.findChildViewById(rootView, id);
      if (dialogMessage == null) {
        break missingId;
      }

      id = R.id.dialog_title;
      TextView dialogTitle = ViewBindings.findChildViewById(rootView, id);
      if (dialogTitle == null) {
        break missingId;
      }

      id = R.id.dont_kill_my_app_link;
      TextView dontKillMyAppLink = ViewBindings.findChildViewById(rootView, id);
      if (dontKillMyAppLink == null) {
        break missingId;
      }

      return new DialogBackgroundPermissionBinding((RelativeLayout) rootView, btnAllow, btnClose,
          dialogMessage, dialogTitle, dontKillMyAppLink);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
