<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_centerInParent="false">
    <RelativeLayout
        android:background="@drawable/white_block"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:elevation="25dp">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <RelativeLayout
                    android:id="@+id/strelka"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignTop="@+id/textView20"
                    android:layout_alignBottom="@+id/textView20"
                    android:layout_alignParentEnd="true">
                    <Button
                        android:id="@+id/exit_theme"
                        android:background="@drawable/grey_block_line_up"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="45sp"
                        android:layout_height="match_parent"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_centerInParent="true"
                        android:layout_marginStart="5dp"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:orientation="vertical"
                        android:background="@drawable/ic_strelka"
                        android:visibility="visible"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4sp"
                        android:layout_marginBottom="4sp"
                        android:layout_centerInParent="true"
                        android:layout_marginStart="15sp"
                        android:layout_marginEnd="15sp"
                        android:layout_alignStart="@+id/exit_theme"
                        android:layout_alignEnd="@+id/exit_theme"/>
                </RelativeLayout>
                <TextView
                    android:textSize="18sp"
                    android:textColor="?attr/black"
                    android:gravity="start"
                    android:id="@+id/textView20"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/choose_theme"
                    android:layout_centerVertical="true"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="2dp"
                    android:layout_toStartOf="@+id/strelka"
                    android:layout_alignParentStart="true"/>
            </RelativeLayout>
            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="7dp">
                <RelativeLayout
                    android:id="@+id/dfsdfb11"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="5dp">
                    <Button
                        android:id="@+id/auto"
                        android:background="@drawable/grey_block_line_up"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_alignTop="@+id/sdfgsdf1g"
                        android:layout_alignBottom="@+id/sdfgsdf1g"
                        android:layout_alignParentTop="false"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="vertical"
                        android:id="@+id/sdfgsdf1g"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/auto"/>
                    </LinearLayout>
                </RelativeLayout>
                <RelativeLayout
                    android:id="@+id/dfsdfb1"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <Button
                        android:id="@+id/light_theme"
                        android:background="@drawable/grey_block_line"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_alignTop="@+id/sdfgsdfg"
                        android:layout_alignBottom="@+id/sdfgsdfg"
                        android:layout_alignParentTop="false"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="vertical"
                        android:id="@+id/sdfgsdfg"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/light"/>
                    </LinearLayout>
                </RelativeLayout>
                <RelativeLayout
                    android:id="@+id/dgdsfg"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp">
                    <Button
                        android:id="@+id/dark_theme"
                        android:background="@drawable/grey_block_line"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_alignTop="@+id/czsx"
                        android:layout_alignBottom="@+id/czsx"
                        android:layout_alignParentTop="false"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="vertical"
                        android:id="@+id/czsx"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/black"/>
                    </LinearLayout>
                </RelativeLayout>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp">
                    <Button
                        android:id="@+id/amoled_theme"
                        android:background="@drawable/grey_block_line"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_alignTop="@+id/rgrtjk"
                        android:layout_alignBottom="@+id/rgrtjk"
                        android:layout_alignParentTop="false"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="vertical"
                        android:id="@+id/rgrtjk"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/amoled"/>
                    </LinearLayout>
                </RelativeLayout>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp">
                    <Button
                        android:id="@+id/grey_theme"
                        android:background="@drawable/grey_block_line"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_alignTop="@+id/rg2rtwjk"
                        android:layout_alignBottom="@+id/rg2rtwjk"
                        android:layout_alignParentTop="false"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="vertical"
                        android:id="@+id/rg2rtwjk"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/grey"/>
                    </LinearLayout>
                </RelativeLayout>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp">
                    <Button
                        android:id="@+id/light_theme_inverted"
                        android:background="@drawable/grey_block_line"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_alignTop="@+id/f1221"
                        android:layout_alignBottom="@+id/f1221"
                        android:layout_alignParentTop="false"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="vertical"
                        android:id="@+id/f1221"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/light__nverted_res_0x7f130141"/>
                    </LinearLayout>
                </RelativeLayout>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp">
                    <Button
                        android:id="@+id/dark_theme_inverted"
                        android:background="@drawable/grey_block_line"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_alignTop="@+id/czsux"
                        android:layout_alignBottom="@+id/czsux"
                        android:layout_alignParentTop="false"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="vertical"
                        android:id="@+id/czsux"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/black_inverted"/>
                    </LinearLayout>
                </RelativeLayout>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp">
                    <Button
                        android:id="@+id/amoled_theme_inverted"
                        android:background="@drawable/grey_block_line"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_alignTop="@+id/rgrtwj1k"
                        android:layout_alignBottom="@+id/rgrtwj1k"
                        android:layout_alignParentTop="false"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="vertical"
                        android:id="@+id/rgrtwj1k"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/amoled_inverted"/>
                    </LinearLayout>
                </RelativeLayout>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp">
                    <Button
                        android:id="@+id/grey_theme_inverted"
                        android:background="@drawable/grey_block_line"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_alignTop="@+id/rgrtwjk"
                        android:layout_alignBottom="@+id/rgrtwjk"
                        android:layout_alignParentTop="false"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:gravity="center"
                        android:orientation="vertical"
                        android:id="@+id/rgrtwjk"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/grey_inverted"/>
                    </LinearLayout>
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>
