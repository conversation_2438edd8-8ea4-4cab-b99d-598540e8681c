package com.tqhit.battery.one.features.stats.discharge.repository;

import android.content.Context;
import com.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCache;
import com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver;
import com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator;
import com.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator;
import com.tqhit.battery.one.features.stats.discharge.domain.SessionManager;
import com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DischargeSessionRepository_Factory implements Factory<DischargeSessionRepository> {
  private final Provider<Context> contextProvider;

  private final Provider<CurrentSessionCache> currentSessionCacheProvider;

  private final Provider<SessionManager> sessionManagerProvider;

  private final Provider<GapEstimationCalculator> gapEstimationCalculatorProvider;

  private final Provider<FullSessionReEstimator> fullSessionReEstimatorProvider;

  private final Provider<SessionMetricsCalculator> sessionMetricsCalculatorProvider;

  private final Provider<ScreenStateReceiver> screenStateReceiverProvider;

  public DischargeSessionRepository_Factory(Provider<Context> contextProvider,
      Provider<CurrentSessionCache> currentSessionCacheProvider,
      Provider<SessionManager> sessionManagerProvider,
      Provider<GapEstimationCalculator> gapEstimationCalculatorProvider,
      Provider<FullSessionReEstimator> fullSessionReEstimatorProvider,
      Provider<SessionMetricsCalculator> sessionMetricsCalculatorProvider,
      Provider<ScreenStateReceiver> screenStateReceiverProvider) {
    this.contextProvider = contextProvider;
    this.currentSessionCacheProvider = currentSessionCacheProvider;
    this.sessionManagerProvider = sessionManagerProvider;
    this.gapEstimationCalculatorProvider = gapEstimationCalculatorProvider;
    this.fullSessionReEstimatorProvider = fullSessionReEstimatorProvider;
    this.sessionMetricsCalculatorProvider = sessionMetricsCalculatorProvider;
    this.screenStateReceiverProvider = screenStateReceiverProvider;
  }

  @Override
  public DischargeSessionRepository get() {
    return newInstance(contextProvider.get(), currentSessionCacheProvider.get(), sessionManagerProvider.get(), gapEstimationCalculatorProvider.get(), fullSessionReEstimatorProvider.get(), sessionMetricsCalculatorProvider.get(), screenStateReceiverProvider.get());
  }

  public static DischargeSessionRepository_Factory create(Provider<Context> contextProvider,
      Provider<CurrentSessionCache> currentSessionCacheProvider,
      Provider<SessionManager> sessionManagerProvider,
      Provider<GapEstimationCalculator> gapEstimationCalculatorProvider,
      Provider<FullSessionReEstimator> fullSessionReEstimatorProvider,
      Provider<SessionMetricsCalculator> sessionMetricsCalculatorProvider,
      Provider<ScreenStateReceiver> screenStateReceiverProvider) {
    return new DischargeSessionRepository_Factory(contextProvider, currentSessionCacheProvider, sessionManagerProvider, gapEstimationCalculatorProvider, fullSessionReEstimatorProvider, sessionMetricsCalculatorProvider, screenStateReceiverProvider);
  }

  public static DischargeSessionRepository newInstance(Context context,
      CurrentSessionCache currentSessionCache, SessionManager sessionManager,
      GapEstimationCalculator gapEstimationCalculator,
      FullSessionReEstimator fullSessionReEstimator,
      SessionMetricsCalculator sessionMetricsCalculator, ScreenStateReceiver screenStateReceiver) {
    return new DischargeSessionRepository(context, currentSessionCache, sessionManager, gapEstimationCalculator, fullSessionReEstimator, sessionMetricsCalculator, screenStateReceiver);
  }
}
