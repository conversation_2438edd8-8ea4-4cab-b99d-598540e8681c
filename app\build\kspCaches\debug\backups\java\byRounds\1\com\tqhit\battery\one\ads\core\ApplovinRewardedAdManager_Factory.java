package com.tqhit.battery.one.ads.core;

import com.tqhit.adlib.sdk.analytics.AnalyticsTracker;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ApplovinRewardedAdManager_Factory implements Factory<ApplovinRewardedAdManager> {
  private final Provider<AnalyticsTracker> analyticsTrackerProvider;

  public ApplovinRewardedAdManager_Factory(Provider<AnalyticsTracker> analyticsTrackerProvider) {
    this.analyticsTrackerProvider = analyticsTrackerProvider;
  }

  @Override
  public ApplovinRewardedAdManager get() {
    return newInstance(analyticsTrackerProvider.get());
  }

  public static ApplovinRewardedAdManager_Factory create(
      Provider<AnalyticsTracker> analyticsTrackerProvider) {
    return new ApplovinRewardedAdManager_Factory(analyticsTrackerProvider);
  }

  public static ApplovinRewardedAdManager newInstance(AnalyticsTracker analyticsTracker) {
    return new ApplovinRewardedAdManager(analyticsTracker);
  }
}
