{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "174,175,176,177,178,179,180,181,183,184,185,186,187,188,189,190,191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14855,14963,15126,15257,15365,15526,15659,15781,16051,16243,16352,16517,16649,16814,16971,17038,17107", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "14958,15121,15252,15360,15521,15654,15776,15886,16238,16347,16512,16644,16809,16966,17033,17102,17187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,263,378", "endColumns": "106,100,114,104", "endOffsets": "157,258,373,478"}, "to": {"startLines": "197,298,299,300", "startColumns": "4,4,4,4", "startOffsets": "17538,28176,28277,28392", "endColumns": "106,100,114,104", "endOffsets": "17640,28272,28387,28492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d148dadd36a9aeec86630cf6fa0b2687\\transformed\\jetified-applovin-sdk-13.3.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,185,269,337,505,636,714,796", "endColumns": "129,83,67,167,130,77,81,70", "endOffsets": "180,264,332,500,631,709,791,862"}, "to": {"startLines": "83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5840,5970,6054,6122,6290,6421,6499,6581", "endColumns": "129,83,67,167,130,77,81,70", "endOffsets": "5965,6049,6117,6285,6416,6494,6576,6647"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,174", "endColumns": "118,119", "endOffsets": "169,289"}, "to": {"startLines": "466,467", "startColumns": "4,4", "startOffsets": "43692,43811", "endColumns": "118,119", "endOffsets": "43806,43926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,418,539,639,739,856,999,1117,1265,1350,1457,1554,1656,1770,1888,2000,2138,2275,2419,2588,2724,2844,2966,3096,3194,3290,3411,3546,3649,3763,3878,4015,4156,4267,4372,4459,4555,4651,4767,4854,4940,5051,5134,5218,5319,5425,5525,5628,5717,5828,5929,6038,6157,6240,6357", "endColumns": "120,118,122,120,99,99,116,142,117,147,84,106,96,101,113,117,111,137,136,143,168,135,119,121,129,97,95,120,134,102,113,114,136,140,110,104,86,95,95,115,86,85,110,82,83,100,105,99,102,88,110,100,108,118,82,116,108", "endOffsets": "171,290,413,534,634,734,851,994,1112,1260,1345,1452,1549,1651,1765,1883,1995,2133,2270,2414,2583,2719,2839,2961,3091,3189,3285,3406,3541,3644,3758,3873,4010,4151,4262,4367,4454,4550,4646,4762,4849,4935,5046,5129,5213,5314,5420,5520,5623,5712,5823,5924,6033,6152,6235,6352,6461"}, "to": {"startLines": "341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "31997,32118,32237,32360,32481,32581,32681,32798,32941,33059,33207,33292,33399,33496,33598,33712,33830,33942,34080,34217,34361,34530,34666,34786,34908,35038,35136,35232,35353,35488,35591,35705,35820,35957,36098,36209,36314,36401,36497,36593,36709,36796,36882,36993,37076,37160,37261,37367,37467,37570,37659,37770,37871,37980,38099,38182,38299", "endColumns": "120,118,122,120,99,99,116,142,117,147,84,106,96,101,113,117,111,137,136,143,168,135,119,121,129,97,95,120,134,102,113,114,136,140,110,104,86,95,95,115,86,85,110,82,83,100,105,99,102,88,110,100,108,118,82,116,108", "endOffsets": "32113,32232,32355,32476,32576,32676,32793,32936,33054,33202,33287,33394,33491,33593,33707,33825,33937,34075,34212,34356,34525,34661,34781,34903,35033,35131,35227,35348,35483,35586,35700,35815,35952,36093,36204,36309,36396,36492,36588,36704,36791,36877,36988,37071,37155,37256,37362,37462,37565,37654,37765,37866,37975,38094,38177,38294,38403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,271,352,499,668,756", "endColumns": "69,95,80,146,168,87,81", "endOffsets": "170,266,347,494,663,751,833"}, "to": {"startLines": "196,295,475,510,561,626,627", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "17468,27918,44520,47681,52049,56741,56829", "endColumns": "69,95,80,146,168,87,81", "endOffsets": "17533,28009,44596,47823,52213,56824,56906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "572,573", "startColumns": "4,4", "startOffsets": "53083,53183", "endColumns": "99,101", "endOffsets": "53178,53280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,1010,1101,1254,1334,1410,1492,1562", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,79,75,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,1005,1096,1173,1329,1405,1487,1557,1678"}, "to": {"startLines": "172,173,210,211,234,324,326,468,474,517,518,538,556,557,563,564,566", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14677,14773,20178,20276,23414,30393,30543,43931,44433,48303,48389,49858,51650,51730,52275,52357,52479", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,79,75,81,69,120", "endOffsets": "14768,14850,20271,20374,23498,30467,30631,44018,44515,48384,48475,49930,51725,51801,52352,52422,52595"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,201,266,340,417,484,571,657", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "133,196,261,335,412,479,566,652,721"}, "to": {"startLines": "267,268,269,270,271,272,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "26038,26121,26184,26249,26323,26400,26467,26554,26640", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "26116,26179,26244,26318,26395,26462,26549,26635,26704"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-es\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,265,30,31,32,33,34,35,36,241,251,254,256,37,2,264,244,262,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,239,57,58,243,59,60,61,62,63,64,65,66,67,68,249,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,259,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,260,146,147,268,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,242,168,169,170,171,172,173,174,175,176,177,178,247,246,245,253,257,179,180,181,182,183,184,255,185,186,187,240,188,261,269,189,190,191,192,193,194,195,196,248,197,198,258,199,200,201,202,203,252,204,205,206,207,208,209,270,210,211,212,213,214,215,216,263,217,218,219,220,221,222,223,224,225,226,250,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "128,194,245,294,343,392,444,493,541,596,663,713,760,810,858,22501,910,989,1045,1098,1152,1205,1283,1353,1432,1511,1582,1657,25154,1717,1756,1923,1981,2072,2115,2177,22765,23599,24044,24175,2271,57,25108,22971,24982,2315,2362,2401,2495,2554,2629,3043,3466,12633,12699,11889,11800,12763,3511,3589,3665,3874,4203,4288,4349,4391,4452,4525,4576,22649,4645,4714,22896,4759,4839,4882,4955,5113,5184,5286,5372,5442,5535,23476,5611,5668,5724,5789,5844,5891,5961,6069,6121,6502,6557,6616,7242,7893,7970,8023,8181,8257,8559,8614,9140,9205,9351,9504,9654,9720,9769,9818,9895,10006,10098,10152,10308,10406,10462,10529,10597,10910,11198,11410,11575,11671,12850,12922,24769,12978,13019,13118,13186,13244,13481,13535,13620,13683,13752,13921,13984,14149,14398,14437,14495,14533,14571,14607,14643,14726,14768,14822,14879,14947,24837,15023,15093,25272,15152,15197,15238,15313,15789,15843,15919,16086,16168,16240,16277,16312,16353,16396,16456,16522,16591,16648,16728,16886,22814,16921,16981,17036,17135,17206,17280,17393,17510,17595,17640,17730,23284,23103,23014,23943,24521,18070,18110,18165,18216,18284,18375,24094,18425,18507,18556,22720,18600,24909,25329,18719,18813,18909,18960,19032,19235,19311,19371,23397,19434,19491,24570,19556,19652,19752,19847,19899,23874,19982,20064,20110,20166,20213,20337,25383,20724,20809,20856,20915,21000,21058,21111,25048,21151,21230,21442,21539,21604,21679,21732,21806,21884,21923,23543,21988,22056,22101,22143,22200,22260,22334,22384,22420,22456", "endColumns": "64,49,47,47,47,50,47,46,53,65,48,45,48,46,50,87,77,54,51,52,51,76,68,77,77,69,73,58,69,37,165,56,89,41,60,92,47,273,48,344,42,69,44,41,64,45,37,92,57,73,412,421,43,64,62,742,87,83,76,74,207,327,83,59,40,59,71,49,67,69,67,43,73,78,41,71,156,69,100,84,68,91,74,65,55,54,63,53,45,68,106,50,379,53,57,624,649,75,51,156,74,300,53,524,63,144,151,148,64,47,47,75,109,90,52,154,96,54,65,66,311,286,210,163,94,82,70,54,66,39,97,66,56,235,52,83,61,67,167,61,163,247,37,56,36,36,34,34,81,40,52,55,66,74,70,68,57,55,43,39,73,474,52,74,165,80,70,35,33,39,41,58,64,67,55,78,156,33,80,58,53,97,69,72,111,115,83,43,88,338,111,179,87,99,47,38,53,49,66,89,48,79,80,47,42,43,117,71,52,92,94,49,70,201,74,58,61,77,55,63,197,94,98,93,50,81,67,80,44,54,45,122,385,84,83,45,57,83,56,51,38,58,77,210,95,63,73,51,72,76,37,63,54,66,43,40,55,58,72,48,34,34,43", "endOffsets": "188,239,288,337,386,438,487,535,590,657,707,754,804,852,904,22584,983,1039,1092,1146,1199,1277,1347,1426,1505,1576,1651,1711,25219,1750,1917,1975,2066,2109,2171,2265,22808,23868,24088,24515,2309,122,25148,23008,25042,2356,2395,2489,2548,2623,3037,3460,3505,12693,12757,12627,11883,12842,3583,3659,3868,4197,4282,4343,4385,4446,4519,4570,4639,22714,4708,4753,22965,4833,4876,4949,5107,5178,5280,5366,5436,5529,5605,23537,5662,5718,5783,5838,5885,5955,6063,6115,6496,6551,6610,7236,7887,7964,8017,8175,8251,8553,8608,9134,9199,9345,9498,9648,9714,9763,9812,9889,10000,10092,10146,10302,10400,10456,10523,10591,10904,11192,11404,11569,11665,11749,12916,12972,24831,13013,13112,13180,13238,13475,13529,13614,13677,13746,13915,13978,14143,14392,14431,14489,14527,14565,14601,14637,14720,14762,14816,14873,14941,15017,24903,15087,15146,25323,15191,15232,15307,15783,15837,15913,16080,16162,16234,16271,16306,16347,16390,16450,16516,16585,16642,16722,16880,16915,22890,16975,17030,17129,17200,17274,17387,17504,17589,17634,17724,18064,23391,23278,23097,24038,24564,18104,18159,18210,18278,18369,18419,24169,18501,18550,18594,22759,18713,24976,25377,18807,18903,18954,19026,19229,19305,19365,19428,23470,19485,19550,24763,19646,19746,19841,19893,19976,23937,20058,20104,20160,20207,20331,20718,25463,20803,20850,20909,20994,21052,21105,21145,25102,21224,21436,21533,21598,21673,21726,21800,21878,21917,21982,23593,22050,22095,22137,22194,22254,22328,22378,22414,22450,22495"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,45,46,57,58,60,61,62,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,133,134,135,141,142,143,144,145,153,154,155,158,159,160,161,162,163,164,165,166,167,168,169,170,192,193,194,195,198,199,200,201,202,203,204,205,206,207,208,209,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,235,236,237,238,239,241,296,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,322,323,325,327,328,329,331,332,333,334,335,336,337,338,339,340,398,399,400,401,415,416,462,469,470,471,472,473,476,477,478,479,480,481,482,483,492,493,494,495,496,497,498,500,501,502,503,504,505,506,507,508,509,511,512,513,514,515,516,519,520,521,522,523,524,532,533,537,539,540,541,542,543,544,545,546,548,549,550,551,552,553,554,555,559,560,562,565,567,568,569,570,571,620,621,622,623,624,625,628,629,630,631,633,634,635,636,637,638,639,640", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "931,996,1046,1094,1142,1190,1241,1289,1336,1390,1456,1505,1551,1600,1647,1698,2001,2079,2363,2415,3415,3467,3655,3724,3802,4146,4216,4290,4349,4419,4457,4623,4680,4770,4812,4873,4966,5014,5288,5337,5682,5725,5795,6652,6694,6759,6805,6843,6936,6994,7068,7481,7903,7947,8012,8075,8818,8906,8990,9067,9142,9350,9678,11504,11564,11605,12109,12181,12231,12299,12369,13169,13213,13287,13592,13634,13706,13863,13933,14034,14119,14188,14280,14355,14421,14477,14532,17192,17246,17292,17361,17645,17696,18076,18130,18188,18813,19463,19539,19591,19748,19823,20124,20379,20904,20968,21113,21265,21414,21479,21527,21575,21651,21761,21852,21905,22060,22157,22212,22278,22345,22657,22944,23155,23319,23503,23586,23657,23712,23779,23881,28014,28497,28554,28790,28843,28927,28989,29057,29225,29287,29451,29699,29737,29794,29831,29868,29903,29938,30020,30061,30114,30251,30318,30472,30636,30705,30763,30882,30926,30966,31040,31515,31568,31643,31809,31890,31961,38408,38442,38482,38524,39577,39642,43461,44023,44102,44259,44293,44374,44601,44655,44753,44823,44896,45008,45124,45208,46041,46130,46469,46581,46761,46849,46949,47080,47119,47173,47223,47290,47380,47429,47509,47590,47638,47828,47872,47990,48062,48115,48208,48480,48530,48601,48803,48878,48937,49418,49496,49794,49935,50133,50228,50327,50421,50472,50554,50622,50780,50825,50880,50926,51049,51435,51520,51604,51907,51965,52218,52427,52600,52639,52698,52776,52987,56363,56427,56501,56553,56626,56703,56911,56975,57030,57097,57201,57242,57298,57357,57430,57479,57514,57549", "endColumns": "64,49,47,47,47,50,47,46,53,65,48,45,48,46,50,87,77,54,51,52,51,76,68,77,77,69,73,58,69,37,165,56,89,41,60,92,47,273,48,344,42,69,44,41,64,45,37,92,57,73,412,421,43,64,62,742,87,83,76,74,207,327,83,59,40,59,71,49,67,69,67,43,73,78,41,71,156,69,100,84,68,91,74,65,55,54,63,53,45,68,106,50,379,53,57,624,649,75,51,156,74,300,53,524,63,144,151,148,64,47,47,75,109,90,52,154,96,54,65,66,311,286,210,163,94,82,70,54,66,39,97,66,56,235,52,83,61,67,167,61,163,247,37,56,36,36,34,34,81,40,52,55,66,74,70,68,57,55,43,39,73,474,52,74,165,80,70,35,33,39,41,58,64,67,55,78,156,33,80,58,53,97,69,72,111,115,83,43,88,338,111,179,87,99,47,38,53,49,66,89,48,79,80,47,42,43,117,71,52,92,94,49,70,201,74,58,61,77,55,63,197,94,98,93,50,81,67,80,44,54,45,122,385,84,83,45,57,83,56,51,38,58,77,210,95,63,73,51,72,76,37,63,54,66,43,40,55,58,72,48,34,34,43", "endOffsets": "991,1041,1089,1137,1185,1236,1284,1331,1385,1451,1500,1546,1595,1642,1693,1781,2074,2129,2410,2463,3462,3539,3719,3797,3875,4211,4285,4344,4414,4452,4618,4675,4765,4807,4868,4961,5009,5283,5332,5677,5720,5790,5835,6689,6754,6800,6838,6931,6989,7063,7476,7898,7942,8007,8070,8813,8901,8985,9062,9137,9345,9673,9757,11559,11600,11660,12176,12226,12294,12364,12432,13208,13282,13361,13629,13701,13858,13928,14029,14114,14183,14275,14350,14416,14472,14527,14591,17241,17287,17356,17463,17691,18071,18125,18183,18808,19458,19534,19586,19743,19818,20119,20173,20899,20963,21108,21260,21409,21474,21522,21570,21646,21756,21847,21900,22055,22152,22207,22273,22340,22652,22939,23150,23314,23409,23581,23652,23707,23774,23814,23974,28076,28549,28785,28838,28922,28984,29052,29220,29282,29446,29694,29732,29789,29826,29863,29898,29933,30015,30056,30109,30165,30313,30388,30538,30700,30758,30814,30921,30961,31035,31510,31563,31638,31804,31885,31956,31992,38437,38477,38519,38578,39637,39705,43512,44097,44254,44288,44369,44428,44650,44748,44818,44891,45003,45119,45203,45247,46125,46464,46576,46756,46844,46944,46992,47114,47168,47218,47285,47375,47424,47504,47585,47633,47676,47867,47985,48057,48110,48203,48298,48525,48596,48798,48873,48932,48994,49491,49547,49853,50128,50223,50322,50416,50467,50549,50617,50698,50820,50875,50921,51044,51430,51515,51599,51645,51960,52044,52270,52474,52634,52693,52771,52982,53078,56422,56496,56548,56621,56698,56736,56970,57025,57092,57136,57237,57293,57352,57425,57474,57509,57544,57588"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1091,1156,1251,1332,1395,1484,1548,1617,1680,1754,1818,1875,1993,2051,2113,2170,2250,2389,2478,2554,2649,2730,2812,2953,3034,3114,3265,3355,3435,3491,3547,3613,3692,3774,3845,3934,4008,4085,4155,4234,4334,4418,4502,4594,4694,4768,4849,4951,5004,5089,5156,5249,5338,5400,5464,5527,5595,5708,5815,5919,6020,6080,6140,6223,6306,6382", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "273,354,433,520,621,717,821,943,1024,1086,1151,1246,1327,1390,1479,1543,1612,1675,1749,1813,1870,1988,2046,2108,2165,2245,2384,2473,2549,2644,2725,2807,2948,3029,3109,3260,3350,3430,3486,3542,3608,3687,3769,3840,3929,4003,4080,4150,4229,4329,4413,4497,4589,4689,4763,4844,4946,4999,5084,5151,5244,5333,5395,5459,5522,5590,5703,5810,5914,6015,6075,6135,6218,6301,6377,6454"}, "to": {"startLines": "19,136,137,138,139,140,156,157,171,240,242,297,321,330,402,403,404,405,406,407,408,409,410,411,412,413,414,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,499,535,536,547", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "753,11665,11746,11825,11912,12013,13366,13470,14596,23819,23979,28081,30170,30819,38583,38672,38736,38805,38868,38942,39006,39063,39181,39239,39301,39358,39438,39710,39799,39875,39970,40051,40133,40274,40355,40435,40586,40676,40756,40812,40868,40934,41013,41095,41166,41255,41329,41406,41476,41555,41655,41739,41823,41915,42015,42089,42170,42272,42325,42410,42477,42570,42659,42721,42785,42848,42916,43029,43136,43240,43341,43401,46997,49635,49718,50703", "endLines": "22,136,137,138,139,140,156,157,171,240,242,297,321,330,402,403,404,405,406,407,408,409,410,411,412,413,414,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,499,535,536,547", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "926,11741,11820,11907,12008,12104,13465,13587,14672,23876,24039,28171,30246,30877,38667,38731,38800,38863,38937,39001,39058,39176,39234,39296,39353,39433,39572,39794,39870,39965,40046,40128,40269,40350,40430,40581,40671,40751,40807,40863,40929,41008,41090,41161,41250,41324,41401,41471,41550,41650,41734,41818,41910,42010,42084,42165,42267,42320,42405,42472,42565,42654,42716,42780,42843,42911,43024,43131,43235,43336,43396,43456,47075,49713,49789,50775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,513,614,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,2045,2471,2578,2836", "endColumns": "101,112,100,127,91,93,96,93,99,93,95,95,91,91,110,106,158,82", "endOffsets": "202,315,609,737,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,2151,2573,2732,2914"}, "to": {"startLines": "39,40,43,44,47,48,49,50,51,52,53,54,55,56,59,63,64,534", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1786,1888,2134,2235,2468,2560,2654,2751,2845,2945,3039,3135,3231,3323,3544,3880,3987,49552", "endColumns": "101,112,100,127,91,93,96,93,99,93,95,95,91,91,110,106,158,82", "endOffsets": "1883,1996,2230,2358,2555,2649,2746,2840,2940,3034,3130,3226,3318,3410,3650,3982,4141,49630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9b1d1c56b528abe32d69bef2c9ce868e\\transformed\\jetified-bigo-ads-5.3.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,207,268,324,390,470,556,629,693,747,804,1158,1234,1308,1363,1438,1499,1553,1615,1662,1722", "endColumns": "79,71,60,55,65,79,85,72,63,53,56,353,75,73,54,74,60,53,61,46,59,74", "endOffsets": "130,202,263,319,385,465,551,624,688,742,799,1153,1229,1303,1358,1433,1494,1548,1610,1657,1717,1792"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9762,9842,9914,9975,10031,10097,10177,10263,10336,10400,10454,10511,10865,10941,11015,11070,11145,11206,11260,11322,11369,11429", "endColumns": "79,71,60,55,65,79,85,72,63,53,56,353,75,73,54,74,60,53,61,46,59,74", "endOffsets": "9837,9909,9970,10026,10092,10172,10258,10331,10395,10449,10506,10860,10936,11010,11065,11140,11201,11255,11317,11364,11424,11499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "146,147,148,149,150,151,152,558", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "12437,12536,12638,12738,12836,12943,13049,51806", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "12531,12633,12733,12831,12938,13044,13164,51902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-es\\values-es.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3326,3391", "endColumns": "64,65", "endOffsets": "3386,3452"}, "to": {"startLines": "287,288", "startColumns": "4,4", "startOffsets": "27383,27448", "endColumns": "64,65", "endOffsets": "27443,27509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "182", "startColumns": "4", "startOffsets": "15891", "endColumns": "159", "endOffsets": "16046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,744,831,916,1012,1108,1183,1251,1346,1441,1507,1576,1642,1713,1821,1927,2034,2104,2191,2261,2341,2431,2522,2588,2652,2705,2763,2811,2870,2935,2997,3063,3135,3199,3260,3326,3379,3444,3523,3602,3660", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,52,64,78,78,57,69", "endOffsets": "280,466,653,739,826,911,1007,1103,1178,1246,1341,1436,1502,1571,1637,1708,1816,1922,2029,2099,2186,2256,2336,2426,2517,2583,2647,2700,2758,2806,2865,2930,2992,3058,3130,3194,3255,3321,3374,3439,3518,3597,3655,3725"}, "to": {"startLines": "2,11,15,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,276,277,278,279,280,281,282,283,284,285,286,289,290,291,292,293,294", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,566,24044,24130,24217,24302,24398,24494,24569,24637,24732,24827,24893,24962,25028,25099,25207,25313,25420,25490,25577,25647,25727,25817,25908,25974,26709,26762,26820,26868,26927,26992,27054,27120,27192,27256,27317,27514,27567,27632,27711,27790,27848", "endLines": "10,14,18,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,276,277,278,279,280,281,282,283,284,285,286,289,290,291,292,293,294", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,52,64,78,78,57,69", "endOffsets": "375,561,748,24125,24212,24297,24393,24489,24564,24632,24727,24822,24888,24957,25023,25094,25202,25308,25415,25485,25572,25642,25722,25812,25903,25969,26033,26757,26815,26863,26922,26987,27049,27115,27187,27251,27312,27378,27562,27627,27706,27785,27843,27913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,242,289,605,668,795,902,1022,1077,1136,1266,1362,1404,1501,1536,1572,1626,1708,1753", "endColumns": "42,46,72,62,126,106,119,54,58,129,95,41,96,34,35,53,81,44,55", "endOffsets": "241,288,361,667,794,901,1021,1076,1135,1265,1361,1403,1500,1535,1571,1625,1707,1752,1808"}, "to": {"startLines": "463,464,465,484,485,486,487,488,489,490,491,525,526,527,528,529,530,531,632", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "43517,43564,43615,45252,45319,45450,45561,45685,45744,45807,45941,48999,49045,49146,49185,49225,49283,49369,57141", "endColumns": "46,50,76,66,130,110,123,58,62,133,99,45,100,38,39,57,85,48,59", "endOffsets": "43559,43610,43687,45314,45445,45556,45680,45739,45802,45936,46036,49040,49141,49180,49220,49278,49364,49413,57196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,116,178,224,315,354,404,460,514,574,641,709,751,849,951,1031,1115,1156,1224,1272,1366,1440,1495,1547,1645,1699,1757,1812,1893,1944,2012,2110,2169,2236,2328,2388,2454,2531,2587,2639,2691,2752,2818,2882,2972,3076", "endColumns": "60,61,45,90,38,49,55,53,59,66,67,41,97,101,79,83,40,67,47,93,73,54,51,97,53,57,54,80,50,67,97,58,66,91,59,65,76,55,51,51,60,65,63,89,103,56", "endOffsets": "111,173,219,310,349,399,455,509,569,636,704,746,844,946,1026,1110,1151,1219,1267,1361,1435,1490,1542,1640,1694,1752,1807,1888,1939,2007,2105,2164,2231,2323,2383,2449,2526,2582,2634,2686,2747,2813,2877,2967,3071,3128"}, "to": {"startLines": "574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "53285,53346,53408,53454,53545,53584,53634,53690,53744,53804,53871,53939,53981,54079,54181,54261,54345,54386,54454,54502,54596,54670,54725,54777,54875,54929,54987,55042,55123,55174,55242,55340,55399,55466,55558,55618,55684,55761,55817,55869,55921,55982,56048,56112,56202,56306", "endColumns": "60,61,45,90,38,49,55,53,59,66,67,41,97,101,79,83,40,67,47,93,73,54,51,97,53,57,54,80,50,67,97,58,66,91,59,65,76,55,51,51,60,65,63,89,103,56", "endOffsets": "53341,53403,53449,53540,53579,53629,53685,53739,53799,53866,53934,53976,54074,54176,54256,54340,54381,54449,54497,54591,54665,54720,54772,54870,54924,54982,55037,55118,55169,55237,55335,55394,55461,55553,55613,55679,55756,55812,55864,55916,55977,56043,56107,56197,56301,56358"}}]}]}