package com.tqhit.battery.one.features.stats.notifications;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class UnifiedBatteryNotificationServiceHelper_Factory implements Factory<UnifiedBatteryNotificationServiceHelper> {
  private final Provider<Context> contextProvider;

  public UnifiedBatteryNotificationServiceHelper_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public UnifiedBatteryNotificationServiceHelper get() {
    return newInstance(contextProvider.get());
  }

  public static UnifiedBatteryNotificationServiceHelper_Factory create(
      Provider<Context> contextProvider) {
    return new UnifiedBatteryNotificationServiceHelper_Factory(contextProvider);
  }

  public static UnifiedBatteryNotificationServiceHelper newInstance(Context context) {
    return new UnifiedBatteryNotificationServiceHelper(context);
  }
}
