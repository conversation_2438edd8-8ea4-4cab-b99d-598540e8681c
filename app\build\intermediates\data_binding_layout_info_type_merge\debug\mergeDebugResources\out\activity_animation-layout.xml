<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_animation" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\activity_animation.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_animation_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="167" endOffset="51"/></Target><Target id="@+id/playerView" view="androidx.media3.ui.PlayerView"><Expressions/><location startLine="7" startOffset="4" endLine="15" endOffset="50"/></Target><Target id="@+id/date_time_container" view="LinearLayout"><Expressions/><location startLine="19" startOffset="4" endLine="46" endOffset="18"/></Target><Target id="@+id/text_time" view="TextView"><Expressions/><location startLine="30" startOffset="8" endLine="37" endOffset="37"/></Target><Target id="@+id/text_date" view="TextView"><Expressions/><location startLine="39" startOffset="8" endLine="45" endOffset="53"/></Target><Target id="@+id/battery_percent" view="TextView"><Expressions/><location startLine="49" startOffset="4" endLine="60" endOffset="43"/></Target><Target id="@+id/back_button" view="Button"><Expressions/><location startLine="70" startOffset="8" endLine="77" endOffset="62"/></Target><Target id="@+id/time_remaining" view="TextView"><Expressions/><location startLine="90" startOffset="4" endLine="103" endOffset="46"/></Target><Target id="@+id/time_remaining_value" view="TextView"><Expressions/><location startLine="104" startOffset="4" endLine="117" endOffset="33"/></Target><Target id="@+id/apply_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="119" startOffset="4" endLine="166" endOffset="55"/></Target><Target id="@+id/applyButton" view="Button"><Expressions/><location startLine="128" startOffset="8" endLine="137" endOffset="62"/></Target><Target id="@+id/icon_ad" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="148" startOffset="12" endLine="154" endOffset="47"/></Target><Target id="@+id/text_btn" view="TextView"><Expressions/><location startLine="155" startOffset="12" endLine="164" endOffset="55"/></Target></Targets></Layout>