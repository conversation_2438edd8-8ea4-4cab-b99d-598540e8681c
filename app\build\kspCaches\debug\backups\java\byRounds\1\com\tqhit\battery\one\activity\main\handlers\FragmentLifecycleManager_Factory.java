package com.tqhit.battery.one.activity.main.handlers;

import com.tqhit.battery.one.features.navigation.DynamicNavigationManager;
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class FragmentLifecycleManager_Factory implements Factory<FragmentLifecycleManager> {
  private final Provider<DynamicNavigationManager> dynamicNavigationManagerProvider;

  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  public FragmentLifecycleManager_Factory(
      Provider<DynamicNavigationManager> dynamicNavigationManagerProvider,
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider) {
    this.dynamicNavigationManagerProvider = dynamicNavigationManagerProvider;
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
  }

  @Override
  public FragmentLifecycleManager get() {
    return newInstance(dynamicNavigationManagerProvider.get(), coreBatteryStatsProvider.get());
  }

  public static FragmentLifecycleManager_Factory create(
      Provider<DynamicNavigationManager> dynamicNavigationManagerProvider,
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider) {
    return new FragmentLifecycleManager_Factory(dynamicNavigationManagerProvider, coreBatteryStatsProvider);
  }

  public static FragmentLifecycleManager newInstance(
      DynamicNavigationManager dynamicNavigationManager,
      CoreBatteryStatsProvider coreBatteryStatsProvider) {
    return new FragmentLifecycleManager(dynamicNavigationManager, coreBatteryStatsProvider);
  }
}
