package com.tqhit.battery.one.ads.core;

import android.content.Context;
import com.tqhit.adlib.sdk.analytics.AnalyticsTracker;
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ApplovinNativeAdManager_Factory implements Factory<ApplovinNativeAdManager> {
  private final Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider;

  private final Provider<Context> contextProvider;

  private final Provider<AnalyticsTracker> analyticsTrackerProvider;

  public ApplovinNativeAdManager_Factory(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<Context> contextProvider, Provider<AnalyticsTracker> analyticsTrackerProvider) {
    this.remoteConfigHelperProvider = remoteConfigHelperProvider;
    this.contextProvider = contextProvider;
    this.analyticsTrackerProvider = analyticsTrackerProvider;
  }

  @Override
  public ApplovinNativeAdManager get() {
    return newInstance(remoteConfigHelperProvider.get(), contextProvider.get(), analyticsTrackerProvider.get());
  }

  public static ApplovinNativeAdManager_Factory create(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<Context> contextProvider, Provider<AnalyticsTracker> analyticsTrackerProvider) {
    return new ApplovinNativeAdManager_Factory(remoteConfigHelperProvider, contextProvider, analyticsTrackerProvider);
  }

  public static ApplovinNativeAdManager newInstance(FirebaseRemoteConfigHelper remoteConfigHelper,
      Context context, AnalyticsTracker analyticsTracker) {
    return new ApplovinNativeAdManager(remoteConfigHelper, context, analyticsTracker);
  }
}
