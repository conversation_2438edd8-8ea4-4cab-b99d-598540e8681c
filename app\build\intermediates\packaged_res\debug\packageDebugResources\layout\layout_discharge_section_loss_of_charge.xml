<?xml version="1.0" encoding="utf-8"?>
<!-- 
 * Loss of charge in current session section
 * This section contains:
 * - Section title with info button
 * - Screen on consumption data (percentage and mAh)
 * - Screen off consumption data (percentage and mAh)
 * - App power consumption button
 */
-->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/lossOfChargeRoot"
    android:orientation="vertical"
    android:background="@drawable/white_block"
    android:paddingTop="7dp"
    android:paddingBottom="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:layout_marginStart="9dp"
    android:layout_marginEnd="9dp"
    android:layout_marginTop="14dp">

    <!-- Section Title and Info Button -->
    <TextView
        android:id="@+id/loc_tv_title"
        android:textSize="19sp"
        android:textColor="?attr/black"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/info_in_current_session"
        android:layout_marginStart="2dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/loc_iv_info_button"
        app:layout_constraintTop_toTopOf="parent"/>
        <!-- Original i_t -->
        
    <ImageView
        android:id="@+id/loc_iv_info_button"
        android:layout_width="22sp"
        android:layout_height="0dp"
        android:src="@drawable/ic_note"
        android:scaleType="fitEnd"
        android:layout_marginStart="5dp"
        app:layout_constraintTop_toTopOf="@id/loc_tv_title"
        app:layout_constraintBottom_toBottomOf="@id/loc_tv_title"
        app:layout_constraintEnd_toEndOf="parent"/>
        <!-- Original discharge_rate_info -->

    <!-- Screen On Consumption (Left Block) -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/loc_cl_screen_on_consumption"
        android:background="@drawable/grey_block_static"
        android:paddingVertical="7dp"
        android:paddingHorizontal="9dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="4dp"
        app:layout_constraintTop_toBottomOf="@id/loc_tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/loc_cl_screen_off_consumption">
        
        <TextView
            android:id="@+id/loc_tv_screen_on_title"
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:text="@string/active_mode"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
            
        <TextView
            android:id="@+id/loc_tv_screen_on_percentage_dropped"
            android:textSize="19sp"
            android:textColor="?attr/black"
            android:text="@string/zero"
            app:layout_constraintTop_toBottomOf="@id/loc_tv_screen_on_title"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
            <!-- Original info_day_percent_session -->
            
        <TextView
            android:id="@+id/loc_tv_screen_on_percentage_unit"
            android:textSize="11sp"
            android:textColor="?attr/black"
            android:text="@string/percent"
            app:layout_constraintBaseline_toBaselineOf="@id/loc_tv_screen_on_percentage_dropped"
            app:layout_constraintStart_toEndOf="@id/loc_tv_screen_on_percentage_dropped"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
            
        <TextView
            android:id="@+id/loc_tv_screen_on_time"
            android:textSize="19sp"
            android:textColor="?attr/black"
            android:ellipsize="marquee"
            android:focusableInTouchMode="true"
            android:visibility="visible"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/zero"
            android:singleLine="true"
            android:marqueeRepeatLimit="marquee_forever"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/loc_tv_screen_on_percentage_dropped"/>
            <!-- Matches time_day_session -->
            
        <TextView
            android:id="@+id/loc_tv_screen_on_mah_consumed"
            android:textSize="14sp"
            android:textColor="?attr/colorr"
            android:text="@string/zero"
            app:layout_constraintTop_toBottomOf="@id/loc_tv_screen_on_time"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
            <!-- Original info_day_speed_session -->
            
        <TextView
            android:id="@+id/loc_tv_screen_on_mah_unit"
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:text="@string/ma_in_medium"
            app:layout_constraintBaseline_toBaselineOf="@id/loc_tv_screen_on_mah_consumed"
            app:layout_constraintStart_toEndOf="@id/loc_tv_screen_on_mah_consumed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Screen Off Consumption (Right Block) -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/loc_cl_screen_off_consumption"
        android:background="@drawable/grey_block_static"
        android:paddingVertical="7dp"
        android:paddingHorizontal="9dp"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="4dp"
        app:layout_constraintTop_toTopOf="@id/loc_cl_screen_on_consumption"
        app:layout_constraintBottom_toBottomOf="@id/loc_cl_screen_on_consumption"
        app:layout_constraintStart_toEndOf="@id/loc_cl_screen_on_consumption"
        app:layout_constraintEnd_toEndOf="parent">
        
        <TextView
            android:id="@+id/loc_tv_screen_off_title"
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:text="@string/screen_off"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
            
        <TextView
            android:id="@+id/loc_tv_screen_off_percentage_dropped"
            android:textSize="19sp"
            android:textColor="?attr/black"
            android:text="@string/zero"
            app:layout_constraintTop_toBottomOf="@id/loc_tv_screen_off_title"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
            <!-- Original info_night_percent_session -->
            
        <TextView
            android:id="@+id/loc_tv_screen_off_percentage_unit"
            android:textSize="11sp"
            android:textColor="?attr/black"
            android:text="@string/percent"
            app:layout_constraintBaseline_toBaselineOf="@id/loc_tv_screen_off_percentage_dropped"
            app:layout_constraintStart_toEndOf="@id/loc_tv_screen_off_percentage_dropped"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
            
        <TextView
            android:id="@+id/loc_tv_screen_off_time"
            android:textSize="19sp"
            android:textColor="?attr/black"
            android:ellipsize="marquee"
            android:focusableInTouchMode="true"
            android:visibility="visible"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/zero"
            android:singleLine="true"
            android:marqueeRepeatLimit="marquee_forever"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/loc_tv_screen_off_percentage_dropped"/>
            <!-- Matches time_night_session -->
            
        <TextView
            android:id="@+id/loc_tv_screen_off_mah_consumed"
            android:textSize="14sp"
            android:textColor="?attr/colorr"
            android:text="@string/zero"
            app:layout_constraintTop_toBottomOf="@id/loc_tv_screen_off_time"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
            <!-- Original info_night_speed_session -->
            
        <TextView
            android:id="@+id/loc_tv_screen_off_mah_unit"
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:text="@string/ma_in_medium"
            app:layout_constraintBaseline_toBaselineOf="@id/loc_tv_screen_off_mah_consumed"
            app:layout_constraintStart_toEndOf="@id/loc_tv_screen_off_mah_consumed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    
    <!-- App power consumption button -->
    <TextView
        android:id="@+id/loc_btn_app_power_consumption"
        android:textSize="14sp"
        android:textColor="?attr/black"
        android:gravity="center"
        android:background="@drawable/grey_block"
        android:paddingTop="12.5dp"
        android:paddingBottom="12.5dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/using_energy"
        android:singleLine="true"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/loc_cl_screen_on_consumption"/>
        <!-- Original button_disscharge_using -->
</androidx.constraintlayout.widget.ConstraintLayout> 