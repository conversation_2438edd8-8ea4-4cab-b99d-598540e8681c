package com.tqhit.battery.one.features.stats.apppower.permission;

import android.content.Context;
import com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class UsageStatsPermissionManager_Factory implements Factory<UsageStatsPermissionManager> {
  private final Provider<Context> contextProvider;

  private final Provider<AppUsageStatsRepository> appUsageStatsRepositoryProvider;

  public UsageStatsPermissionManager_Factory(Provider<Context> contextProvider,
      Provider<AppUsageStatsRepository> appUsageStatsRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.appUsageStatsRepositoryProvider = appUsageStatsRepositoryProvider;
  }

  @Override
  public UsageStatsPermissionManager get() {
    return newInstance(contextProvider.get(), appUsageStatsRepositoryProvider.get());
  }

  public static UsageStatsPermissionManager_Factory create(Provider<Context> contextProvider,
      Provider<AppUsageStatsRepository> appUsageStatsRepositoryProvider) {
    return new UsageStatsPermissionManager_Factory(contextProvider, appUsageStatsRepositoryProvider);
  }

  public static UsageStatsPermissionManager newInstance(Context context,
      AppUsageStatsRepository appUsageStatsRepository) {
    return new UsageStatsPermissionManager(context, appUsageStatsRepository);
  }
}
