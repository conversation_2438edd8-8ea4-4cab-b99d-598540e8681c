// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutNativeAdsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout adOptionsView;

  @NonNull
  public final TextView advertiserTextView;

  @NonNull
  public final TextView bodyTextView;

  @NonNull
  public final Button ctaButton;

  @NonNull
  public final ImageView iconImageView;

  @NonNull
  public final LinearLayout maxNativeAdView;

  @NonNull
  public final FrameLayout mediaViewContainer;

  @NonNull
  public final FrameLayout starRatingView;

  @NonNull
  public final TextView titleTextView;

  private LayoutNativeAdsBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout adOptionsView, @NonNull TextView advertiserTextView,
      @NonNull TextView bodyTextView, @NonNull Button ctaButton, @NonNull ImageView iconImageView,
      @NonNull LinearLayout maxNativeAdView, @NonNull FrameLayout mediaViewContainer,
      @NonNull FrameLayout starRatingView, @NonNull TextView titleTextView) {
    this.rootView = rootView;
    this.adOptionsView = adOptionsView;
    this.advertiserTextView = advertiserTextView;
    this.bodyTextView = bodyTextView;
    this.ctaButton = ctaButton;
    this.iconImageView = iconImageView;
    this.maxNativeAdView = maxNativeAdView;
    this.mediaViewContainer = mediaViewContainer;
    this.starRatingView = starRatingView;
    this.titleTextView = titleTextView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutNativeAdsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutNativeAdsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_native_ads, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutNativeAdsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ad_options_view;
      LinearLayout adOptionsView = ViewBindings.findChildViewById(rootView, id);
      if (adOptionsView == null) {
        break missingId;
      }

      id = R.id.advertiser_text_view;
      TextView advertiserTextView = ViewBindings.findChildViewById(rootView, id);
      if (advertiserTextView == null) {
        break missingId;
      }

      id = R.id.body_text_view;
      TextView bodyTextView = ViewBindings.findChildViewById(rootView, id);
      if (bodyTextView == null) {
        break missingId;
      }

      id = R.id.cta_button;
      Button ctaButton = ViewBindings.findChildViewById(rootView, id);
      if (ctaButton == null) {
        break missingId;
      }

      id = R.id.icon_image_view;
      ImageView iconImageView = ViewBindings.findChildViewById(rootView, id);
      if (iconImageView == null) {
        break missingId;
      }

      LinearLayout maxNativeAdView = (LinearLayout) rootView;

      id = R.id.media_view_container;
      FrameLayout mediaViewContainer = ViewBindings.findChildViewById(rootView, id);
      if (mediaViewContainer == null) {
        break missingId;
      }

      id = R.id.star_rating_view;
      FrameLayout starRatingView = ViewBindings.findChildViewById(rootView, id);
      if (starRatingView == null) {
        break missingId;
      }

      id = R.id.title_text_view;
      TextView titleTextView = ViewBindings.findChildViewById(rootView, id);
      if (titleTextView == null) {
        break missingId;
      }

      return new LayoutNativeAdsBinding((LinearLayout) rootView, adOptionsView, advertiserTextView,
          bodyTextView, ctaButton, iconImageView, maxNativeAdView, mediaViewContainer,
          starRatingView, titleTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
