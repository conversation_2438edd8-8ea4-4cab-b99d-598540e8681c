<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_new_charge" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_new_charge.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView" rootNodeViewId="@+id/new_charge_scroll_view"><Targets><Target id="@+id/new_charge_scroll_view" tag="layout/fragment_new_charge_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="7" startOffset="0" endLine="86" endOffset="39"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="13" startOffset="4" endLine="85" endOffset="18"/></Target><Target id="@+id/chargeMainDisplaySection" tag="binding_1" include="section_charge_main_display"><Expressions/><location startLine="28" startOffset="8" endLine="30" endOffset="58"/></Target><Target id="@+id/chargeBatteryWearSection" tag="binding_1" include="section_charge_battery_wear"><Expressions/><location startLine="56" startOffset="8" endLine="58" endOffset="58"/></Target><Target id="@+id/chargeStatusDetailsSection" tag="binding_1" include="section_charge_status_details"><Expressions/><location startLine="61" startOffset="8" endLine="63" endOffset="60"/></Target><Target id="@+id/chargeRemainingTimeSection" tag="binding_1" include="section_charge_remaining_time"><Expressions/><location startLine="66" startOffset="8" endLine="68" endOffset="60"/></Target><Target id="@+id/chargeCurrentSessionSection" tag="binding_1" include="section_charge_current_session"><Expressions/><location startLine="71" startOffset="8" endLine="73" endOffset="61"/></Target><Target id="@+id/chargeOverallAverageSection" tag="binding_1" include="section_charge_overall_average"><Expressions/><location startLine="76" startOffset="8" endLine="78" endOffset="61"/></Target><Target id="@+id/promo_container" view="LinearLayout"><Expressions/><location startLine="20" startOffset="8" endLine="25" endOffset="48"/></Target><Target id="@+id/nativeAd" view="com.facebook.shimmer.ShimmerFrameLayout"><Expressions/><location startLine="32" startOffset="8" endLine="36" endOffset="45"/></Target><Target id="@+id/newChargeNotChargingMessage" view="TextView"><Expressions/><location startLine="39" startOffset="8" endLine="53" endOffset="39"/></Target></Targets></Layout>