// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SectionChargeBatteryWearBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView batteryAlarmBtn;

  @NonNull
  public final TextView batteryWearDescription;

  @NonNull
  public final ImageView batteryWearInfo;

  @NonNull
  public final ConstraintLayout batteryWearRoot;

  @NonNull
  public final TextView batteryWearTitle;

  @NonNull
  public final View dividerWear;

  @NonNull
  public final TextView targetPercentLabel;

  @NonNull
  public final SeekBar targetPercentSeekbar;

  @NonNull
  public final TextView targetPercentValue;

  private SectionChargeBatteryWearBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView batteryAlarmBtn, @NonNull TextView batteryWearDescription,
      @NonNull ImageView batteryWearInfo, @NonNull ConstraintLayout batteryWearRoot,
      @NonNull TextView batteryWearTitle, @NonNull View dividerWear,
      @NonNull TextView targetPercentLabel, @NonNull SeekBar targetPercentSeekbar,
      @NonNull TextView targetPercentValue) {
    this.rootView = rootView;
    this.batteryAlarmBtn = batteryAlarmBtn;
    this.batteryWearDescription = batteryWearDescription;
    this.batteryWearInfo = batteryWearInfo;
    this.batteryWearRoot = batteryWearRoot;
    this.batteryWearTitle = batteryWearTitle;
    this.dividerWear = dividerWear;
    this.targetPercentLabel = targetPercentLabel;
    this.targetPercentSeekbar = targetPercentSeekbar;
    this.targetPercentValue = targetPercentValue;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SectionChargeBatteryWearBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SectionChargeBatteryWearBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.section_charge_battery_wear, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SectionChargeBatteryWearBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.battery_alarm_btn;
      TextView batteryAlarmBtn = ViewBindings.findChildViewById(rootView, id);
      if (batteryAlarmBtn == null) {
        break missingId;
      }

      id = R.id.battery_wear_description;
      TextView batteryWearDescription = ViewBindings.findChildViewById(rootView, id);
      if (batteryWearDescription == null) {
        break missingId;
      }

      id = R.id.battery_wear_info;
      ImageView batteryWearInfo = ViewBindings.findChildViewById(rootView, id);
      if (batteryWearInfo == null) {
        break missingId;
      }

      ConstraintLayout batteryWearRoot = (ConstraintLayout) rootView;

      id = R.id.battery_wear_title;
      TextView batteryWearTitle = ViewBindings.findChildViewById(rootView, id);
      if (batteryWearTitle == null) {
        break missingId;
      }

      id = R.id.divider_wear;
      View dividerWear = ViewBindings.findChildViewById(rootView, id);
      if (dividerWear == null) {
        break missingId;
      }

      id = R.id.target_percent_label;
      TextView targetPercentLabel = ViewBindings.findChildViewById(rootView, id);
      if (targetPercentLabel == null) {
        break missingId;
      }

      id = R.id.target_percent_seekbar;
      SeekBar targetPercentSeekbar = ViewBindings.findChildViewById(rootView, id);
      if (targetPercentSeekbar == null) {
        break missingId;
      }

      id = R.id.target_percent_value;
      TextView targetPercentValue = ViewBindings.findChildViewById(rootView, id);
      if (targetPercentValue == null) {
        break missingId;
      }

      return new SectionChargeBatteryWearBinding((ConstraintLayout) rootView, batteryAlarmBtn,
          batteryWearDescription, batteryWearInfo, batteryWearRoot, batteryWearTitle, dividerWear,
          targetPercentLabel, targetPercentSeekbar, targetPercentValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
