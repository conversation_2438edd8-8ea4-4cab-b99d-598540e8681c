<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_health" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_health.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView" rootNodeViewId="@+id/scroll_view"><Targets><Target id="@+id/scroll_view" tag="layout/fragment_health_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="2188" endOffset="39"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="5" startOffset="4" endLine="2187" endOffset="18"/></Target><Target id="@+id/include_back_navigation" tag="binding_1" include="layout_back_navigation"><Expressions/><location startLine="11" startOffset="8" endLine="13" endOffset="53"/></Target><Target id="@+id/degree_of_wear" view="LinearLayout"><Expressions/><location startLine="15" startOffset="8" endLine="562" endOffset="22"/></Target><Target id="@+id/d_t" view="TextView"><Expressions/><location startLine="33" startOffset="16" endLine="41" endOffset="53"/></Target><Target id="@+id/degree_wear_info" view="ImageView"><Expressions/><location startLine="42" startOffset="16" endLine="49" endOffset="53"/></Target><Target id="@+id/cumulative_1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="56" startOffset="16" endLine="126" endOffset="67"/></Target><Target id="@+id/health_first_progressbar_cumulative" view="ProgressBar"><Expressions/><location startLine="66" startOffset="20" endLine="81" endOffset="73"/></Target><Target id="@+id/health_percent_damage_cumulative" view="TextView"><Expressions/><location startLine="82" startOffset="20" endLine="93" endOffset="66"/></Target><Target id="@+id/percent_cumulative" view="TextView"><Expressions/><location startLine="94" startOffset="20" endLine="106" endOffset="66"/></Target><Target id="@+id/text_remain_var_cumulative" view="TextView"><Expressions/><location startLine="107" startOffset="20" endLine="125" endOffset="66"/></Target><Target id="@+id/singular_1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="127" startOffset="16" endLine="196" endOffset="67"/></Target><Target id="@+id/health_first_progressbar_singular" view="ProgressBar"><Expressions/><location startLine="137" startOffset="20" endLine="152" endOffset="73"/></Target><Target id="@+id/health_percent_damage_singular" view="TextView"><Expressions/><location startLine="153" startOffset="20" endLine="164" endOffset="66"/></Target><Target id="@+id/percent_singular" view="TextView"><Expressions/><location startLine="165" startOffset="20" endLine="177" endOffset="66"/></Target><Target id="@+id/text_remain_var_singular" view="TextView"><Expressions/><location startLine="178" startOffset="20" endLine="195" endOffset="66"/></Target><Target id="@+id/d_2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="200" startOffset="12" endLine="249" endOffset="63"/></Target><Target id="@+id/d_21" view="TextView"><Expressions/><location startLine="207" startOffset="16" endLine="218" endOffset="62"/></Target><Target id="@+id/health_full_batery_capacity" view="TextView"><Expressions/><location startLine="219" startOffset="16" endLine="232" endOffset="62"/></Target><Target id="@+id/d_22" view="TextView"><Expressions/><location startLine="233" startOffset="16" endLine="248" endOffset="62"/></Target><Target id="@+id/singular_calculated" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="255" startOffset="16" endLine="320" endOffset="67"/></Target><Target id="@+id/singular_capacity_ni" view="TextView"><Expressions/><location startLine="265" startOffset="20" endLine="277" endOffset="66"/></Target><Target id="@+id/singular_31" view="TextView"><Expressions/><location startLine="278" startOffset="20" endLine="289" endOffset="66"/></Target><Target id="@+id/health_checked_batery_capacity_singular" view="TextView"><Expressions/><location startLine="290" startOffset="20" endLine="303" endOffset="66"/></Target><Target id="@+id/singular_32" view="TextView"><Expressions/><location startLine="304" startOffset="20" endLine="319" endOffset="66"/></Target><Target id="@+id/cumulative_calculated" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="321" startOffset="16" endLine="386" endOffset="67"/></Target><Target id="@+id/cumulative_capacity_ni" view="TextView"><Expressions/><location startLine="331" startOffset="20" endLine="343" endOffset="66"/></Target><Target id="@+id/cumulative_31" view="TextView"><Expressions/><location startLine="344" startOffset="20" endLine="355" endOffset="66"/></Target><Target id="@+id/health_checked_batery_capacity_cumulative" view="TextView"><Expressions/><location startLine="356" startOffset="20" endLine="369" endOffset="66"/></Target><Target id="@+id/cumulative_32" view="TextView"><Expressions/><location startLine="370" startOffset="20" endLine="385" endOffset="66"/></Target><Target id="@+id/cumulative_session_info" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="393" startOffset="16" endLine="424" endOffset="67"/></Target><Target id="@+id/health_count_of_sessions_cumulative" view="TextView"><Expressions/><location startLine="403" startOffset="20" endLine="423" endOffset="66"/></Target><Target id="@+id/singular_session_info" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="425" startOffset="16" endLine="456" endOffset="67"/></Target><Target id="@+id/health_count_of_sessions_singular" view="TextView"><Expressions/><location startLine="435" startOffset="20" endLine="455" endOffset="66"/></Target><Target id="@+id/nativeAd" view="com.facebook.shimmer.ShimmerFrameLayout"><Expressions/><location startLine="459" startOffset="12" endLine="463" endOffset="49"/></Target><Target id="@+id/cumulative_btn" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="468" startOffset="16" endLine="493" endOffset="67"/></Target><Target id="@+id/singular_btn" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="494" startOffset="16" endLine="520" endOffset="67"/></Target><Target id="@+id/method_text" view="TextView"><Expressions/><location startLine="528" startOffset="16" endLine="543" endOffset="62"/></Target><Target id="@+id/method_text_singular" view="TextView"><Expressions/><location startLine="544" startOffset="16" endLine="560" endOffset="62"/></Target><Target id="@+id/time_dead_viewgroup" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="563" startOffset="8" endLine="961" endOffset="59"/></Target><Target id="@+id/dead_time_up" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="573" startOffset="12" endLine="878" endOffset="63"/></Target><Target id="@+id/dead_time_up_singular" view="LinearLayout"><Expressions/><location startLine="581" startOffset="16" endLine="729" endOffset="30"/></Target><Target id="@+id/proggersBarDamage_cumulative" view="RelativeLayout"><Expressions/><location startLine="599" startOffset="24" endLine="637" endOffset="40"/></Target><Target id="@+id/damage_bar_percent_current_singular" view="ProgressBar"><Expressions/><location startLine="607" startOffset="28" endLine="617" endOffset="81"/></Target><Target id="@+id/damage_bar_seekwhite_singular" view="ProgressBar"><Expressions/><location startLine="618" startOffset="28" endLine="627" endOffset="81"/></Target><Target id="@+id/seekBar_singular" view="SeekBar"><Expressions/><location startLine="638" startOffset="24" endLine="651" endOffset="61"/></Target><Target id="@+id/text_top_dead_time_111_cumulative" view="RelativeLayout"><Expressions/><location startLine="670" startOffset="24" endLine="708" endOffset="40"/></Target><Target id="@+id/percent_damage_cumulative" view="LinearLayout"><Expressions/><location startLine="685" startOffset="28" endLine="707" endOffset="42"/></Target><Target id="@+id/percent_damage_dead_singular" view="TextView"><Expressions/><location startLine="693" startOffset="32" endLine="699" endOffset="64"/></Target><Target id="@+id/text_dead_singular" view="TextView"><Expressions/><location startLine="716" startOffset="24" endLine="727" endOffset="60"/></Target><Target id="@+id/dead_time_up_cummulative" view="LinearLayout"><Expressions/><location startLine="730" startOffset="16" endLine="877" endOffset="30"/></Target><Target id="@+id/proggersBarDamage" view="RelativeLayout"><Expressions/><location startLine="748" startOffset="24" endLine="786" endOffset="40"/></Target><Target id="@+id/damage_bar_percent_current" view="ProgressBar"><Expressions/><location startLine="756" startOffset="28" endLine="766" endOffset="81"/></Target><Target id="@+id/damage_bar_seekwhite" view="ProgressBar"><Expressions/><location startLine="767" startOffset="28" endLine="776" endOffset="81"/></Target><Target id="@+id/seekBar" view="SeekBar"><Expressions/><location startLine="787" startOffset="24" endLine="800" endOffset="61"/></Target><Target id="@+id/text_top_dead_time_111" view="RelativeLayout"><Expressions/><location startLine="819" startOffset="24" endLine="857" endOffset="40"/></Target><Target id="@+id/percent_damage" view="LinearLayout"><Expressions/><location startLine="834" startOffset="28" endLine="856" endOffset="42"/></Target><Target id="@+id/percent_damage_dead" view="TextView"><Expressions/><location startLine="842" startOffset="32" endLine="848" endOffset="64"/></Target><Target id="@+id/text_dead" view="TextView"><Expressions/><location startLine="865" startOffset="24" endLine="875" endOffset="60"/></Target><Target id="@+id/dead_time_text" view="TextView"><Expressions/><location startLine="879" startOffset="12" endLine="896" endOffset="58"/></Target><Target id="@+id/prediction_wear_info" view="ImageView"><Expressions/><location startLine="897" startOffset="12" endLine="909" endOffset="71"/></Target><Target id="@+id/blurView_dead_top" view="eightbitlab.com.blurview.BlurView"><Expressions/><location startLine="927" startOffset="12" endLine="960" endOffset="47"/></Target><Target id="@+id/health_access2" view="LinearLayout"><Expressions/><location startLine="938" startOffset="16" endLine="959" endOffset="30"/></Target><Target id="@+id/why_i_need_to_do_this" view="LinearLayout"><Expressions/><location startLine="971" startOffset="12" endLine="976" endOffset="53"/></Target><Target id="@+id/percent_graph_change" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="977" startOffset="12" endLine="1057" endOffset="63"/></Target><Target id="@+id/btn0" view="TextView"><Expressions/><location startLine="985" startOffset="16" endLine="1001" endOffset="62"/></Target><Target id="@+id/btn1" view="TextView"><Expressions/><location startLine="1002" startOffset="16" endLine="1019" endOffset="62"/></Target><Target id="@+id/btn2" view="TextView"><Expressions/><location startLine="1020" startOffset="16" endLine="1037" endOffset="62"/></Target><Target id="@+id/btn3" view="TextView"><Expressions/><location startLine="1038" startOffset="16" endLine="1056" endOffset="62"/></Target><Target id="@+id/history_database" view="TextView"><Expressions/><location startLine="1058" startOffset="12" endLine="1081" endOffset="47"/></Target><Target id="@+id/wear_rate_percent" view="TextView"><Expressions/><location startLine="1082" startOffset="12" endLine="1090" endOffset="50"/></Target><Target id="@+id/sdfsd" view="TextView"><Expressions/><location startLine="1091" startOffset="12" endLine="1107" endOffset="47"/></Target><Target id="@+id/under_graph_percent" view="LinearLayout"><Expressions/><location startLine="1108" startOffset="12" endLine="1195" endOffset="26"/></Target><Target id="@+id/day_7_percent" view="TextView"><Expressions/><location startLine="1125" startOffset="16" endLine="1133" endOffset="46"/></Target><Target id="@+id/day_6_percent" view="TextView"><Expressions/><location startLine="1134" startOffset="16" endLine="1142" endOffset="46"/></Target><Target id="@+id/day_5_percent" view="TextView"><Expressions/><location startLine="1143" startOffset="16" endLine="1151" endOffset="46"/></Target><Target id="@+id/day_4_percent" view="TextView"><Expressions/><location startLine="1152" startOffset="16" endLine="1160" endOffset="46"/></Target><Target id="@+id/day_3_percent" view="TextView"><Expressions/><location startLine="1161" startOffset="16" endLine="1169" endOffset="46"/></Target><Target id="@+id/day_2_percent" view="TextView"><Expressions/><location startLine="1170" startOffset="16" endLine="1178" endOffset="46"/></Target><Target id="@+id/day_1_percent" view="TextView"><Expressions/><location startLine="1179" startOffset="16" endLine="1187" endOffset="46"/></Target><Target id="@+id/graph_percent" view="RelativeLayout"><Expressions/><location startLine="1196" startOffset="12" endLine="1393" endOffset="28"/></Target><Target id="@+id/chart1_percent" view="RelativeLayout"><Expressions/><location startLine="1381" startOffset="16" endLine="1392" endOffset="32"/></Target><Target id="@+id/chart_percent" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="1388" startOffset="20" endLine="1391" endOffset="61"/></Target><Target id="@+id/grahp_temp_viewgroup" view="RelativeLayout"><Expressions/><location startLine="1395" startOffset="8" endLine="1809" endOffset="24"/></Target><Target id="@+id/time_graph_change" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1406" startOffset="12" endLine="1487" endOffset="63"/></Target><Target id="@+id/btn0_t" view="TextView"><Expressions/><location startLine="1415" startOffset="16" endLine="1431" endOffset="63"/></Target><Target id="@+id/btn1_t" view="TextView"><Expressions/><location startLine="1432" startOffset="16" endLine="1449" endOffset="62"/></Target><Target id="@+id/btn2_t" view="TextView"><Expressions/><location startLine="1450" startOffset="16" endLine="1467" endOffset="62"/></Target><Target id="@+id/btn3_t" view="TextView"><Expressions/><location startLine="1468" startOffset="16" endLine="1486" endOffset="62"/></Target><Target id="@+id/wear_rate3" view="TextView"><Expressions/><location startLine="1488" startOffset="12" endLine="1496" endOffset="50"/></Target><Target id="@+id/under_graph_temp" view="LinearLayout"><Expressions/><location startLine="1513" startOffset="12" endLine="1600" endOffset="26"/></Target><Target id="@+id/day_7_temp" view="TextView"><Expressions/><location startLine="1530" startOffset="16" endLine="1538" endOffset="46"/></Target><Target id="@+id/day_6_temp" view="TextView"><Expressions/><location startLine="1539" startOffset="16" endLine="1547" endOffset="46"/></Target><Target id="@+id/day_5_temp" view="TextView"><Expressions/><location startLine="1548" startOffset="16" endLine="1556" endOffset="46"/></Target><Target id="@+id/day_4_temp" view="TextView"><Expressions/><location startLine="1557" startOffset="16" endLine="1565" endOffset="46"/></Target><Target id="@+id/day_3_temp" view="TextView"><Expressions/><location startLine="1566" startOffset="16" endLine="1574" endOffset="46"/></Target><Target id="@+id/day_2_temp" view="TextView"><Expressions/><location startLine="1575" startOffset="16" endLine="1583" endOffset="46"/></Target><Target id="@+id/day_1_temp" view="TextView"><Expressions/><location startLine="1584" startOffset="16" endLine="1592" endOffset="46"/></Target><Target id="@+id/graph_temp" view="RelativeLayout"><Expressions/><location startLine="1601" startOffset="12" endLine="1808" endOffset="28"/></Target><Target id="@+id/t1_temp" view="TextView"><Expressions/><location startLine="1620" startOffset="20" endLine="1630" endOffset="51"/></Target><Target id="@+id/t2_temp" view="TextView"><Expressions/><location startLine="1631" startOffset="20" endLine="1641" endOffset="51"/></Target><Target id="@+id/t3_temp" view="TextView"><Expressions/><location startLine="1642" startOffset="20" endLine="1652" endOffset="51"/></Target><Target id="@+id/t4_temp" view="TextView"><Expressions/><location startLine="1653" startOffset="20" endLine="1663" endOffset="51"/></Target><Target id="@+id/t5_temp" view="TextView"><Expressions/><location startLine="1664" startOffset="20" endLine="1674" endOffset="51"/></Target><Target id="@+id/t6_temp" view="TextView"><Expressions/><location startLine="1675" startOffset="20" endLine="1685" endOffset="51"/></Target><Target id="@+id/t7_temp" view="TextView"><Expressions/><location startLine="1686" startOffset="20" endLine="1696" endOffset="51"/></Target><Target id="@+id/t8_temp" view="TextView"><Expressions/><location startLine="1697" startOffset="20" endLine="1707" endOffset="51"/></Target><Target id="@+id/t9_temp" view="TextView"><Expressions/><location startLine="1708" startOffset="20" endLine="1718" endOffset="51"/></Target><Target id="@+id/chart1_l" view="RelativeLayout"><Expressions/><location startLine="1795" startOffset="16" endLine="1807" endOffset="32"/></Target><Target id="@+id/chart1" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="1803" startOffset="20" endLine="1806" endOffset="62"/></Target><Target id="@+id/wear_rate2" view="TextView"><Expressions/><location startLine="1818" startOffset="12" endLine="1826" endOffset="50"/></Target><Target id="@+id/under_graph" view="LinearLayout"><Expressions/><location startLine="1844" startOffset="12" endLine="1930" endOffset="26"/></Target><Target id="@+id/day_7" view="TextView"><Expressions/><location startLine="1860" startOffset="16" endLine="1868" endOffset="46"/></Target><Target id="@+id/day_6" view="TextView"><Expressions/><location startLine="1869" startOffset="16" endLine="1877" endOffset="46"/></Target><Target id="@+id/day_5" view="TextView"><Expressions/><location startLine="1878" startOffset="16" endLine="1886" endOffset="46"/></Target><Target id="@+id/day_4" view="TextView"><Expressions/><location startLine="1887" startOffset="16" endLine="1895" endOffset="46"/></Target><Target id="@+id/day_3" view="TextView"><Expressions/><location startLine="1896" startOffset="16" endLine="1904" endOffset="46"/></Target><Target id="@+id/day_2" view="TextView"><Expressions/><location startLine="1905" startOffset="16" endLine="1913" endOffset="46"/></Target><Target id="@+id/day_1" view="TextView"><Expressions/><location startLine="1914" startOffset="16" endLine="1922" endOffset="46"/></Target><Target id="@+id/graph" view="RelativeLayout"><Expressions/><location startLine="1931" startOffset="12" endLine="2150" endOffset="28"/></Target><Target id="@+id/t1" view="TextView"><Expressions/><location startLine="1949" startOffset="20" endLine="1959" endOffset="51"/></Target><Target id="@+id/t2" view="TextView"><Expressions/><location startLine="1960" startOffset="20" endLine="1970" endOffset="51"/></Target><Target id="@+id/t3" view="TextView"><Expressions/><location startLine="1971" startOffset="20" endLine="1981" endOffset="51"/></Target><Target id="@+id/t4" view="TextView"><Expressions/><location startLine="1982" startOffset="20" endLine="1992" endOffset="51"/></Target><Target id="@+id/t5" view="TextView"><Expressions/><location startLine="1993" startOffset="20" endLine="2003" endOffset="51"/></Target><Target id="@+id/t6" view="TextView"><Expressions/><location startLine="2004" startOffset="20" endLine="2014" endOffset="51"/></Target><Target id="@+id/t7" view="TextView"><Expressions/><location startLine="2015" startOffset="20" endLine="2025" endOffset="51"/></Target><Target id="@+id/t8" view="TextView"><Expressions/><location startLine="2026" startOffset="20" endLine="2036" endOffset="51"/></Target><Target id="@+id/t9" view="TextView"><Expressions/><location startLine="2037" startOffset="20" endLine="2047" endOffset="51"/></Target><Target id="@+id/progbar_7" view="com.tqhit.battery.one.component.progress.VerticalProgressBar"><Expressions/><location startLine="2058" startOffset="20" endLine="2070" endOffset="73"/></Target><Target id="@+id/progbar_6" view="com.tqhit.battery.one.component.progress.VerticalProgressBar"><Expressions/><location startLine="2071" startOffset="20" endLine="2083" endOffset="73"/></Target><Target id="@+id/progbar_5" view="com.tqhit.battery.one.component.progress.VerticalProgressBar"><Expressions/><location startLine="2084" startOffset="20" endLine="2096" endOffset="73"/></Target><Target id="@+id/progbar_4" view="com.tqhit.battery.one.component.progress.VerticalProgressBar"><Expressions/><location startLine="2097" startOffset="20" endLine="2109" endOffset="73"/></Target><Target id="@+id/progbar_3" view="com.tqhit.battery.one.component.progress.VerticalProgressBar"><Expressions/><location startLine="2110" startOffset="20" endLine="2122" endOffset="73"/></Target><Target id="@+id/progbar_2" view="com.tqhit.battery.one.component.progress.VerticalProgressBar"><Expressions/><location startLine="2123" startOffset="20" endLine="2135" endOffset="73"/></Target><Target id="@+id/progbar_1" view="com.tqhit.battery.one.component.progress.VerticalProgressBar"><Expressions/><location startLine="2136" startOffset="20" endLine="2148" endOffset="73"/></Target><Target id="@+id/indent_down" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2152" startOffset="8" endLine="2157" endOffset="47"/></Target><Target id="@+id/update_view" view="LinearLayout"><Expressions/><location startLine="2158" startOffset="8" endLine="2186" endOffset="22"/></Target><Target id="@+id/update_view_btn" view="TextView"><Expressions/><location startLine="2168" startOffset="12" endLine="2185" endOffset="47"/></Target></Targets></Layout>