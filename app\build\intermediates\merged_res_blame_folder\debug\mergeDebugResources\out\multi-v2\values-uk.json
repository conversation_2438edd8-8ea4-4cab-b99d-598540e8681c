{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,11,17,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,252,253,254,255,256,257,258,259,260,261,262,265,266,267,268,269,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,721,20785,20869,20951,21034,21134,21233,21318,21381,21479,21578,21649,21718,21784,21852,21978,22103,22240,22317,22399,22474,22562,22657,22750,22818,23591,23644,23704,23752,23813,23880,23948,24012,24079,24144,24204,24401,24453,24514,24599,24684,24739", "endLines": "10,16,22,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,252,253,254,255,256,257,258,259,260,261,262,265,266,267,268,269,270", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,51,60,84,84,54,66", "endOffsets": "377,716,1046,20864,20946,21029,21129,21228,21313,21376,21474,21573,21644,21713,21779,21847,21973,22098,22235,22312,22394,22469,22557,22652,22745,22813,22898,23639,23699,23747,23808,23875,23943,24007,24074,24139,24199,24265,24448,24509,24594,24679,24734,24801"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-uk\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,267,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,268,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,269,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,23413,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,23470,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,23524,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,55,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,52,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,80,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,23464,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,23518,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,23600,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,47,48,51,52,63,64,66,67,68,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,117,118,119,120,121,129,130,131,134,135,136,137,138,139,140,141,142,143,144,145,146,168,169,170,171,174,175,176,177,178,179,180,181,182,183,184,185,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,211,212,213,214,215,217,272,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,298,299,301,303,304,305,307,308,309,310,311,312,313,314,315,316,374,375,376,377,391,392,438,445,446,447,448,449,452,453,454,455,456,457,458,459,468,469,470,471,472,473,474,476,477,478,479,480,481,482,483,484,485,487,488,489,490,491,492,495,496,497,498,499,500,508,509,513,515,516,517,518,519,520,521,522,524,525,526,527,528,529,530,531,535,536,538,541,543,544,545,546,547,550,551,552,553,554,555,558,559,560,561,563,564,565,566,567,568,569,570", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1325,1386,1439,1491,1542,1592,1644,1694,1743,1801,1870,1922,1973,2024,2074,2126,2413,2491,2770,2819,3806,3857,4039,4107,4186,4538,4608,4669,4725,4790,4829,4994,5052,5150,5192,5258,5345,5392,5651,5701,6022,6062,6130,6178,6224,6293,6341,6379,6462,6522,6600,6995,7395,7438,7504,7568,8213,8296,8380,8454,8531,8732,9018,9089,9145,9185,9691,9766,9814,9882,9950,10746,10791,10866,11154,11195,11273,11433,11507,11600,11679,11741,11832,11896,11963,12019,12074,14623,14688,14736,14808,15081,15133,15442,15499,15557,16070,16598,16664,16713,16851,16911,17179,17434,17859,17920,18042,18192,18329,18391,18436,18481,18559,18656,18741,18794,18928,19027,19083,19146,19211,19495,19748,19923,20081,20245,20330,20399,20453,20511,20625,24894,25388,25446,25654,25701,25773,25832,25887,26021,26079,26204,26415,26454,26517,26554,26591,26626,26661,26730,26770,26827,26941,27008,27160,27317,27383,27441,27560,27602,27643,27722,28087,28141,28216,28378,28446,28514,34851,34886,34921,34958,35953,36016,39786,40329,40407,40546,40580,40651,40870,40922,41007,41078,41152,41255,41366,41435,42263,42337,42560,42668,42839,42922,43026,43152,43191,43242,43292,43354,43429,43477,43557,43641,43690,43877,43921,44051,44125,44178,44269,44535,44593,44660,44841,44915,44971,45427,45499,45796,45927,46115,46195,46291,46367,46417,46489,46559,46715,46756,46811,46858,46977,47310,47391,47463,47767,47827,48068,48275,48450,48489,48545,48608,48796,49083,49149,49220,49274,49352,49421,49622,49676,49732,49795,49899,49936,49988,50040,50104,50160,50196,50231", "endColumns": "60,52,51,50,49,51,49,48,57,68,51,50,50,49,51,75,77,55,48,48,50,75,67,78,76,69,60,55,64,38,164,57,97,41,65,86,46,258,49,320,39,67,47,45,68,47,37,82,59,77,394,399,42,65,63,644,82,83,73,76,200,285,70,55,39,62,74,47,67,67,68,44,74,75,40,77,159,73,92,78,61,90,63,66,55,54,62,64,47,71,88,51,308,56,57,512,527,65,48,137,59,267,51,424,60,121,149,136,61,44,44,77,96,84,52,133,98,55,62,64,283,252,174,157,79,84,68,53,57,41,92,67,57,207,46,71,58,54,133,57,124,210,38,62,36,36,34,34,68,39,56,47,66,69,67,65,57,55,41,40,78,364,53,74,161,67,67,36,34,34,36,52,62,71,53,77,138,33,70,55,51,84,70,73,102,110,68,37,73,222,107,170,82,103,43,38,50,49,61,74,47,79,83,48,44,43,129,73,52,90,92,57,66,180,73,55,56,71,53,58,187,79,95,75,49,71,69,78,40,54,46,118,332,80,71,48,59,71,56,51,38,55,62,187,89,65,70,53,77,68,32,53,55,62,43,36,51,51,63,55,35,34,43", "endOffsets": "1381,1434,1486,1537,1587,1639,1689,1738,1796,1865,1917,1968,2019,2069,2121,2197,2486,2542,2814,2863,3852,3928,4102,4181,4258,4603,4664,4720,4785,4824,4989,5047,5145,5187,5253,5340,5387,5646,5696,6017,6057,6125,6173,6219,6288,6336,6374,6457,6517,6595,6990,7390,7433,7499,7563,8208,8291,8375,8449,8526,8727,9013,9084,9140,9180,9243,9761,9809,9877,9945,10014,10786,10861,10937,11190,11268,11428,11502,11595,11674,11736,11827,11891,11958,12014,12069,12132,14683,14731,14803,14892,15128,15437,15494,15552,16065,16593,16659,16708,16846,16906,17174,17226,17854,17915,18037,18187,18324,18386,18431,18476,18554,18651,18736,18789,18923,19022,19078,19141,19206,19490,19743,19918,20076,20156,20325,20394,20448,20506,20548,20713,24957,25441,25649,25696,25768,25827,25882,26016,26074,26199,26410,26449,26512,26549,26586,26621,26656,26725,26765,26822,26870,27003,27073,27223,27378,27436,27492,27597,27638,27717,28082,28136,28211,28373,28441,28509,28546,34881,34916,34953,35006,36011,36083,39835,40402,40541,40575,40646,40702,40917,41002,41073,41147,41250,41361,41430,41468,42332,42555,42663,42834,42917,43021,43065,43186,43237,43287,43349,43424,43472,43552,43636,43685,43730,43916,44046,44120,44173,44264,44357,44588,44655,44836,44910,44966,45023,45494,45548,45850,46110,46190,46286,46362,46412,46484,46554,46633,46751,46806,46853,46972,47305,47386,47458,47507,47822,47894,48120,48322,48484,48540,48603,48791,48881,49144,49215,49269,49347,49416,49449,49671,49727,49790,49834,49931,49983,50035,50099,50155,50191,50226,50270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "172,271,451,486,537,556,557", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14897,24806,40789,43735,47899,49454,49539", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "14966,24889,40865,43872,48063,49534,49617"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "148,149,186,187,210,300,302,444,450,493,494,514,532,533,539,540,542", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12220,12313,17231,17333,20161,27078,27228,40241,40707,44362,44447,45855,47512,47589,48125,48205,48327", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,76,76,79,69,122", "endOffsets": "12308,12392,17328,17429,20240,27155,27312,40324,40784,44442,44530,45922,47584,47661,48200,48270,48445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-uk\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12397,12505,12668,12795,12905,13059,13188,13303,13554,13722,13828,13990,14115,14262,14404,14474,14535", "endColumns": "107,162,126,109,153,128,114,104,167,105,161,124,146,141,69,60,87", "endOffsets": "12500,12663,12790,12900,13054,13183,13298,13403,13717,13823,13985,14110,14257,14399,14469,14530,14618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "45,46,49,50,53,54,55,56,57,58,59,60,61,62,65,69,70,510", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2202,2311,2547,2652,2868,2959,3052,3147,3241,3341,3434,3529,3624,3715,3933,4263,4368,45553", "endColumns": "108,101,104,117,90,92,94,93,99,92,94,94,90,90,105,104,169,81", "endOffsets": "2306,2408,2647,2765,2954,3047,3142,3236,3336,3429,3524,3619,3710,3801,4034,4363,4533,45630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-uk\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "13408", "endColumns": "145", "endOffsets": "13549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-uk\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "439,440,441,460,461,462,463,464,465,466,467,501,502,503,504,505,506,507,562", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39840,39895,39949,41473,41541,41690,41808,41937,41991,42054,42164,45028,45079,45162,45202,45243,45299,45377,49839", "endColumns": "54,53,63,67,148,117,128,53,62,109,98,50,82,39,40,55,77,49,59", "endOffsets": "39890,39944,40008,41536,41685,41803,41932,41986,42049,42159,42258,45074,45157,45197,45238,45294,45372,45422,49894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "548,549", "startColumns": "4,4", "startOffsets": "48886,48983", "endColumns": "96,99", "endOffsets": "48978,49078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "122,123,124,125,126,127,128,534", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "10019,10119,10221,10322,10423,10528,10633,47666", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "10114,10216,10317,10418,10523,10628,10741,47762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "243,244,245,246,247,248,249,250,251", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "22903,22977,23042,23110,23181,23261,23334,23427,23516", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "22972,23037,23105,23176,23256,23329,23422,23511,23586"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-uk\\values-uk.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "263,264", "startColumns": "4,4", "startOffsets": "24270,24335", "endColumns": "64,65", "endOffsets": "24330,24396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28551,28669,28785,28903,29021,29120,29215,29327,29465,29581,29728,29812,29912,30005,30101,30217,30341,30446,30587,30724,30859,31048,31175,31299,31428,31549,31643,31744,31870,32000,32098,32203,32312,32457,32608,32716,32816,32891,32986,33082,33201,33287,33374,33473,33553,33639,33738,33842,33937,34037,34126,34233,34329,34432,34550,34630,34745", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "28664,28780,28898,29016,29115,29210,29322,29460,29576,29723,29807,29907,30000,30096,30212,30336,30441,30582,30719,30854,31043,31170,31294,31423,31544,31638,31739,31865,31995,32093,32198,32307,32452,32603,32711,32811,32886,32981,33077,33196,33282,33369,33468,33548,33634,33733,33837,33932,34032,34121,34228,34324,34427,34545,34625,34740,34846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "23,112,113,114,115,116,132,133,147,216,218,273,297,306,378,379,380,381,382,383,384,385,386,387,388,389,390,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,475,511,512,523", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1051,9248,9326,9404,9492,9600,10942,11038,12137,20553,20718,24962,26875,27497,35011,35099,35161,35228,35286,35357,35416,35470,35584,35644,35707,35761,35834,36088,36174,36250,36341,36422,36505,36644,36729,36816,36949,37037,37115,37172,37223,37289,37361,37437,37508,37591,37664,37741,37823,37897,38006,38096,38175,38266,38362,38436,38517,38612,38666,38748,38814,38901,38987,39049,39113,39176,39249,39356,39466,39564,39670,39731,43070,45635,45720,46638", "endLines": "28,112,113,114,115,116,132,133,147,216,218,273,297,306,378,379,380,381,382,383,384,385,386,387,388,389,390,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,475,511,512,523", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,132,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81,84,75,76", "endOffsets": "1320,9321,9399,9487,9595,9686,11033,11149,12215,20620,20780,25048,26936,27555,35094,35156,35223,35281,35352,35411,35465,35579,35639,35702,35756,35829,35948,36169,36245,36336,36417,36500,36639,36724,36811,36944,37032,37110,37167,37218,37284,37356,37432,37503,37586,37659,37736,37818,37892,38001,38091,38170,38261,38357,38431,38512,38607,38661,38743,38809,38896,38982,39044,39108,39171,39244,39351,39461,39559,39665,39726,39781,43147,45715,45791,46710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "173,274,275,276", "startColumns": "4,4,4,4", "startOffsets": "14971,25053,25160,25280", "endColumns": "109,106,119,107", "endOffsets": "15076,25155,25275,25383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-uk\\values-uk.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "442,443", "startColumns": "4,4", "startOffsets": "40013,40121", "endColumns": "107,119", "endOffsets": "40116,40236"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tqhit.battery.one.app-mergeDebugResources-132:\\values-uk\\values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,626,956,1040,1122,1205,1305,1404,1489,1552,1650,1749,1820,1889,1955,2023,2149,2274,2411,2488,2570,2645,2733,2828,2921,2989,3074,3127,3187,3235,3296,3363,3431,3495,3562,3627,3687,3753,3805,3866,3951,4036,4091", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,51,60,84,84,54,66", "endOffsets": "282,621,951,1035,1117,1200,1300,1399,1484,1547,1645,1744,1815,1884,1950,2018,2144,2269,2406,2483,2565,2640,2728,2823,2916,2984,3069,3122,3182,3230,3291,3358,3426,3490,3557,3622,3682,3748,3800,3861,3946,4031,4086,4153"}, "to": {"startLines": "2,11,17,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,252,253,254,255,256,257,258,259,260,261,262,265,266,267,268,269,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,721,20785,20869,20951,21034,21134,21233,21318,21381,21479,21578,21649,21718,21784,21852,21978,22103,22240,22317,22399,22474,22562,22657,22750,22818,23591,23644,23704,23752,23813,23880,23948,24012,24079,24144,24204,24401,24453,24514,24599,24684,24739", "endLines": "10,16,22,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,252,253,254,255,256,257,258,259,260,261,262,265,266,267,268,269,270", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,51,60,84,84,54,66", "endOffsets": "377,716,1046,20864,20946,21029,21129,21228,21313,21376,21474,21573,21644,21713,21779,21847,21973,22098,22235,22312,22394,22469,22557,22652,22745,22813,22898,23639,23699,23747,23808,23875,23943,24007,24074,24139,24199,24265,24448,24509,24594,24679,24734,24801"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-uk\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "126,188,242,295,347,398,451,502,552,611,681,734,786,838,889,20800,942,1021,1078,1128,1178,1230,1307,1376,1456,1534,1605,1667,23300,1724,1764,1930,1989,2088,2131,2198,20991,21797,22233,22365,2286,57,23251,21187,23124,2327,2376,2415,2499,2560,2639,3035,3436,11721,11788,11075,10991,11853,3480,3555,3633,3835,4122,4194,4251,4292,4356,4432,4481,20877,4550,4620,21111,4666,4743,4785,4864,5025,5100,5194,5274,5337,5429,21672,5494,5551,5607,5671,5737,5786,5859,5949,6002,6312,6370,6429,6943,7472,7539,7589,7728,7789,8058,8111,8537,8599,8722,8873,9011,9074,9120,9166,9245,9343,9429,9483,9618,9718,9775,9839,9905,10190,10444,10620,10779,10860,11940,12010,22921,12065,12108,12202,12271,12330,12539,12587,12660,12720,12776,12911,12970,13096,13308,13348,13412,13450,13488,13524,13560,13630,13671,13729,13778,13846,22980,13917,13984,14043,14086,14128,14208,14574,14629,14705,14868,14937,15006,15044,15080,15116,15154,15208,15272,15345,15400,15479,15619,21039,15654,15711,15764,15850,15922,15997,16101,16213,16283,16322,16397,21490,21318,21234,22128,22687,16621,16661,16713,16764,16827,16903,22284,16952,17037,17087,20946,17133,23049,17264,17356,17450,17509,17577,17759,17834,17891,21599,17949,18004,22732,18064,18145,18242,18319,18370,22057,18443,18523,18565,18621,18669,18789,19123,19196,19246,19307,19380,19438,19491,23194,19531,19595,19784,19875,19942,20014,20069,20148,20218,20252,21740,20307,20371,20416,20454,20507,20560,20625,20682,20719,20755", "endColumns": "60,52,51,50,49,51,49,48,57,68,51,50,50,49,51,75,77,55,48,48,50,75,67,78,76,69,60,55,64,38,164,57,97,41,65,86,46,258,49,320,39,67,47,45,68,47,37,82,59,77,394,399,42,65,63,644,82,83,73,76,200,285,70,55,39,62,74,47,67,67,68,44,74,75,40,77,159,73,92,78,61,90,63,66,55,54,62,64,47,71,88,51,308,56,57,512,527,65,48,137,59,267,51,424,60,121,149,136,61,44,44,77,96,84,52,133,98,55,62,64,283,252,174,157,79,84,68,53,57,41,92,67,57,207,46,71,58,54,133,57,124,210,38,62,36,36,34,34,68,39,56,47,66,69,67,65,57,41,40,78,364,53,74,161,67,67,36,34,34,36,52,62,71,53,77,138,33,70,55,51,84,70,73,102,110,68,37,73,222,107,170,82,103,43,38,50,49,61,74,47,79,83,48,44,43,129,73,90,92,57,66,180,73,55,56,71,53,58,187,79,95,75,49,71,69,78,40,54,46,118,332,71,48,59,71,56,51,38,55,62,187,89,65,70,53,77,68,32,53,55,62,43,36,51,51,63,55,35,34,43", "endOffsets": "182,236,289,341,392,445,496,546,605,675,728,780,832,883,936,20871,1015,1072,1122,1172,1224,1301,1370,1450,1528,1599,1661,1718,23360,1758,1924,1983,2082,2125,2192,2280,21033,22051,22278,22681,2321,120,23294,21228,23188,2370,2409,2493,2554,2633,3029,3430,3474,11782,11847,11715,11069,11932,3549,3627,3829,4116,4188,4245,4286,4350,4426,4475,4544,20940,4614,4660,21181,4737,4779,4858,5019,5094,5188,5268,5331,5423,5488,21734,5545,5601,5665,5731,5780,5853,5943,5996,6306,6364,6423,6937,7466,7533,7583,7722,7783,8052,8105,8531,8593,8716,8867,9005,9068,9114,9160,9239,9337,9423,9477,9612,9712,9769,9833,9899,10184,10438,10614,10773,10854,10940,12004,12059,22974,12102,12196,12265,12324,12533,12581,12654,12714,12770,12905,12964,13090,13302,13342,13406,13444,13482,13518,13554,13624,13665,13723,13772,13840,13911,23043,13978,14037,14080,14122,14202,14568,14623,14699,14862,14931,15000,15038,15074,15110,15148,15202,15266,15339,15394,15473,15613,15648,21105,15705,15758,15844,15916,15991,16095,16207,16277,16316,16391,16615,21593,21484,21312,22227,22726,16655,16707,16758,16821,16897,16946,22359,17031,17081,17127,20985,17258,23118,17350,17444,17503,17571,17753,17828,17885,17943,21666,17998,18058,22915,18139,18236,18313,18364,18437,22122,18517,18559,18615,18663,18783,19117,19190,19240,19301,19374,19432,19485,19525,23245,19589,19778,19869,19936,20008,20063,20142,20212,20246,20301,21791,20365,20410,20448,20501,20554,20619,20676,20713,20749,20794"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,47,48,51,52,63,64,66,67,68,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,117,118,119,120,121,129,130,131,134,135,136,137,138,139,140,141,142,143,144,145,146,168,169,170,171,174,175,176,177,178,179,180,181,182,183,184,185,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,211,212,213,214,215,217,272,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,298,299,301,303,304,306,307,308,309,310,311,312,313,314,315,373,374,375,376,390,391,437,444,445,446,447,448,451,452,453,454,455,456,457,458,467,468,469,470,471,472,473,475,476,477,478,479,480,481,482,483,484,486,487,488,489,490,493,494,495,496,497,498,506,507,511,513,514,515,516,517,518,519,520,522,523,524,525,526,527,528,532,533,535,538,540,541,542,543,544,547,548,549,550,551,552,555,556,557,558,560,561,562,563,564,565,566,567", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1325,1386,1439,1491,1542,1592,1644,1694,1743,1801,1870,1922,1973,2024,2074,2126,2413,2491,2770,2819,3806,3857,4039,4107,4186,4538,4608,4669,4725,4790,4829,4994,5052,5150,5192,5258,5345,5392,5651,5701,6022,6062,6130,6178,6224,6293,6341,6379,6462,6522,6600,6995,7395,7438,7504,7568,8213,8296,8380,8454,8531,8732,9018,9089,9145,9185,9691,9766,9814,9882,9950,10746,10791,10866,11154,11195,11273,11433,11507,11600,11679,11741,11832,11896,11963,12019,12074,14623,14688,14736,14808,15081,15133,15442,15499,15557,16070,16598,16664,16713,16851,16911,17179,17434,17859,17920,18042,18192,18329,18391,18436,18481,18559,18656,18741,18794,18928,19027,19083,19146,19211,19495,19748,19923,20081,20245,20330,20399,20453,20511,20625,24894,25388,25446,25654,25701,25773,25832,25887,26021,26079,26204,26415,26454,26517,26554,26591,26626,26661,26730,26770,26827,26941,27008,27160,27317,27383,27504,27546,27587,27666,28031,28085,28160,28322,28390,28458,34795,34830,34865,34902,35897,35960,39730,40273,40351,40490,40524,40595,40814,40866,40951,41022,41096,41199,41310,41379,42207,42281,42504,42612,42783,42866,42970,43096,43135,43186,43236,43298,43373,43421,43501,43585,43634,43821,43865,43995,44069,44160,44426,44484,44551,44732,44806,44862,45318,45390,45687,45818,46006,46086,46182,46258,46308,46380,46450,46606,46647,46702,46749,46868,47201,47273,47577,47637,47878,48085,48260,48299,48355,48418,48606,48893,48959,49030,49084,49162,49231,49432,49486,49542,49605,49709,49746,49798,49850,49914,49970,50006,50041", "endColumns": "60,52,51,50,49,51,49,48,57,68,51,50,50,49,51,75,77,55,48,48,50,75,67,78,76,69,60,55,64,38,164,57,97,41,65,86,46,258,49,320,39,67,47,45,68,47,37,82,59,77,394,399,42,65,63,644,82,83,73,76,200,285,70,55,39,62,74,47,67,67,68,44,74,75,40,77,159,73,92,78,61,90,63,66,55,54,62,64,47,71,88,51,308,56,57,512,527,65,48,137,59,267,51,424,60,121,149,136,61,44,44,77,96,84,52,133,98,55,62,64,283,252,174,157,79,84,68,53,57,41,92,67,57,207,46,71,58,54,133,57,124,210,38,62,36,36,34,34,68,39,56,47,66,69,67,65,57,41,40,78,364,53,74,161,67,67,36,34,34,36,52,62,71,53,77,138,33,70,55,51,84,70,73,102,110,68,37,73,222,107,170,82,103,43,38,50,49,61,74,47,79,83,48,44,43,129,73,90,92,57,66,180,73,55,56,71,53,58,187,79,95,75,49,71,69,78,40,54,46,118,332,71,48,59,71,56,51,38,55,62,187,89,65,70,53,77,68,32,53,55,62,43,36,51,51,63,55,35,34,43", "endOffsets": "1381,1434,1486,1537,1587,1639,1689,1738,1796,1865,1917,1968,2019,2069,2121,2197,2486,2542,2814,2863,3852,3928,4102,4181,4258,4603,4664,4720,4785,4824,4989,5047,5145,5187,5253,5340,5387,5646,5696,6017,6057,6125,6173,6219,6288,6336,6374,6457,6517,6595,6990,7390,7433,7499,7563,8208,8291,8375,8449,8526,8727,9013,9084,9140,9180,9243,9761,9809,9877,9945,10014,10786,10861,10937,11190,11268,11428,11502,11595,11674,11736,11827,11891,11958,12014,12069,12132,14683,14731,14803,14892,15128,15437,15494,15552,16065,16593,16659,16708,16846,16906,17174,17226,17854,17915,18037,18187,18324,18386,18431,18476,18554,18651,18736,18789,18923,19022,19078,19141,19206,19490,19743,19918,20076,20156,20325,20394,20448,20506,20548,20713,24957,25441,25649,25696,25768,25827,25882,26016,26074,26199,26410,26449,26512,26549,26586,26621,26656,26725,26765,26822,26870,27003,27073,27223,27378,27436,27541,27582,27661,28026,28080,28155,28317,28385,28453,28490,34825,34860,34897,34950,35955,36027,39779,40346,40485,40519,40590,40646,40861,40946,41017,41091,41194,41305,41374,41412,42276,42499,42607,42778,42861,42965,43009,43130,43181,43231,43293,43368,43416,43496,43580,43629,43674,43860,43990,44064,44155,44248,44479,44546,44727,44801,44857,44914,45385,45439,45741,46001,46081,46177,46253,46303,46375,46445,46524,46642,46697,46744,46863,47196,47268,47317,47632,47704,47930,48132,48294,48350,48413,48601,48691,48954,49025,49079,49157,49226,49259,49481,49537,49600,49644,49741,49793,49845,49909,49965,50001,50036,50080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,267,348,490,659,744", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "174,262,343,485,654,739,822"}, "to": {"startLines": "172,271,450,485,534,553,554", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14897,24806,40733,43679,47709,49264,49349", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "14966,24889,40809,43816,47873,49344,49427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,384,485,569,651,740,828,910,995,1083,1231,1308,1385,1465,1535", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,76,76,79,69,122", "endOffsets": "193,277,379,480,564,646,735,823,905,990,1078,1150,1303,1380,1460,1530,1653"}, "to": {"startLines": "148,149,186,187,210,300,302,443,449,491,492,512,529,530,536,537,539", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12220,12313,17231,17333,20161,27078,27228,40185,40651,44253,44338,45746,47322,47399,47935,48015,48137", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,76,76,79,69,122", "endOffsets": "12308,12392,17328,17429,20240,27155,27312,40268,40728,44333,44421,45813,47394,47471,48010,48080,48255"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-uk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,579,685,835,960,1071,1172,1336,1438,1596,1717,1860,1998,2064,2121", "endColumns": "103,158,122,105,149,124,110,100,163,101,157,120,142,137,65,56,83", "endOffsets": "296,455,578,684,834,959,1070,1171,1335,1437,1595,1716,1859,1997,2063,2120,2204"}, "to": {"startLines": "150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12397,12505,12668,12795,12905,13059,13188,13303,13554,13722,13828,13990,14115,14262,14404,14474,14535", "endColumns": "107,162,126,109,153,128,114,104,167,105,161,124,146,141,69,60,87", "endOffsets": "12500,12663,12790,12900,13054,13183,13298,13403,13717,13823,13985,14110,14257,14399,14469,14530,14618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,510,615,898,989,1082,1177,1271,1371,1464,1559,1654,1745,2041,2459,2564,2834", "endColumns": "108,101,104,117,90,92,94,93,99,92,94,94,90,90,105,104,169,81", "endOffsets": "209,311,610,728,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,2142,2559,2729,2911"}, "to": {"startLines": "45,46,49,50,53,54,55,56,57,58,59,60,61,62,65,69,70,508", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2202,2311,2547,2652,2868,2959,3052,3147,3241,3341,3434,3529,3624,3715,3933,4263,4368,45444", "endColumns": "108,101,104,117,90,92,94,93,99,92,94,94,90,90,105,104,169,81", "endOffsets": "2306,2408,2647,2765,2954,3047,3142,3236,3336,3429,3524,3619,3710,3801,4034,4363,4533,45521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-uk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "13408", "endColumns": "145", "endOffsets": "13549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-uk\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,250,300,596,660,805,919,1044,1094,1153,1259,1354,1401,1480,1516,1553,1605,1679,1725", "endColumns": "50,49,59,63,144,113,124,49,58,105,94,46,78,35,36,51,73,45,55", "endOffsets": "249,299,359,659,804,918,1043,1093,1152,1258,1353,1400,1479,1515,1552,1604,1678,1724,1780"}, "to": {"startLines": "438,439,440,459,460,461,462,463,464,465,466,499,500,501,502,503,504,505,559", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39784,39839,39893,41417,41485,41634,41752,41881,41935,41998,42108,44919,44970,45053,45093,45134,45190,45268,49649", "endColumns": "54,53,63,67,148,117,128,53,62,109,98,50,82,39,40,55,77,49,59", "endOffsets": "39834,39888,39952,41480,41629,41747,41876,41930,41993,42103,42202,44965,45048,45088,45129,45185,45263,45313,49704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,99", "endOffsets": "147,247"}, "to": {"startLines": "545,546", "startColumns": "4,4", "startOffsets": "48696,48793", "endColumns": "96,99", "endOffsets": "48788,48888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "122,123,124,125,126,127,128,531", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "10019,10119,10221,10322,10423,10528,10633,47476", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "10114,10216,10317,10418,10523,10628,10741,47572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,262,333,413,486,579,668", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "124,189,257,328,408,481,574,663,738"}, "to": {"startLines": "243,244,245,246,247,248,249,250,251", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "22903,22977,23042,23110,23181,23261,23334,23427,23516", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "22972,23037,23105,23176,23256,23329,23422,23511,23586"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-uk\\values-uk.xml", "from": {"startLines": "58,59", "startColumns": "4,4", "startOffsets": "3753,3818", "endColumns": "64,65", "endOffsets": "3813,3879"}, "to": {"startLines": "263,264", "startColumns": "4,4", "startOffsets": "24270,24335", "endColumns": "64,65", "endOffsets": "24330,24396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,407,525,624,719,831,969,1085,1232,1316,1416,1509,1605,1721,1845,1950,2091,2228,2363,2552,2679,2803,2932,3053,3147,3248,3374,3504,3602,3707,3816,3961,4112,4220,4320,4395,4490,4586,4705,4791,4878,4977,5057,5143,5242,5346,5441,5541,5630,5737,5833,5936,6054,6134,6249", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "168,284,402,520,619,714,826,964,1080,1227,1311,1411,1504,1600,1716,1840,1945,2086,2223,2358,2547,2674,2798,2927,3048,3142,3243,3369,3499,3597,3702,3811,3956,4107,4215,4315,4390,4485,4581,4700,4786,4873,4972,5052,5138,5237,5341,5436,5536,5625,5732,5828,5931,6049,6129,6244,6350"}, "to": {"startLines": "316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28495,28613,28729,28847,28965,29064,29159,29271,29409,29525,29672,29756,29856,29949,30045,30161,30285,30390,30531,30668,30803,30992,31119,31243,31372,31493,31587,31688,31814,31944,32042,32147,32256,32401,32552,32660,32760,32835,32930,33026,33145,33231,33318,33417,33497,33583,33682,33786,33881,33981,34070,34177,34273,34376,34494,34574,34689", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "28608,28724,28842,28960,29059,29154,29266,29404,29520,29667,29751,29851,29944,30040,30156,30280,30385,30526,30663,30798,30987,31114,31238,31367,31488,31582,31683,31809,31939,32037,32142,32251,32396,32547,32655,32755,32830,32925,33021,33140,33226,33313,33412,33492,33578,33677,33781,33876,33976,34065,34172,34268,34371,34489,34569,34684,34790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,726,817,913,1029,1112,1184,1251,1342,1408,1471,1559,1621,1688,1746,1817,1876,1930,2044,2104,2167,2221,2294,2413,2499,2575,2666,2747,2830,2969,3054,3141,3274,3362,3440,3497,3548,3614,3686,3762,3833,3916,3989,4066,4148,4222,4331,4421,4500,4591,4687,4761,4842,4937,4991,5073,5139,5226,5312,5374,5438,5501,5574,5681,5791,5889,5995,6056,6111,6193,6278,6354", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,132,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81,84,75,76", "endOffsets": "369,447,525,613,721,812,908,1024,1107,1179,1246,1337,1403,1466,1554,1616,1683,1741,1812,1871,1925,2039,2099,2162,2216,2289,2408,2494,2570,2661,2742,2825,2964,3049,3136,3269,3357,3435,3492,3543,3609,3681,3757,3828,3911,3984,4061,4143,4217,4326,4416,4495,4586,4682,4756,4837,4932,4986,5068,5134,5221,5307,5369,5433,5496,5569,5676,5786,5884,5990,6051,6106,6188,6273,6349,6426"}, "to": {"startLines": "23,112,113,114,115,116,132,133,147,216,218,273,297,305,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,474,509,510,521", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1051,9248,9326,9404,9492,9600,10942,11038,12137,20553,20718,24962,26875,27441,34955,35043,35105,35172,35230,35301,35360,35414,35528,35588,35651,35705,35778,36032,36118,36194,36285,36366,36449,36588,36673,36760,36893,36981,37059,37116,37167,37233,37305,37381,37452,37535,37608,37685,37767,37841,37950,38040,38119,38210,38306,38380,38461,38556,38610,38692,38758,38845,38931,38993,39057,39120,39193,39300,39410,39508,39614,39675,43014,45526,45611,46529", "endLines": "28,112,113,114,115,116,132,133,147,216,218,273,297,305,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,474,509,510,521", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,132,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81,84,75,76", "endOffsets": "1320,9321,9399,9487,9595,9686,11033,11149,12215,20620,20780,25048,26936,27499,35038,35100,35167,35225,35296,35355,35409,35523,35583,35646,35700,35773,35892,36113,36189,36280,36361,36444,36583,36668,36755,36888,36976,37054,37111,37162,37228,37300,37376,37447,37530,37603,37680,37762,37836,37945,38035,38114,38205,38301,38375,38456,38551,38605,38687,38753,38840,38926,38988,39052,39115,39188,39295,39405,39503,39609,39670,39725,43091,45606,45682,46601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,107", "endOffsets": "160,267,387,495"}, "to": {"startLines": "173,274,275,276", "startColumns": "4,4,4,4", "startOffsets": "14971,25053,25160,25280", "endColumns": "109,106,119,107", "endOffsets": "15076,25155,25275,25383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,119", "endOffsets": "158,278"}, "to": {"startLines": "441,442", "startColumns": "4,4", "startOffsets": "39957,40065", "endColumns": "107,119", "endOffsets": "40060,40180"}}]}]}