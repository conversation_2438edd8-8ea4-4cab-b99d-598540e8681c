<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="section_charge_battery_wear" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\section_charge_battery_wear.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/battery_wear_root"><Targets><Target id="@+id/battery_wear_root" tag="layout/section_charge_battery_wear_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="9" startOffset="0" endLine="128" endOffset="51"/></Target><Target id="@+id/battery_wear_title" view="TextView"><Expressions/><location startLine="25" startOffset="4" endLine="34" endOffset="69"/></Target><Target id="@+id/battery_wear_info" view="ImageView"><Expressions/><location startLine="36" startOffset="4" endLine="46" endOffset="58"/></Target><Target id="@+id/divider_wear" view="View"><Expressions/><location startLine="48" startOffset="4" endLine="56" endOffset="71"/></Target><Target id="@+id/battery_alarm_btn" view="TextView"><Expressions/><location startLine="59" startOffset="4" endLine="77" endOffset="65"/></Target><Target id="@+id/target_percent_label" view="TextView"><Expressions/><location startLine="80" startOffset="4" endLine="89" endOffset="70"/></Target><Target id="@+id/target_percent_value" view="TextView"><Expressions/><location startLine="91" startOffset="4" endLine="102" endOffset="70"/></Target><Target id="@+id/target_percent_seekbar" view="SeekBar"><Expressions/><location startLine="105" startOffset="4" endLine="114" endOffset="73"/></Target><Target id="@+id/battery_wear_description" view="TextView"><Expressions/><location startLine="117" startOffset="4" endLine="127" endOffset="75"/></Target></Targets></Layout>