// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentNewChargeBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final SectionChargeBatteryWearBinding chargeBatteryWearSection;

  @NonNull
  public final SectionChargeCurrentSessionBinding chargeCurrentSessionSection;

  @NonNull
  public final SectionChargeMainDisplayBinding chargeMainDisplaySection;

  @NonNull
  public final SectionChargeOverallAverageBinding chargeOverallAverageSection;

  @NonNull
  public final SectionChargeRemainingTimeBinding chargeRemainingTimeSection;

  @NonNull
  public final SectionChargeStatusDetailsBinding chargeStatusDetailsSection;

  @NonNull
  public final ShimmerFrameLayout nativeAd;

  @NonNull
  public final TextView newChargeNotChargingMessage;

  @NonNull
  public final NestedScrollView newChargeScrollView;

  @NonNull
  public final LinearLayout promoContainer;

  private FragmentNewChargeBinding(@NonNull NestedScrollView rootView,
      @NonNull SectionChargeBatteryWearBinding chargeBatteryWearSection,
      @NonNull SectionChargeCurrentSessionBinding chargeCurrentSessionSection,
      @NonNull SectionChargeMainDisplayBinding chargeMainDisplaySection,
      @NonNull SectionChargeOverallAverageBinding chargeOverallAverageSection,
      @NonNull SectionChargeRemainingTimeBinding chargeRemainingTimeSection,
      @NonNull SectionChargeStatusDetailsBinding chargeStatusDetailsSection,
      @NonNull ShimmerFrameLayout nativeAd, @NonNull TextView newChargeNotChargingMessage,
      @NonNull NestedScrollView newChargeScrollView, @NonNull LinearLayout promoContainer) {
    this.rootView = rootView;
    this.chargeBatteryWearSection = chargeBatteryWearSection;
    this.chargeCurrentSessionSection = chargeCurrentSessionSection;
    this.chargeMainDisplaySection = chargeMainDisplaySection;
    this.chargeOverallAverageSection = chargeOverallAverageSection;
    this.chargeRemainingTimeSection = chargeRemainingTimeSection;
    this.chargeStatusDetailsSection = chargeStatusDetailsSection;
    this.nativeAd = nativeAd;
    this.newChargeNotChargingMessage = newChargeNotChargingMessage;
    this.newChargeScrollView = newChargeScrollView;
    this.promoContainer = promoContainer;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentNewChargeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentNewChargeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_new_charge, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentNewChargeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.chargeBatteryWearSection;
      View chargeBatteryWearSection = ViewBindings.findChildViewById(rootView, id);
      if (chargeBatteryWearSection == null) {
        break missingId;
      }
      SectionChargeBatteryWearBinding binding_chargeBatteryWearSection = SectionChargeBatteryWearBinding.bind(chargeBatteryWearSection);

      id = R.id.chargeCurrentSessionSection;
      View chargeCurrentSessionSection = ViewBindings.findChildViewById(rootView, id);
      if (chargeCurrentSessionSection == null) {
        break missingId;
      }
      SectionChargeCurrentSessionBinding binding_chargeCurrentSessionSection = SectionChargeCurrentSessionBinding.bind(chargeCurrentSessionSection);

      id = R.id.chargeMainDisplaySection;
      View chargeMainDisplaySection = ViewBindings.findChildViewById(rootView, id);
      if (chargeMainDisplaySection == null) {
        break missingId;
      }
      SectionChargeMainDisplayBinding binding_chargeMainDisplaySection = SectionChargeMainDisplayBinding.bind(chargeMainDisplaySection);

      id = R.id.chargeOverallAverageSection;
      View chargeOverallAverageSection = ViewBindings.findChildViewById(rootView, id);
      if (chargeOverallAverageSection == null) {
        break missingId;
      }
      SectionChargeOverallAverageBinding binding_chargeOverallAverageSection = SectionChargeOverallAverageBinding.bind(chargeOverallAverageSection);

      id = R.id.chargeRemainingTimeSection;
      View chargeRemainingTimeSection = ViewBindings.findChildViewById(rootView, id);
      if (chargeRemainingTimeSection == null) {
        break missingId;
      }
      SectionChargeRemainingTimeBinding binding_chargeRemainingTimeSection = SectionChargeRemainingTimeBinding.bind(chargeRemainingTimeSection);

      id = R.id.chargeStatusDetailsSection;
      View chargeStatusDetailsSection = ViewBindings.findChildViewById(rootView, id);
      if (chargeStatusDetailsSection == null) {
        break missingId;
      }
      SectionChargeStatusDetailsBinding binding_chargeStatusDetailsSection = SectionChargeStatusDetailsBinding.bind(chargeStatusDetailsSection);

      id = R.id.nativeAd;
      ShimmerFrameLayout nativeAd = ViewBindings.findChildViewById(rootView, id);
      if (nativeAd == null) {
        break missingId;
      }

      id = R.id.newChargeNotChargingMessage;
      TextView newChargeNotChargingMessage = ViewBindings.findChildViewById(rootView, id);
      if (newChargeNotChargingMessage == null) {
        break missingId;
      }

      NestedScrollView newChargeScrollView = (NestedScrollView) rootView;

      id = R.id.promo_container;
      LinearLayout promoContainer = ViewBindings.findChildViewById(rootView, id);
      if (promoContainer == null) {
        break missingId;
      }

      return new FragmentNewChargeBinding((NestedScrollView) rootView,
          binding_chargeBatteryWearSection, binding_chargeCurrentSessionSection,
          binding_chargeMainDisplaySection, binding_chargeOverallAverageSection,
          binding_chargeRemainingTimeSection, binding_chargeStatusDetailsSection, nativeAd,
          newChargeNotChargingMessage, newChargeScrollView, promoContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
