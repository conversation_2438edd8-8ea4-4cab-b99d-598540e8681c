<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
    <LinearLayout
        android:orientation="vertical"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">
        <LinearLayout
            android:gravity="center"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:background="@drawable/ic_health_1"
            android:visibility="visible"
            android:layout_width="105dp"
            android:layout_height="106dp"/>
        <TextView
            android:textSize="22sp"
            android:textColor="?attr/black"
            android:gravity="center"
            android:id="@+id/textView4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/important_information"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="14dp"/>
        <TextView
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:gravity="center"
            android:id="@+id/textView16"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:layout_marginBottom="15dp"
            android:text="@string/dont_kill_my_app1"
            android:textAlignment="gravity"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"/>
        <TextView
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:gravity="center"
            android:id="@+id/textView15"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="7dp"
            android:text="@string/dont_kill_my_app2"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"/>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:background="@drawable/white_block"
            android:padding="8dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="7dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp">
            <Button
                android:textColor="?attr/black"
                android:gravity="top|center_horizontal"
                android:id="@+id/dontkillmyapp_button"
                android:background="@drawable/grey_block"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:longClickable="false"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="3dp"
                android:layout_marginBottom="8dp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="@string/DontKillMyApp"
                android:textAllCaps="false"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                style="@style/Widget.AppCompat.Button.Borderless"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <TextView
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:gravity="center"
            android:id="@+id/textView18"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="7dp"
            android:text="@string/dont_kill_my_app3"
            android:textAlignment="gravity"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"/>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:background="@drawable/white_block"
            android:padding="8dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp">
            <Button
                android:textColor="?attr/black"
                android:gravity="top|center_horizontal"
                android:id="@+id/work_in_background_permission"
                android:background="@drawable/grey_block"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:longClickable="false"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="3dp"
                android:layout_marginBottom="8dp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="@string/dont_kill_my_app_permission"
                android:textAllCaps="false"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                style="@style/Widget.AppCompat.Button.Borderless"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/autorun_view"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/textView19"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="7dp"
                android:text="@string/dont_kill_my_app4"
                android:textAlignment="gravity"
                android:layout_marginStart="15dp"
                android:layout_marginEnd="15dp"
                android:justificationMode="inter_word"/>
            <LinearLayout
                android:orientation="vertical"
                android:background="@drawable/white_block"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="4dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:elevation="1dp">
                <RelativeLayout
                    android:gravity="center"
                    android:id="@+id/button_layout"
                    android:background="@drawable/grey_block"
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:elevation="5dp">
                    <Button
                        android:gravity="top|center_horizontal"
                        android:id="@+id/buttonEnable"
                        android:background="@drawable/grey_block"
                        android:longClickable="false"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_alignTop="@+id/text_view_reset_charge"
                        android:layout_alignBottom="@+id/text_view_reset_charge"
                        android:layout_alignParentBottom="false"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:gravity="center"
                        android:id="@+id/text_view_reset_charge"
                        android:paddingTop="12.5dp"
                        android:paddingBottom="12.5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/settings_autostart"
                        android:paddingStart="10dp"
                        android:paddingEnd="10dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"/>
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>

    <RelativeLayout
        android:layout_gravity="end"
        android:id="@+id/button"
        android:background="@drawable/white_block_round"
        android:visibility="visible"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginBottom="50dp"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="30dp"
        android:layout_alignParentEnd="true"
        android:stateListAnimator="@null"
        android:outlineProvider="background">
        <LinearLayout
            android:orientation="vertical"
            android:background="@drawable/white_block_round"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"/>
        <Button
            android:id="@+id/next_page"
            android:background="@drawable/button_static"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="8dp"
            android:layout_centerInParent="true"
            style="@style/Widget.AppCompat.Button.Borderless"/>
        <LinearLayout
            android:orientation="vertical"
            android:background="@drawable/ic_strelka"
            android:visibility="visible"
            android:layout_width="26dp"
            android:layout_height="20dp"
            android:layout_centerInParent="true"
            android:scaleX="-1"/>
    </RelativeLayout>
</LinearLayout>
