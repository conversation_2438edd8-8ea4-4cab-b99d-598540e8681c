// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutBackNavigationBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout backNavigationContainer;

  @NonNull
  public final ImageButton btnBackNavigation;

  @NonNull
  public final TextView tvBackNavigationTitle;

  private LayoutBackNavigationBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout backNavigationContainer, @NonNull ImageButton btnBackNavigation,
      @NonNull TextView tvBackNavigationTitle) {
    this.rootView = rootView;
    this.backNavigationContainer = backNavigationContainer;
    this.btnBackNavigation = btnBackNavigation;
    this.tvBackNavigationTitle = tvBackNavigationTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutBackNavigationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutBackNavigationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_back_navigation, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutBackNavigationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      ConstraintLayout backNavigationContainer = (ConstraintLayout) rootView;

      id = R.id.btn_back_navigation;
      ImageButton btnBackNavigation = ViewBindings.findChildViewById(rootView, id);
      if (btnBackNavigation == null) {
        break missingId;
      }

      id = R.id.tv_back_navigation_title;
      TextView tvBackNavigationTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvBackNavigationTitle == null) {
        break missingId;
      }

      return new LayoutBackNavigationBinding((ConstraintLayout) rootView, backNavigationContainer,
          btnBackNavigation, tvBackNavigationTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
