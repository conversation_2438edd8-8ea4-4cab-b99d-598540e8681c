<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_setup_password" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\dialog_setup_password.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/dialog_setup_password_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="190" endOffset="16"/></Target><Target id="@+id/strelka" view="RelativeLayout"><Expressions/><location startLine="24" startOffset="16" endLine="51" endOffset="32"/></Target><Target id="@+id/exit_change_capacity" view="Button"><Expressions/><location startLine="31" startOffset="20" endLine="38" endOffset="74"/></Target><Target id="@+id/textView20" view="TextView"><Expressions/><location startLine="52" startOffset="16" endLine="68" endOffset="59"/></Target><Target id="@+id/textInputEdit" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="111" startOffset="20" endLine="120" endOffset="58"/></Target><Target id="@+id/cancel_change_capacity" view="Button"><Expressions/><location startLine="132" startOffset="20" endLine="140" endOffset="74"/></Target><Target id="@+id/sdfgsdfg1" view="LinearLayout"><Expressions/><location startLine="141" startOffset="20" endLine="155" endOffset="34"/></Target><Target id="@+id/confirm_change_capacity" view="Button"><Expressions/><location startLine="162" startOffset="20" endLine="170" endOffset="74"/></Target><Target id="@+id/w22" view="LinearLayout"><Expressions/><location startLine="171" startOffset="20" endLine="185" endOffset="34"/></Target></Targets></Layout>