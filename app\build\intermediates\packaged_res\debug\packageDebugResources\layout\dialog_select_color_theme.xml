<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:background="@drawable/white_block"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:elevation="25dp">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:textSize="18sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:gravity="start"
                    android:id="@+id/textView20"
                    android:focusableInTouchMode="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/choose_color"
                    android:singleLine="true"
                    android:layout_centerVertical="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:onClick="OnClick"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="2dp"
                    android:layout_toStartOf="@+id/strelka"
                    android:layout_alignParentStart="true"/>
                <RelativeLayout
                    android:id="@+id/strelka"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignTop="@+id/textView20"
                    android:layout_alignBottom="@+id/textView20"
                    android:layout_alignParentEnd="true">
                    <Button
                        android:id="@+id/exit_color"
                        android:background="@drawable/grey_block_line_up"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="45sp"
                        android:layout_height="match_parent"
                        android:minWidth="0dp"
                        android:minHeight="0dp"
                        android:layout_centerInParent="true"
                        android:layout_marginStart="5dp"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <LinearLayout
                        android:orientation="vertical"
                        android:background="@drawable/ic_strelka"
                        android:visibility="visible"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4sp"
                        android:layout_marginBottom="4sp"
                        android:layout_centerInParent="true"
                        android:layout_marginStart="15sp"
                        android:layout_marginEnd="15sp"
                        android:layout_alignStart="@+id/exit_color"
                        android:layout_alignEnd="@+id/exit_color"/>
                </RelativeLayout>
            </RelativeLayout>
            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:background="@drawable/grey_block"
                    android:padding="8dp"
                    android:focusable="true"
                    android:visibility="visible"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:layout_marginBottom="8dp"
                    android:layout_weight="1">
                    <Button
                        android:id="@+id/blue_btn"
                        android:background="@drawable/blue_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/green_btn"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/green_btn"
                        android:background="@drawable/green_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/orange_btn"
                        app:layout_constraintStart_toEndOf="@+id/blue_btn"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/orange_btn"
                        android:background="@drawable/orange_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/light_blue_btn"
                        app:layout_constraintStart_toEndOf="@+id/green_btn"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/light_blue_btn"
                        android:background="@drawable/light_blue_button"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/red_btn"
                        app:layout_constraintStart_toEndOf="@+id/orange_btn"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/red_btn"
                        android:background="@drawable/red_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/light_blue_btn"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:background="@drawable/grey_block"
                    android:padding="8dp"
                    android:focusable="true"
                    android:visibility="visible"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1">
                    <Button
                        android:id="@+id/pink_btn"
                        android:background="@drawable/pink_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/light_green_btn"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/light_green_btn"
                        android:background="@drawable/light_green_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/telo_btn"
                        app:layout_constraintStart_toEndOf="@+id/pink_btn"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/telo_btn"
                        android:background="@drawable/telo_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/gold_btn"
                        app:layout_constraintStart_toEndOf="@+id/light_green_btn"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/gold_btn"
                        android:background="@drawable/gold_button"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/night_blue_btn"
                        app:layout_constraintStart_toEndOf="@+id/telo_btn"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/night_blue_btn"
                        android:background="@drawable/night_blue_button"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/gold_btn"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/table1"
                    android:background="@drawable/grey_block"
                    android:padding="8dp"
                    android:focusable="true"
                    android:visibility="visible"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="8dp"
                    android:layout_weight="1">
                    <Button
                        android:id="@+id/color_btn_1"
                        android:background="@drawable/color_button_1"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/color_btn_2"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/color_btn_2"
                        android:background="@drawable/color_button_2"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/color_btn_3"
                        app:layout_constraintStart_toEndOf="@+id/color_btn_1"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/color_btn_3"
                        android:background="@drawable/color_button_3"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/color_btn_4"
                        app:layout_constraintStart_toEndOf="@+id/color_btn_2"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/color_btn_4"
                        android:background="@drawable/color_button_4"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/color_btn_5"
                        app:layout_constraintStart_toEndOf="@+id/color_btn_3"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/color_btn_5"
                        android:background="@drawable/color_button_5"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/color_btn_4"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/table2"
                    android:background="@drawable/grey_block"
                    android:padding="8dp"
                    android:focusable="true"
                    android:visibility="visible"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="8dp"
                    android:layout_weight="1">
                    <Button
                        android:id="@+id/color_btn_6"
                        android:background="@drawable/color_button_6"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/color_btn_7"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/color_btn_7"
                        android:background="@drawable/color_button_7"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/color_btn_8"
                        app:layout_constraintStart_toEndOf="@+id/color_btn_6"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/color_btn_8"
                        android:background="@drawable/color_button_8"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/color_btn_9"
                        app:layout_constraintStart_toEndOf="@+id/color_btn_7"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/color_btn_9"
                        android:background="@drawable/color_button_9"
                        android:visibility="visible"
                        android:longClickable="false"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/color_btn_10"
                        app:layout_constraintStart_toEndOf="@+id/color_btn_8"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                    <Button
                        android:id="@+id/color_btn_10"
                        android:background="@drawable/color_button_10"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/color_btn_9"
                        app:layout_constraintTop_toTopOf="parent"
                        style="@style/Widget.AppCompat.Button.Borderless"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>
