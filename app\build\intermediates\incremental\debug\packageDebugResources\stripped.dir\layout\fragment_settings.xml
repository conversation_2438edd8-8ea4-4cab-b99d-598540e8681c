<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scroll_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Back navigation component -->
        <include
            android:id="@+id/include_back_navigation"
            layout="@layout/layout_back_navigation" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/p6"
                android:background="@drawable/grey_block_line_down"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/p5">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/p_12334"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/enable_vibration"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switch_vibration"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:layout_gravity="center_vertical"
                    android:id="@+id/switch_vibration"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/p_12334"
                    app:layout_constraintTop_toTopOf="parent"
                    app:splitTrack="false"
                    app:track="@drawable/switch_track"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/p5"
                android:background="@drawable/grey_block_line"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_marginTop="5dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/change_temp">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/p_11"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/info_text"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switch_info"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:layout_gravity="center_vertical"
                    android:id="@+id/switch_info"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/p_11"
                    app:layout_constraintTop_toTopOf="parent"
                    app:splitTrack="false"
                    app:track="@drawable/switch_track"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:id="@+id/p_t"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/pers"
                android:layout_marginStart="2dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/change_theme"
                android:background="@drawable/grey_block_line_up"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:text="@string/theme"
                android:singleLine="true"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/p_t"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/change_icon"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/icon"
                android:singleLine="true"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/change_second_color_theme"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/change_second_color_theme"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/second_color"
                android:singleLine="true"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/change_theme"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/change_lang"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/language"
                android:singleLine="true"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/change_icon"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/change_temp"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:visibility="gone"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/view_temp"
                android:singleLine="true"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/change_lang"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:id="@+id/n_t"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/notification"
                android:layout_marginStart="2dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/n_1.1"
                android:background="@drawable/grey_block_line_up"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/n_t">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/n1.1"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/charge_notification_switch"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:rotation="0"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switch_is_charge_notify"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_is_charge_notify"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:track="@drawable/switch_track"
                    style="@android:style/Widget.Material.CompoundButton.Switch"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/n_1.2"
                android:background="@drawable/grey_block_line"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/n_1.1">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/n1.2"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/discharge_notification_switch"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:rotation="0"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switch_is_discharge_notify"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_is_discharge_notify"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:track="@drawable/switch_track"
                    style="@android:style/Widget.Material.CompoundButton.Switch"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/n_2"
                android:background="@drawable/grey_block_line"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/n_1.2">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/n2"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/show_on_lockscreen"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:rotation="0"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switch_is_showed_on_lockscreen"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_is_showed_on_lockscreen"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:track="@drawable/switch_track"
                    style="@android:style/Widget.Material.CompoundButton.Switch"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:gravity="center"
                android:id="@+id/n_3"
                android:visibility="gone"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/notification_for_tempBat"
                android:singleLine="true"
                android:layout_weight="1"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/n_2"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:gravity="center"
                android:id="@+id/button_change_notify"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:visibility="gone"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/setting_notify_battery_service"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/n_3"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:gravity="center"
                android:id="@+id/button_change_frequency"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/setting_notify_rate"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/button_change_notify"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:gravity="center"
                android:id="@+id/button_change_notify_icon"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/status_icon"
                android:scrollHorizontally="true"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/button_change_frequency"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:gravity="center"
                android:id="@+id/button_settings_notify"
                android:background="@drawable/grey_block_line_down"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/setting_notify"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/button_change_notify_icon"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:id="@+id/animation_title_block"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/animation"
                android:layout_marginStart="2dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/switch_animation_block"
                android:background="@drawable/grey_block_line_up"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/animation_title_block">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/switch_animation_title"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/charging_animation"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:rotation="0"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switch_enable_animation"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_enable_animation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:track="@drawable/switch_track"
                    style="@android:style/Widget.Material.CompoundButton.Switch"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/switch_animation_time_block"
                android:background="@drawable/grey_block_line_up"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/switch_animation_block">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/switch_animation_time_title"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/show_date_and_time"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:rotation="0"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switch_enable_animation_time"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_enable_animation_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:track="@drawable/switch_track"
                    style="@android:style/Widget.Material.CompoundButton.Switch"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/anti_thief_block"
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:id="@+id/anti_thief_title_block"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/anti_thief"
                android:layout_marginStart="2dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <ImageView
                android:id="@+id/anti_thief_info"
                android:layout_width="22sp"
                android:layout_height="22sp"
                android:src="@drawable/ic_note"
                android:scaleType="fitEnd"
                android:layout_marginStart="5dp"
                android:layout_alignParentEnd="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/switch_anti_thief_block"
                android:background="@drawable/grey_block_line_up"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/anti_thief_title_block">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/switch_anti_thief_title"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/anti_thief"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:rotation="0"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switch_enable_anti_thief"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_enable_anti_thief"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:track="@drawable/switch_track"
                    style="@android:style/Widget.Material.CompoundButton.Switch"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/switch_anti_thief_sound_block"
                android:background="@drawable/grey_block_line_up"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/switch_anti_thief_block">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/switch_anti_thief_sound_title"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/play_warning_sound"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:rotation="0"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switch_enable_anti_thief_sound"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_enable_anti_thief_sound"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:track="@drawable/switch_track"
                    style="@android:style/Widget.Material.CompoundButton.Switch"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/change_BLM_button"
                android:background="@drawable/grey_block_line"
                android:focusable="true"
                android:visibility="visible"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/hand_reset_button">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/alternative_charge_level"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:rotation="0"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switch_BLM"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_BLM"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:track="@drawable/switch_track"
                    style="@android:style/Widget.Material.CompoundButton.Switch"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/hand_reset_button"
                android:background="@drawable/grey_block_line_up"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="7dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/d1">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/hand_reset_sessions"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:rotation="0"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switch_hand_reset_session"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_hand_reset_session"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:track="@drawable/switch_track"
                    style="@android:style/Widget.Material.CompoundButton.Switch"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:gravity="center"
                android:id="@+id/import_database"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/import_database"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/clear_database"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:gravity="center"
                android:id="@+id/export_database"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/export_database"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/import_database"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/change_dual_battery"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/dual_battery"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/export_database"/>
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:id="@+id/d1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/data_base"
                android:layout_marginStart="2dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/current_session_amperage_button"
                android:background="@drawable/grey_block_line"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/change_BLM_button">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/display_charging_amperage_graph"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:rotation="0"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/current_session_amperage_session"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/current_session_amperage_session"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:track="@drawable/switch_track"
                    style="@android:style/Widget.Material.CompoundButton.Switch"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:gravity="center"
                android:id="@+id/heand_stab_database"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/stab_database"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/current_session_amperage_button"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:gravity="center"
                android:id="@+id/auto_stab_database"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/auto_stab_database"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/heand_stab_database"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:gravity="center"
                android:id="@+id/clear_database"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/clear_database"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/auto_stab_database"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/change_capacity"
                android:background="@drawable/grey_block_line_down"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/change_design_capacity"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/change_dual_battery"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:id="@+id/indent_down"
            android:background="@drawable/white_block"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="14dp"
            android:layout_marginBottom="90dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/battery_info"
                android:visibility="gone"
                android:background="@drawable/grey_block_line_up"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/battery_info"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/a_t"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/privacy_setting_button"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/privacy_setting"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/work_in_backgound_button"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/privacy_button"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/Privacy_Policy"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/privacy_setting_button"/>
            <TextView
                android:visibility="gone"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/debugger_button"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="Debugger"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/privacy_button"/>

            <TextView
                android:visibility="gone"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/mediation_debugger_button"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="Mediation Debugger"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/debugger_button"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/about_translations"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/about_translations"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/write_me"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/write_me"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:visibility="gone"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/write_me"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/support_me_button"/>

            <TextView
                android:id="@+id/a_t"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:text="@string/app"
                android:textAlignment="viewStart"
                android:textColor="?attr/black"
                android:textSize="19sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/remove_ads"
                android:visibility="gone"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/remove_add"
                android:singleLine="true"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/battery_info"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:visibility="gone"
                android:id="@+id/buy_advance_access"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/buy_advanced_access"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/remove_ads"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/work_in_backgound_button"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/work_in_background"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/buy_advance_access"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:visibility="gone"
                android:id="@+id/reset_purchases_button"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/my_subscribe"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/privacy_button"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:visibility="gone"
                android:id="@+id/rate_button"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/rate"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/reset_purchases_button"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/support_me_button"
                android:background="@drawable/grey_block_line"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:visibility="gone"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:text="@string/support_us"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rate_button"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/version_app_button"
                android:background="@drawable/grey_block_line_down"
                android:focusable="true"
                android:clickable="true"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/about_translations">
                <LinearLayout
                    android:gravity="center"
                    android:id="@+id/ertretr"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/versions"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/version_app"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/zero"/>
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
        
        <!-- New Test Section for Development -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginTop="14dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:id="@+id/debug_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Debug"
                android:layout_marginStart="2dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/test_new_discharge"
                android:background="@drawable/grey_block_line_up"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:text="Test New Discharge Fragment"
                android:singleLine="true"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/debug_title"/>
                
        </androidx.constraintlayout.widget.ConstraintLayout>
        
    </LinearLayout>
</androidx.core.widget.NestedScrollView>
