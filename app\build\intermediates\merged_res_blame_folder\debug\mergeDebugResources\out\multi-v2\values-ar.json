{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tqhit.battery.one.app-mergeDebugResources-132:\\values-ar\\values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "182,183,184,185,186,187,188,189,191,192,193,194,195,196,197,198,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13811,13915,14059,14181,14286,14424,14552,14663,14895,15032,15136,15286,15408,15547,15693,15757,15823", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "13910,14054,14176,14281,14419,14547,14658,14760,15027,15131,15281,15403,15542,15688,15752,15818,15902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,11,19,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,1314,1393,1471,1547,1641,1733,1807,1872,1964,2054,2124,2188,2251,2320,2428,2537,2652,2718,2801,2873,2945,3037,3128,3192,3255,3308,3379,3434,3495,3553,3627,3691,3755,3815,3880,3944,3996,4053,4124,4195,4251", "endLines": "10,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,51,56,70,70,55,67", "endOffsets": "344,844,1309,1388,1466,1542,1636,1728,1802,1867,1959,2049,2119,2183,2246,2315,2423,2532,2647,2713,2796,2868,2940,3032,3123,3187,3250,3303,3374,3429,3490,3548,3622,3686,3750,3810,3875,3939,3991,4048,4119,4190,4246,4314"}, "to": {"startLines": "2,11,19,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,284,285,286,287,288,289,290,291,292,293,294,297,298,299,300,301,302", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,444,944,21421,21500,21578,21654,21748,21840,21914,21979,22071,22161,22231,22295,22358,22427,22535,22644,22759,22825,22908,22980,23052,23144,23235,23299,24058,24111,24182,24237,24298,24356,24430,24494,24558,24618,24683,24875,24927,24984,25055,25126,25182", "endLines": "10,18,26,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,284,285,286,287,288,289,290,291,292,293,294,297,298,299,300,301,302", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,51,56,70,70,55,67", "endOffsets": "439,939,1404,21495,21573,21649,21743,21835,21909,21974,22066,22156,22226,22290,22353,22422,22530,22639,22754,22820,22903,22975,23047,23139,23230,23294,23357,24106,24177,24232,24293,24351,24425,24489,24553,24613,24678,24742,24922,24979,25050,25121,25177,25245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "154,155,156,157,158,159,160,563", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "11583,11676,11778,11873,11976,12079,12181,47207", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "11671,11773,11868,11971,12074,12176,12290,47303"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "190", "startColumns": "4", "startOffsets": "14765", "endColumns": "129", "endOffsets": "14890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,184,230,312,351,401,457,509,568,629,687,728,806,909,986,1067,1109,1175,1220,1303,1375,1431,1480,1573,1622,1677,1731,1800,1849,1910,2008,2065,2135,2217,2275,2336,2404,2459,2507,2560,2618,2679,2744,2833,2950", "endColumns": "67,60,45,81,38,49,55,51,58,60,57,40,77,102,76,80,41,65,44,82,71,55,48,92,48,54,53,68,48,60,97,56,69,81,57,60,67,54,47,52,57,60,64,88,116,56", "endOffsets": "118,179,225,307,346,396,452,504,563,624,682,723,801,904,981,1062,1104,1170,1215,1298,1370,1426,1475,1568,1617,1672,1726,1795,1844,1905,2003,2060,2130,2212,2270,2331,2399,2454,2502,2555,2613,2674,2739,2828,2945,3002"}, "to": {"startLines": "579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "48553,48621,48682,48728,48810,48849,48899,48955,49007,49066,49127,49185,49226,49304,49407,49484,49565,49607,49673,49718,49801,49873,49929,49978,50071,50120,50175,50229,50298,50347,50408,50506,50563,50633,50715,50773,50834,50902,50957,51005,51058,51116,51177,51242,51331,51448", "endColumns": "67,60,45,81,38,49,55,51,58,60,57,40,77,102,76,80,41,65,44,82,71,55,48,92,48,54,53,68,48,60,97,56,69,81,57,60,67,54,47,52,57,60,64,88,116,56", "endOffsets": "48616,48677,48723,48805,48844,48894,48950,49002,49061,49122,49180,49221,49299,49402,49479,49560,49602,49668,49713,49796,49868,49924,49973,50066,50115,50170,50224,50293,50342,50403,50501,50558,50628,50710,50768,50829,50897,50952,51000,51053,51111,51172,51237,51326,51443,51500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,506,607,880,971,1064,1156,1250,1350,1443,1538,1631,1722,2000,2404,2507,2759", "endColumns": "107,103,100,113,90,92,91,93,99,92,94,92,90,93,97,102,154,81", "endOffsets": "208,312,602,716,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,2093,2502,2657,2836"}, "to": {"startLines": "51,52,55,56,59,60,61,62,63,64,65,66,67,68,71,75,76,540", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2597,2705,2945,3046,3259,3350,3443,3535,3629,3729,3822,3917,4010,4101,4319,4631,4734,45361", "endColumns": "107,103,100,113,90,92,91,93,99,92,94,92,90,93,97,102,154,81", "endOffsets": "2700,2804,3041,3155,3345,3438,3530,3624,3724,3817,3912,4005,4096,4190,4412,4729,4884,45438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9b1d1c56b528abe32d69bef2c9ce868e\\transformed\\jetified-bigo-ads-5.3.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,189,252,311,376,449,536,613,677,734,788,1084,1155,1232,1286,1350,1405,1460,1516,1563,1626,1687,1738,1784,1845", "endColumns": "68,64,62,58,64,72,86,76,63,56,53,295,70,76,53,63,54,54,55,46,62,60,50,45,60,60", "endOffsets": "119,184,247,306,371,444,531,608,672,729,783,1079,1150,1227,1281,1345,1400,1455,1511,1558,1621,1682,1733,1779,1840,1901"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8879,8948,9013,9076,9135,9200,9273,9360,9437,9501,9558,9612,9908,9979,10056,10110,10174,10229,10284,10340,10387,10450,10511,10562,10608,10669", "endColumns": "68,64,62,58,64,72,86,76,63,56,53,295,70,76,53,63,54,54,55,46,62,60,50,45,60,60", "endOffsets": "8943,9008,9071,9130,9195,9268,9355,9432,9496,9553,9607,9903,9974,10051,10105,10169,10224,10279,10335,10382,10445,10506,10557,10603,10664,10725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,308,390,471,572,667", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "115,174,241,303,385,466,567,662,746"}, "to": {"startLines": "275,276,277,278,279,280,281,282,283", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23362,23427,23486,23553,23615,23697,23778,23879,23974", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "23422,23481,23548,23610,23692,23773,23874,23969,24053"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-ar\\values-ar.xml", "from": {"startLines": "62,63", "startColumns": "4,4", "startOffsets": "3944,4006", "endColumns": "61,65", "endOffsets": "4001,4067"}, "to": {"startLines": "295,296", "startColumns": "4,4", "startOffsets": "24747,24809", "endColumns": "61,65", "endOffsets": "24804,24870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,85", "endOffsets": "134,220"}, "to": {"startLines": "577,578", "startColumns": "4,4", "startOffsets": "48383,48467", "endColumns": "83,85", "endOffsets": "48462,48548"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1234,1297,1388,1457,1524,1624,1687,1752,1813,1881,1943,2001,2115,2175,2236,2293,2366,2489,2570,2662,2769,2867,2947,3095,3176,3257,3385,3474,3550,3603,3657,3723,3801,3881,3952,4034,4106,4180,4253,4323,4432,4523,4594,4684,4779,4853,4936,5029,5078,5159,5228,5314,5399,5461,5525,5588,5657,5766,5876,5973,6073,6130,6188,6268,6347,6422", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1229,1292,1383,1452,1519,1619,1682,1747,1808,1876,1938,1996,2110,2170,2231,2288,2361,2484,2565,2657,2764,2862,2942,3090,3171,3252,3380,3469,3545,3598,3652,3718,3796,3876,3947,4029,4101,4175,4248,4318,4427,4518,4589,4679,4774,4848,4931,5024,5073,5154,5223,5309,5394,5456,5520,5583,5652,5761,5871,5968,6068,6125,6183,6263,6342,6417,6493"}, "to": {"startLines": "27,144,145,146,147,148,164,165,179,248,250,305,329,337,409,410,411,412,413,414,415,416,417,418,419,420,421,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,506,541,542,553", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1409,10872,10950,11026,11110,11202,12473,12574,13562,21210,21358,25408,27209,27782,35234,35334,35397,35462,35523,35591,35653,35711,35825,35885,35946,36003,36076,36324,36405,36497,36604,36702,36782,36930,37011,37092,37220,37309,37385,37438,37492,37558,37636,37716,37787,37869,37941,38015,38088,38158,38267,38358,38429,38519,38614,38688,38771,38864,38913,38994,39063,39149,39234,39296,39360,39423,39492,39601,39711,39808,39908,39965,43125,45443,45522,46364", "endLines": "34,144,145,146,147,148,164,165,179,248,250,305,329,337,409,410,411,412,413,414,415,416,417,418,419,420,421,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,506,541,542,553", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "1769,10945,11021,11105,11197,11280,12569,12688,13634,21264,21416,25494,27273,27844,35329,35392,35457,35518,35586,35648,35706,35820,35880,35941,35998,36071,36194,36400,36492,36599,36697,36777,36925,37006,37087,37215,37304,37380,37433,37487,37553,37631,37711,37782,37864,37936,38010,38083,38153,38262,38353,38424,38514,38609,38683,38766,38859,38908,38989,39058,39144,39229,39291,39355,39418,39487,39596,39706,39803,39903,39960,40018,43200,45517,45592,46435"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "122,180,227,277,324,373,422,469,517,570,629,679,726,775,822,18960,872,957,1010,1061,1111,1161,1237,1304,1383,1454,1519,1580,21218,1640,1681,1810,1868,1948,1991,2049,19156,19914,20270,20388,2129,57,21170,19355,21048,2168,2217,2258,2336,2392,2459,2805,3158,10467,10531,9913,9835,10594,3200,3269,3339,3508,3724,3795,3845,3885,3940,4011,4055,19049,4119,4181,19286,4223,4293,4333,4394,4530,4591,4668,4738,4799,4880,19790,4938,4991,5044,5107,5162,5205,5268,5343,5390,5658,5711,5771,6248,6745,6813,6867,6977,7035,7257,7306,7660,7717,7829,7958,8082,8140,8184,8229,8295,8377,8454,8502,8600,8688,8744,8808,8874,9115,9341,9497,9640,9718,10681,10746,20854,10799,10838,10928,10994,11052,11234,11282,11352,11408,11462,11584,11642,11764,11956,11996,12051,12089,12127,12163,12199,12274,12316,12367,12416,12484,20915,12549,12614,12667,12711,12751,12821,13188,13243,13314,13459,13531,13597,13634,13669,13706,13751,13804,13863,13931,13981,14050,14162,19211,14197,14250,14298,14384,14451,14524,14618,14709,14773,14811,14883,19620,19468,19396,20182,20637,15125,15165,15215,15266,15332,15408,20322,15448,15521,15568,19111,15610,20986,15705,15792,15881,15925,15991,16155,16226,16282,19713,16345,16398,20687,16457,16529,16614,16682,16730,20115,16803,16870,16913,16971,17014,17121,17374,17445,17491,17545,17616,17669,17723,21112,17764,17829,17995,18077,18138,18206,18256,18329,18394,18431,19858,18485,18541,18584,18623,18674,18730,18795,18842,18879,18915", "endColumns": "56,45,48,45,47,47,45,46,51,57,48,45,47,45,48,87,83,51,49,48,48,74,65,77,69,63,59,58,66,39,127,56,78,41,56,78,53,199,50,247,37,63,46,39,62,47,39,76,54,65,344,351,40,62,61,552,76,83,67,68,167,214,69,48,38,53,69,42,62,60,60,40,67,68,38,59,134,59,75,68,59,79,56,66,51,51,61,53,41,61,73,45,266,51,58,475,495,66,52,108,56,220,47,352,55,110,127,122,56,42,43,64,80,75,46,96,86,54,62,64,239,224,154,141,76,70,63,51,59,37,88,64,56,180,46,68,54,52,120,56,120,190,38,53,36,36,34,34,73,40,49,47,66,63,69,63,51,42,38,68,365,53,69,143,70,64,35,33,35,43,51,57,66,48,67,110,33,73,51,46,84,65,71,92,89,62,36,70,240,91,150,70,86,48,38,48,49,64,74,38,64,71,45,40,43,93,60,85,87,42,64,162,69,54,61,75,51,57,165,70,83,66,46,71,65,65,41,56,41,105,251,69,44,52,69,51,52,39,56,63,164,80,59,66,48,71,63,35,52,54,54,41,37,49,54,63,45,35,34,43", "endOffsets": "174,221,271,318,367,416,463,511,564,623,673,720,769,816,866,19043,951,1004,1055,1105,1155,1231,1298,1377,1448,1513,1574,1634,21280,1675,1804,1862,1942,1985,2043,2123,19205,20109,20316,20631,2162,116,21212,19390,21106,2211,2252,2330,2386,2453,2799,3152,3194,10525,10588,10461,9907,10673,3263,3333,3502,3718,3789,3839,3879,3934,4005,4049,4113,19105,4175,4217,19349,4287,4327,4388,4524,4585,4662,4732,4793,4874,4932,19852,4985,5038,5101,5156,5199,5262,5337,5384,5652,5705,5765,6242,6739,6807,6861,6971,7029,7251,7300,7654,7711,7823,7952,8076,8134,8178,8223,8289,8371,8448,8496,8594,8682,8738,8802,8868,9109,9335,9491,9634,9712,9784,10740,10793,20909,10832,10922,10988,11046,11228,11276,11346,11402,11456,11578,11636,11758,11950,11990,12045,12083,12121,12157,12193,12268,12310,12361,12410,12478,12543,20980,12608,12661,12705,12745,12815,13182,13237,13308,13453,13525,13591,13628,13663,13700,13745,13798,13857,13925,13975,14044,14156,14191,19280,14244,14292,14378,14445,14518,14612,14703,14767,14805,14877,15119,19707,19614,19462,20264,20681,15159,15209,15260,15326,15402,15442,20382,15515,15562,15604,19150,15699,21042,15786,15875,15919,15985,16149,16220,16276,16339,19784,16392,16451,20848,16523,16608,16676,16724,16797,20176,16864,16907,16965,17008,17115,17368,17439,17485,17539,17610,17663,17717,17758,21164,17823,17989,18071,18132,18200,18250,18323,18388,18425,18479,19908,18535,18578,18617,18668,18724,18789,18836,18873,18909,18954"}, "to": {"startLines": "35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,53,54,57,58,69,70,72,73,74,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,141,142,143,149,150,151,152,153,161,162,163,166,167,168,169,170,171,172,173,174,175,176,177,178,200,201,202,203,206,207,208,209,210,211,212,213,214,215,216,217,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,243,244,245,246,247,249,304,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,330,331,333,335,336,338,339,340,341,342,343,344,345,346,347,405,406,407,408,422,423,469,476,477,478,479,480,483,484,485,486,487,488,489,490,499,500,501,502,503,504,505,507,508,509,510,511,512,513,514,515,516,518,519,520,521,522,525,526,527,528,529,530,538,539,543,545,546,547,548,549,550,551,552,554,555,556,557,558,559,560,564,565,567,570,572,573,574,575,576,625,626,627,628,629,630,633,634,635,636,638,639,640,641,642,643,644,645", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1774,1831,1877,1926,1972,2020,2068,2114,2161,2213,2271,2320,2366,2414,2460,2509,2809,2893,3160,3210,4195,4244,4417,4483,4561,4889,4953,5013,5072,5139,5179,5307,5364,5443,5485,5542,5621,5675,5875,5926,6174,6212,6276,6323,6363,6426,6474,6514,6591,6646,6712,7057,7409,7450,7513,7575,8128,8205,8289,8357,8426,8594,8809,10730,10779,10818,11285,11355,11398,11461,11522,12295,12336,12404,12693,12732,12792,12927,12987,13063,13132,13192,13272,13329,13396,13448,13500,15907,15961,16003,16065,16306,16352,16619,16671,16730,17206,17702,17769,17822,17931,17988,18209,18450,18803,18859,18970,19098,19221,19278,19321,19365,19430,19511,19587,19634,19731,19818,19873,19936,20001,20241,20466,20621,20763,20925,20996,21060,21112,21172,21269,25343,25807,25864,26045,26092,26161,26216,26269,26390,26447,26568,26759,26798,26852,26889,26926,26961,26996,27070,27111,27161,27278,27345,27490,27666,27730,27849,27892,27931,28000,28366,28420,28490,28634,28705,28770,35068,35102,35138,35182,36199,36257,40023,40532,40600,40711,40745,40819,41038,41085,41170,41236,41308,41401,41491,41554,42363,42434,42675,42767,42918,42989,43076,43205,43244,43293,43343,43408,43483,43522,43587,43659,43705,43879,43923,44017,44078,44164,44416,44459,44524,44687,44757,44812,45233,45309,45597,45725,45891,45962,46046,46113,46160,46232,46298,46440,46482,46539,46581,46687,46939,47009,47308,47361,47600,47802,47976,48016,48073,48137,48302,51505,51565,51632,51681,51753,51817,52015,52068,52123,52178,52280,52318,52368,52423,52487,52533,52569,52604", "endColumns": "56,45,48,45,47,47,45,46,51,57,48,45,47,45,48,87,83,51,49,48,48,74,65,77,69,63,59,58,66,39,127,56,78,41,56,78,53,199,50,247,37,63,46,39,62,47,39,76,54,65,344,351,40,62,61,552,76,83,67,68,167,214,69,48,38,53,69,42,62,60,60,40,67,68,38,59,134,59,75,68,59,79,56,66,51,51,61,53,41,61,73,45,266,51,58,475,495,66,52,108,56,220,47,352,55,110,127,122,56,42,43,64,80,75,46,96,86,54,62,64,239,224,154,141,76,70,63,51,59,37,88,64,56,180,46,68,54,52,120,56,120,190,38,53,36,36,34,34,73,40,49,47,66,63,69,63,51,42,38,68,365,53,69,143,70,64,35,33,35,43,51,57,66,48,67,110,33,73,51,46,84,65,71,92,89,62,36,70,240,91,150,70,86,48,38,48,49,64,74,38,64,71,45,40,43,93,60,85,87,42,64,162,69,54,61,75,51,57,165,70,83,66,46,71,65,65,41,56,41,105,251,69,44,52,69,51,52,39,56,63,164,80,59,66,48,71,63,35,52,54,54,41,37,49,54,63,45,35,34,43", "endOffsets": "1826,1872,1921,1967,2015,2063,2109,2156,2208,2266,2315,2361,2409,2455,2504,2592,2888,2940,3205,3254,4239,4314,4478,4556,4626,4948,5008,5067,5134,5174,5302,5359,5438,5480,5537,5616,5670,5870,5921,6169,6207,6271,6318,6358,6421,6469,6509,6586,6641,6707,7052,7404,7445,7508,7570,8123,8200,8284,8352,8421,8589,8804,8874,10774,10813,10867,11350,11393,11456,11517,11578,12331,12399,12468,12727,12787,12922,12982,13058,13127,13187,13267,13324,13391,13443,13495,13557,15956,15998,16060,16134,16347,16614,16666,16725,17201,17697,17764,17817,17926,17983,18204,18252,18798,18854,18965,19093,19216,19273,19316,19360,19425,19506,19582,19629,19726,19813,19868,19931,19996,20236,20461,20616,20758,20835,20991,21055,21107,21167,21205,21353,25403,25859,26040,26087,26156,26211,26264,26385,26442,26563,26754,26793,26847,26884,26921,26956,26991,27065,27106,27156,27204,27340,27404,27555,27725,27777,27887,27926,27995,28361,28415,28485,28629,28700,28765,28801,35097,35133,35177,35229,36252,36319,40067,40595,40706,40740,40814,40866,41080,41165,41231,41303,41396,41486,41549,41586,42429,42670,42762,42913,42984,43071,43120,43239,43288,43338,43403,43478,43517,43582,43654,43700,43741,43918,44012,44073,44159,44247,44454,44519,44682,44752,44807,44869,45304,45356,45650,45886,45957,46041,46108,46155,46227,46293,46359,46477,46534,46576,46682,46934,47004,47049,47356,47426,47647,47850,48011,48068,48132,48297,48378,51560,51627,51676,51748,51812,51848,52063,52118,52173,52215,52313,52363,52418,52482,52528,52564,52599,52643"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,265,351,484,653,735", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "167,260,346,479,648,730,810"}, "to": {"startLines": "204,303,482,517,566,631,632", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "16139,25250,40952,43746,47431,51853,51935", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "16201,25338,41033,43874,47595,51930,52010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,400,516,614,719,842,979,1100,1243,1330,1435,1527,1627,1745,1871,1981,2127,2271,2408,2560,2686,2806,2929,3047,3140,3238,3361,3485,3585,3688,3796,3941,4091,4198,4300,4380,4474,4567,4684,4773,4858,4958,5037,5121,5222,5325,5424,5522,5609,5715,5815,5915,6044,6123,6224", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,116,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "168,285,395,511,609,714,837,974,1095,1238,1325,1430,1522,1622,1740,1866,1976,2122,2266,2403,2555,2681,2801,2924,3042,3135,3233,3356,3480,3580,3683,3791,3936,4086,4193,4295,4375,4469,4562,4679,4768,4853,4953,5032,5116,5217,5320,5419,5517,5604,5710,5810,5910,6039,6118,6219,6312"}, "to": {"startLines": "348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28806,28924,29041,29151,29267,29365,29470,29593,29730,29851,29994,30081,30186,30278,30378,30496,30622,30732,30878,31022,31159,31311,31437,31557,31680,31798,31891,31989,32112,32236,32336,32439,32547,32692,32842,32949,33051,33131,33225,33318,33435,33524,33609,33709,33788,33872,33973,34076,34175,34273,34360,34466,34566,34666,34795,34874,34975", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,116,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "28919,29036,29146,29262,29360,29465,29588,29725,29846,29989,30076,30181,30273,30373,30491,30617,30727,30873,31017,31154,31306,31432,31552,31675,31793,31886,31984,32107,32231,32331,32434,32542,32687,32837,32944,33046,33126,33220,33313,33430,33519,33604,33704,33783,33867,33968,34071,34170,34268,34355,34461,34561,34661,34790,34869,34970,35063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,110", "endOffsets": "160,271"}, "to": {"startLines": "473,474", "startColumns": "4,4", "startOffsets": "40227,40337", "endColumns": "109,110", "endOffsets": "40332,40443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,288,573,634,755,871,1006,1059,1116,1228,1313,1351,1430,1462,1493,1536,1604,1644", "endColumns": "40,47,53,60,120,115,134,52,56,111,84,37,78,31,30,42,67,39,55", "endOffsets": "239,287,341,633,754,870,1005,1058,1115,1227,1312,1350,1429,1461,1492,1535,1603,1643,1699"}, "to": {"startLines": "470,471,472,491,492,493,494,495,496,497,498,531,532,533,534,535,536,537,637", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "40072,40117,40169,41591,41656,41781,41901,42040,42097,42158,42274,44874,44916,44999,45035,45070,45117,45189,52220", "endColumns": "44,51,57,64,124,119,138,56,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "40112,40164,40222,41651,41776,41896,42035,42092,42153,42269,42358,44911,44994,45030,45065,45112,45184,45228,52275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,372,470,555,636,742,826,907,988,1071,1220,1299,1373,1449,1523", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,78,73,75,73,120", "endOffsets": "189,272,367,465,550,631,737,821,902,983,1066,1136,1294,1368,1444,1518,1639"}, "to": {"startLines": "180,181,218,219,242,332,334,475,481,523,524,544,561,562,568,569,571", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13639,13728,18257,18352,20840,27409,27560,40448,40871,44252,44333,45655,47054,47133,47652,47728,47855", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,78,73,75,73,120", "endOffsets": "13723,13806,18347,18445,20920,27485,27661,40527,40947,44328,44411,45720,47128,47202,47723,47797,47971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "205,306,307,308", "startColumns": "4,4,4,4", "startOffsets": "16206,25499,25597,25705", "endColumns": "99,97,107,101", "endOffsets": "16301,25592,25700,25802"}}]}, {"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "182,183,184,185,186,187,188,189,191,192,193,194,195,196,197,198,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13811,13915,14059,14181,14286,14424,14552,14663,14895,15032,15136,15286,15408,15547,15693,15757,15823", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "13910,14054,14176,14281,14419,14547,14658,14760,15027,15131,15281,15403,15542,15688,15752,15818,15902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,11,19,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,284,285,286,287,288,289,290,291,292,293,294,297,298,299,300,301,302", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,444,944,21421,21500,21578,21654,21748,21840,21914,21979,22071,22161,22231,22295,22358,22427,22535,22644,22759,22825,22908,22980,23052,23144,23235,23299,24058,24111,24182,24237,24298,24356,24430,24494,24558,24618,24683,24875,24927,24984,25055,25126,25182", "endLines": "10,18,26,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,284,285,286,287,288,289,290,291,292,293,294,297,298,299,300,301,302", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,51,56,70,70,55,67", "endOffsets": "439,939,1404,21495,21573,21649,21743,21835,21909,21974,22066,22156,22226,22290,22353,22422,22530,22639,22754,22820,22903,22975,23047,23139,23230,23294,23357,24106,24177,24232,24293,24351,24425,24489,24553,24613,24678,24742,24922,24979,25050,25121,25177,25245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "154,155,156,157,158,159,160,566", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "11583,11676,11778,11873,11976,12079,12181,47387", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "11671,11773,11868,11971,12074,12176,12290,47483"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "190", "startColumns": "4", "startOffsets": "14765", "endColumns": "129", "endOffsets": "14890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "48733,48801,48862,48908,48990,49029,49079,49135,49187,49246,49307,49365,49406,49484,49587,49664,49745,49787,49853,49898,49981,50053,50109,50158,50251,50300,50355,50409,50478,50527,50588,50686,50743,50813,50895,50953,51014,51082,51137,51185,51238,51296,51357,51422,51511,51628", "endColumns": "67,60,45,81,38,49,55,51,58,60,57,40,77,102,76,80,41,65,44,82,71,55,48,92,48,54,53,68,48,60,97,56,69,81,57,60,67,54,47,52,57,60,64,88,116,56", "endOffsets": "48796,48857,48903,48985,49024,49074,49130,49182,49241,49302,49360,49401,49479,49582,49659,49740,49782,49848,49893,49976,50048,50104,50153,50246,50295,50350,50404,50473,50522,50583,50681,50738,50808,50890,50948,51009,51077,51132,51180,51233,51291,51352,51417,51506,51623,51680"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "51,52,55,56,59,60,61,62,63,64,65,66,67,68,71,75,76,542", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2597,2705,2945,3046,3259,3350,3443,3535,3629,3729,3822,3917,4010,4101,4319,4631,4734,45469", "endColumns": "107,103,100,113,90,92,91,93,99,92,94,92,90,93,97,102,154,81", "endOffsets": "2700,2804,3041,3155,3345,3438,3530,3624,3724,3817,3912,4005,4096,4190,4412,4729,4884,45546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9b1d1c56b528abe32d69bef2c9ce868e\\transformed\\jetified-bigo-ads-5.3.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8879,8948,9013,9076,9135,9200,9273,9360,9437,9501,9558,9612,9908,9979,10056,10110,10174,10229,10284,10340,10387,10450,10511,10562,10608,10669", "endColumns": "68,64,62,58,64,72,86,76,63,56,53,295,70,76,53,63,54,54,55,46,62,60,50,45,60,60", "endOffsets": "8943,9008,9071,9130,9195,9268,9355,9432,9496,9553,9607,9903,9974,10051,10105,10169,10224,10279,10335,10382,10445,10506,10557,10603,10664,10725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "275,276,277,278,279,280,281,282,283", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23362,23427,23486,23553,23615,23697,23778,23879,23974", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "23422,23481,23548,23610,23692,23773,23874,23969,24053"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "295,296", "startColumns": "4,4", "startOffsets": "24747,24809", "endColumns": "61,65", "endOffsets": "24804,24870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "580,581", "startColumns": "4,4", "startOffsets": "48563,48647", "endColumns": "83,85", "endOffsets": "48642,48728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "27,144,145,146,147,148,164,165,179,248,250,305,329,338,410,411,412,413,414,415,416,417,418,419,420,421,422,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,507,543,544,555", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1409,10872,10950,11026,11110,11202,12473,12574,13562,21210,21358,25408,27209,27837,35289,35389,35452,35517,35578,35646,35708,35766,35880,35940,36001,36058,36131,36379,36460,36552,36659,36757,36837,36985,37066,37147,37275,37364,37440,37493,37547,37613,37691,37771,37842,37924,37996,38070,38143,38213,38322,38413,38484,38574,38669,38743,38826,38919,38968,39049,39118,39204,39289,39351,39415,39478,39547,39656,39766,39863,39963,40020,43180,45551,45630,46472", "endLines": "34,144,145,146,147,148,164,165,179,248,250,305,329,338,410,411,412,413,414,415,416,417,418,419,420,421,422,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,507,543,544,555", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "1769,10945,11021,11105,11197,11280,12569,12688,13634,21264,21416,25494,27273,27899,35384,35447,35512,35573,35641,35703,35761,35875,35935,35996,36053,36126,36249,36455,36547,36654,36752,36832,36980,37061,37142,37270,37359,37435,37488,37542,37608,37686,37766,37837,37919,37991,38065,38138,38208,38317,38408,38479,38569,38664,38738,38821,38914,38963,39044,39113,39199,39284,39346,39410,39473,39542,39651,39761,39858,39958,40015,40073,43255,45625,45700,46543"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,267,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,268,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,269,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,21333,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,21389,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,21443,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,54,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,52,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,71,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,21383,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,21437,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,21510,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,53,54,57,58,69,70,72,73,74,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,141,142,143,149,150,151,152,153,161,162,163,166,167,168,169,170,171,172,173,174,175,176,177,178,200,201,202,203,206,207,208,209,210,211,212,213,214,215,216,217,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,243,244,245,246,247,249,304,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,330,331,333,335,336,337,339,340,341,342,343,344,345,346,347,348,406,407,408,409,423,424,470,477,478,479,480,481,484,485,486,487,488,489,490,491,500,501,502,503,504,505,506,508,509,510,511,512,513,514,515,516,517,519,520,521,522,523,524,527,528,529,530,531,532,540,541,545,547,548,549,550,551,552,553,554,556,557,558,559,560,561,562,563,567,568,570,573,575,576,577,578,579,628,629,630,631,632,633,636,637,638,639,641,642,643,644,645,646,647,648", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1774,1831,1877,1926,1972,2020,2068,2114,2161,2213,2271,2320,2366,2414,2460,2509,2809,2893,3160,3210,4195,4244,4417,4483,4561,4889,4953,5013,5072,5139,5179,5307,5364,5443,5485,5542,5621,5675,5875,5926,6174,6212,6276,6323,6363,6426,6474,6514,6591,6646,6712,7057,7409,7450,7513,7575,8128,8205,8289,8357,8426,8594,8809,10730,10779,10818,11285,11355,11398,11461,11522,12295,12336,12404,12693,12732,12792,12927,12987,13063,13132,13192,13272,13329,13396,13448,13500,15907,15961,16003,16065,16306,16352,16619,16671,16730,17206,17702,17769,17822,17931,17988,18209,18450,18803,18859,18970,19098,19221,19278,19321,19365,19430,19511,19587,19634,19731,19818,19873,19936,20001,20241,20466,20621,20763,20925,20996,21060,21112,21172,21269,25343,25807,25864,26045,26092,26161,26216,26269,26390,26447,26568,26759,26798,26852,26889,26926,26961,26996,27070,27111,27161,27278,27345,27490,27666,27730,27782,27904,27947,27986,28055,28421,28475,28545,28689,28760,28825,35123,35157,35193,35237,36254,36312,40078,40587,40655,40766,40800,40874,41093,41140,41225,41291,41363,41456,41546,41609,42418,42489,42730,42822,42973,43044,43131,43260,43299,43348,43398,43463,43538,43577,43642,43714,43760,43934,43978,44072,44133,44186,44272,44524,44567,44632,44795,44865,44920,45341,45417,45705,45833,45999,46070,46154,46221,46268,46340,46406,46548,46590,46647,46689,46795,47047,47119,47189,47488,47541,47780,47982,48156,48196,48253,48317,48482,51685,51745,51812,51861,51933,51997,52195,52248,52303,52358,52460,52498,52548,52603,52667,52713,52749,52784", "endColumns": "56,45,48,45,47,47,45,46,51,57,48,45,47,45,48,87,83,51,49,48,48,74,65,77,69,63,59,58,66,39,127,56,78,41,56,78,53,199,50,247,37,63,46,39,62,47,39,76,54,65,344,351,40,62,61,552,76,83,67,68,167,214,69,48,38,53,69,42,62,60,60,40,67,68,38,59,134,59,75,68,59,79,56,66,51,51,61,53,41,61,73,45,266,51,58,475,495,66,52,108,56,220,47,352,55,110,127,122,56,42,43,64,80,75,46,96,86,54,62,64,239,224,154,141,76,70,63,51,59,37,88,64,56,180,46,68,54,52,120,56,120,190,38,53,36,36,34,34,73,40,49,47,66,63,69,63,51,54,42,38,68,365,53,69,143,70,64,35,33,35,43,51,57,66,48,67,110,33,73,51,46,84,65,71,92,89,62,36,70,240,91,150,70,86,48,38,48,49,64,74,38,64,71,45,40,43,93,60,52,85,87,42,64,162,69,54,61,75,51,57,165,70,83,66,46,71,65,65,41,56,41,105,251,71,69,44,52,69,51,52,39,56,63,164,80,59,66,48,71,63,35,52,54,54,41,37,49,54,63,45,35,34,43", "endOffsets": "1826,1872,1921,1967,2015,2063,2109,2156,2208,2266,2315,2361,2409,2455,2504,2592,2888,2940,3205,3254,4239,4314,4478,4556,4626,4948,5008,5067,5134,5174,5302,5359,5438,5480,5537,5616,5670,5870,5921,6169,6207,6271,6318,6358,6421,6469,6509,6586,6641,6707,7052,7404,7445,7508,7570,8123,8200,8284,8352,8421,8589,8804,8874,10774,10813,10867,11350,11393,11456,11517,11578,12331,12399,12468,12727,12787,12922,12982,13058,13127,13187,13267,13324,13391,13443,13495,13557,15956,15998,16060,16134,16347,16614,16666,16725,17201,17697,17764,17817,17926,17983,18204,18252,18798,18854,18965,19093,19216,19273,19316,19360,19425,19506,19582,19629,19726,19813,19868,19931,19996,20236,20461,20616,20758,20835,20991,21055,21107,21167,21205,21353,25403,25859,26040,26087,26156,26211,26264,26385,26442,26563,26754,26793,26847,26884,26921,26956,26991,27065,27106,27156,27204,27340,27404,27555,27725,27777,27832,27942,27981,28050,28416,28470,28540,28684,28755,28820,28856,35152,35188,35232,35284,36307,36374,40122,40650,40761,40795,40869,40921,41135,41220,41286,41358,41451,41541,41604,41641,42484,42725,42817,42968,43039,43126,43175,43294,43343,43393,43458,43533,43572,43637,43709,43755,43796,43973,44067,44128,44181,44267,44355,44562,44627,44790,44860,44915,44977,45412,45464,45758,45994,46065,46149,46216,46263,46335,46401,46467,46585,46642,46684,46790,47042,47114,47184,47229,47536,47606,47827,48030,48191,48248,48312,48477,48558,51740,51807,51856,51928,51992,52028,52243,52298,52353,52395,52493,52543,52598,52662,52708,52744,52779,52823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "204,303,483,518,569,634,635", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "16139,25250,41007,43801,47611,52033,52115", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "16201,25338,41088,43929,47775,52110,52190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28861,28979,29096,29206,29322,29420,29525,29648,29785,29906,30049,30136,30241,30333,30433,30551,30677,30787,30933,31077,31214,31366,31492,31612,31735,31853,31946,32044,32167,32291,32391,32494,32602,32747,32897,33004,33106,33186,33280,33373,33490,33579,33664,33764,33843,33927,34028,34131,34230,34328,34415,34521,34621,34721,34850,34929,35030", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,116,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "28974,29091,29201,29317,29415,29520,29643,29780,29901,30044,30131,30236,30328,30428,30546,30672,30782,30928,31072,31209,31361,31487,31607,31730,31848,31941,32039,32162,32286,32386,32489,32597,32742,32892,32999,33101,33181,33275,33368,33485,33574,33659,33759,33838,33922,34023,34126,34225,34323,34410,34516,34616,34716,34845,34924,35025,35118"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "474,475", "startColumns": "4,4", "startOffsets": "40282,40392", "endColumns": "109,110", "endOffsets": "40387,40498"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-ar\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "471,472,473,492,493,494,495,496,497,498,499,533,534,535,536,537,538,539,640", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "40127,40172,40224,41646,41711,41836,41956,42095,42152,42213,42329,44982,45024,45107,45143,45178,45225,45297,52400", "endColumns": "44,51,57,64,124,119,138,56,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "40167,40219,40277,41706,41831,41951,42090,42147,42208,42324,42413,45019,45102,45138,45173,45220,45292,45336,52455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "180,181,218,219,242,332,334,476,482,525,526,546,564,565,571,572,574", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13639,13728,18257,18352,20840,27409,27560,40503,40926,44360,44441,45763,47234,47313,47832,47908,48035", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,78,73,75,73,120", "endOffsets": "13723,13806,18347,18445,20920,27485,27661,40582,41002,44436,44519,45828,47308,47382,47903,47977,48151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "205,306,307,308", "startColumns": "4,4,4,4", "startOffsets": "16206,25499,25597,25705", "endColumns": "99,97,107,101", "endOffsets": "16301,25592,25700,25802"}}]}]}