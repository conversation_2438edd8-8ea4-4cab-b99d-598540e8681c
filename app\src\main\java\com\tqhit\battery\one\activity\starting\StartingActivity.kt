package com.tqhit.battery.one.activity.starting

import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.core.content.ContentProviderCompat.requireContext
import androidx.core.net.toUri
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.viewbinding.ViewBinding
import com.applovin.mediation.nativeAds.MaxNativeAdLoader
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdViewBinder
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager
import com.tqhit.battery.one.base.LocaleAwareActivity
import com.tqhit.battery.one.databinding.ActivityStartingBinding
import com.tqhit.battery.one.databinding.ItemSlideLayout1Binding
import com.tqhit.battery.one.databinding.ItemSlideLayout2Binding
import com.tqhit.battery.one.databinding.ItemSlideLayout3Binding
import com.tqhit.battery.one.databinding.ItemSlideLayout4Binding
import com.tqhit.battery.one.databinding.ItemSlideLayout5Binding
import com.tqhit.battery.one.databinding.ItemSlideLayout6Binding
import com.tqhit.battery.one.databinding.ItemSlideLayout7Binding
import com.tqhit.battery.one.dialog.capacity.ChangeCapacityDialog
import com.tqhit.battery.one.utils.PermissionUtils
import com.tqhit.battery.one.viewmodel.AppViewModel
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import me.tankery.lib.circularseekbar.CircularSeekBar
import javax.inject.Inject

@AndroidEntryPoint
class StartingActivity : LocaleAwareActivity<ActivityStartingBinding>() {
    override val binding by lazy { ActivityStartingBinding.inflate(layoutInflater) }

    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager
    @Inject lateinit var applovinNativeAdManager: ApplovinNativeAdManager
    private var nativeAdView: MaxNativeAdView? = null

    private val appViewModel: AppViewModel by viewModels()
    private val batteryViewModel: BatteryViewModel by viewModels()

    companion object {
        private const val TAG = "StartingActivity"
    }

    /**
     * Logs comprehensive language and locale debugging information
     */
    private fun logLanguageDebugInfo() {
        try {
            val currentLocale = resources.configuration.locales[0]
            val savedLanguage = if (::appRepository.isInitialized) appRepository.getLanguage() else "N/A"

            Log.d(TAG, "=== STARTING ACTIVITY LANGUAGE DEBUG ===")
            Log.d(TAG, "Current locale: $currentLocale")
            Log.d(TAG, "Saved language from repository: $savedLanguage")
            Log.d(TAG, "Default locale: ${java.util.Locale.getDefault()}")
            Log.d(TAG, "Activity context locale: ${this.resources.configuration.locales[0]}")
            Log.d(TAG, "Application context locale: ${applicationContext.resources.configuration.locales[0]}")

            // Test comprehensive string resource loading
            logStringResources()

            Log.d(TAG, "=== END STARTING ACTIVITY LANGUAGE DEBUG ===")
        } catch (e: Exception) {
            Log.e(TAG, "Error in language debug logging", e)
        }
    }

    /**
     * Logs all key string resources used in onboarding slides
     */
    private fun logStringResources() {
        try {
            Log.d(TAG, "--- STRING RESOURCES TEST ---")

            // Basic app strings
            Log.d(TAG, "app_name: '${getString(R.string.app_name)}'")

            // Onboarding specific strings
            try {
                Log.d(TAG, "privacy_policy_starting: '${getString(R.string.privacy_policy_starting)}'")
            } catch (e: Exception) {
                Log.w(TAG, "Failed to load privacy_policy_starting", e)
            }

            try {
                Log.d(TAG, "confirmed: '${getString(R.string.confirmed)}'")
            } catch (e: Exception) {
                Log.w(TAG, "Failed to load confirmed", e)
            }

            // Test other common onboarding strings that exist
            try {
                val nextText = getString(R.string.next)
                Log.d(TAG, "next: '$nextText'")
            } catch (e: Exception) {
                Log.w(TAG, "Failed to load 'next' string", e)
            }

            Log.d(TAG, "--- END STRING RESOURCES TEST ---")
        } catch (e: Exception) {
            Log.e(TAG, "Error in string resources logging", e)
        }
    }

    /**
     * Logs the content of a specific slide to track localization
     */
    private fun logSlideContent(slideName: String, binding: ViewBinding) {
        try {
            Log.d(TAG, "--- $slideName CONTENT ---")

            when (binding) {
                is ItemSlideLayout1Binding -> {
                    Log.d(TAG, "Slide 1 - Next button text: '${binding.nextPage.text}'")
                    // Log any text views or other content in slide 1
                }
                is ItemSlideLayout2Binding -> {
                    Log.d(TAG, "Slide 2 - Privacy button text: '${binding.privacyPolicyButton.text}'")
                    Log.d(TAG, "Slide 2 - Confirm button text: '${binding.confirmAndContinueButton.text}'")
                    Log.d(TAG, "Slide 2 - Under text: '${binding.underText.text}'")
                }
                is ItemSlideLayout3Binding -> {
                    // Log slide 3 content
                    Log.d(TAG, "Slide 3 - Content logged")
                }
                is ItemSlideLayout4Binding -> {
                    // Log slide 4 content
                    Log.d(TAG, "Slide 4 - Content logged")
                }
                is ItemSlideLayout5Binding -> {
                    Log.d(TAG, "Slide 5 - Switch text: '${binding.switchInfo.text}'")
                    // Log other slide 5 content
                }
                is ItemSlideLayout6Binding -> {
                    // Log slide 6 content
                    Log.d(TAG, "Slide 6 - Content logged")
                }
                is ItemSlideLayout7Binding -> {
                    Log.d(TAG, "Slide 7 - Change capacity text: '${binding.changeCapacity.text}'")
                    // Note: getStarted property may not exist, check the actual binding
                    Log.d(TAG, "Slide 7 - Content logged")
                }
            }

            Log.d(TAG, "--- END $slideName CONTENT ---")
        } catch (e: Exception) {
            Log.e(TAG, "Error logging slide content for $slideName", e)
        }
    }

    private val layouts =
            listOf(
                    R.layout.item_slide_layout_1,
                    R.layout.item_slide_layout_2,
                    R.layout.item_slide_layout_3,
                    R.layout.item_slide_layout_4,
                    R.layout.item_slide_layout_5,
                    R.layout.item_slide_layout_6,
                    R.layout.item_slide_layout_7,
            )

    private val views = arrayListOf<ViewBinding>()

    private val startingViewAdapter by lazy { StartingViewAdapter(views.filter { view -> view !is ItemSlideLayout2Binding }.toCollection(ArrayList())) }

    private val permissionLauncher =
            registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
                val layout5Binding = views[4] as ItemSlideLayout5Binding
                if (isGranted) {
                    layout5Binding.switchInfo.isChecked = true
                    layout5Binding.switchInfo.isEnabled = false
                    appViewModel.setChargeAlarmEnabled(true)
                    PermissionUtils.resetNotificationPermissionDeniedCount()
                } else {
                    layout5Binding.switchInfo.isChecked = false
                    layout5Binding.switchInfo.isEnabled = true
                    appViewModel.setChargeAlarmEnabled(false)
                    if (!PermissionUtils.shouldShowRequestPermissionRationale(this)) {
                        PermissionUtils.incrementNotificationPermissionDeniedCount()
                    }
                }
            }

    override fun setupData() {
        super.setupData()

        // Enhanced logging for language debugging
        logLanguageDebugInfo()

        // Check if we need to create a locale-aware LayoutInflater
        val savedLanguage = if (::appRepository.isInitialized) appRepository.getLanguage() else ""
        Log.d(TAG, "=== LAYOUT INFLATER SETUP ===")
        Log.d(TAG, "Saved language: '$savedLanguage'")
        Log.d(TAG, "Default LayoutInflater context locale: ${layoutInflater.context.resources.configuration.locales[0]}")

        // Use the default LayoutInflater since the activity context is already locale-aware
        val localeAwareInflater = layoutInflater
        Log.d(TAG, "Using activity's default LayoutInflater with context locale: ${layoutInflater.context.resources.configuration.locales[0]}")

        Log.d(TAG, "Final LayoutInflater context locale: ${localeAwareInflater.context.resources.configuration.locales[0]}")
        Log.d(TAG, "=== END LAYOUT INFLATER SETUP ===")

        for ((index, layout) in layouts.withIndex()) {
            Log.d(TAG, "=== CREATING SLIDE ${index + 1} ===")
            Log.d(TAG, "Layout resource: $layout")
            Log.d(TAG, "Using LayoutInflater context locale: ${localeAwareInflater.context.resources.configuration.locales[0]}")

            val binding = when (layout) {
                R.layout.item_slide_layout_1 -> {
                    Log.d(TAG, "Inflating ItemSlideLayout1Binding")
                    val slideBinding = ItemSlideLayout1Binding.inflate(localeAwareInflater)
                    logSlideContent("Slide 1", slideBinding)
                    slideBinding
                }
                R.layout.item_slide_layout_2 -> {
                    Log.d(TAG, "Inflating ItemSlideLayout2Binding")
                    val slideBinding = ItemSlideLayout2Binding.inflate(localeAwareInflater)
                    logSlideContent("Slide 2", slideBinding)
                    slideBinding
                }
                R.layout.item_slide_layout_3 -> {
                    Log.d(TAG, "Inflating ItemSlideLayout3Binding")
                    val slideBinding = ItemSlideLayout3Binding.inflate(localeAwareInflater)
                    logSlideContent("Slide 3", slideBinding)
                    slideBinding
                }
                R.layout.item_slide_layout_4 -> {
                    Log.d(TAG, "Inflating ItemSlideLayout4Binding")
                    val slideBinding = ItemSlideLayout4Binding.inflate(localeAwareInflater)
                    logSlideContent("Slide 4", slideBinding)
                    slideBinding
                }
                R.layout.item_slide_layout_5 -> {
                    Log.d(TAG, "Inflating ItemSlideLayout5Binding")
                    val slideBinding = ItemSlideLayout5Binding.inflate(localeAwareInflater)
                    logSlideContent("Slide 5", slideBinding)
                    slideBinding
                }
                R.layout.item_slide_layout_6 -> {
                    Log.d(TAG, "Inflating ItemSlideLayout6Binding")
                    val slideBinding = ItemSlideLayout6Binding.inflate(localeAwareInflater)
                    logSlideContent("Slide 6", slideBinding)
                    slideBinding
                }
                R.layout.item_slide_layout_7 -> {
                    Log.d(TAG, "Inflating ItemSlideLayout7Binding")
                    val slideBinding = ItemSlideLayout7Binding.inflate(localeAwareInflater)
                    logSlideContent("Slide 7", slideBinding)
                    slideBinding
                }
                else -> break
            }

            Log.d(TAG, "Slide ${index + 1} root view context locale: ${binding.root.context.resources.configuration.locales[0]}")
            views.add(binding)
            Log.d(TAG, "=== SLIDE ${index + 1} CREATION COMPLETE ===")
        }

        Log.d(TAG, "=== SETTING UP SLIDE CONTENT WITH STRING RESOURCES ===")

        val layout2Binding = views[1] as ItemSlideLayout2Binding
        val privacyPolicyAccepted = appViewModel.isPrivacyPolicyAccepted()

        Log.d(TAG, "Privacy policy accepted: $privacyPolicyAccepted")

        layout2Binding.confirmAndContinueButton.visibility =
                privacyPolicyAccepted.let { if (it) View.GONE else View.VISIBLE }

        val underTextValue = privacyPolicyAccepted.let {
            if (it) {
                val confirmedText = getString(R.string.confirmed)
                Log.d(TAG, "Loading 'confirmed' string: '$confirmedText'")
                confirmedText
            } else {
                val privacyText = getString(R.string.privacy_policy_starting)
                Log.d(TAG, "Loading 'privacy_policy_starting' string: '$privacyText'")
                privacyText
            }
        }

        layout2Binding.underText.text = underTextValue
        Log.d(TAG, "Final underText value: '${layout2Binding.underText.text}'")

        val layout5Binding = views[4] as ItemSlideLayout5Binding
        layout5Binding.switchInfo.isChecked = PermissionUtils.isNotificationPermissionGranted(this)
        appViewModel.setChargeAlarmEnabled(layout5Binding.switchInfo.isChecked)
        layout5Binding.switchInfo.isEnabled = !layout5Binding.switchInfo.isChecked
        layout5Binding.textPercent.text = buildString {
            append(appViewModel.getChargeAlarmPercent())
            append("%")
        }
        layout5Binding.circularbar.progress = appViewModel.getChargeAlarmPercent().toFloat()

        // Fill in device information in layout 7
        val layout7Binding = views[6] as ItemSlideLayout7Binding
        // Set device name
        val deviceName =
                if (Build.MODEL.lowercase().startsWith(Build.MANUFACTURER.lowercase())) {
                    Build.MODEL
                } else {
                    "${Build.MANUFACTURER} ${Build.MODEL}"
                }
        layout7Binding.deviceName.text = deviceName

        // Observe battery capacity changes regardless of charging state
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                batteryViewModel.batteryCapacity.collect { capacity ->
                    layout7Binding.capacity.text = buildString {
                        append(capacity)
                        append(getString(R.string.ma))
                    }
                }
            }
        }

        // Observe charging state and update UI accordingly
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                batteryViewModel.isCharging.collect { isCharging ->
                    val isDischarging = !isCharging
                    layout7Binding.discharging.text =
                            if (isDischarging) getString(R.string.yes) else getString(R.string.no)

                    // Show text5 only when discharging
                    layout7Binding.text5.visibility = if (isDischarging) View.GONE else View.VISIBLE

                    // Update UI based on discharging state
                    if (isDischarging) {
                        // Set polarity
                        layout7Binding.polarity.text = batteryViewModel.getBatteryPolarity()
                        // Set measurement parameter
                        layout7Binding.parameter.text = getString(R.string.mA)
                        // Show button and hide progress when discharging
                        layout7Binding.button.visibility = View.VISIBLE
                        layout7Binding.progressbar2.visibility = View.GONE
                        // Enable start button when discharging
                        layout7Binding.startMainActivity.isEnabled = true
                    } else {
                        // When not discharging, hide diagnostics
                        layout7Binding.polarity.text = getString(R.string.not_identified)
                        layout7Binding.parameter.text = getString(R.string.not_identified)
                        // Show progress and hide button when not discharging
                        layout7Binding.button.visibility = View.GONE
                        layout7Binding.progressbar2.visibility = View.VISIBLE
                        // Disable start button when not discharging
                        layout7Binding.startMainActivity.isEnabled = false
                    }
                }
            }
        }
    }

    override fun setupUI() {
        super.setupUI()

        binding.slidePager.adapter = startingViewAdapter
        binding.springDotsIndicator.attachTo(binding.slidePager)
        setupNativeAd()

    }

    private fun setupNativeAd(){
        val container = binding.nativeAd
        val nativeAdView = createNativeAdView()

        // Defer native ad loading to avoid blocking UI startup
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                applovinNativeAdManager.loadNativeAd(
                    nativeAdView = nativeAdView,
                    onAdLoaded = {
                        container.removeAllViews()
                        container.hideShimmer()
                        container.addView(it)
                        Log.d("NativeAd", "Native ad loaded successfully in StartingActivity")
                    },
                    onAdLoadFailed = { errorMsg ->
                        Log.e("NativeAd", "Failed to load native ad in StartingActivity: $errorMsg")
                        container.hideShimmer()
                    }
                )
            } catch (e: Exception) {
                Log.e("NativeAd", "Error loading native ad, MAX SDK may not be ready yet", e)
                container.hideShimmer()
            }
        }, 3000) // 3 second delay to allow MAX SDK initialization
    }

    private fun createNativeAdView(): MaxNativeAdView
    {
        val binder: MaxNativeAdViewBinder =
            MaxNativeAdViewBinder.Builder(R.layout.layout_native_ads)
                .setTitleTextViewId(R.id.title_text_view)
                .setBodyTextViewId(R.id.body_text_view)
                .setStarRatingContentViewGroupId(R.id.star_rating_view )
                .setAdvertiserTextViewId(R.id.advertiser_text_view)
                .setIconImageViewId(R.id.icon_image_view)
                .setMediaContentViewGroupId(R.id.media_view_container)
                .setOptionsContentViewGroupId(R.id.ad_options_view)
                .setCallToActionButtonId(R.id.cta_button)
                .build()
        return MaxNativeAdView(binder, this)
    }






    override fun setupListener() {
        super.setupListener()

        val layout1Binding = views[0] as ItemSlideLayout1Binding
        layout1Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
        }

        val layout2Binding = views[1] as ItemSlideLayout2Binding
        layout2Binding.confirmAndContinueButton.setOnClickListener {
            appViewModel.acceptPrivacyPolicy()
            layout2Binding.confirmAndContinueButton.visibility = View.GONE
            layout2Binding.underText.text = getString(R.string.confirmed)
        }
        layout2Binding.privacyPolicyButton.setOnClickListener {
            startActivity(Intent(Intent.ACTION_VIEW, appViewModel.getPrivacyPolicyUrl().toUri()))
        }
        layout2Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
        }

        val layout3Binding = views[2] as ItemSlideLayout3Binding
        layout3Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
        }

        val layout4Binding = views[3] as ItemSlideLayout4Binding
        layout4Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
        }

        val layout5Binding = views[4] as ItemSlideLayout5Binding
        layout5Binding.switchInfo.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                PermissionUtils.requestNotificationPermission(
                        context = this,
                        permissionLauncher = permissionLauncher,
                        onPermissionGranted = {
                            layout5Binding.switchInfo.isChecked = true
                            layout5Binding.switchInfo.isEnabled = false
                            appViewModel.setChargeAlarmEnabled(true)
                        },
                        onPermissionDenied = {
                            layout5Binding.switchInfo.isChecked = false
                            layout5Binding.switchInfo.isEnabled = true
                            appViewModel.setChargeAlarmEnabled(false)
                        }
                )
            }
        }

        layout5Binding.circularbar.setOnSeekBarChangeListener(
                object : CircularSeekBar.OnCircularSeekBarChangeListener {
                    override fun onProgressChanged(
                            circularSeekBar: CircularSeekBar?,
                            progress: Float,
                            fromUser: Boolean
                    ) {
                        // Map 0-100 range to 60-100 range
                        val mappedPercent = 60 + (progress * 0.4).toInt()
                        layout5Binding.textPercent.text = buildString {
                            append(mappedPercent)
                            append("%")
                        }
                        appViewModel.setChargeAlarmPercent(mappedPercent)
                    }

                    override fun onStartTrackingTouch(seekBar: CircularSeekBar?) {}

                    override fun onStopTrackingTouch(seekBar: CircularSeekBar?) {}
                }
        )
        layout5Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
        }

        val layout6Binding = views[5] as ItemSlideLayout6Binding
        layout6Binding.dontkillmyappButton.setOnClickListener {
            startActivity(Intent(Intent.ACTION_VIEW, appViewModel.getDoNotKillMyAppUrl().toUri()))
        }
        layout6Binding.workInBackgroundPermission.setOnClickListener {
            requestBatteryOptimizationPermission()
        }
        layout6Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
        }

        val layout7Binding = views[6] as ItemSlideLayout7Binding
        layout7Binding.startMainActivity.setOnClickListener {
            appViewModel.setShowedStartPage(true)
            startActivity(Intent(this, MainActivity::class.java))
            finish()
        }

        layout7Binding.changeCapacity.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                this@StartingActivity
            ) {
                ChangeCapacityDialog(this, batteryViewModel).show()
            }
        }

        binding.slidePager.clearOnPageChangeListeners()
        binding.slidePager.addOnPageChangeListener(
                object : androidx.viewpager.widget.ViewPager.OnPageChangeListener {
                    override fun onPageScrolled(
                            position: Int,
                            positionOffset: Float,
                            positionOffsetPixels: Int
                    ) {}

                    override fun onPageSelected(position: Int) {
                        if (views[position] is ItemSlideLayout2Binding && position > 1 && !appViewModel.isPrivacyPolicyAccepted()) {
                            binding.slidePager.setCurrentItem(1, true)
                        }
                    }

                    override fun onPageScrollStateChanged(state: Int) {}
                }
        )
    }

    private fun requestBatteryOptimizationPermission() {
        try {
            if (batteryViewModel.isIgnoringBatteryOptimizations()) {
                Toast.makeText(this, R.string.permission_granted, Toast.LENGTH_SHORT).show()
                val layout6Binding = views[5] as ItemSlideLayout6Binding
                layout6Binding.workInBackgroundPermission.isEnabled = false
                layout6Binding.workInBackgroundPermission.text =
                        getString(R.string.permission_granted)
                return
            }

            val intent =
                    Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                        data = "package:$packageName".toUri()
                    }
            startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(this, R.string.unexpected_error, Toast.LENGTH_SHORT).show()
        }
    }

    override fun onResume() {
        super.onResume()

        val layout5Binding = views[4] as ItemSlideLayout5Binding
        layout5Binding.switchInfo.isChecked = PermissionUtils.isNotificationPermissionGranted(this)
        layout5Binding.switchInfo.isEnabled = !layout5Binding.switchInfo.isChecked
        appViewModel.setChargeAlarmEnabled(layout5Binding.switchInfo.isChecked)

        val layout6Binding = views[5] as ItemSlideLayout6Binding
        if (batteryViewModel.isIgnoringBatteryOptimizations()) {
            layout6Binding.workInBackgroundPermission.isEnabled = false
            layout6Binding.workInBackgroundPermission.text = getString(R.string.permission_granted)
        }
    }

    override fun onDestroy() {
        applovinNativeAdManager.destroy()
        super.onDestroy()
    }
}
