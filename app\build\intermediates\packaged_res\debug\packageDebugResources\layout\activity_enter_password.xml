<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="?attr/grey">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="false">
        <RelativeLayout
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="7dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:elevation="25dp">
            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <TextView
                        android:textSize="18sp"
                        android:textColor="?attr/black"
                        android:ellipsize="marquee"
                        android:gravity="start"
                        android:id="@+id/textView20"
                        android:focusableInTouchMode="true"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/enter_password"
                        android:singleLine="true"
                        android:layout_centerVertical="true"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:textAlignment="viewStart"
                        android:layout_marginStart="2dp"
                        android:layout_alignParentStart="true"/>
                </RelativeLayout>
                <LinearLayout
                    android:orientation="vertical"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_marginTop="7dp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="8dp">
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="5dp"
                        android:layout_marginStart="5dp"
                        android:layout_marginEnd="5dp"
                        app:boxCornerRadiusBottomEnd="6dp"
                        app:boxCornerRadiusBottomStart="6dp"
                        app:boxCornerRadiusTopEnd="6dp"
                        app:boxCornerRadiusTopStart="6dp"
                        app:hintEnabled="false">
                        <com.google.android.material.textfield.TextInputEditText
                            android:scrollbarSize="0dp"
                            android:textColor="?attr/textColorTextInput"
                            android:id="@+id/textInputEdit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/state_empty"
                            android:singleLine="true"
                            android:inputType="number"
                            android:textAlignment="viewStart"/>
                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:baselineAligned="false">
                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:layout_marginStart="4dp">
                        <Button
                            android:id="@+id/confirm_change_capacity"
                            android:background="@drawable/grey_block"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_alignTop="@+id/w22"
                            android:layout_alignBottom="@+id/w22"
                            android:layout_alignParentTop="false"
                            style="@style/Widget.AppCompat.Button.Borderless"/>
                        <LinearLayout
                            android:gravity="center"
                            android:orientation="vertical"
                            android:id="@+id/w22"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <TextView
                                android:textSize="14sp"
                                android:textColor="?attr/black"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="12dp"
                                android:layout_marginBottom="12dp"
                                android:text="@string/confirm"/>
                        </LinearLayout>
                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>
        </RelativeLayout>
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>