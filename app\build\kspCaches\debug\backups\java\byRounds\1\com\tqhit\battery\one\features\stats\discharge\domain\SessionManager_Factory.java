package com.tqhit.battery.one.features.stats.discharge.domain;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SessionManager_Factory implements Factory<SessionManager> {
  private final Provider<SessionMetricsCalculator> sessionMetricsCalculatorProvider;

  private final Provider<ScreenTimeCalculator> screenTimeCalculatorProvider;

  private final Provider<DischargeRateCalculator> dischargeRateCalculatorProvider;

  private final Provider<DischargeCalculator> dischargeCalculatorProvider;

  public SessionManager_Factory(Provider<SessionMetricsCalculator> sessionMetricsCalculatorProvider,
      Provider<ScreenTimeCalculator> screenTimeCalculatorProvider,
      Provider<DischargeRateCalculator> dischargeRateCalculatorProvider,
      Provider<DischargeCalculator> dischargeCalculatorProvider) {
    this.sessionMetricsCalculatorProvider = sessionMetricsCalculatorProvider;
    this.screenTimeCalculatorProvider = screenTimeCalculatorProvider;
    this.dischargeRateCalculatorProvider = dischargeRateCalculatorProvider;
    this.dischargeCalculatorProvider = dischargeCalculatorProvider;
  }

  @Override
  public SessionManager get() {
    return newInstance(sessionMetricsCalculatorProvider.get(), screenTimeCalculatorProvider.get(), dischargeRateCalculatorProvider.get(), dischargeCalculatorProvider.get());
  }

  public static SessionManager_Factory create(
      Provider<SessionMetricsCalculator> sessionMetricsCalculatorProvider,
      Provider<ScreenTimeCalculator> screenTimeCalculatorProvider,
      Provider<DischargeRateCalculator> dischargeRateCalculatorProvider,
      Provider<DischargeCalculator> dischargeCalculatorProvider) {
    return new SessionManager_Factory(sessionMetricsCalculatorProvider, screenTimeCalculatorProvider, dischargeRateCalculatorProvider, dischargeCalculatorProvider);
  }

  public static SessionManager newInstance(SessionMetricsCalculator sessionMetricsCalculator,
      ScreenTimeCalculator screenTimeCalculator, DischargeRateCalculator dischargeRateCalculator,
      DischargeCalculator dischargeCalculator) {
    return new SessionManager(sessionMetricsCalculator, screenTimeCalculator, dischargeRateCalculator, dischargeCalculator);
  }
}
