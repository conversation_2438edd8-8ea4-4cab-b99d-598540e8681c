// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SectionChargeOverallAverageBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView chargeAvgInfoButton;

  @NonNull
  public final LinearLayout chargeOverallAverageRoot;

  @NonNull
  public final TextView valOverallAvgSpeedMixed;

  @NonNull
  public final TextView valOverallAvgSpeedScreenOff;

  @NonNull
  public final TextView valOverallAvgSpeedScreenOn;

  private SectionChargeOverallAverageBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView chargeAvgInfoButton, @NonNull LinearLayout chargeOverallAverageRoot,
      @NonNull TextView valOverallAvgSpeedMixed, @NonNull TextView valOverallAvgSpeedScreenOff,
      @NonNull TextView valOverallAvgSpeedScreenOn) {
    this.rootView = rootView;
    this.chargeAvgInfoButton = chargeAvgInfoButton;
    this.chargeOverallAverageRoot = chargeOverallAverageRoot;
    this.valOverallAvgSpeedMixed = valOverallAvgSpeedMixed;
    this.valOverallAvgSpeedScreenOff = valOverallAvgSpeedScreenOff;
    this.valOverallAvgSpeedScreenOn = valOverallAvgSpeedScreenOn;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SectionChargeOverallAverageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SectionChargeOverallAverageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.section_charge_overall_average, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SectionChargeOverallAverageBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.charge_avg_info_button;
      ImageView chargeAvgInfoButton = ViewBindings.findChildViewById(rootView, id);
      if (chargeAvgInfoButton == null) {
        break missingId;
      }

      LinearLayout chargeOverallAverageRoot = (LinearLayout) rootView;

      id = R.id.val_overall_avg_speed_mixed;
      TextView valOverallAvgSpeedMixed = ViewBindings.findChildViewById(rootView, id);
      if (valOverallAvgSpeedMixed == null) {
        break missingId;
      }

      id = R.id.val_overall_avg_speed_screen_off;
      TextView valOverallAvgSpeedScreenOff = ViewBindings.findChildViewById(rootView, id);
      if (valOverallAvgSpeedScreenOff == null) {
        break missingId;
      }

      id = R.id.val_overall_avg_speed_screen_on;
      TextView valOverallAvgSpeedScreenOn = ViewBindings.findChildViewById(rootView, id);
      if (valOverallAvgSpeedScreenOn == null) {
        break missingId;
      }

      return new SectionChargeOverallAverageBinding((LinearLayout) rootView, chargeAvgInfoButton,
          chargeOverallAverageRoot, valOverallAvgSpeedMixed, valOverallAvgSpeedScreenOff,
          valOverallAvgSpeedScreenOn);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
