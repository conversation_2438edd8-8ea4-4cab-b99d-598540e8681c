// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDischargeBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final RelativeLayout allBlock;

  @NonNull
  public final ImageView averageInfo;

  @NonNull
  public final TextView awakePercent;

  @NonNull
  public final TextView batteryAlarmBtn;

  @NonNull
  public final TextView buttonDisschargeUsing;

  @NonNull
  public final RelativeLayout dayBlock;

  @NonNull
  public final TextView deepPercent;

  @NonNull
  public final TextView dischargeAll;

  @NonNull
  public final TextView dischargeFulltimeRemainingAll;

  @NonNull
  public final TextView dischargeFulltimeRemainingDay;

  @NonNull
  public final TextView dischargeFulltimeRemainingNight;

  @NonNull
  public final TextView dischargeNight;

  @NonNull
  public final ImageView dischargeRateInfo;

  @NonNull
  public final ImageView dischargeSessionInfo;

  @NonNull
  public final TextView dischargeSessionPercent;

  @NonNull
  public final TextView dischargeSpeedMahAll;

  @NonNull
  public final TextView dischargeSpeedMahDay;

  @NonNull
  public final TextView dischargeSpeedMahNight;

  @NonNull
  public final TextView dischargeSpeedPercentAll;

  @NonNull
  public final TextView dischargeSpeedPercentDay;

  @NonNull
  public final TextView dischargeSpeedPercentNight;

  @NonNull
  public final TextView dischargeSun;

  @NonNull
  public final TextView dischargeTextPercent;

  @NonNull
  public final TextView dischargeTextPercent3;

  @NonNull
  public final ConstraintLayout f1;

  @NonNull
  public final ConstraintLayout f2;

  @NonNull
  public final ConstraintLayout f3;

  @NonNull
  public final TextView fText;

  @NonNull
  public final TextView full1;

  @NonNull
  public final TextView full2;

  @NonNull
  public final TextView full3;

  @NonNull
  public final ImageView fullPercentInfo;

  @NonNull
  public final ConstraintLayout i1;

  @NonNull
  public final TextView i11;

  @NonNull
  public final ConstraintLayout i12;

  @NonNull
  public final ConstraintLayout i2;

  @NonNull
  public final ConstraintLayout i21;

  @NonNull
  public final ConstraintLayout i3;

  @NonNull
  public final TextView i332;

  @NonNull
  public final TextView i93;

  @NonNull
  public final TextView iT;

  @NonNull
  public final TextView iText;

  @NonNull
  public final TextView iir;

  @NonNull
  public final ConstraintLayout indentDown;

  @NonNull
  public final TextView infoDayPercentSession;

  @NonNull
  public final TextView infoDaySpeedSession;

  @NonNull
  public final TextView infoNightPercentSession;

  @NonNull
  public final TextView infoNightSpeedSession;

  @NonNull
  public final TextView mah1;

  @NonNull
  public final TextView mah2;

  @NonNull
  public final TextView mah3;

  @NonNull
  public final RelativeLayout nightBlock;

  @NonNull
  public final ImageView operationSessionInfo;

  @NonNull
  public final TextView per1;

  @NonNull
  public final TextView per2;

  @NonNull
  public final TextView per3;

  @NonNull
  public final TextView resetSessionDischargeButton;

  @NonNull
  public final ConstraintLayout s1;

  @NonNull
  public final ConstraintLayout s2;

  @NonNull
  public final ConstraintLayout s3;

  @NonNull
  public final ConstraintLayout s4;

  @NonNull
  public final ConstraintLayout s5;

  @NonNull
  public final ConstraintLayout s6;

  @NonNull
  public final ConstraintLayout s7;

  @NonNull
  public final ConstraintLayout s8;

  @NonNull
  public final NestedScrollView scrollView;

  @NonNull
  public final TextView stTexeet;

  @NonNull
  public final TextView stText;

  @NonNull
  public final TextView te0;

  @NonNull
  public final TextView te77;

  @NonNull
  public final TextView te7722;

  @NonNull
  public final TextView te88;

  @NonNull
  public final TextView te88ww;

  @NonNull
  public final TextView te99;

  @NonNull
  public final TextView text226;

  @NonNull
  public final TextView textFulltimeDisSession;

  @NonNull
  public final TextView textNowDisSession;

  @NonNull
  public final TextView textPercentDisAllSession;

  @NonNull
  public final TextView textPercentDisDaySession;

  @NonNull
  public final TextView textPercentDisDaySessionDeep;

  @NonNull
  public final TextView textPercentDisNightSession;

  @NonNull
  public final TextView textPercentDisNightSessionAwake;

  @NonNull
  public final TextView textPercentDisSessionLast;

  @NonNull
  public final TextView textSpeedDisAllSession;

  @NonNull
  public final TextView textSpeedDisDaySession;

  @NonNull
  public final TextView textSpeedDisDaySession2;

  @NonNull
  public final TextView textSpeedDisNightSession;

  @NonNull
  public final TextView textSpeedDisNightSessionAwake;

  @NonNull
  public final TextView textView6;

  @NonNull
  public final TextView textView7;

  @NonNull
  public final TextView textView9;

  @NonNull
  public final TextView textViewPercent;

  @NonNull
  public final TextView timeDaySession;

  @NonNull
  public final TextView timeDisSessionStart;

  @NonNull
  public final TextView timeNightSession;

  @NonNull
  public final LinearLayout updateView;

  @NonNull
  public final TextView updateViewBtn;

  private FragmentDischargeBinding(@NonNull NestedScrollView rootView,
      @NonNull RelativeLayout allBlock, @NonNull ImageView averageInfo,
      @NonNull TextView awakePercent, @NonNull TextView batteryAlarmBtn,
      @NonNull TextView buttonDisschargeUsing, @NonNull RelativeLayout dayBlock,
      @NonNull TextView deepPercent, @NonNull TextView dischargeAll,
      @NonNull TextView dischargeFulltimeRemainingAll,
      @NonNull TextView dischargeFulltimeRemainingDay,
      @NonNull TextView dischargeFulltimeRemainingNight, @NonNull TextView dischargeNight,
      @NonNull ImageView dischargeRateInfo, @NonNull ImageView dischargeSessionInfo,
      @NonNull TextView dischargeSessionPercent, @NonNull TextView dischargeSpeedMahAll,
      @NonNull TextView dischargeSpeedMahDay, @NonNull TextView dischargeSpeedMahNight,
      @NonNull TextView dischargeSpeedPercentAll, @NonNull TextView dischargeSpeedPercentDay,
      @NonNull TextView dischargeSpeedPercentNight, @NonNull TextView dischargeSun,
      @NonNull TextView dischargeTextPercent, @NonNull TextView dischargeTextPercent3,
      @NonNull ConstraintLayout f1, @NonNull ConstraintLayout f2, @NonNull ConstraintLayout f3,
      @NonNull TextView fText, @NonNull TextView full1, @NonNull TextView full2,
      @NonNull TextView full3, @NonNull ImageView fullPercentInfo, @NonNull ConstraintLayout i1,
      @NonNull TextView i11, @NonNull ConstraintLayout i12, @NonNull ConstraintLayout i2,
      @NonNull ConstraintLayout i21, @NonNull ConstraintLayout i3, @NonNull TextView i332,
      @NonNull TextView i93, @NonNull TextView iT, @NonNull TextView iText, @NonNull TextView iir,
      @NonNull ConstraintLayout indentDown, @NonNull TextView infoDayPercentSession,
      @NonNull TextView infoDaySpeedSession, @NonNull TextView infoNightPercentSession,
      @NonNull TextView infoNightSpeedSession, @NonNull TextView mah1, @NonNull TextView mah2,
      @NonNull TextView mah3, @NonNull RelativeLayout nightBlock,
      @NonNull ImageView operationSessionInfo, @NonNull TextView per1, @NonNull TextView per2,
      @NonNull TextView per3, @NonNull TextView resetSessionDischargeButton,
      @NonNull ConstraintLayout s1, @NonNull ConstraintLayout s2, @NonNull ConstraintLayout s3,
      @NonNull ConstraintLayout s4, @NonNull ConstraintLayout s5, @NonNull ConstraintLayout s6,
      @NonNull ConstraintLayout s7, @NonNull ConstraintLayout s8,
      @NonNull NestedScrollView scrollView, @NonNull TextView stTexeet, @NonNull TextView stText,
      @NonNull TextView te0, @NonNull TextView te77, @NonNull TextView te7722,
      @NonNull TextView te88, @NonNull TextView te88ww, @NonNull TextView te99,
      @NonNull TextView text226, @NonNull TextView textFulltimeDisSession,
      @NonNull TextView textNowDisSession, @NonNull TextView textPercentDisAllSession,
      @NonNull TextView textPercentDisDaySession, @NonNull TextView textPercentDisDaySessionDeep,
      @NonNull TextView textPercentDisNightSession,
      @NonNull TextView textPercentDisNightSessionAwake,
      @NonNull TextView textPercentDisSessionLast, @NonNull TextView textSpeedDisAllSession,
      @NonNull TextView textSpeedDisDaySession, @NonNull TextView textSpeedDisDaySession2,
      @NonNull TextView textSpeedDisNightSession, @NonNull TextView textSpeedDisNightSessionAwake,
      @NonNull TextView textView6, @NonNull TextView textView7, @NonNull TextView textView9,
      @NonNull TextView textViewPercent, @NonNull TextView timeDaySession,
      @NonNull TextView timeDisSessionStart, @NonNull TextView timeNightSession,
      @NonNull LinearLayout updateView, @NonNull TextView updateViewBtn) {
    this.rootView = rootView;
    this.allBlock = allBlock;
    this.averageInfo = averageInfo;
    this.awakePercent = awakePercent;
    this.batteryAlarmBtn = batteryAlarmBtn;
    this.buttonDisschargeUsing = buttonDisschargeUsing;
    this.dayBlock = dayBlock;
    this.deepPercent = deepPercent;
    this.dischargeAll = dischargeAll;
    this.dischargeFulltimeRemainingAll = dischargeFulltimeRemainingAll;
    this.dischargeFulltimeRemainingDay = dischargeFulltimeRemainingDay;
    this.dischargeFulltimeRemainingNight = dischargeFulltimeRemainingNight;
    this.dischargeNight = dischargeNight;
    this.dischargeRateInfo = dischargeRateInfo;
    this.dischargeSessionInfo = dischargeSessionInfo;
    this.dischargeSessionPercent = dischargeSessionPercent;
    this.dischargeSpeedMahAll = dischargeSpeedMahAll;
    this.dischargeSpeedMahDay = dischargeSpeedMahDay;
    this.dischargeSpeedMahNight = dischargeSpeedMahNight;
    this.dischargeSpeedPercentAll = dischargeSpeedPercentAll;
    this.dischargeSpeedPercentDay = dischargeSpeedPercentDay;
    this.dischargeSpeedPercentNight = dischargeSpeedPercentNight;
    this.dischargeSun = dischargeSun;
    this.dischargeTextPercent = dischargeTextPercent;
    this.dischargeTextPercent3 = dischargeTextPercent3;
    this.f1 = f1;
    this.f2 = f2;
    this.f3 = f3;
    this.fText = fText;
    this.full1 = full1;
    this.full2 = full2;
    this.full3 = full3;
    this.fullPercentInfo = fullPercentInfo;
    this.i1 = i1;
    this.i11 = i11;
    this.i12 = i12;
    this.i2 = i2;
    this.i21 = i21;
    this.i3 = i3;
    this.i332 = i332;
    this.i93 = i93;
    this.iT = iT;
    this.iText = iText;
    this.iir = iir;
    this.indentDown = indentDown;
    this.infoDayPercentSession = infoDayPercentSession;
    this.infoDaySpeedSession = infoDaySpeedSession;
    this.infoNightPercentSession = infoNightPercentSession;
    this.infoNightSpeedSession = infoNightSpeedSession;
    this.mah1 = mah1;
    this.mah2 = mah2;
    this.mah3 = mah3;
    this.nightBlock = nightBlock;
    this.operationSessionInfo = operationSessionInfo;
    this.per1 = per1;
    this.per2 = per2;
    this.per3 = per3;
    this.resetSessionDischargeButton = resetSessionDischargeButton;
    this.s1 = s1;
    this.s2 = s2;
    this.s3 = s3;
    this.s4 = s4;
    this.s5 = s5;
    this.s6 = s6;
    this.s7 = s7;
    this.s8 = s8;
    this.scrollView = scrollView;
    this.stTexeet = stTexeet;
    this.stText = stText;
    this.te0 = te0;
    this.te77 = te77;
    this.te7722 = te7722;
    this.te88 = te88;
    this.te88ww = te88ww;
    this.te99 = te99;
    this.text226 = text226;
    this.textFulltimeDisSession = textFulltimeDisSession;
    this.textNowDisSession = textNowDisSession;
    this.textPercentDisAllSession = textPercentDisAllSession;
    this.textPercentDisDaySession = textPercentDisDaySession;
    this.textPercentDisDaySessionDeep = textPercentDisDaySessionDeep;
    this.textPercentDisNightSession = textPercentDisNightSession;
    this.textPercentDisNightSessionAwake = textPercentDisNightSessionAwake;
    this.textPercentDisSessionLast = textPercentDisSessionLast;
    this.textSpeedDisAllSession = textSpeedDisAllSession;
    this.textSpeedDisDaySession = textSpeedDisDaySession;
    this.textSpeedDisDaySession2 = textSpeedDisDaySession2;
    this.textSpeedDisNightSession = textSpeedDisNightSession;
    this.textSpeedDisNightSessionAwake = textSpeedDisNightSessionAwake;
    this.textView6 = textView6;
    this.textView7 = textView7;
    this.textView9 = textView9;
    this.textViewPercent = textViewPercent;
    this.timeDaySession = timeDaySession;
    this.timeDisSessionStart = timeDisSessionStart;
    this.timeNightSession = timeNightSession;
    this.updateView = updateView;
    this.updateViewBtn = updateViewBtn;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDischargeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDischargeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_discharge, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDischargeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.all_block;
      RelativeLayout allBlock = ViewBindings.findChildViewById(rootView, id);
      if (allBlock == null) {
        break missingId;
      }

      id = R.id.average_info;
      ImageView averageInfo = ViewBindings.findChildViewById(rootView, id);
      if (averageInfo == null) {
        break missingId;
      }

      id = R.id.awake_percent;
      TextView awakePercent = ViewBindings.findChildViewById(rootView, id);
      if (awakePercent == null) {
        break missingId;
      }

      id = R.id.battery_alarm_btn;
      TextView batteryAlarmBtn = ViewBindings.findChildViewById(rootView, id);
      if (batteryAlarmBtn == null) {
        break missingId;
      }

      id = R.id.button_disscharge_using;
      TextView buttonDisschargeUsing = ViewBindings.findChildViewById(rootView, id);
      if (buttonDisschargeUsing == null) {
        break missingId;
      }

      id = R.id.day_block;
      RelativeLayout dayBlock = ViewBindings.findChildViewById(rootView, id);
      if (dayBlock == null) {
        break missingId;
      }

      id = R.id.deep_percent;
      TextView deepPercent = ViewBindings.findChildViewById(rootView, id);
      if (deepPercent == null) {
        break missingId;
      }

      id = R.id.discharge_all;
      TextView dischargeAll = ViewBindings.findChildViewById(rootView, id);
      if (dischargeAll == null) {
        break missingId;
      }

      id = R.id.discharge_fulltime_remaining_all;
      TextView dischargeFulltimeRemainingAll = ViewBindings.findChildViewById(rootView, id);
      if (dischargeFulltimeRemainingAll == null) {
        break missingId;
      }

      id = R.id.discharge_fulltime_remaining_day;
      TextView dischargeFulltimeRemainingDay = ViewBindings.findChildViewById(rootView, id);
      if (dischargeFulltimeRemainingDay == null) {
        break missingId;
      }

      id = R.id.discharge_fulltime_remaining_night;
      TextView dischargeFulltimeRemainingNight = ViewBindings.findChildViewById(rootView, id);
      if (dischargeFulltimeRemainingNight == null) {
        break missingId;
      }

      id = R.id.discharge_night;
      TextView dischargeNight = ViewBindings.findChildViewById(rootView, id);
      if (dischargeNight == null) {
        break missingId;
      }

      id = R.id.discharge_rate_info;
      ImageView dischargeRateInfo = ViewBindings.findChildViewById(rootView, id);
      if (dischargeRateInfo == null) {
        break missingId;
      }

      id = R.id.discharge_session_info;
      ImageView dischargeSessionInfo = ViewBindings.findChildViewById(rootView, id);
      if (dischargeSessionInfo == null) {
        break missingId;
      }

      id = R.id.discharge_session_percent;
      TextView dischargeSessionPercent = ViewBindings.findChildViewById(rootView, id);
      if (dischargeSessionPercent == null) {
        break missingId;
      }

      id = R.id.discharge_speed_mah_all;
      TextView dischargeSpeedMahAll = ViewBindings.findChildViewById(rootView, id);
      if (dischargeSpeedMahAll == null) {
        break missingId;
      }

      id = R.id.discharge_speed_mah_day;
      TextView dischargeSpeedMahDay = ViewBindings.findChildViewById(rootView, id);
      if (dischargeSpeedMahDay == null) {
        break missingId;
      }

      id = R.id.discharge_speed_mah_night;
      TextView dischargeSpeedMahNight = ViewBindings.findChildViewById(rootView, id);
      if (dischargeSpeedMahNight == null) {
        break missingId;
      }

      id = R.id.discharge_speed_percent_all;
      TextView dischargeSpeedPercentAll = ViewBindings.findChildViewById(rootView, id);
      if (dischargeSpeedPercentAll == null) {
        break missingId;
      }

      id = R.id.discharge_speed_percent_day;
      TextView dischargeSpeedPercentDay = ViewBindings.findChildViewById(rootView, id);
      if (dischargeSpeedPercentDay == null) {
        break missingId;
      }

      id = R.id.discharge_speed_percent_night;
      TextView dischargeSpeedPercentNight = ViewBindings.findChildViewById(rootView, id);
      if (dischargeSpeedPercentNight == null) {
        break missingId;
      }

      id = R.id.discharge_sun;
      TextView dischargeSun = ViewBindings.findChildViewById(rootView, id);
      if (dischargeSun == null) {
        break missingId;
      }

      id = R.id.discharge_text_percent;
      TextView dischargeTextPercent = ViewBindings.findChildViewById(rootView, id);
      if (dischargeTextPercent == null) {
        break missingId;
      }

      id = R.id.discharge_text_percent3;
      TextView dischargeTextPercent3 = ViewBindings.findChildViewById(rootView, id);
      if (dischargeTextPercent3 == null) {
        break missingId;
      }

      id = R.id.f1;
      ConstraintLayout f1 = ViewBindings.findChildViewById(rootView, id);
      if (f1 == null) {
        break missingId;
      }

      id = R.id.f2;
      ConstraintLayout f2 = ViewBindings.findChildViewById(rootView, id);
      if (f2 == null) {
        break missingId;
      }

      id = R.id.f3;
      ConstraintLayout f3 = ViewBindings.findChildViewById(rootView, id);
      if (f3 == null) {
        break missingId;
      }

      id = R.id.f_text;
      TextView fText = ViewBindings.findChildViewById(rootView, id);
      if (fText == null) {
        break missingId;
      }

      id = R.id.full1;
      TextView full1 = ViewBindings.findChildViewById(rootView, id);
      if (full1 == null) {
        break missingId;
      }

      id = R.id.full2;
      TextView full2 = ViewBindings.findChildViewById(rootView, id);
      if (full2 == null) {
        break missingId;
      }

      id = R.id.full3;
      TextView full3 = ViewBindings.findChildViewById(rootView, id);
      if (full3 == null) {
        break missingId;
      }

      id = R.id.full_percent_info;
      ImageView fullPercentInfo = ViewBindings.findChildViewById(rootView, id);
      if (fullPercentInfo == null) {
        break missingId;
      }

      id = R.id.i_1;
      ConstraintLayout i1 = ViewBindings.findChildViewById(rootView, id);
      if (i1 == null) {
        break missingId;
      }

      id = R.id.i11;
      TextView i11 = ViewBindings.findChildViewById(rootView, id);
      if (i11 == null) {
        break missingId;
      }

      id = R.id.i1;
      ConstraintLayout i12 = ViewBindings.findChildViewById(rootView, id);
      if (i12 == null) {
        break missingId;
      }

      id = R.id.i_2;
      ConstraintLayout i2 = ViewBindings.findChildViewById(rootView, id);
      if (i2 == null) {
        break missingId;
      }

      id = R.id.i2;
      ConstraintLayout i21 = ViewBindings.findChildViewById(rootView, id);
      if (i21 == null) {
        break missingId;
      }

      id = R.id.i3;
      ConstraintLayout i3 = ViewBindings.findChildViewById(rootView, id);
      if (i3 == null) {
        break missingId;
      }

      id = R.id.i332;
      TextView i332 = ViewBindings.findChildViewById(rootView, id);
      if (i332 == null) {
        break missingId;
      }

      id = R.id.i93;
      TextView i93 = ViewBindings.findChildViewById(rootView, id);
      if (i93 == null) {
        break missingId;
      }

      id = R.id.i_t;
      TextView iT = ViewBindings.findChildViewById(rootView, id);
      if (iT == null) {
        break missingId;
      }

      id = R.id.i_text;
      TextView iText = ViewBindings.findChildViewById(rootView, id);
      if (iText == null) {
        break missingId;
      }

      id = R.id.iir;
      TextView iir = ViewBindings.findChildViewById(rootView, id);
      if (iir == null) {
        break missingId;
      }

      id = R.id.indent_down;
      ConstraintLayout indentDown = ViewBindings.findChildViewById(rootView, id);
      if (indentDown == null) {
        break missingId;
      }

      id = R.id.info_day_percent_session;
      TextView infoDayPercentSession = ViewBindings.findChildViewById(rootView, id);
      if (infoDayPercentSession == null) {
        break missingId;
      }

      id = R.id.info_day_speed_session;
      TextView infoDaySpeedSession = ViewBindings.findChildViewById(rootView, id);
      if (infoDaySpeedSession == null) {
        break missingId;
      }

      id = R.id.info_night_percent_session;
      TextView infoNightPercentSession = ViewBindings.findChildViewById(rootView, id);
      if (infoNightPercentSession == null) {
        break missingId;
      }

      id = R.id.info_night_speed_session;
      TextView infoNightSpeedSession = ViewBindings.findChildViewById(rootView, id);
      if (infoNightSpeedSession == null) {
        break missingId;
      }

      id = R.id.mah_1;
      TextView mah1 = ViewBindings.findChildViewById(rootView, id);
      if (mah1 == null) {
        break missingId;
      }

      id = R.id.mah_2;
      TextView mah2 = ViewBindings.findChildViewById(rootView, id);
      if (mah2 == null) {
        break missingId;
      }

      id = R.id.mah_3;
      TextView mah3 = ViewBindings.findChildViewById(rootView, id);
      if (mah3 == null) {
        break missingId;
      }

      id = R.id.night_block;
      RelativeLayout nightBlock = ViewBindings.findChildViewById(rootView, id);
      if (nightBlock == null) {
        break missingId;
      }

      id = R.id.operation_session_info;
      ImageView operationSessionInfo = ViewBindings.findChildViewById(rootView, id);
      if (operationSessionInfo == null) {
        break missingId;
      }

      id = R.id.per_1;
      TextView per1 = ViewBindings.findChildViewById(rootView, id);
      if (per1 == null) {
        break missingId;
      }

      id = R.id.per_2;
      TextView per2 = ViewBindings.findChildViewById(rootView, id);
      if (per2 == null) {
        break missingId;
      }

      id = R.id.per_3;
      TextView per3 = ViewBindings.findChildViewById(rootView, id);
      if (per3 == null) {
        break missingId;
      }

      id = R.id.reset_session_discharge_button;
      TextView resetSessionDischargeButton = ViewBindings.findChildViewById(rootView, id);
      if (resetSessionDischargeButton == null) {
        break missingId;
      }

      id = R.id.s_1;
      ConstraintLayout s1 = ViewBindings.findChildViewById(rootView, id);
      if (s1 == null) {
        break missingId;
      }

      id = R.id.s_2;
      ConstraintLayout s2 = ViewBindings.findChildViewById(rootView, id);
      if (s2 == null) {
        break missingId;
      }

      id = R.id.s_3;
      ConstraintLayout s3 = ViewBindings.findChildViewById(rootView, id);
      if (s3 == null) {
        break missingId;
      }

      id = R.id.s_4;
      ConstraintLayout s4 = ViewBindings.findChildViewById(rootView, id);
      if (s4 == null) {
        break missingId;
      }

      id = R.id.s_5;
      ConstraintLayout s5 = ViewBindings.findChildViewById(rootView, id);
      if (s5 == null) {
        break missingId;
      }

      id = R.id.s_6;
      ConstraintLayout s6 = ViewBindings.findChildViewById(rootView, id);
      if (s6 == null) {
        break missingId;
      }

      id = R.id.s_7;
      ConstraintLayout s7 = ViewBindings.findChildViewById(rootView, id);
      if (s7 == null) {
        break missingId;
      }

      id = R.id.s_8;
      ConstraintLayout s8 = ViewBindings.findChildViewById(rootView, id);
      if (s8 == null) {
        break missingId;
      }

      NestedScrollView scrollView = (NestedScrollView) rootView;

      id = R.id.st_texeet;
      TextView stTexeet = ViewBindings.findChildViewById(rootView, id);
      if (stTexeet == null) {
        break missingId;
      }

      id = R.id.st_text;
      TextView stText = ViewBindings.findChildViewById(rootView, id);
      if (stText == null) {
        break missingId;
      }

      id = R.id.te0;
      TextView te0 = ViewBindings.findChildViewById(rootView, id);
      if (te0 == null) {
        break missingId;
      }

      id = R.id.te77;
      TextView te77 = ViewBindings.findChildViewById(rootView, id);
      if (te77 == null) {
        break missingId;
      }

      id = R.id.te7722;
      TextView te7722 = ViewBindings.findChildViewById(rootView, id);
      if (te7722 == null) {
        break missingId;
      }

      id = R.id.te88;
      TextView te88 = ViewBindings.findChildViewById(rootView, id);
      if (te88 == null) {
        break missingId;
      }

      id = R.id.te88ww;
      TextView te88ww = ViewBindings.findChildViewById(rootView, id);
      if (te88ww == null) {
        break missingId;
      }

      id = R.id.te99;
      TextView te99 = ViewBindings.findChildViewById(rootView, id);
      if (te99 == null) {
        break missingId;
      }

      id = R.id.text226;
      TextView text226 = ViewBindings.findChildViewById(rootView, id);
      if (text226 == null) {
        break missingId;
      }

      id = R.id.text_fulltime_dis_session;
      TextView textFulltimeDisSession = ViewBindings.findChildViewById(rootView, id);
      if (textFulltimeDisSession == null) {
        break missingId;
      }

      id = R.id.text_now_dis_session;
      TextView textNowDisSession = ViewBindings.findChildViewById(rootView, id);
      if (textNowDisSession == null) {
        break missingId;
      }

      id = R.id.text_percent_dis_all_session;
      TextView textPercentDisAllSession = ViewBindings.findChildViewById(rootView, id);
      if (textPercentDisAllSession == null) {
        break missingId;
      }

      id = R.id.text_percent_dis_day_session;
      TextView textPercentDisDaySession = ViewBindings.findChildViewById(rootView, id);
      if (textPercentDisDaySession == null) {
        break missingId;
      }

      id = R.id.text_percent_dis_day_session_deep;
      TextView textPercentDisDaySessionDeep = ViewBindings.findChildViewById(rootView, id);
      if (textPercentDisDaySessionDeep == null) {
        break missingId;
      }

      id = R.id.text_percent_dis_night_session;
      TextView textPercentDisNightSession = ViewBindings.findChildViewById(rootView, id);
      if (textPercentDisNightSession == null) {
        break missingId;
      }

      id = R.id.text_percent_dis_night_session_awake;
      TextView textPercentDisNightSessionAwake = ViewBindings.findChildViewById(rootView, id);
      if (textPercentDisNightSessionAwake == null) {
        break missingId;
      }

      id = R.id.text_percent_dis_session_last;
      TextView textPercentDisSessionLast = ViewBindings.findChildViewById(rootView, id);
      if (textPercentDisSessionLast == null) {
        break missingId;
      }

      id = R.id.text_speed_dis_all_session;
      TextView textSpeedDisAllSession = ViewBindings.findChildViewById(rootView, id);
      if (textSpeedDisAllSession == null) {
        break missingId;
      }

      id = R.id.text_speed_dis_day_session;
      TextView textSpeedDisDaySession = ViewBindings.findChildViewById(rootView, id);
      if (textSpeedDisDaySession == null) {
        break missingId;
      }

      id = R.id.text_speed_dis_day_session2;
      TextView textSpeedDisDaySession2 = ViewBindings.findChildViewById(rootView, id);
      if (textSpeedDisDaySession2 == null) {
        break missingId;
      }

      id = R.id.text_speed_dis_night_session;
      TextView textSpeedDisNightSession = ViewBindings.findChildViewById(rootView, id);
      if (textSpeedDisNightSession == null) {
        break missingId;
      }

      id = R.id.text_speed_dis_night_session_awake;
      TextView textSpeedDisNightSessionAwake = ViewBindings.findChildViewById(rootView, id);
      if (textSpeedDisNightSessionAwake == null) {
        break missingId;
      }

      id = R.id.textView6;
      TextView textView6 = ViewBindings.findChildViewById(rootView, id);
      if (textView6 == null) {
        break missingId;
      }

      id = R.id.textView7;
      TextView textView7 = ViewBindings.findChildViewById(rootView, id);
      if (textView7 == null) {
        break missingId;
      }

      id = R.id.textView9;
      TextView textView9 = ViewBindings.findChildViewById(rootView, id);
      if (textView9 == null) {
        break missingId;
      }

      id = R.id.textView_percent;
      TextView textViewPercent = ViewBindings.findChildViewById(rootView, id);
      if (textViewPercent == null) {
        break missingId;
      }

      id = R.id.time_day_session;
      TextView timeDaySession = ViewBindings.findChildViewById(rootView, id);
      if (timeDaySession == null) {
        break missingId;
      }

      id = R.id.time_dis_session_start;
      TextView timeDisSessionStart = ViewBindings.findChildViewById(rootView, id);
      if (timeDisSessionStart == null) {
        break missingId;
      }

      id = R.id.time_night_session;
      TextView timeNightSession = ViewBindings.findChildViewById(rootView, id);
      if (timeNightSession == null) {
        break missingId;
      }

      id = R.id.update_view;
      LinearLayout updateView = ViewBindings.findChildViewById(rootView, id);
      if (updateView == null) {
        break missingId;
      }

      id = R.id.update_view_btn;
      TextView updateViewBtn = ViewBindings.findChildViewById(rootView, id);
      if (updateViewBtn == null) {
        break missingId;
      }

      return new FragmentDischargeBinding((NestedScrollView) rootView, allBlock, averageInfo,
          awakePercent, batteryAlarmBtn, buttonDisschargeUsing, dayBlock, deepPercent, dischargeAll,
          dischargeFulltimeRemainingAll, dischargeFulltimeRemainingDay,
          dischargeFulltimeRemainingNight, dischargeNight, dischargeRateInfo, dischargeSessionInfo,
          dischargeSessionPercent, dischargeSpeedMahAll, dischargeSpeedMahDay,
          dischargeSpeedMahNight, dischargeSpeedPercentAll, dischargeSpeedPercentDay,
          dischargeSpeedPercentNight, dischargeSun, dischargeTextPercent, dischargeTextPercent3, f1,
          f2, f3, fText, full1, full2, full3, fullPercentInfo, i1, i11, i12, i2, i21, i3, i332, i93,
          iT, iText, iir, indentDown, infoDayPercentSession, infoDaySpeedSession,
          infoNightPercentSession, infoNightSpeedSession, mah1, mah2, mah3, nightBlock,
          operationSessionInfo, per1, per2, per3, resetSessionDischargeButton, s1, s2, s3, s4, s5,
          s6, s7, s8, scrollView, stTexeet, stText, te0, te77, te7722, te88, te88ww, te99, text226,
          textFulltimeDisSession, textNowDisSession, textPercentDisAllSession,
          textPercentDisDaySession, textPercentDisDaySessionDeep, textPercentDisNightSession,
          textPercentDisNightSessionAwake, textPercentDisSessionLast, textSpeedDisAllSession,
          textSpeedDisDaySession, textSpeedDisDaySession2, textSpeedDisNightSession,
          textSpeedDisNightSessionAwake, textView6, textView7, textView9, textViewPercent,
          timeDaySession, timeDisSessionStart, timeNightSession, updateView, updateViewBtn);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
