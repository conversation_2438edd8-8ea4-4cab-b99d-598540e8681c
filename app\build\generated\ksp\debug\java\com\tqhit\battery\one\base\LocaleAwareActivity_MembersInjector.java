package com.tqhit.battery.one.base;

import androidx.viewbinding.ViewBinding;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class LocaleAwareActivity_MembersInjector<T extends ViewBinding> implements MembersInjector<LocaleAwareActivity<T>> {
  private final Provider<AppRepository> appRepositoryProvider;

  public LocaleAwareActivity_MembersInjector(Provider<AppRepository> appRepositoryProvider) {
    this.appRepositoryProvider = appRepositoryProvider;
  }

  public static <T extends ViewBinding> MembersInjector<LocaleAwareActivity<T>> create(
      Provider<AppRepository> appRepositoryProvider) {
    return new LocaleAwareActivity_MembersInjector<T>(appRepositoryProvider);
  }

  @Override
  public void injectMembers(LocaleAwareActivity<T> instance) {
    injectAppRepository(instance, appRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.base.LocaleAwareActivity.appRepository")
  public static <T extends ViewBinding> void injectAppRepository(LocaleAwareActivity<T> instance,
      AppRepository appRepository) {
    instance.appRepository = appRepository;
  }
}
