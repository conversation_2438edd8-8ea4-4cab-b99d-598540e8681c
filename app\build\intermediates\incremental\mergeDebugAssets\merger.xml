<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="io.bidmachine:ads.networks.gam_dynamic:3.3.0.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf25d24cf81b4604f91c3bf5f1aa130\transformed\jetified-ads.networks.gam_dynamic-3.3.0.0\assets"><file name="bm_networks/gam_dynamic.bmnetwork" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf25d24cf81b4604f91c3bf5f1aa130\transformed\jetified-ads.networks.gam_dynamic-3.3.0.0\assets\bm_networks\gam_dynamic.bmnetwork"/></source></dataSet><dataSet config="io.bidmachine:ads.networks.gam:3.3.0.3" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd511c80e84af72750c7af79b9c916c1\transformed\jetified-ads.networks.gam-3.3.0.3\assets"><file name="bm_networks/gam.bmnetwork" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd511c80e84af72750c7af79b9c916c1\transformed\jetified-ads.networks.gam-3.3.0.3\assets\bm_networks\gam.bmnetwork"/></source></dataSet><dataSet config="com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\assets"><file name="builddatas.json" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\assets\builddatas.json"/><file name="mbridge_download_dialog_view.xml" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\assets\mbridge_download_dialog_view.xml"/><file name="rv_binddatas.xml" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\assets\rv_binddatas.xml"/></source></dataSet><dataSet config="com.fyber:marketplace-sdk:8.3.7" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\assets"><file name="fyb_iframe_endcard_tmpl.html" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\assets\fyb_iframe_endcard_tmpl.html"/><file name="fyb_static_endcard_tmpl.html" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\assets\fyb_static_endcard_tmpl.html"/><file name="ia_js_load_monitor.txt" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\assets\ia_js_load_monitor.txt"/><file name="ia_mraid_bridge.txt" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\assets\ia_mraid_bridge.txt"/></source></dataSet><dataSet config="com.facebook.android:audience-network-sdk:6.20.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\assets"><file name="audience_network.dex" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\assets\audience_network.dex"/></source></dataSet><dataSet config="com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\assets"><file name="dic" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\assets\dic"/><file name="tt_mime_type.pro" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\assets\tt_mime_type.pro"/></source></dataSet><dataSet config="com.unity3d.ads:unity-ads:4.15.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\assets"><file name="ad-viewer/omid-session-client-v1.js" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\assets\ad-viewer\omid-session-client-v1.js"/><file name="ad-viewer/omsdk-v1.js" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\assets\ad-viewer\omsdk-v1.js"/></source></dataSet><dataSet config="com.moloco.sdk:moloco-sdk:3.11.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\assets"><file name="com.moloco.sdk.xenoss.sdkdevkit.mraid.js" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\assets\com.moloco.sdk.xenoss.sdkdevkit.mraid.js"/><file name="favicon.ico" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\assets\favicon.ico"/><file name="mraid-bridge.js" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\assets\mraid-bridge.js"/><file name="mraid.js" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87efb37cc196946f0acdbe6a19ad095d\transformed\jetified-moloco-sdk-3.11.0\assets\mraid.js"/></source></dataSet><dataSet config="com.hyprmx.android:HyprMX-SDK:6.4.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\assets"><file name="sdk_core.min.js" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\assets\sdk_core.min.js"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>