package com.tqhit.battery.one.features.stats.discharge.domain;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DischargeCalculator_Factory implements Factory<DischargeCalculator> {
  @Override
  public DischargeCalculator get() {
    return newInstance();
  }

  public static DischargeCalculator_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static DischargeCalculator newInstance() {
    return new DischargeCalculator();
  }

  private static final class InstanceHolder {
    static final DischargeCalculator_Factory INSTANCE = new DischargeCalculator_Factory();
  }
}
