!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).sdk_core={})}(this,(function(e){"use strict";const t=parseInt("1.1.5+252".split("+").pop(),10)||0;function i(){return"hyprmx.com"}function r(){return"/v1/initialization"}function s(){return 3600}function n(e){return null==e||"string"==typeof e&&0===e.trim().length}function o(e,t){return"string"==typeof e&&"string"==typeof t&&e.toUpperCase()===t.toUpperCase()}function a(e){return new RegExp(/(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-/]))?/).test(e)}function l(e){return JSON.parse(JSON.stringify(e))}globalThis.version=function(){return t};const d=e=>e&&"object"==typeof e&&!Array.isArray(e),c=(e,...t)=>{if(!t.length)return e;const i=t.shift();if(d(e)&&d(i))for(const t in i)d(i[t])?(e[t]||Object.assign(e,{[t]:{}}),c(e[t],i[t])):Object.assign(e,{[t]:i[t]});return c(e,...t)};globalThis.HYPREventController=void 0,globalThis.HYPRRequestParameterManager=void 0,globalThis.HYPRBiddingController=void 0,globalThis.HYPRPlacementController=void 0,globalThis.HYPRAudioEventPublisher=void 0;const h="persistent_id",u="ad_id_opted_out";globalThis.defaultUIComponents={};const p="mraid",g="web_traffic",m="banner",f="display",b="next_button_text",_="finish_button_text",v="title_text",w="countdown_text",y=["http","https","about","file","data"],P="text/html",A="utf-8",S={CONSENT_STATUS_UNKNOWN:0,CONSENT_GIVEN:1,CONSENT_DECLINED:2},T={COMPLETED:"COMPLETED",PAYOUT_COMPLETE:"PAYOUT_COMPLETE",BACKGROUNDED:"BACKGROUNDED",IN_PROGRESS:"IN_PROGRESS"},C={BACK_PRESS:"BACK_PRESS",PRESENT_DIALOG_OK:"PRESENT_DIALOG_OK",ERROR_DIALOG_OK:"ERROR_DIALOG_OK",CANCELLATION_DIALOG_OK:"CANCELLATION_DIALOG_OK",ABORT:"ABORT",NATIVE_CLOSE_BUTTON:"NATIVE_CLOSE_BUTTON",COMPLETE_NO_THANK_YOU:"COMPLETE_NO_THANK_YOU",MRAID_CLOSE:"MRAID_CLOSE",CLOSE_AD:"CLOSE_AD",CRASHED:"CRASHED",WEBVIEW_CRASH:"WEBVIEW_CRASH",UNKNOWN:"UNKNOWN"};globalThis.PRESENTATION_STATES={BROWSER:"presentingHyprMXBrowser",OUTSIDE_APP:"presentingDeepLinkOutsideApplication",APP_STORE:"presentingAppStoreOverlay"};const E={TOKEN:"token",USER_ID:"uid",VIEWING_ID:"viewing_id",REWARD_TOKEN:"reward_token",DISTRIBUTOR_ID:"distributor_id",DURATION:"duration"},R={LOADED:"loaded",TIMED_OUT:"timed_out"};globalThis.COMPLETION_URL="",globalThis.DURATION_URL="",globalThis.AVAILABLE_FOR=3600,globalThis.IS_IOS=!0;class k{constructor(e,t,i,r){this.followRedirect=e,this.readTimeout=t,this.connectionTimeout=i,this.headers=r}}const x="application/json",N=`${x};charset=UTF-8`,O=6e4,I=6e4,M={"Content-Type":N},V=new k(!0,O,I,M),D=new k(!0,2e4,2e4,M),F=new k(!0,O,I,{}),B=["app_bound_domains"];class U{static compact(e){const t=JSON.parse(JSON.stringify(e));U.compactHelper(t);for(const i of B)void 0!==e[i]&&(t[i]=e[i]);return t}static compactHelper(e){null!=e&&(this.compactJsonArray(e),Object.keys(e).forEach((t=>{(this.isObject(e[t])||Array.isArray(e[t]))&&this.compactHelper(e[t]),this.isEmpty(e[t])&&delete e[t]})))}static compactJsonArray(e){if(Array.isArray(e)){e.forEach((e=>this.compactHelper(e)));for(let t=Object.keys(e).length-1;t>=0;t-=1)this.isEmpty(e[t])&&e.splice(t,1)}}static isEmpty(e){return!!(null==e||Array.isArray(e)&&0===e.length||this.isObject(e)&&0===Object.keys(e).length||""===e)}static isObject(e){return"[object Object]"===Object.prototype.toString.call(e)}}const $="__CANCELLED__";class L{constructor(){this._rejection=void 0,this.abortHandler=()=>{}}set reject(e){this._rejection=()=>{this.abortHandler(),e($)}}cancel(){void 0!==this._rejection?this._rejection():console.error("no reject handler set")}}const W="GET",q="POST",z="PATCH",H="PUT",J=".a.hyprmx.",Y="jungroup-pbs.relevant-digital.com";class j{constructor(e=globalThis.HYPRRequestParamListener){this.requestMap=new Map,this.requestId=0,this.nativeParams=e}transform(e,t=[J,Y],i=this.nativeParams.getPlatform()){if("iOS"===i&&!0===this.nativeParams.getAdIdOptedOut())for(const i of t)if(e.includes(i)){if(i===J)return e.replace(J,".b.hyprmx.");if(i===Y)return e.replace(Y,"jungroup-pbs-nt.relevant-digital.com")}return e}request(e,t,i,r,s=(()=>{})){const n=this.transform(e),o=this.requestId.toString();return this.prettyLogEvent(!0,o,n,JSON.parse(t||"{}")),this.requestId+=1,HYPRNativeNetworkController.request(o,n,t,i,JSON.stringify(r),"HYPRNetworkController.onNetworkResponse"),this.requestMap[o]=s,o}get(e,t=(()=>{}),i=F){return this.request(e,void 0,W,i,t)}async getAsync(e,t=F){return await new Promise((i=>{this.get(e,((e,t,r,s)=>{i({response:e,body:t,error:r,headers:s})}),t)}))}post(e,t,i=(()=>{}),r=!0,s=V){let n;return n=r?JSON.stringify(U.compact(t)):JSON.stringify(t),this.request(e,n,q,s,i)}async postAsync(e,t,i=V){const{promise:r}=this.cancellableAsyncPost(e,t,i);return await r}cancellableAsyncPost(e,t,i=V){const r=new L;return{promise:new Promise(((s,n)=>{r.reject=n;const o=this.post(e,t,((e,t,i,r)=>{s({response:e,body:t,error:i,headers:r})}),!0,i);r.abortHandler=()=>{this.abortRequest(o)}})),canceller:r}}put(e,t,i=(()=>{}),r=!0,s=V){let n;return n=r?JSON.stringify(U.compact(t)):JSON.stringify(t),this.request(e,n,H,s,i)}abortRequest(e){console.debug(`Network request aborted for ${e}`),HYPRNativeNetworkController.abortRequest(e),this.requestMap[e]=null}onNetworkResponse(e,t){j.validJSONType(t.headers)?this.prettyLogEvent(!1,e,"",JSON.parse(t.body||"{}"),t.headers):console.debug(`Network response received for id: ${e}`);const i=this.requestMap[e];this.requestMap[e]=null,"function"==typeof i&&i(t.code,t.body,t.error,t.headers)}prettyLogEvent(e,t,i,r={},s=void 0){const n=JSON.stringify(r,null,2);let o="";"object"==typeof s&&(o=`\nHeaders: ${JSON.stringify(s,null,2)}`);const a=e?`URL: ${i}`:"";console.debug(`\n\nNetwork ${e?"Request":"Response"}: ${t}\n${a}\n Body:\n${n}${o}\n`)}static validResponseStatus(e){return!(e>=400||0===e)}static validXMLType(e){return void 0!==e&&"string"==typeof e["Content-Type"]&&e["Content-Type"].includes("application/xml")}static validHtmlType(e){return void 0!==e&&"string"==typeof e["Content-Type"]&&e["Content-Type"].includes("text/html")}static validJSONType(e){return void 0!==e&&"string"==typeof e["Content-Type"]&&e["Content-Type"].includes("application/json")}static validateResponseBody(e){if(void 0===e)return"Response data not parseable."}}var K="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function Z(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var G={exports:{}};
/*!
   * validate.js 0.13.1
   * http://validatejs.org/
   * (c) 2013-2015 Nicklas Ansman, 2013 Wrapp
   * validate.js may be freely distributed under the MIT license.
  */!function(e,t){(function(e,t,i){var r=function(e,t,i){i=s.extend({},s.options,i);var n=s.runValidations(e,t,i);if(n.some((function(e){return s.isPromise(e.error)})))throw new Error("Use validate.async if you want support for promises");return r.processValidationResults(n,i)},s=r;s.extend=function(e){return[].slice.call(arguments,1).forEach((function(t){for(var i in t)e[i]=t[i]})),e},s.extend(r,{version:{major:0,minor:13,patch:1,metadata:null,toString:function(){var e=s.format("%{major}.%{minor}.%{patch}",s.version);return s.isEmpty(s.version.metadata)||(e+="+"+s.version.metadata),e}},Promise:"undefined"!=typeof Promise?Promise:null,EMPTY_STRING_REGEXP:/^\s*$/,runValidations:function(e,t,i){var r,n,o,a,l,d,c,h=[];for(r in(s.isDomElement(e)||s.isJqueryElement(e))&&(e=s.collectFormValues(e)),t)for(n in o=s.getDeepObjectValue(e,r),a=s.result(t[r],o,e,r,i,t)){if(!(l=s.validators[n]))throw c=s.format("Unknown validator %{name}",{name:n}),new Error(c);d=a[n],(d=s.result(d,o,e,r,i,t))&&h.push({attribute:r,value:o,validator:n,globalOptions:i,attributes:e,options:d,error:l.call(l,o,d,r,e,i)})}return h},processValidationResults:function(e,t){e=s.pruneEmptyErrors(e,t),e=s.expandMultipleErrors(e,t),e=s.convertErrorMessages(e,t);var i=t.format||"grouped";if("function"!=typeof s.formatters[i])throw new Error(s.format("Unknown format %{format}",t));return e=s.formatters[i](e),s.isEmpty(e)?void 0:e},async:function(e,t,i){var r=(i=s.extend({},s.async.options,i)).wrapErrors||function(e){return e};!1!==i.cleanAttributes&&(e=s.cleanAttributes(e,t));var n=s.runValidations(e,t,i);return new s.Promise((function(o,a){s.waitForResults(n).then((function(){var l=s.processValidationResults(n,i);l?a(new r(l,i,e,t)):o(e)}),(function(e){a(e)}))}))},single:function(e,t,i){return i=s.extend({},s.single.options,i,{format:"flat",fullMessages:!1}),s({single:e},{single:t},i)},waitForResults:function(e){return e.reduce((function(e,t){return s.isPromise(t.error)?e.then((function(){return t.error.then((function(e){t.error=e||null}))})):e}),new s.Promise((function(e){e()})))},result:function(e){var t=[].slice.call(arguments,1);return"function"==typeof e&&(e=e.apply(null,t)),e},isNumber:function(e){return"number"==typeof e&&!isNaN(e)},isFunction:function(e){return"function"==typeof e},isInteger:function(e){return s.isNumber(e)&&e%1==0},isBoolean:function(e){return"boolean"==typeof e},isObject:function(e){return e===Object(e)},isDate:function(e){return e instanceof Date},isDefined:function(e){return null!=e},isPromise:function(e){return!!e&&s.isFunction(e.then)},isJqueryElement:function(e){return e&&s.isString(e.jquery)},isDomElement:function(e){return!!e&&!(!e.querySelectorAll||!e.querySelector)&&(!(!s.isObject(document)||e!==document)||("object"==typeof HTMLElement?e instanceof HTMLElement:e&&"object"==typeof e&&null!==e&&1===e.nodeType&&"string"==typeof e.nodeName))},isEmpty:function(e){var t;if(!s.isDefined(e))return!0;if(s.isFunction(e))return!1;if(s.isString(e))return s.EMPTY_STRING_REGEXP.test(e);if(s.isArray(e))return 0===e.length;if(s.isDate(e))return!1;if(s.isObject(e)){for(t in e)return!1;return!0}return!1},format:s.extend((function(e,t){return s.isString(e)?e.replace(s.format.FORMAT_REGEXP,(function(e,i,r){return"%"===i?"%{"+r+"}":String(t[r])})):e}),{FORMAT_REGEXP:/(%?)%\{([^\}]+)\}/g}),prettify:function(e){return s.isNumber(e)?100*e%1==0?""+e:parseFloat(Math.round(100*e)/100).toFixed(2):s.isArray(e)?e.map((function(e){return s.prettify(e)})).join(", "):s.isObject(e)?s.isDefined(e.toString)?e.toString():JSON.stringify(e):(e=""+e).replace(/([^\s])\.([^\s])/g,"$1 $2").replace(/\\+/g,"").replace(/[_-]/g," ").replace(/([a-z])([A-Z])/g,(function(e,t,i){return t+" "+i.toLowerCase()})).toLowerCase()},stringifyValue:function(e,t){return(t&&t.prettify||s.prettify)(e)},isString:function(e){return"string"==typeof e},isArray:function(e){return"[object Array]"==={}.toString.call(e)},isHash:function(e){return s.isObject(e)&&!s.isArray(e)&&!s.isFunction(e)},contains:function(e,t){return!!s.isDefined(e)&&(s.isArray(e)?-1!==e.indexOf(t):t in e)},unique:function(e){return s.isArray(e)?e.filter((function(e,t,i){return i.indexOf(e)==t})):e},forEachKeyInKeypath:function(e,t,i){if(s.isString(t)){var r,n="",o=!1;for(r=0;r<t.length;++r)switch(t[r]){case".":o?(o=!1,n+="."):(e=i(e,n,!1),n="");break;case"\\":o?(o=!1,n+="\\"):o=!0;break;default:o=!1,n+=t[r]}return i(e,n,!0)}},getDeepObjectValue:function(e,t){if(s.isObject(e))return s.forEachKeyInKeypath(e,t,(function(e,t){if(s.isObject(e))return e[t]}))},collectFormValues:function(e,t){var i,r,n,o,a,l,d={};if(s.isJqueryElement(e)&&(e=e[0]),!e)return d;for(t=t||{},o=e.querySelectorAll("input[name], textarea[name]"),i=0;i<o.length;++i)if(n=o.item(i),!s.isDefined(n.getAttribute("data-ignored"))){var c=n.name.replace(/\./g,"\\\\.");l=s.sanitizeFormValue(n.value,t),"number"===n.type?l=l?+l:null:"checkbox"===n.type?n.attributes.value?n.checked||(l=d[c]||null):l=n.checked:"radio"===n.type&&(n.checked||(l=d[c]||null)),d[c]=l}for(o=e.querySelectorAll("select[name]"),i=0;i<o.length;++i)if(n=o.item(i),!s.isDefined(n.getAttribute("data-ignored"))){if(n.multiple)for(r in l=[],n.options)(a=n.options[r])&&a.selected&&l.push(s.sanitizeFormValue(a.value,t));else{var h=void 0!==n.options[n.selectedIndex]?n.options[n.selectedIndex].value:"";l=s.sanitizeFormValue(h,t)}d[n.name]=l}return d},sanitizeFormValue:function(e,t){return t.trim&&s.isString(e)&&(e=e.trim()),!1!==t.nullify&&""===e?null:e},capitalize:function(e){return s.isString(e)?e[0].toUpperCase()+e.slice(1):e},pruneEmptyErrors:function(e){return e.filter((function(e){return!s.isEmpty(e.error)}))},expandMultipleErrors:function(e){var t=[];return e.forEach((function(e){s.isArray(e.error)?e.error.forEach((function(i){t.push(s.extend({},e,{error:i}))})):t.push(e)})),t},convertErrorMessages:function(e,t){var i=[],r=(t=t||{}).prettify||s.prettify;return e.forEach((function(e){var n=s.result(e.error,e.value,e.attribute,e.options,e.attributes,e.globalOptions);return s.isString(n)?("^"===n[0]?n=n.slice(1):!1!==t.fullMessages&&(n=s.capitalize(r(e.attribute))+" "+n),n=n.replace(/\\\^/g,"^"),n=s.format(n,{value:s.stringifyValue(e.value,t)}),void i.push(s.extend({},e,{error:n}))):void i.push(e)})),i},groupErrorsByAttribute:function(e){var t={};return e.forEach((function(e){var i=t[e.attribute];i?i.push(e):t[e.attribute]=[e]})),t},flattenErrorsToArray:function(e){return e.map((function(e){return e.error})).filter((function(e,t,i){return i.indexOf(e)===t}))},cleanAttributes:function(e,t){function i(e,t,i){return s.isObject(e[t])?e[t]:e[t]=!!i||{}}return s.isObject(t)&&s.isObject(e)?(t=function(e){var t,r={};for(t in e)e[t]&&s.forEachKeyInKeypath(r,t,i);return r}(t),function e(t,i){if(!s.isObject(t))return t;var r,n,o=s.extend({},t);for(n in t)r=i[n],s.isObject(r)?o[n]=e(o[n],r):r||delete o[n];return o}(e,t)):{}},exposeModule:function(e,t,i,r,s){i?(r&&r.exports&&(i=r.exports=e),i.validate=e):(t.validate=e,e.isFunction(s)&&s.amd&&s([],(function(){return e})))},warn:function(e){"undefined"!=typeof console&&console.warn&&console.warn("[validate.js] "+e)},error:function(e){"undefined"!=typeof console&&console.error&&console.error("[validate.js] "+e)}}),r.validators={presence:function(e,t){if(!1!==(t=s.extend({},this.options,t)).allowEmpty?!s.isDefined(e):s.isEmpty(e))return t.message||this.message||"can't be blank"},length:function(e,t,i){if(s.isDefined(e)){var r,n=(t=s.extend({},this.options,t)).is,o=t.maximum,a=t.minimum,l=t.tokenizer||function(e){return e},d=[],c=(e=l(e)).length;return s.isNumber(c)?(s.isNumber(n)&&c!==n&&(r=t.wrongLength||this.wrongLength||"is the wrong length (should be %{count} characters)",d.push(s.format(r,{count:n}))),s.isNumber(a)&&c<a&&(r=t.tooShort||this.tooShort||"is too short (minimum is %{count} characters)",d.push(s.format(r,{count:a}))),s.isNumber(o)&&c>o&&(r=t.tooLong||this.tooLong||"is too long (maximum is %{count} characters)",d.push(s.format(r,{count:o}))),d.length>0?t.message||d:void 0):t.message||this.notValid||"has an incorrect length"}},numericality:function(e,t,i,r,n){if(s.isDefined(e)){var o,a,l=[],d={greaterThan:function(e,t){return e>t},greaterThanOrEqualTo:function(e,t){return e>=t},equalTo:function(e,t){return e===t},lessThan:function(e,t){return e<t},lessThanOrEqualTo:function(e,t){return e<=t},divisibleBy:function(e,t){return e%t==0}},c=(t=s.extend({},this.options,t)).prettify||n&&n.prettify||s.prettify;if(s.isString(e)&&t.strict){var h="^-?(0|[1-9]\\d*)";if(t.onlyInteger||(h+="(\\.\\d+)?"),h+="$",!new RegExp(h).test(e))return t.message||t.notValid||this.notValid||this.message||"must be a valid number"}if(!0!==t.noStrings&&s.isString(e)&&!s.isEmpty(e)&&(e=+e),!s.isNumber(e))return t.message||t.notValid||this.notValid||this.message||"is not a number";if(t.onlyInteger&&!s.isInteger(e))return t.message||t.notInteger||this.notInteger||this.message||"must be an integer";for(o in d)if(a=t[o],s.isNumber(a)&&!d[o](e,a)){var u="not"+s.capitalize(o),p=t[u]||this[u]||this.message||"must be %{type} %{count}";l.push(s.format(p,{count:a,type:c(o)}))}return t.odd&&e%2!=1&&l.push(t.notOdd||this.notOdd||this.message||"must be odd"),t.even&&e%2!=0&&l.push(t.notEven||this.notEven||this.message||"must be even"),l.length?t.message||l:void 0}},datetime:s.extend((function(e,t){if(!s.isFunction(this.parse)||!s.isFunction(this.format))throw new Error("Both the parse and format functions needs to be set to use the datetime/date validator");if(s.isDefined(e)){var i,r=[],n=(t=s.extend({},this.options,t)).earliest?this.parse(t.earliest,t):NaN,o=t.latest?this.parse(t.latest,t):NaN;return e=this.parse(e,t),isNaN(e)||t.dateOnly&&e%864e5!=0?(i=t.notValid||t.message||this.notValid||"must be a valid date",s.format(i,{value:arguments[0]})):(!isNaN(n)&&e<n&&(i=t.tooEarly||t.message||this.tooEarly||"must be no earlier than %{date}",i=s.format(i,{value:this.format(e,t),date:this.format(n,t)}),r.push(i)),!isNaN(o)&&e>o&&(i=t.tooLate||t.message||this.tooLate||"must be no later than %{date}",i=s.format(i,{date:this.format(o,t),value:this.format(e,t)}),r.push(i)),r.length?s.unique(r):void 0)}}),{parse:null,format:null}),date:function(e,t){return t=s.extend({},t,{dateOnly:!0}),s.validators.datetime.call(s.validators.datetime,e,t)},format:function(e,t){(s.isString(t)||t instanceof RegExp)&&(t={pattern:t});var i,r=(t=s.extend({},this.options,t)).message||this.message||"is invalid",n=t.pattern;if(s.isDefined(e))return s.isString(e)?(s.isString(n)&&(n=new RegExp(t.pattern,t.flags)),(i=n.exec(e))&&i[0].length==e.length?void 0:r):r},inclusion:function(e,t){if(s.isDefined(e)&&(s.isArray(t)&&(t={within:t}),t=s.extend({},this.options,t),!s.contains(t.within,e))){var i=t.message||this.message||"^%{value} is not included in the list";return s.format(i,{value:e})}},exclusion:function(e,t){if(s.isDefined(e)&&(s.isArray(t)&&(t={within:t}),t=s.extend({},this.options,t),s.contains(t.within,e))){var i=t.message||this.message||"^%{value} is restricted";return s.isString(t.within[e])&&(e=t.within[e]),s.format(i,{value:e})}},email:s.extend((function(e,t){var i=(t=s.extend({},this.options,t)).message||this.message||"is not a valid email";if(s.isDefined(e))return s.isString(e)&&this.PATTERN.exec(e)?void 0:i}),{PATTERN:/^(?:[a-z0-9!#$%&'*+\/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+\/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])$/i}),equality:function(e,t,i,r,n){if(s.isDefined(e)){s.isString(t)&&(t={attribute:t});var o=(t=s.extend({},this.options,t)).message||this.message||"is not equal to %{attribute}";if(s.isEmpty(t.attribute)||!s.isString(t.attribute))throw new Error("The attribute must be a non empty string");var a=s.getDeepObjectValue(r,t.attribute),l=t.comparator||function(e,t){return e===t},d=t.prettify||n&&n.prettify||s.prettify;return l(e,a,t,i,r)?void 0:s.format(o,{attribute:d(t.attribute)})}},url:function(e,t){if(s.isDefined(e)){var i=(t=s.extend({},this.options,t)).message||this.message||"is not a valid url",r=t.schemes||this.schemes||["http","https"],n=t.allowLocal||this.allowLocal||!1,o=t.allowDataUrl||this.allowDataUrl||!1;if(!s.isString(e))return i;var a="^(?:(?:"+r.join("|")+")://)(?:\\S+(?::\\S*)?@)?(?:",l="(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))";if(n?l+="?":a+="(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})",a+="(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*"+l+")(?::\\d{2,5})?(?:[/?#]\\S*)?$",o){a="(?:"+a+")|(?:^data:(?:\\w+\\/[-+.\\w]+(?:;[\\w=]+)*)?(?:;base64)?,[A-Za-z0-9-_.!~\\*'();\\/?:@&=+$,%]*$)"}return new RegExp(a,"i").exec(e)?void 0:i}},type:s.extend((function(e,t,i,r,n){if(s.isString(t)&&(t={type:t}),s.isDefined(e)){var o,a=s.extend({},this.options,t),l=a.type;if(!s.isDefined(l))throw new Error("No type was specified");if(o=s.isFunction(l)?l:this.types[l],!s.isFunction(o))throw new Error("validate.validators.type.types."+l+" must be a function.");if(!o(e,a,i,r,n)){var d=t.message||this.messages[l]||this.message||a.message||(s.isFunction(l)?"must be of the correct type":"must be of type %{type}");return s.isFunction(d)&&(d=d(e,t,i,r,n)),s.format(d,{attribute:s.prettify(i),type:l})}}}),{types:{object:function(e){return s.isObject(e)&&!s.isArray(e)},array:s.isArray,integer:s.isInteger,number:s.isNumber,string:s.isString,date:s.isDate,boolean:s.isBoolean},messages:{}})},r.formatters={detailed:function(e){return e},flat:s.flattenErrorsToArray,grouped:function(e){var t;for(t in e=s.groupErrorsByAttribute(e))e[t]=s.flattenErrorsToArray(e[t]);return e},constraint:function(e){var t;for(t in e=s.groupErrorsByAttribute(e))e[t]=e[t].map((function(e){return e.validator})).sort();return e}},r.exposeModule(r,this,e,t,i)}).call(K,t,e,null)}(G,G.exports);var X=Z(G.exports);class Q{static parseJSON(e){let t;try{t=JSON.parse(e)}catch(t){console.error(`Error parsing json string (${e}) with error ${t}`)}return t}static hasRequiredKeys(e={},t=[],i="string"){let r;r="url"===i?e=>Q.urlValidation(e):e=>Q.typeValidation(e,i);for(const i of t)if(void 0===e[i]||!r(e[i]))return!1;return!0}static typeValidation(e,t){return typeof e===t}static urlValidation(e,t=["https","http","localhost","data","file"]){return void 0===X({website:e},{website:{url:{schemes:t}}})}}class ee{constructor(e,t,i,r,s,n,o,a,l=globalThis.HYPRNetworkController,d=HYPRRequestParameterManager){this.adImpressionTrackingUrl=e,this.userInfoTrackingUrl=t,this.webTrafficVisitUrl=i,this.noAdTrackingUrl=n,this.networkRequestController=l,this.errorReportingURL=r,this.errorSeverityLevel=s,this.adProgressUrl=o,this.durationTrackingUrl=a,this.requestParameterManager=d,this.webTrafficVisitInfo={}}sendAdProgressTracking(e,t){void 0!==e&&void 0!==this.adProgressUrl&&this.networkRequestController.post(this.adProgressUrl,JSON.parse(e),t)}async sendWebTrafficVisitEvent(e,t,i){const r={};r.url=e,r.viewing_id=t,r.wtu_id=i;const{code:s,body:n,error:o}=await this.networkRequestController.postAsync(this.webTrafficVisitUrl,r),a=Q.parseJSON(n);void 0===o&&j.validResponseStatus(s)&&void 0!==a&&void 0!==a.id?this.recordWebTrafficVisitInfo(e,a.id):console.error(`[EC] sendWebTrafficVisitEvent error received: ${o}`)}recordWebTrafficVisitInfo(e,t){this.webTrafficVisitInfo[e]=t}sendWebTrafficTimeSpent(e,t){const i=this.webTrafficVisitInfo[e];if(void 0===i)return void console.error("[EC] Cannot find id for WTTS");const r=`${this.webTrafficVisitUrl}/${i}`;this.networkRequestController.request(r,t,z,V)}sendNoAdTracking(e){this.sendTrackingEventWithParams(e,this.noAdTrackingUrl)}sendAdImpressionTrackingEvent(e,t){this.sendTrackingEventWithParams(e,this.adImpressionTrackingUrl,t)}sendUserInfoTrackingEvent(e,t,i){this.sendTrackingEventWithParams(e,this.userInfoTrackingUrl,i,ee.extractPrequalQuestionNames(t))}sendPixelTrackingEvents(e=[]){for(const t of e)console.debug(`[EC] firing impression url: ${t}`),this.networkRequestController.get(t)}static extractPrequalQuestionNames(e){return{questions_shown:JSON.parse(e).map((e=>e.Name))}}sendTrackingEventWithParams(e,t,i,r){const s=void 0!==i?i.getAdIdentifier():"",n=void 0!==i?i.getAdType():e.getAdType();let o={placement_id:e.id,offer_identifier:s,offer_type:n};console.debug(`[EC] Requesting additional trackings params for: ${JSON.stringify(o)}`);const a=this.requestParameterManager.getTrackingParams();Object.assign(o,a),void 0!==r&&(o=Object.assign(o,r)),console.debug(`[EC] Sending trackings to ${t}`),this.networkRequestController.post(t,o)}sendDurationUpdateTracking(e,t,i){const r={};r[E.DURATION]=t,r.distributorid=this.requestParameterManager.getDistributorID(),r[E.USER_ID]=this.requestParameterManager.getUserId(),r[E.TOKEN]=e,this.networkRequestController.put(`${this.durationTrackingUrl}/${i}`,r)}}const te={REWARDED:"rewarded",INTERSTITIAL:"interstitial",BANNER:"banner"};class ie{constructor(e,t,i,r,s,n=globalThis.HYPRNetworkController,o=globalThis.HYPRTimerController,a=globalThis.consentController,l=HYPRRequestParameterManager){this.id=e,this.name=t,this.type=i,this.inventoryCheckURL=r,this.networkRequestController=n,this.timerController=o,this.consentController=a,this.placementListener=s,this.requestParameterManager=l,this.request=void 0,this.imp=void 0}async loadAd(e){}inventoryCheckCallback(e,t,i,r){}processResponse(e,t,i,r=!1){}loadBidResponse(e){return!1}isAdAvailable(){return!1}getAdType(){return""}getUIComponentsString(){return"{}"}clearAdAndStopAdAvailableTimer(e=!1){}startAdAvailableTimer(){}handleRefresh(e,t){}isEqualToPlacement(e){return this.id===e.id}compileRequestParamsFromParams(e={},t=!0,i=this.ad){return e.placement_id=this.id,e}appStoreSignatureParams(e,t){const i=this.requestParameterManager.getTrackingParams();return{app_store_params:e,device_os_version:i.device_os_version,bundle_id:i.bundle_id,distributor_id:i.distributor_id,placement_id:this.id,offer:t.getOfferName(),msdkv:i.msdkv,mobile_js_version:globalThis.version(),xcode_version:i.xcode_version}}getOfferName(){return""}isFullscreenAdType(){return this.type===te.REWARDED||this.type===te.INTERSTITIAL}isBannerAdType(){return this.type===te.BANNER}toString(){return`[id=${this.id}, name=${this.name}, type=${this.type}, inventoryCheckURL=${this.inventoryCheckURL}]`}}class re{constructor(e,t){this.decision=e,this.url=t}}const se="NAVIGATION_ALLOWED",ne="NAVIGATION_BLOCKED",oe="OPEN_OUTSIDE_APPLICATION",ae="NAVIGATION_REDIRECTED",le="NATIVE_APP_STORE",de="fullscreen",ce="overlay",he="outside_app";class ue{constructor(e,t,i=globalThis.HYPRNetworkController){this.configuration=e,this.networkRequestController=i,this.adnetworkSignatureEndpoint=t,this.presentationModes=ue.getSupportedPresentationModes(e)}async presentStore(e,t,i,r,s=void 0){console.debug(`presentStore: AD MODEL: ${r}`);const n=this.getStorePresentationMode(r,s),o=new k(!0,O,I,{"Content-Type":N,Accept:x}),a=t.appStoreSignatureParams(e,r);console.debug(`fetchAttributionSignature: Request with paramters ${JSON.stringify(a)}`);const{response:l,body:d,error:c}=await this.networkRequestController.postAsync(this.adnetworkSignatureEndpoint,a,o);let h;console.debug(`SKAdNetwork Signature response received with status: ${l} error: ${c}`),204!==l&&j.validResponseStatus(l)?(h=JSON.stringify({presentationMode:n,...Q.parseJSON(d)}),console.debug(`Store Presentation with updated parameters: ${h}`)):(h=JSON.stringify({presentationMode:n,...e}),console.debug(`Store Presentation with original parameters: ${h}`)),i.showAppStore(n,h)}getStorePresentationMode(e,t=void 0){let i=t;return console.debug(`StorePresentationController: initial mode is: ${i}`),void 0===i&&(i=this.presentationModeForPlacement(e)),console.debug(`StorePresentationController: preferred store presentation mode: ${i}`),this.presentationModes.indexOf(i)>-1?(console.debug(`StorePresentationController: using preferred store presentation mode: ${i}`),i):(console.debug(`StorePresentationController: fallback to store presentation mode: ${this.presentationModes[0]}`),this.presentationModes[0])}presentationModeForPlacement(e){return e?.shouldAppStoreUseOverlay()?ce:de}static getSupportedPresentationModes(e){const t=[];return e.isIos()&&e.isPortraitSupported()&&t.push(de),e.isIos()&&e.version>=14&&t.push(ce),t.push(he),console.debug(`StorePresentationController: available presentation modes: ${JSON.stringify(t)}`),t}}const pe=["itunes.apple.com","apps.apple.com"],ge=["play.google.com","market://details"];class me{constructor(e,t,i,r,s=globalThis.HYPRNetworkController,n=globalThis.initializationController.deviceConfiguration){this.initialUrl=e,this.placement=t,this.baseAdInstanceId=i,this.ad=r,this.networkRequestController=s,this.resolvedStoreUrl=void 0,this.complete=!1,this.networkRequest=void 0,this.configuration=n}customUserAgent(){return`HyprMX/${this.configuration.sdkVersion}`}shouldPresentNativeBrowser(){return void 0!==this.ad&&this.ad.openClickInNativeBrowser()}navigationDecisionBasedOnProtocol(){return this.configuration.isAndroid()&&this.isPlayStoreUrl()?new re(oe,this.resolvedStoreUrl):this.configuration.isIos()&&this.resolveAppStoreUrl()?new re(le,this.resolvedStoreUrl):y.indexOf(this.initialUrl.split(":").shift())>=0?new re(se,this.initialUrl):new re(oe,this.initialUrl)}async evaluateUrl(){const e=new k(!0,O,I,{"User-Agent":this.customUserAgent()});this.networkRequest=await this.networkRequestController.getAsync(this.initialUrl,e),this.networkRequest?.response>=200&&this.networkRequest?.response<400&&void 0!==this.networkRequest?.headers?.Location&&(console.debug(`will re-resolve url: ${this.networkRequest?.headers?.Location}`),this.resolveAppStoreUrl(this.networkRequest?.headers?.Location))}isStoreClickMode(){return"store"===this.ad?.getClickMode()}isPlayStoreUrl(e=this.initialUrl){return this.compareUrlWithPattern(e,ge)}resolveAppStoreUrl(e=this.initialUrl){return this.compareUrlWithPattern(e,pe)}compareUrlWithPattern(e,t){return t.filter((t=>e.includes(t))).length>=1&&(this.resolvedStoreUrl=e,this.complete=!0),this.complete}getAppStoreOverlayParams(){let e;if(this.complete||this.resolveAppStoreUrl(this.initialUrl),void 0===this.resolvedStoreUrl)return e;const t=me.getUrlParams(this.resolvedStoreUrl),i=me.getAppStoreId(this.resolvedStoreUrl.split("?").shift(),t);return void 0===i?e:{url:this.resolvedStoreUrl,SKStoreProductParameterITunesItemIdentifier:i,SKStoreProductParameterAffiliateToken:t.at,SKStoreProductParameterCampaignToken:t.ct,SKStoreProductParameterProviderToken:t.pt}}static getAppStoreId(e,t){if(void 0!==t.id)return parseInt(t.id,10);let i=e.split("/").slice(-1).shift();0===i.indexOf("id")&&(i=i.split("id").slice(-1).shift());const r=parseInt(i,10);return Number.isNaN(r)?void 0:r}static getUrlParams(e){const t={},i=e.indexOf("?");if(-1===i||i+1===e.length)return t;return e.slice(e.indexOf("?")+1).split("&").map((e=>{const[i,r]=e.split("=");t[i]=decodeURIComponent(r)})),t}}globalThis.UrlResolver=me;class fe{constructor(e,t=!0,i=!0,r="",s="ffffff",n="ff",o=!1,a=!0,l=!0){this.isWebViewScrollable=e,this.isWebViewScrollBounceEnabled=t,this.allowsPinchGesture=i,this.alpha=n,this.backgroundColor=s,this.customUserAgent=r,this.headScripts=['document.onreadystatechange = function () {if(typeof window.webkit === "object" && typeof window.webkit.messageHandlers === "object"){window.webkit.messageHandlers["setReadyState"].postMessage(document.readyState); document.onreadystatechange = null;}}'],l&&this.headScripts.push('!function(){let e=document.querySelector("meta[name=viewport]");e||((e=document.createElement("meta")).name="viewport",document.getElementsByTagName("head")[0].appendChild(e)),e.setAttribute("content",e.getAttribute("content")+", initial-scale=1.0"),setTimeout(function(){window.scrollTo(0,0)},100)}();'),this.allowsLinkPreview=a,this.playbackRequiresUserAction=o,this.allowsInlineMediaPlayback=!0,this.allowsAirPlayForMediaPlayback=!0,this.allowsPictureInPictureMediaPlayback=!1,this.isNavigationLimited=!1,this.javaScriptEnabled=!0,this.domStorageEnabled=!0,this.loadWithOverviewMode=!0,this.useWideViewPort=!0,this.displayZoomControls=!1,this.builtInZoomControls=!0,this.supportsMultipleWindows=!0}setWebViewNavigationLimited(e){if("base_ad"===e&&"object"==typeof hyprAppDomainController&&hyprAppDomainController.isAppBoundDomainEnabled()){const e=hyprAppDomainController.getAppBoundDomains();this.isNavigationLimited=e.indexOf("hyprmx.com")>-1}else this.isNavigationLimited=!1}}const be="inline",_e="interstitial";class ve{constructor(e,t){this.viewModel=t,this.data=e,this.scripts=[]}generateEvent(e){return this.scripts.push(this[e](this.data)),this}generateBootstrapScripts(){return this.scripts.push(this.setPlacementType(this.data)),this.scripts.push(we),this.scripts.push(this.fireStateChangeEvent("loading")),this.scripts.push(this.setSupports()),this.scripts.push(this.setMaxSize()),this.scripts.push(this.setScreenSize()),this.scripts.push(this.setDefaultPosition(this.data)),this.scripts.push(this.setCurrentPosition(this.data)),this.scripts.push(this.fireStateChangeEvent("default")),void 0!==this.data.containerWidth&&void 0!==this.data.containerHeight&&this.scripts.push(this.fireSizeChangeEvent(this.data)),this}generateAdReadyScripts(){return this.scripts.push(this.fireViewableChangeEvent(this.data)),this.scripts.push(this.fireReadyEvent()),this}fire(){this.scripts.forEach((e=>this.viewModel.publishASyncScriptEvent(e)))}setPlacementType(e){return"string"==typeof e.type?this.mraidEventScript("setPlacementType",`'${e.type}'`):""}fireSizeChangeEvent(e){if(void 0!==e.containerWidth&&void 0!==e.containerHeight){const t=JSON.stringify({width:e.containerWidth,height:e.containerHeight});return this.mraidEventScript("fireSizeChangeEvent",t)}return""}setCurrentPosition(e){if(void 0!==e.containerWidth&&void 0!==e.containerHeight){const t=JSON.stringify({x:0,y:0,width:e.containerWidth,height:e.containerHeight});return this.mraidEventScript("setCurrentPosition",t)}return""}setDefaultPosition(e){if(void 0!==e.containerWidth&&void 0!==e.containerHeight){const t=JSON.stringify({x:0,y:0,width:e.containerWidth,height:e.containerHeight});return this.mraidEventScript("setDefaultPosition",t)}return""}fireStateChangeEvent(e){return void 0!==e?this.mraidEventScript("fireStateChangeEvent",`'${e}'`):""}fireViewableChangeEvent(e){return void 0!==e&&void 0!==e.viewable?this.mraidEventScript("fireViewableChangeEvent",`${e.viewable}`):""}setMaxSize(){if("object"!=typeof this.data||void 0===this.data.containerWidth||void 0===this.data.containerHeight)return"";const e=JSON.stringify({width:this.data.containerWidth,height:this.data.containerHeight});return this.mraidEventScript("setMaxSize",e)}setScreenSize(){const e=JSON.parse(HYPRRequestParamListener.getScreenSize()||"{}");if(void 0===e||void 0!==e.width&&void 0!==e.height){const t=JSON.stringify({width:e.width,height:e.height});return this.mraidEventScript("setScreenSize",t)}return""}setSupports(){const e=HYPRRequestParamListener.getMraidSupportsString();return void 0!==e?this.mraidEventScript("setSupports",e):""}fireReadyEvent(){return this.mraidEventScript("fireReadyEvent","{}")}mraidEventScript(e,t){if(void 0!==e)return`window.dispatchEvent(new CustomEvent("${e}", {detail: ${t}}));`}}const we="var hyprmx_celtra_mraid_check = function(time) {if (typeof celtra !== 'undefined') { var frames = document.querySelectorAll(\"iframe\");for (let i=0; i < frames.length; i++){ frames[i].contentWindow.mraid = window.mraid; }} else {setTimeout(function() {hyprmx_celtra_mraid_check(time+1)}, time + 500);}};hyprmx_celtra_mraid_check(500);";class ye{static bootstrapEvent(e,t){new ve(e,t).generateBootstrapScripts().fire()}static adReadyEvent(e,t){new ve(e,t).generateAdReadyScripts().fire()}static containerEvents(e,t,i){const r=new ve(t,i);for(const t of e)r.generateEvent(t);r.fire()}}function Pe(e=globalThis.HYPRNativeAudioListener){if(void 0===e)return{};return{outputVolume:e.getVolume(),isMuted:e.getIsMuted(),audioCategory:e.getAudioCategory()}}class Ae{constructor(){this.eventName=void 0}execute(e,t){void 0!==e?"function"==typeof t.publishCustomEvent&&t.publishCustomEvent(this.eventName,e,!0):console.error(`[WE] ${this.eventName} received with no value.`)}}class Se extends Ae{constructor(){super(),this.eventName="hyprmx_volume_change"}}class Te extends Ae{constructor(){super(),this.eventName="hyprmx_mute_change"}}class Ce extends Ae{constructor(){super(),this.eventName="hyprmx_audio_category_change"}}let Ee;class Re{constructor(e){this._playerAdDisplayEndpoint=e.player_ad_display_endpoint??"https://tst-mit-player-ad-display-js.hyprmx.technology"}get playerAdDisplayIndex(){return`${this._playerAdDisplayEndpoint}/index.html`}}const ke={EVENT_NAME:"hyprAdPresentationChanged",EVENT_SHARING_CLOSED:"sharing_closed",EVENT_SHARING_PRESENTED:"sharing_presented",EVENT_AD_CLOSED:"ad_closed",EVENT_APP_PAUSED:"app_paused",EVENT_APP_RESUMED:"app_resumed",EVENT_NAVIGATION_BLOCKED:"hyprWebViewNavigationBlocked",EVENT_VIEWABLE:"viewable",EVENT_NOT_VIEWABLE:"not_viewable"},xe="notInitialized",Ne="initialized",Oe="ready";class Ie{constructor(e){this.currentIndex=e.currentIndex,this.currentUrl=e.currentUrl,this.currentHost=e.currentHost,this.currentTitle=e.currentTitle,this.history=e.history,this.canNavigateForward=e.canNavigateForward,this.canNavigateBack=e.canNavigateBack}}const Me={webViewSizeData:{},adBaseUrl:Ee?.playerAdDisplayIndex,adBaseEncoding:A,adBaseMime:P,mraidState:xe,currentInstanceId(){return"string"==typeof this.preloadInstanceId?this.preloadInstanceId:this.instanceId},getWebViewConfigurationString(e){return"object"==typeof e&&"string"==typeof e.mode&&this.getWebViewConfiguration().setWebViewNavigationLimited(e.mode),JSON.stringify(this.getWebViewConfiguration())},windowOpenAttempt(e={}){const{url:t}=e;if("string"!=typeof t||void 0===this.placement)return void console.error("[WVVM] Window.open attempt could not parse url");if(console.debug(`[WVVM] Window.open attempt for ${t.split("?").shift()}`),!this.shouldAllowWindowOpenEvent(e))return;const i=new me(t,this.placement,this.instanceId,this.getAdModel());if(i.isStoreClickMode())return void this.handlePotentialStoreClick(i);const r=i.navigationDecisionBasedOnProtocol();switch(r.decision){case oe:return console.debug("[WVVM] Window.open will open outside app"),void this.openOutsideApplication(r.url);case le:return console.debug("[WVVM] Window.open found store url without resolving"),void this.startStorePresentation(i);default:return console.debug("[WVVM] Window.open will show browser"),this.showBrowser(i)}},shouldTakeFocus(){return!this.placement?.isBannerAdType()||this.onSharePressed},shouldKeepFocus(){return!this.placement?.isBannerAdType()||this.onSharePressed},webviewDestroyScript:()=>'window.addEventListener("pagehide", (e) => {window.webkit.messageHandlers.hypr_pagehide_complete.postMessage("hypr_pagehide_complete");});setTimeout(() => {window.webkit.messageHandlers.hypr_pagehide_complete.postMessage("hypr_pagehide_complete");}, 2000);',shouldLoadAboutBlank:()=>!0,shouldRedirectURL(e){const t="object"==typeof e?e.url:void 0,i="object"==typeof e?e.mimeType:void 0;return"text/html"===i?JSON.stringify(new re(se,e.url)):"string"!=typeof t||void 0===this.placement?JSON.stringify(new re(ne,t)):"application/pdf"===i||t.endsWith(".pdf")||"application/msword"===i||t.endsWith(".doc")||"application/vnd.openxmlformats-officedocument.wordprocessingml.document"===i||t.endsWith("docx")||"application/vnd.ms-excel"===i||t.endsWith("xls")||"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"===i||t.endsWith("xlsx")||"application/vnd.ms-powerpoint"===i||t.endsWith("ppt")||"application/vnd.openxmlformats-officedocument.presentationml.presentation"===i||t.endsWith(".pptx")?JSON.stringify(new re(ae,`https://docs.google.com/viewer?url=${t}&embedded=true`)):JSON.stringify(new re(se,e.url))},shouldInterceptRequest(e){"http"===e.scheme&&"function"==typeof this.webViewPresenter.removeJavascriptInterface&&(console.debug(`[WVVM] Insecure data loaded:  ${e.url} Removing Javascript Interface.`),this.webViewPresenter.removeJavascriptInterface())},getWebViewContainerData(){const e={};return e.containerWidth=Math.floor(this.webViewSizeData.width),e.containerHeight=Math.floor(this.webViewSizeData.height),e.viewable=this.isViewable(),e.type=this.placement?.isBannerAdType()?be:_e,e},onPageFinished(e){this.loadInProgress=!1,this.handlePageFinishFromWebView(e.url),this.publishVolumeData(),this.shouldBootstrapMraid()&&(console.debug(`[WVVM] [mraid loading] Starting Bootstrap Load for instance: ${this.currentInstanceId()}`),ye.bootstrapEvent(this.getWebViewContainerData(),this),this.mraidState=Ne,this.isViewable()&&this.fireMraidReady(),console.debug(`[WVVM] [mraid loading] onPageFinished() MRAID State is: ${this.mraidState}`))},onJSMessage(e){this.handleJSMessageFromWebView(e)},onLoadData(){this.onWebViewLoadData()},onPageStarted(e){this.mraidState=xe,this.loadInProgress=!0,this.webViewPresenter.updateWebViewConfiguration(JSON.stringify(this.getWebViewConfiguration())),this.handlePageStartedFromWebView(e.url)},onReceivedError(e){this.handleErrorFromWebView(e)},onWebViewCrash(){return this.onWebViewTermination()},fireMraidReady(){console.debug(`[WVVM] [mraid loading] Firing MRAID AdReady to instance: ${this.currentInstanceId()}`),ye.adReadyEvent(this.getWebViewContainerData(),this),this.mraidState=Oe},webViewSizeChange(e){this.webViewSizeData=e,ye.containerEvents(["setMaxSize","setDefaultPosition","setCurrentPosition","fireSizeChangeEvent"],this.getWebViewContainerData(),this),console.debug(`[WVVM] [mraid loading] webViewSizeChange() MRAID State is: ${this.mraidState}`)},reportContainerSizeChange(){ye.containerEvents(["setScreenSize","fireSizeChangeEvent"],this.getWebViewContainerData(),this),console.debug(`[WVVM] [mraid loading] reportContainerSizeChange() MRAID State is: ${this.mraidState}`)},publishVolumeData(){const{outputVolume:e,isMuted:t}=Pe();(new Se).execute(e,this),(new Te).execute(t,this)},reportVisibilityChanged(){this.isViewable()&&this.mraidState!==Oe&&this.fireMraidReady(),ye.containerEvents(["fireViewableChangeEvent"],this.getWebViewContainerData(),this);const e=this.isViewable()?ke.EVENT_SHARING_CLOSED:ke.EVENT_SHARING_PRESENTED;this.publishCustomEvent(ke.EVENT_NAME,e),console.debug(`[WVVM] [mraid loading] reportVisibilityChanged() MRAID State is: ${this.mraidState}`)},reportLifecycleChanged(e){const t=e?ke.EVENT_APP_PAUSED:ke.EVENT_APP_RESUMED;this.publishCustomEvent(ke.EVENT_NAME,t),this.publishJSExecutionState(e)},adWillClose(){this.publishCustomEvent(ke.EVENT_NAME,ke.EVENT_AD_CLOSED)},setCurrentUrl(e,t=void 0){void 0!==t&&(this.userAgent=t),this.adHtml=void 0,this.queryParams=void 0,this.url=e},setCurrentData(e,t=Ee?.playerAdDisplayIndex){console.debug(`[WVVM] Setting current data to: ${e}`),void 0!==t&&t!==this.adBaseUrl&&(this.adBaseUrl=t),this.url=void 0;const i=this.adBaseMime,r=this.adBaseEncoding;this.adHtml=JSON.stringify({data:e,baseUrl:t,mimeType:i,encoding:r})},openFileChooser(e){return e.acceptTypes.indexOf("image/*")<0?(console.error(`[WVVM] Unsupported acceptTypes ${JSON.stringify(e.acceptTypes)}`),!1):this.captureImage()},onHistoryChanged(e){console.debug(`[WVVM] ${JSON.stringify(e)}`),this.webViewHistory=new Ie(e),this.onWebViewHistoryChanged(this.webViewHistory)},navigateBack(){this.webViewPresenter.navigateBack()},navigateForward(){this.webViewPresenter.navigateForward()},notifyWindowOpenURLBlock(e){this.publishCustomEvent(ke.EVENT_NAVIGATION_BLOCKED,e)},publishASyncScriptEvent(e){this.webViewPresenter?.runScript(e)},publishJSExecutionState(e){"function"==typeof this.webViewPresenter.pauseJSExecution&&"function"==typeof this.webViewPresenter.resumeJSExecution&&(e?this.webViewPresenter.pauseJSExecution():this.webViewPresenter.resumeJSExecution())},publishCustomEvent(e,t,i=!1){const r=`window.dispatchEvent(new CustomEvent('${e}', { detail: '${t}'}));${i?`\nwindow.${e} = ${t};`:""}`;this.publishASyncScriptEvent(r)},setWebViewPresenter(e){this.webViewPresenter=e,this.webViewPresenterSubscriptions.forEach((e=>{try{this[e]=this[e]}catch(t){console.error(`[WVVM] Error setting binded property ${e} on webview presenter`)}}))},imageCaptured(e){this.webViewPresenter.imageCaptured(e.url)},denyPermissionRequest(e){console.debug(`[WVVM] Unsupported permission request: ${JSON.stringify(e)}`);const t=[];e.permissions?.forEach((e=>{t.push({permission:e,granted:!1})}));const i=JSON.stringify({permissions:t,permissionId:e.permissionId});this.webViewPresenter.permissionResponse(i)},permissionResponse(e){console.debug(`[WVVM] permissionResponse: ${JSON.stringify(e)}`);for(let t=0;t<e.permissions.length;t+=1)"android.permission.CAMERA"===e.permissions[t].permission?e.permissions[t].permission="android.webkit.resource.VIDEO_CAPTURE":"android.permission.RECORD_AUDIO"===e.permissions[t].permission&&(e.permissions[t].permission="android.webkit.resource.AUDIO_CAPTURE");this.webViewPresenter.permissionResponse(JSON.stringify(e))}},Ve=["start","description","end","id","location","summary","status","transparent","freebusy","reminder","recurrence"];class De{constructor(e,t="https://0606delr85.execute-api.us-east-1.amazonaws.com/default/calendar_proxy"){const i=JSON.parse(e);i.recurrence=this.processRecurrenceData(i.recurrence),this.calendarEventParams=i,this.proxyUrl=t}processRecurrenceData(e){try{const t=[];switch(e?.frequency){case"daily":t.push("FREQ=DAILY");break;case"weekly":t.push("FREQ=WEEKLY");break;case"monthly":t.push("FREQ=MONTHLY");break;case"yearly":t.push("FREQ=YEARLY");break;default:return void console.error("processRecurrenceData: No parsable recurrence rule without frequency")}if(void 0!==e.daysInWeek){const i=[];for(const t of e.daysInWeek)switch(t){case 1:i.push("MO");break;case 2:i.push("TU");break;case 3:i.push("WE");break;case 4:i.push("TH");break;case 5:i.push("FR");break;case 6:i.push("SA");break;case 7:i.push("SU");break;default:console.error(`processRecurrenceData: Invalid weekday data: ${t}`)}t.push(`BYDAY=${i.join(",")}`)}return void 0!==e.daysInMonth&&t.push(`BYMONTHDAY=${e.daysInMonth.join(",")}`),void 0!==e.monthsInYear&&t.push(`BYMONTH=${e.monthsInYear.join(",")}`),void 0!==e.daysInYear&&t.push(`BYYEARDAY=${e.daysInYear.join(",")}`),void 0!==e.expires&&t.push(`UNTIL=${e.expires}`),t.join(";")}catch(e){return void console.error(`Error parsing recurrence rule: ${e}`)}}webcalUrl(){return`webcal://${this.proxyUrl.split("://").pop()}`}buildProxyQuery(){const e=[];for(const t of Ve)void 0!==this.calendarEventParams[t]&&e.push(`${t}=${this.calendarEventParams[t]}`);return`?${e.join("&")}`}toProxyRequest(){const e=encodeURI(`${this.webcalUrl()}${this.buildProxyQuery()}`);return console.debug(`toProxyRequest() generated final url ${e}`),e}}const Fe=["onStop","UISceneDidEnterBackgroundNotification","UIApplicationDidEnterBackgroundNotification"],Be=["onResume","UISceneWillEnterForegroundNotification","UIApplicationWillEnterForegroundNotification"];class Ue{constructor(e,t,i="",r=HYPRRequestParamListener,s=globalThis.HYPRNetworkController,n=globalThis.HYPRStorePresentationController,o=globalThis.initializationController.deviceConfiguration,a=globalThis.HYPREventController,l=HYPRRequestParameterManager,d=globalThis.HYPRTimerController){this.placement=e,this.listener=t,this.paramRequestListener=r,this.networkRequestController=s,this.storePresentationController=n,this.configuration=o,this.presentationListener=void 0,this.eventController=a,this.requestParameterManager=l,this.instanceId=i,this.preloadInstanceId=void 0,this.lastTransition="",this.hasParentView=!1,this.currentSizeData=void 0,this.visible=!1,this.hasBecomeVisible=!1,this.timerController=d,this.backgrounded=!1,this.browserInstance=void 0,this.webViewHistory=void 0,this.currentSizeData={},this.loadInProgress=!1,this.WebViewConfiguration=void 0,this._url=void 0,this._adHtml=void 0,this.presenterSubscriptions=[],this.webViewPresenterSubscriptions=["url","adHtml"]}get url(){return this._url}set url(e){this._url=e,void 0!==e&&this.webViewPresenter?.setUrl(e)}get adHtml(){return this._adHtml}set adHtml(e){this._adHtml=e,void 0!==e&&this.webViewPresenter?.setAdHtml(e)}destroy(){console.debug(`[BAVM] destroy for ${this.instanceId}`),this.pageReadyController?.stopPageReadyTimer()}onBackButtonPressed(){}getWebViewConfiguration(){const e=this.getAdModel()?.user_agent||"";return void 0===this.webViewConfiguration&&(this.webViewConfiguration=new fe(this.scrollingEnabled(),this.shouldScrollBounce(),this.isPinchEnabled(),e,this.webViewBackground(),this.webViewAlpha(),this.playbackRequiresUserAction(),this.allowsWebViewLinkPreview(),this.shouldScaleViewport())),this.webViewConfiguration}onLifecycleEvent(e){console.debug(`[BAVM] onLifecycleEvent: ${e.event} received.`),this.handleLifecycleTransition(e.event)}onWebViewLoadData(){}onParentViewChangeEvent(e){console.debug(`[BAVM] onParentViewChangeEvent: View has superview: ${e.parentView}.`),this.hasParentView=e.parentView}containerSizeChange(e){console.debug(`[BAVM] Received new container size: ${JSON.stringify(e)}`),this.currentSizeData=e,"function"==typeof this.reportContainerSizeChange&&this.reportContainerSizeChange(e)}containerVisibleChange(e){if(!0===e.visible&&(this.hasBecomeVisible=!0),console.debug(`[BAVM] Received new visible state: ${e.visible}`),this.visible!==e.visible&&(this.visible=e.visible,"function"==typeof this.reportVisibilityChanged))try{this.reportVisibilityChanged()}catch(e){console.debug(`[BAVM] Error reporting visibility change: ${JSON.stringify(e)}`)}}handleLifecycleTransition(e){this.isBackgroundTransition(e)?(this.onBackgroundTransition(),this.lastTransition=e):this.isForegroundTransition(e)&&(this.onForegroundTransition(),this.lastTransition=e),this.reportVisibilityChanged()}onForegroundTransition(){this.backgrounded=!1,"function"==typeof this.reportLifecycleChanged&&this.reportLifecycleChanged(!1)}onBackgroundTransition(){this.backgrounded=!0,"function"==typeof this.reportLifecycleChanged&&this.reportLifecycleChanged(!0)}isBackgroundTransition(e){return Fe.includes(e)&&!Fe.includes(this.lastTransition)}isForegroundTransition(e){return Be.includes(e)&&!Be.includes(this.lastTransition)}isValid(){return void 0!==this.placement}getPlacementName(){return this.placement?.name||""}getPlacementType(){return this.placement?.type||""}shouldBootstrapMraid(){return!1}shouldScrollBounce(){return!0}isPinchEnabled(){return!0}scrollingEnabled(){return!0}allowsWebViewLinkPreview(){return!0}playbackRequiresUserAction(){return!1}webViewBackground(){return"ffffff"}webViewAlpha(){return"ff"}shouldScaleViewport(){return!1}handleJSMessageFromWebView(e){const{name:t,body:i}=e;switch(t){case"open":case"openInSafariVC":this.windowOpenAttempt({url:i});break;case"createCalendarEvent":this.createCalendarEvent(i);break;case"storePicture":this.storePicture(i);break;case"presentStoreView":this.launchStore({url:JSON.parse(i).url},JSON.parse(i).mode);break;case"setOrientationProperties":{const{allowOrientationChange:e,forceOrientation:t}=JSON.parse(i);this.presenter.setOrientationProperties("true"===e,t);break}case"useCustomClose":this.presenter.useCustomClose("true"===i);break;case"setTrampoline":this.setTrampoline(JSON.parse(i));break;case"setNativeAdConfiguration":this.setNativeAdConfiguration(JSON.parse(i));break;default:"function"==typeof this[t]?(console.debug(`[BAVM] applying JS Event: ${t} with params ${i}`),this[t].apply(this,[i])):console.error(`[BAVM] Unsupported JS Event: ${t} with params ${i}`)}}setNativeAdConfiguration(e){}setTrampoline(e){}handleErrorFromWebView(e){}handlePageFinishFromWebView(e){}handlePageStartedFromWebView(e){}onWebViewHistoryChanged(e){}javaScriptAlertAttempt(e){return!0}permissionRequest(e){if(console.debug(`[BAVM] Permission Request for ${JSON.stringify(e)}`),null!=e.permissions){for(let t=0;t<e.permissions.length;t+=1)"android.webkit.resource.VIDEO_CAPTURE"===e.permissions[t]?e.permissions[t]="android.permission.CAMERA":"android.webkit.resource.AUDIO_CAPTURE"===e.permissions[t]&&(e.permissions[t]="android.permission.RECORD_AUDIO");"function"==typeof this.presenter?.permissionRequest&&this.presenter.permissionRequest(JSON.stringify(e.permissions),e.permissionId)}else this.denyPermissionRequest(e)}shouldAllowWindowOpenEvent(e){return!0}nativeBrowserWillPresent(e){}async handlePotentialStoreClick(e){if(console.debug("[BAVM] Window.open evaluating url for resolution"),e.isPlayStoreUrl()&&(console.debug("[BAVM] Window.open will open outside app"),this.openOutsideApplication(e.resolvedStoreUrl)),await e.evaluateUrl(),console.debug("[BAVM] Window.open url evaluation complete"),e.complete)return console.debug("[BAVM] Window.open resolved to good store url"),void this.startStorePresentation(e);console.debug("[BAVM] Window.open failed resolution"),this.showBrowser(e)}showAppStore(e,t){console.debug(`[BAVM] Will show AppStore with mode: ${e}`),e!==he?(this.presenter.showAppStore(t),this.storePresentationPromise=void 0):this.openOutsideApplication(JSON.parse(t).url)}showBrowser(e){if(this.nativeBrowserWillPresent(e),e.shouldPresentNativeBrowser())return void this.presenter.showPlatformBrowser(e.initialUrl);const t=this.listener.initializeBrowserViewModel(this.instanceId,e,this,this.placement);return this.presenter.showHyprMXBrowser(t),t}onModalClosed(){this.browserInstance=void 0}urlNavigationAttempt(e){const t="object"==typeof e?e.url:void 0;if("string"!=typeof t||void 0===this.placement)return JSON.stringify(new re(ne,t));console.debug(`[BAVM] urlNavigationAttempt() Navigation Attempt check for ${t.split("?").shift()};\n\n    request is main frame: ${e.isMainFrame}`);const i=new me(t),r=i.navigationDecisionBasedOnProtocol();return r.decision===oe?(console.debug(`[BAVM] urlNavigationAttempt() will open outside App: ${t.split("?").shift()}`),this.openOutsideApplication(r.url),JSON.stringify(new re(ne,t))):r.decision===le?(console.debug(`[BAVM] urlNavigationAttempt() will open native App Store: ${t.split("?").shift()}`),this.startStorePresentation(i),JSON.stringify(new re(ne,t))):(console.debug(`[BAVM] urlNavigationAttempt() navigation result: ${r.decision}`),JSON.stringify(r))}startStorePresentation(e,t=void 0){console.debug("[BAVM] startStorePresentation() starting store presentation");const i=e.getAppStoreOverlayParams();return"object"==typeof i?(console.debug("[BAVM] startStorePresentation() launching app store"),this.launchStore(i,t),!0):(console.debug("[BAVM] startStorePresentation() failed to parse params"),!1)}launchStore(e,t=void 0){void 0===this.storePresentationPromise?this.storePresentationPromise=this.storePresentationController.presentStore(e,this.placement,this,this.getAdModel(),t):console.debug("[BAVM] Debounced app store presentation events")}openOutsideApplication(e){this.presenter.openOutsideApplication(e)}createCalendarEvent(e){if(this.configuration.isIos()){console.debug("[BAVM] createCalendarEvent on iOS Device - building proxy request");const t=new De(e);this.openOutsideApplication(t.toProxyRequest())}else console.debug("[BAVM] createCalendarEvent on Android Device - sending data for native Intent"),this.presenter.createCalendarEvent(e)}storePicture(e){this.presenter.storePicture(e)}captureImage(){return this.presenter.captureImage(),!0}generateTimeOnSiteTracking(){}onWebViewTermination(){return!0}onClose(){}clearPreloadId(){this.preloadInstanceId=void 0}getAdModel(){return this.ad}setPresenter(e){console.debug("[BAVM] Setting Presenter"),this.presenter=e,this.presenterSubscriptions.forEach((e=>{console.debug(`[BAVM] rebinding: ${e}`);try{this[e]=this[e]}catch(t){console.error(`[BAVM] Error setting binded property ${e} on presenter`)}}))}isViewable(){return this.visible&&!this.backgrounded}}Object.assign(Ue.prototype,Me);const $e="hyprmx.com/adinfo.html";class Le extends Ue{constructor(e,t="webview",i="",r=void 0,s=void 0,...n){super(...n),this.baseViewModel=r,this.clickMode=t,this.onPageJS=i,this.timeOnSiteTracker=void 0,this.isPresenting=!1,"store"!==this.clickMode&&(this.timeOnSiteTracker=s),this._backButtonEnabled=!1,this._forwardButtonEnabled=!1,this._header="",this.presenterSubscriptions.push("backButtonEnabled"),this.presenterSubscriptions.push("forwardButtonEnabled"),this.presenterSubscriptions.push("header"),this.setCurrentUrl(e)}get forwardButtonEnabled(){return this._forwardButtonEnabled}set forwardButtonEnabled(e){this._forwardButtonEnabled=e,this.presenter?.setForwardButtonEnabled(e)}get backButtonEnabled(){return this._backButtonEnabled}set backButtonEnabled(e){this._backButtonEnabled=e,this.presenter?.setBackButtonEnabled(e)}get header(){return this._header}set header(e){this._header=e,this.presenter?.setHeader(e)}onBrowserReady(){console.debug("onBrowserReady"),this.timeOnSiteTracker?.startOnPageTimer(!1)}adInfoPattern(){return void 0!==globalThis.initializationController&&globalThis.initializationController.getConfiguration().ad_info_url||$e}onNavigateBackPressed(){this.navigateBack()}onNavigateForwardPressed(){this.navigateForward()}onSharePressed(){const e=this.webViewHistory.currentUrl;this.presenter.openShareSheet(JSON.stringify({data:e}))}onClose(){this.timeOnSiteTracker?.onBrowserClosed(),this.baseViewModel.onModalClosed()}onBackButtonPressed(){this.webViewHistory.canNavigateBack?this.navigateBack():this.triggerClose()}hasOnPageJS(){return this.onPageJS.length>0}showBrowser(e){this.setCurrentUrl(e.initialUrl)}triggerClose(){this.presenter.closeBrowser()}openOutsideApplication(e){super.openOutsideApplication(e),this.triggerClose()}handlePageStartedFromWebView(e){this.hasOnPageJS()&&void 0===this.onPageResolve&&new Promise((e=>{this.onPageResolve=e,console.debug("[BrowserOnPageJS] promise stored"),this.onPageJsTimeout=this.timerController.startTimer(8e3,(()=>{console.debug("[BrowserOnPageJS] timeout reached"),this.onPageJsTimeout=void 0,this.resolveOnPageJS()}))})).then((()=>{console.debug(`[BrowserOnPageJS] Firing onPageJS on ClickThrough: ${this.url}, for Placement: ${this.placement}`),this.publishASyncScriptEvent(this.onPageJS)})),this.presenter.webViewLoadStarted(e)}handlePageFinishFromWebView(e){console.debug(`handlePageFinishFromWebView: ${e}`),this.resolveOnPageJS(),super.handlePageFinishFromWebView(e)}resolveOnPageJS(){"function"==typeof this.onPageResolve&&(console.debug("[BrowserOnPageJS] will resolve onPageJS Promise"),this.onPageResolve()),this.onPageResolve=void 0,void 0!==this.onPageJsTimeout&&(this.timerController.stopTimer(this.onPageJsTimeout),console.debug("[BrowserOnPageJS] stopped timer")),this.onPageJsTimeout=void 0}onForegroundTransition(){super.onForegroundTransition(),this.timeOnSiteTracker?.onForegrounded()}onBackgroundTransition(){super.onBackgroundTransition(),this.timeOnSiteTracker?.onBackgrounded()}getAdModel(){return this.baseViewModel?.getAdModel()}shouldScaleViewport(){return!0}onWebViewHistoryChanged(e){this.backButtonEnabled!==e.canNavigateBack&&(this.backButtonEnabled=e.canNavigateBack),this.forwardButtonEnabled!==e.canNavigateForward&&(this.forwardButtonEnabled=e.canNavigateForward),this.header!==e.currentHost&&(this.header=e.currentHost)}}class We{constructor(e={},t=void 0){Object.assign(this,e),this.bidResponse=t,void 0===this.webview_release_in_seconds&&(this.webview_release_in_seconds=0),void 0===this.force_status_bar_display&&(this.force_status_bar_display=this.type===g&&"portrait"===this.allowed_orientation),void 0===this.allowed_orientation&&(this.allowed_orientation="all"),void 0===this.type&&(this.type=f),void 0===this.title&&(this.title=""),void 0===this.offer_initiation_timeout_in_seconds&&(this.offer_initiation_timeout_in_seconds=8),void 0===this.clickMode&&(this.clickMode="webview"),void 0===this.show_close_button&&(this.show_close_button=!1),void 0===this.user_agent&&(this.user_agent=null)}static fromAdsArray(e,t=[]){const i=t.shift();if("object"==typeof i&&Q.hasRequiredKeys(i,["id","type"],"string")&&Q.hasRequiredKeys(i,["offer_initiation_timeout_in_seconds"],"number")&&Q.hasRequiredKeys(i,["catalog_frame_url"],"url"))return e(i)}appendRequestData(e,t){return t?this.relay_promise_token&&Object.assign(e,{last_promise_token:this.getAdRewardToken()}):Object.assign(e,{reward_token:this.getAdRewardToken(),offer:this.getOfferName()}),e}getAdType(){return this.type||""}getAdIdentifier(){return this.id||""}hasAd(){return""!==this.getAdIdentifier()}getOfferName(){return`${this.getAdType()}-${this.getAdIdentifier()}`}getAdString(){return JSON.stringify(this)}getAdRewardText(){return this.reward_text||""}getAdRewardQuantity(){return this.reward_quantity||0}getAdRewardToken(){return this.reward_token||""}getDaaIconUrl(){return this.daa_url||""}isNavigationRuleMatch(e){return!("string"!=typeof e||"string"!=typeof this.navigation_rule||this.navigation_rule.length<1)&&null!==e.match(new RegExp(this.navigation_rule,"i"))}shouldAppStoreUseOverlay(){return!0===this.use_skoverlay}openClickInNativeBrowser(){return void 0!==this.click&&"native"===this.click.mode}getClickMode(){return this.click?.mode||"webview"}}function qe(e=globalThis.defaultUIComponents.user_info_form?.footer){return c({images:[],text:"          ",background_color:"F9F9F9",min_height:52},e)}function ze(e,t="Next",i="Close",r=globalThis.defaultUIComponents.webtraffic,s=globalThis.IS_IOS){const n=c(function(e,t,i,r){return r?{title_text:`<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd"><html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><meta http-equiv="Content-Style-Type" content="text/css"><title></title><style type="text/css">p.p1 {margin: 0.0px 0.0px 0.0px 0.0px; font: 10.0px 'Helvetica Neue'; color: #3a3a3a; text-align:center}span.s1 {font-family: 'Helvetica Neue'; font-weight: normal; font-style: normal; font-size: 10.00pt}</style></head><body><p class="p1"><span class="s1">${e}</span></p></body></html>`,next_button_text:`<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd"><html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><meta http-equiv="Content-Style-Type" content="text/css"><title></title><style type="text/css">p.p1 {margin: 0.0px 0.0px 0.0px 0.0px; font: 10.0px 'Helvetica Neue'; color: #ffffff; text-align:center}span.s1 {font-family: 'Helvetica Neue'; font-weight: normal; font-style: normal; font-size: 10.00pt}</style></head><body><p class="p1"><span class="s1">${t}</span></p></body></html>`,finish_button_text:`<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd"><html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><meta http-equiv="Content-Style-Type" content="text/css"><title></title><style type="text/css">p.p1 {margin: 0.0px 0.0px 0.0px 0.0px; font: 10.0px 'Helvetica Neue'; color: #ffffff; text-align:center}span.s1 {font-family: 'Helvetica Neue'; font-weight: normal; font-style: normal; font-size: 10.00pt}</style></head><body><p class="p1"><span class="s1">${i}</span></p></body></html>`,countdown_text:'<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd"><html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><meta http-equiv="Content-Style-Type" content="text/css"><title></title><style type="text/css">p.p1 {margin: 0.0px 0.0px 0.0px 0.0px; font: 14.0px \'Helvetica Neue\'; color: #5D5D5D; text-align:center; font-weight:bold}span.s1 {font-family: \'Helvetica Neue\'; font-weight: normal; font-style: normal; font-size: 14.00pt; font-weight:bold; line-height: 0; padding-bottom: 1px}</style></head><body><p class="p1"><span class="s1">%@</span></p></body></html>'}:{title_text:`<font color='#3a3a3a'>${e}</font>`,next_button_text:`<font color='#FFFFFF'>${t}</font>`,finish_button_text:`<font color='#FFFFFF'>${i}</font>`,countdown_text:"<font color='#5D5D5D'><b>%s</b></font>"}}(e??"",t,i,s),r);return c({header_background_color:"F9F9F9",finish_button_minimum_size:{width:50,height:20},next_button_minimum_size:{width:50,height:20},next_button_color:"28A362",finish_button_color:"28A362",page_indicator_color:"AAAAAA",page_indicator_color_selected:"28A362",minimum_header_height:44,close_button_color:"5D5D5D",chevron_color:"FFFFFF",close_button:{image:{height:20,width:20}}},n)}function He(e,t=void 0){console.debug(`[FSAF] FullScreenAdFactory raw JSON ${JSON.stringify(e)}`);const i={...{web_traffic_header:ze(e.title),footer:qe()},...e},r=new We(i,t);return console.debug(`[FSAF] FullScreenAdFactory Ad JSON ${JSON.stringify(r)}`),r}class Je{async loadAd(){return console.error("Abstract class for Ad Loading used"),{ad:void 0,required_information:void 0,ui_components:void 0}}cancel(){void 0!==this.canceller&&(console.debug(`[AL] Cancelling network event for: ${this}`),this.canceller?.cancel())}}class Ye extends Je{constructor(e,t){super(),this.placementId=e,this.inventoryCheckURL=t}async loadAd(e={},t=HYPRRequestParameterManager,i=globalThis.HYPRNetworkController){console.debug(`[IVL] Beginning inventory check for placement: ${this.placementId}`),e.placement_id=this.placementId;const r=t.getInventoryCheckParams(e);try{const{promise:e,canceller:t}=i.cancellableAsyncPost(this.inventoryCheckURL,r);this.canceller=t;const{code:s,body:n,error:o}=await e;return this.canceller=void 0,console.debug(`[IVL] Loading ads completed for placement: ${this.placementId}`),{...this.processResponse(s,n,o),error:o}}catch(e){return console.debug(`[IVL] Inventory check aborted for: ${this.placementId} with error: ${e}`),{error:e}}}processResponse(e,t,i){console.debug(`[IVL] Processing inventory check response for placement: ${this.placementId} with status code ${e} and with error: ${i}`);const r=Q.parseJSON(t);let s;j.validResponseStatus(e)&&void 0===i&&this.validResponse(r)&&this.responseHasAd(r)&&(s=We.fromAdsArray(He,r.ads_available));const{required_information:n,ui_components:o}={...r};return{ad:s,required_information:n,ui_components:o}}validResponse(e){return void 0!==e&&Q.hasRequiredKeys(e,["ads_available"],"object")}responseHasAd(e){return void 0!==e&&(void 0!==e.ads_available&&e.ads_available.length>0||void 0!==e.required_information&&e.required_information.length>0)}}var je={},Ke={};!function(e){const t=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",i="["+t+"]["+(t+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040")+"]*",r=new RegExp("^"+i+"$");e.isExist=function(e){return void 0!==e},e.isEmptyObject=function(e){return 0===Object.keys(e).length},e.merge=function(e,t,i){if(t){const r=Object.keys(t),s=r.length;for(let n=0;n<s;n++)e[r[n]]="strict"===i?[t[r[n]]]:t[r[n]]}},e.getValue=function(t){return e.isExist(t)?t:""},e.isName=function(e){const t=r.exec(e);return!(null==t)},e.getAllMatches=function(e,t){const i=[];let r=t.exec(e);for(;r;){const s=[];s.startIndex=t.lastIndex-r[0].length;const n=r.length;for(let e=0;e<n;e++)s.push(r[e]);i.push(s),r=t.exec(e)}return i},e.nameRegexp=i}(Ke);const Ze=Ke,Ge={allowBooleanAttributes:!1,unpairedTags:[]};function Xe(e){return" "===e||"\t"===e||"\n"===e||"\r"===e}function Qe(e,t){const i=t;for(;t<e.length;t++)if("?"!=e[t]&&" "!=e[t]);else{const r=e.substr(i,t-i);if(t>5&&"xml"===r)return at("InvalidXml","XML declaration allowed only at the start of the document.",dt(e,t));if("?"==e[t]&&">"==e[t+1]){t++;break}}return t}function et(e,t){if(e.length>t+5&&"-"===e[t+1]&&"-"===e[t+2]){for(t+=3;t<e.length;t++)if("-"===e[t]&&"-"===e[t+1]&&">"===e[t+2]){t+=2;break}}else if(e.length>t+8&&"D"===e[t+1]&&"O"===e[t+2]&&"C"===e[t+3]&&"T"===e[t+4]&&"Y"===e[t+5]&&"P"===e[t+6]&&"E"===e[t+7]){let i=1;for(t+=8;t<e.length;t++)if("<"===e[t])i++;else if(">"===e[t]&&(i--,0===i))break}else if(e.length>t+9&&"["===e[t+1]&&"C"===e[t+2]&&"D"===e[t+3]&&"A"===e[t+4]&&"T"===e[t+5]&&"A"===e[t+6]&&"["===e[t+7])for(t+=8;t<e.length;t++)if("]"===e[t]&&"]"===e[t+1]&&">"===e[t+2]){t+=2;break}return t}je.validate=function(e,t){t=Object.assign({},Ge,t);const i=[];let r=!1,s=!1;"\ufeff"===e[0]&&(e=e.substr(1));for(let o=0;o<e.length;o++)if("<"===e[o]&&"?"===e[o+1]){if(o+=2,o=Qe(e,o),o.err)return o}else{if("<"!==e[o]){if(Xe(e[o]))continue;return at("InvalidChar","char '"+e[o]+"' is not expected.",dt(e,o))}{let a=o;if(o++,"!"===e[o]){o=et(e,o);continue}{let l=!1;"/"===e[o]&&(l=!0,o++);let d="";for(;o<e.length&&">"!==e[o]&&" "!==e[o]&&"\t"!==e[o]&&"\n"!==e[o]&&"\r"!==e[o];o++)d+=e[o];if(d=d.trim(),"/"===d[d.length-1]&&(d=d.substring(0,d.length-1),o--),n=d,!Ze.isName(n)){let t;return t=0===d.trim().length?"Invalid space after '<'.":"Tag '"+d+"' is an invalid name.",at("InvalidTag",t,dt(e,o))}const c=rt(e,o);if(!1===c)return at("InvalidAttr","Attributes for '"+d+"' have open quote.",dt(e,o));let h=c.value;if(o=c.index,"/"===h[h.length-1]){const i=o-h.length;h=h.substring(0,h.length-1);const s=nt(h,t);if(!0!==s)return at(s.err.code,s.err.msg,dt(e,i+s.err.line));r=!0}else if(l){if(!c.tagClosed)return at("InvalidTag","Closing tag '"+d+"' doesn't have proper closing.",dt(e,o));if(h.trim().length>0)return at("InvalidTag","Closing tag '"+d+"' can't have attributes or invalid starting.",dt(e,a));{const t=i.pop();if(d!==t.tagName){let i=dt(e,t.tagStartPos);return at("InvalidTag","Expected closing tag '"+t.tagName+"' (opened in line "+i.line+", col "+i.col+") instead of closing tag '"+d+"'.",dt(e,a))}0==i.length&&(s=!0)}}else{const n=nt(h,t);if(!0!==n)return at(n.err.code,n.err.msg,dt(e,o-h.length+n.err.line));if(!0===s)return at("InvalidXml","Multiple possible root nodes found.",dt(e,o));-1!==t.unpairedTags.indexOf(d)||i.push({tagName:d,tagStartPos:a}),r=!0}for(o++;o<e.length;o++)if("<"===e[o]){if("!"===e[o+1]){o++,o=et(e,o);continue}if("?"!==e[o+1])break;if(o=Qe(e,++o),o.err)return o}else if("&"===e[o]){const t=ot(e,o);if(-1==t)return at("InvalidChar","char '&' is not expected.",dt(e,o));o=t}else if(!0===s&&!Xe(e[o]))return at("InvalidXml","Extra text at the end",dt(e,o));"<"===e[o]&&o--}}}var n;return r?1==i.length?at("InvalidTag","Unclosed tag '"+i[0].tagName+"'.",dt(e,i[0].tagStartPos)):!(i.length>0)||at("InvalidXml","Invalid '"+JSON.stringify(i.map((e=>e.tagName)),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1}):at("InvalidXml","Start tag expected.",1)};const tt='"',it="'";function rt(e,t){let i="",r="",s=!1;for(;t<e.length;t++){if(e[t]===tt||e[t]===it)""===r?r=e[t]:r!==e[t]||(r="");else if(">"===e[t]&&""===r){s=!0;break}i+=e[t]}return""===r&&{value:i,index:t,tagClosed:s}}const st=new RegExp("(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['\"])(([\\s\\S])*?)\\5)?","g");function nt(e,t){const i=Ze.getAllMatches(e,st),r={};for(let e=0;e<i.length;e++){if(0===i[e][1].length)return at("InvalidAttr","Attribute '"+i[e][2]+"' has no space in starting.",ct(i[e]));if(void 0!==i[e][3]&&void 0===i[e][4])return at("InvalidAttr","Attribute '"+i[e][2]+"' is without value.",ct(i[e]));if(void 0===i[e][3]&&!t.allowBooleanAttributes)return at("InvalidAttr","boolean attribute '"+i[e][2]+"' is not allowed.",ct(i[e]));const s=i[e][2];if(!lt(s))return at("InvalidAttr","Attribute '"+s+"' is an invalid name.",ct(i[e]));if(r.hasOwnProperty(s))return at("InvalidAttr","Attribute '"+s+"' is repeated.",ct(i[e]));r[s]=1}return!0}function ot(e,t){if(";"===e[++t])return-1;if("#"===e[t])return function(e,t){let i=/\d/;for("x"===e[t]&&(t++,i=/[\da-fA-F]/);t<e.length;t++){if(";"===e[t])return t;if(!e[t].match(i))break}return-1}(e,++t);let i=0;for(;t<e.length;t++,i++)if(!(e[t].match(/\w/)&&i<20)){if(";"===e[t])break;return-1}return t}function at(e,t,i){return{err:{code:e,msg:t,line:i.line||i,col:i.col}}}function lt(e){return Ze.isName(e)}function dt(e,t){const i=e.substring(0,t).split(/\r?\n/);return{line:i.length,col:i[i.length-1].length+1}}function ct(e){return e.startIndex+e[1].length}var ht={};const ut={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},stopNodes:[],alwaysCreateTextNode:!1,isArray:()=>!1,commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1,updateTag:function(e,t,i){return e}};ht.buildOptions=function(e){return Object.assign({},ut,e)},ht.defaultOptions=ut;var pt=class{constructor(e){this.tagname=e,this.child=[],this[":@"]={}}add(e,t){"__proto__"===e&&(e="#__proto__"),this.child.push({[e]:t})}addChild(e){"__proto__"===e.tagname&&(e.tagname="#__proto__"),e[":@"]&&Object.keys(e[":@"]).length>0?this.child.push({[e.tagname]:e.child,":@":e[":@"]}):this.child.push({[e.tagname]:e.child})}};const gt=Ke;function mt(e,t){let i="";for(;t<e.length&&"'"!==e[t]&&'"'!==e[t];t++)i+=e[t];if(i=i.trim(),-1!==i.indexOf(" "))throw new Error("External entites are not supported");const r=e[t++];let s="";for(;t<e.length&&e[t]!==r;t++)s+=e[t];return[i,s,t]}function ft(e,t){return"!"===e[t+1]&&"-"===e[t+2]&&"-"===e[t+3]}function bt(e,t){return"!"===e[t+1]&&"E"===e[t+2]&&"N"===e[t+3]&&"T"===e[t+4]&&"I"===e[t+5]&&"T"===e[t+6]&&"Y"===e[t+7]}function _t(e,t){return"!"===e[t+1]&&"E"===e[t+2]&&"L"===e[t+3]&&"E"===e[t+4]&&"M"===e[t+5]&&"E"===e[t+6]&&"N"===e[t+7]&&"T"===e[t+8]}function vt(e,t){return"!"===e[t+1]&&"A"===e[t+2]&&"T"===e[t+3]&&"T"===e[t+4]&&"L"===e[t+5]&&"I"===e[t+6]&&"S"===e[t+7]&&"T"===e[t+8]}function wt(e,t){return"!"===e[t+1]&&"N"===e[t+2]&&"O"===e[t+3]&&"T"===e[t+4]&&"A"===e[t+5]&&"T"===e[t+6]&&"I"===e[t+7]&&"O"===e[t+8]&&"N"===e[t+9]}function yt(e){if(gt.isName(e))return e;throw new Error(`Invalid entity name ${e}`)}var Pt=function(e,t){const i={};if("O"!==e[t+3]||"C"!==e[t+4]||"T"!==e[t+5]||"Y"!==e[t+6]||"P"!==e[t+7]||"E"!==e[t+8])throw new Error("Invalid Tag instead of DOCTYPE");{t+=9;let r=1,s=!1,n=!1,o="";for(;t<e.length;t++)if("<"!==e[t]||n)if(">"===e[t]){if(n?"-"===e[t-1]&&"-"===e[t-2]&&(n=!1,r--):r--,0===r)break}else"["===e[t]?s=!0:o+=e[t];else{if(s&&bt(e,t))t+=7,[entityName,val,t]=mt(e,t+1),-1===val.indexOf("&")&&(i[yt(entityName)]={regx:RegExp(`&${entityName};`,"g"),val:val});else if(s&&_t(e,t))t+=8;else if(s&&vt(e,t))t+=8;else if(s&&wt(e,t))t+=9;else{if(!ft)throw new Error("Invalid DOCTYPE");n=!0}r++,o=""}if(0!==r)throw new Error("Unclosed DOCTYPE")}return{entities:i,i:t}};const At=/^[-+]?0x[a-fA-F0-9]+$/,St=/^([\-\+])?(0*)(\.[0-9]+([eE]\-?[0-9]+)?|[0-9]+(\.[0-9]+([eE]\-?[0-9]+)?)?)$/;!Number.parseInt&&window.parseInt&&(Number.parseInt=window.parseInt),!Number.parseFloat&&window.parseFloat&&(Number.parseFloat=window.parseFloat);const Tt={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0};const Ct=Ke,Et=pt,Rt=Pt,kt=function(e,t={}){if(t=Object.assign({},Tt,t),!e||"string"!=typeof e)return e;let i=e.trim();if(void 0!==t.skipLike&&t.skipLike.test(i))return e;if(t.hex&&At.test(i))return Number.parseInt(i,16);{const r=St.exec(i);if(r){const s=r[1],n=r[2];let o=function(e){if(e&&-1!==e.indexOf("."))return"."===(e=e.replace(/0+$/,""))?e="0":"."===e[0]?e="0"+e:"."===e[e.length-1]&&(e=e.substr(0,e.length-1)),e;return e}(r[3]);const a=r[4]||r[6];if(!t.leadingZeros&&n.length>0&&s&&"."!==i[2])return e;if(!t.leadingZeros&&n.length>0&&!s&&"."!==i[1])return e;{const r=Number(i),l=""+r;return-1!==l.search(/[eE]/)||a?t.eNotation?r:e:-1!==i.indexOf(".")?"0"===l&&""===o||l===o||s&&l==="-"+o?r:e:n?o===l||s+o===l?r:e:i===l||i===s+l?r:e}}return e}};function xt(e){const t=Object.keys(e);for(let i=0;i<t.length;i++){const r=t[i];this.lastEntities[r]={regex:new RegExp("&"+r+";","g"),val:e[r]}}}function Nt(e,t,i,r,s,n,o){if(void 0!==e&&(this.options.trimValues&&!r&&(e=e.trim()),e.length>0)){o||(e=this.replaceEntitiesValue(e));const r=this.options.tagValueProcessor(t,e,i,s,n);if(null==r)return e;if(typeof r!=typeof e||r!==e)return r;if(this.options.trimValues)return qt(e,this.options.parseTagValue,this.options.numberParseOptions);return e.trim()===e?qt(e,this.options.parseTagValue,this.options.numberParseOptions):e}}function Ot(e){if(this.options.removeNSPrefix){const t=e.split(":"),i="/"===e.charAt(0)?"/":"";if("xmlns"===t[0])return"";2===t.length&&(e=i+t[1])}return e}const It=new RegExp("([^\\s=]+)\\s*(=\\s*(['\"])([\\s\\S]*?)\\3)?","gm");function Mt(e,t,i){if(!this.options.ignoreAttributes&&"string"==typeof e){const i=Ct.getAllMatches(e,It),r=i.length,s={};for(let e=0;e<r;e++){const r=this.resolveNameSpace(i[e][1]);let n=i[e][4],o=this.options.attributeNamePrefix+r;if(r.length)if(this.options.transformAttributeName&&(o=this.options.transformAttributeName(o)),"__proto__"===o&&(o="#__proto__"),void 0!==n){this.options.trimValues&&(n=n.trim()),n=this.replaceEntitiesValue(n);const e=this.options.attributeValueProcessor(r,n,t);s[o]=null==e?n:typeof e!=typeof n||e!==n?e:qt(n,this.options.parseAttributeValue,this.options.numberParseOptions)}else this.options.allowBooleanAttributes&&(s[o]=!0)}if(!Object.keys(s).length)return;if(this.options.attributesGroupName){const e={};return e[this.options.attributesGroupName]=s,e}return s}}const Vt=function(e){e=e.replace(/\r\n?/g,"\n");const t=new Et("!xml");let i=t,r="",s="";for(let n=0;n<e.length;n++){if("<"===e[n])if("/"===e[n+1]){const t=$t(e,">",n,"Closing Tag is not closed.");let o=e.substring(n+2,t).trim();if(this.options.removeNSPrefix){const e=o.indexOf(":");-1!==e&&(o=o.substr(e+1))}this.options.transformTagName&&(o=this.options.transformTagName(o)),i&&(r=this.saveTextToParentTag(r,i,s));const a=s.substring(s.lastIndexOf(".")+1);if(o&&-1!==this.options.unpairedTags.indexOf(o))throw new Error(`Unpaired tag can not be used as closing tag: </${o}>`);let l=0;a&&-1!==this.options.unpairedTags.indexOf(a)?(l=s.lastIndexOf(".",s.lastIndexOf(".")-1),this.tagsNodeStack.pop()):l=s.lastIndexOf("."),s=s.substring(0,l),i=this.tagsNodeStack.pop(),r="",n=t}else if("?"===e[n+1]){let t=Lt(e,n,!1,"?>");if(!t)throw new Error("Pi Tag is not closed.");if(r=this.saveTextToParentTag(r,i,s),this.options.ignoreDeclaration&&"?xml"===t.tagName||this.options.ignorePiTags);else{const e=new Et(t.tagName);e.add(this.options.textNodeName,""),t.tagName!==t.tagExp&&t.attrExpPresent&&(e[":@"]=this.buildAttributesMap(t.tagExp,s,t.tagName)),this.addChild(i,e,s)}n=t.closeIndex+1}else if("!--"===e.substr(n+1,3)){const t=$t(e,"--\x3e",n+4,"Comment is not closed.");if(this.options.commentPropName){const o=e.substring(n+4,t-2);r=this.saveTextToParentTag(r,i,s),i.add(this.options.commentPropName,[{[this.options.textNodeName]:o}])}n=t}else if("!D"===e.substr(n+1,2)){const t=Rt(e,n);this.docTypeEntities=t.entities,n=t.i}else if("!["===e.substr(n+1,2)){const t=$t(e,"]]>",n,"CDATA is not closed.")-2,o=e.substring(n+9,t);if(r=this.saveTextToParentTag(r,i,s),this.options.cdataPropName)i.add(this.options.cdataPropName,[{[this.options.textNodeName]:o}]);else{let e=this.parseTextData(o,i.tagname,s,!0,!1,!0);null==e&&(e=""),i.add(this.options.textNodeName,e)}n=t+2}else{let o=Lt(e,n,this.options.removeNSPrefix),a=o.tagName;const l=o.rawTagName;let d=o.tagExp,c=o.attrExpPresent,h=o.closeIndex;this.options.transformTagName&&(a=this.options.transformTagName(a)),i&&r&&"!xml"!==i.tagname&&(r=this.saveTextToParentTag(r,i,s,!1));const u=i;if(u&&-1!==this.options.unpairedTags.indexOf(u.tagname)&&(i=this.tagsNodeStack.pop(),s=s.substring(0,s.lastIndexOf("."))),a!==t.tagname&&(s+=s?"."+a:a),this.isItStopNode(this.options.stopNodes,s,a)){let t="";if(d.length>0&&d.lastIndexOf("/")===d.length-1)n=o.closeIndex;else if(-1!==this.options.unpairedTags.indexOf(a))n=o.closeIndex;else{const i=this.readStopNodeData(e,l,h+1);if(!i)throw new Error(`Unexpected end of ${l}`);n=i.i,t=i.tagContent}const r=new Et(a);a!==d&&c&&(r[":@"]=this.buildAttributesMap(d,s,a)),t&&(t=this.parseTextData(t,a,s,!0,c,!0,!0)),s=s.substr(0,s.lastIndexOf(".")),r.add(this.options.textNodeName,t),this.addChild(i,r,s)}else{if(d.length>0&&d.lastIndexOf("/")===d.length-1){"/"===a[a.length-1]?(a=a.substr(0,a.length-1),s=s.substr(0,s.length-1),d=a):d=d.substr(0,d.length-1),this.options.transformTagName&&(a=this.options.transformTagName(a));const e=new Et(a);a!==d&&c&&(e[":@"]=this.buildAttributesMap(d,s,a)),this.addChild(i,e,s),s=s.substr(0,s.lastIndexOf("."))}else{const e=new Et(a);this.tagsNodeStack.push(i),a!==d&&c&&(e[":@"]=this.buildAttributesMap(d,s,a)),this.addChild(i,e,s),i=e}r="",n=h}}else r+=e[n]}return t.child};function Dt(e,t,i){const r=this.options.updateTag(t.tagname,i,t[":@"]);!1===r||("string"==typeof r?(t.tagname=r,e.addChild(t)):e.addChild(t))}const Ft=function(e){if(this.options.processEntities){for(let t in this.docTypeEntities){const i=this.docTypeEntities[t];e=e.replace(i.regx,i.val)}for(let t in this.lastEntities){const i=this.lastEntities[t];e=e.replace(i.regex,i.val)}if(this.options.htmlEntities)for(let t in this.htmlEntities){const i=this.htmlEntities[t];e=e.replace(i.regex,i.val)}e=e.replace(this.ampEntity.regex,this.ampEntity.val)}return e};function Bt(e,t,i,r){return e&&(void 0===r&&(r=0===Object.keys(t.child).length),void 0!==(e=this.parseTextData(e,t.tagname,i,!1,!!t[":@"]&&0!==Object.keys(t[":@"]).length,r))&&""!==e&&t.add(this.options.textNodeName,e),e=""),e}function Ut(e,t,i){const r="*."+i;for(const i in e){const s=e[i];if(r===s||t===s)return!0}return!1}function $t(e,t,i,r){const s=e.indexOf(t,i);if(-1===s)throw new Error(r);return s+t.length-1}function Lt(e,t,i,r=">"){const s=function(e,t,i=">"){let r,s="";for(let n=t;n<e.length;n++){let t=e[n];if(r)t===r&&(r="");else if('"'===t||"'"===t)r=t;else if(t===i[0]){if(!i[1])return{data:s,index:n};if(e[n+1]===i[1])return{data:s,index:n}}else"\t"===t&&(t=" ");s+=t}}(e,t+1,r);if(!s)return;let n=s.data;const o=s.index,a=n.search(/\s/);let l=n,d=!0;-1!==a&&(l=n.substring(0,a),n=n.substring(a+1).trimStart());const c=l;if(i){const e=l.indexOf(":");-1!==e&&(l=l.substr(e+1),d=l!==s.data.substr(e+1))}return{tagName:l,tagExp:n,closeIndex:o,attrExpPresent:d,rawTagName:c}}function Wt(e,t,i){const r=i;let s=1;for(;i<e.length;i++)if("<"===e[i])if("/"===e[i+1]){const n=$t(e,">",i,`${t} is not closed`);if(e.substring(i+2,n).trim()===t&&(s--,0===s))return{tagContent:e.substring(r,i),i:n};i=n}else if("?"===e[i+1]){i=$t(e,"?>",i+1,"StopNode is not closed.")}else if("!--"===e.substr(i+1,3)){i=$t(e,"--\x3e",i+3,"StopNode is not closed.")}else if("!["===e.substr(i+1,2)){i=$t(e,"]]>",i,"StopNode is not closed.")-2}else{const r=Lt(e,i,">");if(r){(r&&r.tagName)===t&&"/"!==r.tagExp[r.tagExp.length-1]&&s++,i=r.closeIndex}}}function qt(e,t,i){if(t&&"string"==typeof e){const t=e.trim();return"true"===t||"false"!==t&&kt(e,i)}return Ct.isExist(e)?e:""}var zt=class{constructor(e){this.options=e,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"¢"},pound:{regex:/&(pound|#163);/g,val:"£"},yen:{regex:/&(yen|#165);/g,val:"¥"},euro:{regex:/&(euro|#8364);/g,val:"€"},copyright:{regex:/&(copy|#169);/g,val:"©"},reg:{regex:/&(reg|#174);/g,val:"®"},inr:{regex:/&(inr|#8377);/g,val:"₹"}},this.addExternalEntities=xt,this.parseXml=Vt,this.parseTextData=Nt,this.resolveNameSpace=Ot,this.buildAttributesMap=Mt,this.isItStopNode=Ut,this.replaceEntitiesValue=Ft,this.readStopNodeData=Wt,this.saveTextToParentTag=Bt,this.addChild=Dt}},Ht={};function Jt(e,t,i){let r;const s={};for(let n=0;n<e.length;n++){const o=e[n],a=Yt(o);let l="";if(l=void 0===i?a:i+"."+a,a===t.textNodeName)void 0===r?r=o[a]:r+=""+o[a];else{if(void 0===a)continue;if(o[a]){let e=Jt(o[a],t,l);const i=Kt(e,t);o[":@"]?jt(e,o[":@"],l,t):1!==Object.keys(e).length||void 0===e[t.textNodeName]||t.alwaysCreateTextNode?0===Object.keys(e).length&&(t.alwaysCreateTextNode?e[t.textNodeName]="":e=""):e=e[t.textNodeName],void 0!==s[a]&&s.hasOwnProperty(a)?(Array.isArray(s[a])||(s[a]=[s[a]]),s[a].push(e)):t.isArray(a,l,i)?s[a]=[e]:s[a]=e}}}return"string"==typeof r?r.length>0&&(s[t.textNodeName]=r):void 0!==r&&(s[t.textNodeName]=r),s}function Yt(e){const t=Object.keys(e);for(let e=0;e<t.length;e++){const i=t[e];if(":@"!==i)return i}}function jt(e,t,i,r){if(t){const s=Object.keys(t),n=s.length;for(let o=0;o<n;o++){const n=s[o];r.isArray(n,i+"."+n,!0,!0)?e[n]=[t[n]]:e[n]=t[n]}}}function Kt(e,t){const{textNodeName:i}=t,r=Object.keys(e).length;return 0===r||!(1!==r||!e[i]&&"boolean"!=typeof e[i]&&0!==e[i])}Ht.prettify=function(e,t){return Jt(e,t)};const{buildOptions:Zt}=ht,Gt=zt,{prettify:Xt}=Ht,Qt=je;var ei=class{constructor(e){this.externalEntities={},this.options=Zt(e)}parse(e,t){if("string"==typeof e);else{if(!e.toString)throw new Error("XML data is accepted in String or Bytes[] form.");e=e.toString()}if(t){!0===t&&(t={});const i=Qt.validate(e,t);if(!0!==i)throw Error(`${i.err.msg}:${i.err.line}:${i.err.col}`)}const i=new Gt(this.options);i.addExternalEntities(this.externalEntities);const r=i.parseXml(e);return this.options.preserveOrder||void 0===r?r:Xt(r,this.options)}addEntity(e,t){if(-1!==t.indexOf("&"))throw new Error("Entity value can't have '&'");if(-1!==e.indexOf("&")||-1!==e.indexOf(";"))throw new Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");if("&"===t)throw new Error("An entity with value '&' is not permitted");this.externalEntities[e]=t}};function ti(e,t,i,r){let s="",n=!1;for(let o=0;o<e.length;o++){const a=e[o],l=ii(a);if(void 0===l)continue;let d="";if(d=0===i.length?l:`${i}.${l}`,l===t.textNodeName){let e=a[l];si(d,t)||(e=t.tagValueProcessor(l,e),e=ni(e,t)),n&&(s+=r),s+=e,n=!1;continue}if(l===t.cdataPropName){n&&(s+=r),s+=`<![CDATA[${a[l][0][t.textNodeName]}]]>`,n=!1;continue}if(l===t.commentPropName){s+=r+`\x3c!--${a[l][0][t.textNodeName]}--\x3e`,n=!0;continue}if("?"===l[0]){const e=ri(a[":@"],t),i="?xml"===l?"":r;let o=a[l][0][t.textNodeName];o=0!==o.length?" "+o:"",s+=i+`<${l}${o}${e}?>`,n=!0;continue}let c=r;""!==c&&(c+=t.indentBy);const h=r+`<${l}${ri(a[":@"],t)}`,u=ti(a[l],t,d,c);-1!==t.unpairedTags.indexOf(l)?t.suppressUnpairedNode?s+=h+">":s+=h+"/>":u&&0!==u.length||!t.suppressEmptyNode?u&&u.endsWith(">")?s+=h+`>${u}${r}</${l}>`:(s+=h+">",u&&""!==r&&(u.includes("/>")||u.includes("</"))?s+=r+t.indentBy+u+r:s+=u,s+=`</${l}>`):s+=h+"/>",n=!0}return s}function ii(e){const t=Object.keys(e);for(let i=0;i<t.length;i++){const r=t[i];if(e.hasOwnProperty(r)&&":@"!==r)return r}}function ri(e,t){let i="";if(e&&!t.ignoreAttributes)for(let r in e){if(!e.hasOwnProperty(r))continue;let s=t.attributeValueProcessor(r,e[r]);s=ni(s,t),!0===s&&t.suppressBooleanAttributes?i+=` ${r.substr(t.attributeNamePrefix.length)}`:i+=` ${r.substr(t.attributeNamePrefix.length)}="${s}"`}return i}function si(e,t){let i=(e=e.substr(0,e.length-t.textNodeName.length-1)).substr(e.lastIndexOf(".")+1);for(let r in t.stopNodes)if(t.stopNodes[r]===e||t.stopNodes[r]==="*."+i)return!0;return!1}function ni(e,t){if(e&&e.length>0&&t.processEntities)for(let i=0;i<t.entities.length;i++){const r=t.entities[i];e=e.replace(r.regex,r.val)}return e}const oi=function(e,t){let i="";return t.format&&t.indentBy.length>0&&(i="\n"),ti(e,t,"",i)},ai={attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,cdataPropName:!1,format:!1,indentBy:"  ",suppressEmptyNode:!1,suppressUnpairedNode:!0,suppressBooleanAttributes:!0,tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},preserveOrder:!1,commentPropName:!1,unpairedTags:[],entities:[{regex:new RegExp("&","g"),val:"&amp;"},{regex:new RegExp(">","g"),val:"&gt;"},{regex:new RegExp("<","g"),val:"&lt;"},{regex:new RegExp("'","g"),val:"&apos;"},{regex:new RegExp('"',"g"),val:"&quot;"}],processEntities:!0,stopNodes:[],oneListGroup:!1};function li(e){this.options=Object.assign({},ai,e),this.options.ignoreAttributes||this.options.attributesGroupName?this.isAttribute=function(){return!1}:(this.attrPrefixLen=this.options.attributeNamePrefix.length,this.isAttribute=hi),this.processTextOrObjNode=di,this.options.format?(this.indentate=ci,this.tagEndChar=">\n",this.newLine="\n"):(this.indentate=function(){return""},this.tagEndChar=">",this.newLine="")}function di(e,t,i){const r=this.j2x(e,i+1);return void 0!==e[this.options.textNodeName]&&1===Object.keys(e).length?this.buildTextValNode(e[this.options.textNodeName],t,r.attrStr,i):this.buildObjectNode(r.val,t,r.attrStr,i)}function ci(e){return this.options.indentBy.repeat(e)}function hi(e){return!(!e.startsWith(this.options.attributeNamePrefix)||e===this.options.textNodeName)&&e.substr(this.attrPrefixLen)}li.prototype.build=function(e){return this.options.preserveOrder?oi(e,this.options):(Array.isArray(e)&&this.options.arrayNodeName&&this.options.arrayNodeName.length>1&&(e={[this.options.arrayNodeName]:e}),this.j2x(e,0).val)},li.prototype.j2x=function(e,t){let i="",r="";for(let s in e)if(Object.prototype.hasOwnProperty.call(e,s))if(void 0===e[s])this.isAttribute(s)&&(r+="");else if(null===e[s])this.isAttribute(s)?r+="":"?"===s[0]?r+=this.indentate(t)+"<"+s+"?"+this.tagEndChar:r+=this.indentate(t)+"<"+s+"/"+this.tagEndChar;else if(e[s]instanceof Date)r+=this.buildTextValNode(e[s],s,"",t);else if("object"!=typeof e[s]){const n=this.isAttribute(s);if(n)i+=this.buildAttrPairStr(n,""+e[s]);else if(s===this.options.textNodeName){let t=this.options.tagValueProcessor(s,""+e[s]);r+=this.replaceEntitiesValue(t)}else r+=this.buildTextValNode(e[s],s,"",t)}else if(Array.isArray(e[s])){const i=e[s].length;let n="";for(let o=0;o<i;o++){const i=e[s][o];void 0===i||(null===i?"?"===s[0]?r+=this.indentate(t)+"<"+s+"?"+this.tagEndChar:r+=this.indentate(t)+"<"+s+"/"+this.tagEndChar:"object"==typeof i?this.options.oneListGroup?n+=this.j2x(i,t+1).val:n+=this.processTextOrObjNode(i,s,t):n+=this.buildTextValNode(i,s,"",t))}this.options.oneListGroup&&(n=this.buildObjectNode(n,s,"",t)),r+=n}else if(this.options.attributesGroupName&&s===this.options.attributesGroupName){const t=Object.keys(e[s]),r=t.length;for(let n=0;n<r;n++)i+=this.buildAttrPairStr(t[n],""+e[s][t[n]])}else r+=this.processTextOrObjNode(e[s],s,t);return{attrStr:i,val:r}},li.prototype.buildAttrPairStr=function(e,t){return t=this.options.attributeValueProcessor(e,""+t),t=this.replaceEntitiesValue(t),this.options.suppressBooleanAttributes&&"true"===t?" "+e:" "+e+'="'+t+'"'},li.prototype.buildObjectNode=function(e,t,i,r){if(""===e)return"?"===t[0]?this.indentate(r)+"<"+t+i+"?"+this.tagEndChar:this.indentate(r)+"<"+t+i+this.closeTag(t)+this.tagEndChar;{let s="</"+t+this.tagEndChar,n="";return"?"===t[0]&&(n="?",s=""),!i&&""!==i||-1!==e.indexOf("<")?!1!==this.options.commentPropName&&t===this.options.commentPropName&&0===n.length?this.indentate(r)+`\x3c!--${e}--\x3e`+this.newLine:this.indentate(r)+"<"+t+i+n+this.tagEndChar+e+this.indentate(r)+s:this.indentate(r)+"<"+t+i+n+">"+e+s}},li.prototype.closeTag=function(e){let t="";return-1!==this.options.unpairedTags.indexOf(e)?this.options.suppressUnpairedNode||(t="/"):t=this.options.suppressEmptyNode?"/":`></${e}`,t},li.prototype.buildTextValNode=function(e,t,i,r){if(!1!==this.options.cdataPropName&&t===this.options.cdataPropName)return this.indentate(r)+`<![CDATA[${e}]]>`+this.newLine;if(!1!==this.options.commentPropName&&t===this.options.commentPropName)return this.indentate(r)+`\x3c!--${e}--\x3e`+this.newLine;if("?"===t[0])return this.indentate(r)+"<"+t+i+"?"+this.tagEndChar;{let s=this.options.tagValueProcessor(t,e);return s=this.replaceEntitiesValue(s),""===s?this.indentate(r)+"<"+t+i+this.closeTag(t)+this.tagEndChar:this.indentate(r)+"<"+t+i+">"+s+"</"+t+this.tagEndChar}},li.prototype.replaceEntitiesValue=function(e){if(e&&e.length>0&&this.options.processEntities)for(let t=0;t<this.options.entities.length;t++){const i=this.options.entities[t];e=e.replace(i.regex,i.val)}return e};var ui={XMLParser:ei,XMLValidator:je,XMLBuilder:li};class pi{constructor(e,t,i,r){this.message=e,this.code=t,this.severity=i,this.type=r}}const gi={UnexpectedSetUp:250,InvalidSetUp:251,InvalidProgressValue:252,InvalidCloseValue:253,InvalidProgressContext:254,InvalidCloseContext:255},mi=300,fi="vast_parser_failure",bi="ad_progress_tracking_error";class _i{static getVastValidateError(){return new pi("Could not validate VAST xml",101,4,fi)}static getVastNoAdsAvailableError(){return new pi("No ad available",102,0,fi)}static getVastAdWithWrapperError(){return new pi("Ad without wrapper tag received",103,0,fi)}static getVastEmptyTagError(){return new pi("Vast tag returned empty. Proxy error.",104,0,fi)}static getVastEmptyExtensionError(){return new pi("Ad without extension tag received",105,0,fi)}static getVastAdWithExtensionsError(){return new pi("Ad without extenstions tag received",106,0,fi)}static getVastNonHyprMxExtensionError(){return new pi("Ad without HyprMx extension attribute received",107,0,fi)}static getAdProgressInvalidSetUpError(e,t,i,r){return new pi(`Invalid call to AdProgressTracking.setUpTracking with viewingID: ${e} distributorID: ${t} userID: ${i} offerType: ${r}`,gi.InvalidSetUp,3,bi)}static getAdProgressUpdateError(e,t){return new pi(e,t,3,bi)}static getBannerContainerSizeMismatchedError(e){return new pi(e,mi,3,"banner_error")}}class vi{constructor(e){this.adJson=this.parseXML(e)??{}}parseXML(e){const t={attributeNamePrefix:"",textNodeName:"value",ignoreDeclaration:!0,ignoreAttributes:!1,allowBooleanAttributes:!0,parseAttributeValue:!0,removeNSPrefix:!0,transformAttributeName:e=>e.replace("-","_")};if(n(e)||!0!==ui.XMLValidator.validate(e,t))return this.lastError=_i.getVastValidateError(),{};const i=new ui.XMLParser(t).parse(e);return void 0===i.VAST?this.lastError=_i.getVastEmptyTagError():void 0===i.VAST.Ad?this.lastError=_i.getVastNoAdsAvailableError():void 0===i.VAST.Ad.Wrapper?this.lastError=_i.getVastAdWithWrapperError():void 0===i.VAST.Ad.Wrapper.Extensions&&(this.lastError=_i.getVastAdWithExtensionsError()),i}getVastExtension(e){const t=this.adJson?.VAST?.Ad?.Wrapper?.Extensions?.Extension;let i=null;return void 0===t?i=_i.getVastEmptyExtensionError():t.type!==e&&(i=_i.getVastNonHyprMxExtensionError()),[t,i]}getPrequalExtension(e="HyprMX"){const[t,i]=this.getVastExtension(e);if(null!==i)return this.lastError=this.lastError??i,{};const r={id:this.adJson?.VAST?.Ad?.id};return void 0!==t.PreQual&&(r.pre_qual=t.PreQual),r}getFullScreenExtension(e="HyprMX"){const[t,i]=this.getVastExtension(e);if(null!==i)return this.lastError=this.lastError??i,{};const r={id:this.adJson?.VAST?.Ad?.id},s=t.Display;void 0!==s&&vi.buildDisplayExtension(s,r);const n=t.WebTraffic;void 0!==n&&vi.buildWebTrafficExtension(n,r);const o=t.Mraid;void 0!==o&&vi.buildMraidExtension(o,r);const a=t.Reward;return void 0!==a&&vi.buildReward(a,r),r}getBannerExtension(e="HyprMX"){const[t,i]=this.getVastExtension(e);if(console.debug(`[VP] getBannerExtension extension: ${JSON.stringify(t)} error: ${JSON.stringify(i)}`),null!==i)return this.lastError=this.lastError??i,{error:this.lastError,bannerExtensions:{}};const{click_mode:r}=t?.Banner||{},s={time_on_site_url:t?.TimeOnSiteUrl,click_reporting_url:t?.ClickTrackingUrl};return console.debug(`[VP] getBannerExtension bannerExtensions: ${JSON.stringify(s)}`),{bannerExtensions:s,clickMode:r}}static buildDisplayExtension(e,t){t.allowed_orientation=e.orientation,t.type=f,t.offer_initiation_timeout_in_seconds=e.timeout,t.click={mode:e.click_mode}}static buildWebTrafficExtension(e,t){t.type=g,t.title=e.title,t.allowed_orientation=e.orientation,t.offer_initiation_timeout_in_seconds=e.timeout,t.click={mode:e.click_mode},t.skip_proscenium=!1,t.skip_thank_you_screen=!0}static buildMraidExtension(e,t){t.type=p,t.ttl=e.ttl,t.offer_initiation_timeout_in_seconds=e.timeout,t.click={mode:e.click_mode}}static buildReward(e,t){t.reward={reward_id:e.id,reward_quantity:e.quantity,reward_text:e.value}}}class wi extends Je{async loadAd(e){console.log(`[FSBL] bidResponseData is ${JSON.stringify(e)}`);const t=new vi(e);t.lastError&&console.log(`Ad VAST parse produced error: ${JSON.stringify(t.lastError)}`);const i=t.getFullScreenExtension();console.log(`[FSBL] adJson is ${JSON.stringify(i)}`);return{ad:He(i,e),required_information:[],ui_components:void 0}}}const yi="consentChanged";class Pi extends ie{constructor(e,t,i,r,s,n=void 0,o=globalThis.HYPRNetworkController,a=globalThis.HYPRTimerController,l=globalThis.consentController,d=HYPRRequestParameterManager){super(e,t,i,r,s,o,a,l,d),this.refreshRequest=void 0,this.canShowFor=void 0,this.cancellationDialog=n}async loadAd(e){if(console.debug(`[FSP] Loading ads for ${this.name}`),this.refreshInProgress())return void console.debug(`[FSP] Refresh in progress for  ${this.name}`);void 0!==this.secondaryRefreshInfo&&(console.error(`[FSP] Load ad started with a secondaryRefreshInfo still set: ${JSON.stringify(this.secondaryRefreshInfo)}`),this.secondaryRefreshInfo=void 0),this.ad?.appendRequestData(e,!0),this.clearAdAndStopAdAvailableTimer(),console.debug(`[FSP] Loading ads for ${this}`),this.inventoryCheck?.cancel();const t=new Ye(this.id,this.inventoryCheckURL);this.inventoryCheck=t;const{ad:i,required_information:r,ui_components:s,error:n}=await t.loadAd(e,this.requestParameterManager,this.networkRequestController);return n===$?(console.debug("[FSP] Request aborted"),{success:!1,error:n}):(this.inventoryCheck=void 0,this.inventoryCheckCallback(i,r,s),{success:this.isAdAvailable(),error:n})}async loadBidResponse(e){this.request&&(this.request.abort(),this.request=void 0),this.clearAdAndStopAdAvailableTimer();const{ad:t}={...await(new wi).loadAd(e)};return this.ad=t,this.isAdAvailable()&&this.startAdAvailableTimer(),{success:this.isAdAvailable()}}inventoryCheckCallback(e,t=[],i=void 0){this.inventoryCheck=void 0,this.ad=e,this.required_information=t,void 0!==i&&(this.ui_components=i),this.isAdAvailable()&&this.startAdAvailableTimer()}isAdAvailable(){return this.hasRequiredInformation()||void 0!==this.ad&&this.ad.hasAd()}startAdAvailableTimer(){this.canShowFor=globalThis.AVAILABLE_FOR,console.debug(`[FSP] FullScreenPlacement.startAdAvailableTimer(${this.name}) for ${this.canShowFor} seconds`),this.adAvailableTimer=this.timerController.startTimer(1e3*this.canShowFor,(()=>{this.clearAdAndStopAdAvailableTimer(!0)}))}stopAdAvailableTimer(){this.adAvailableTimerIsActive()&&(console.debug(`[FSP] FullScreenPlacement.stopAdAvailableTimer(${this.name})`),this.timerController.stopTimer(this.adAvailableTimer),this.canShowFor=void 0,this.adAvailableTimer=void 0)}clearAd(){console.debug(`[FSP] FullScreenPlacement.clearAd(${this.name})`),this.ad=void 0,this.required_information=void 0,this.ui_components=void 0}clearRequiredInfo(){this.required_information=void 0}compileRequestParamsFromParams(e={},t=!0,i=this.ad){const r=super.compileRequestParamsFromParams(e,t,i);return i?.appendRequestData(r,t),this.requestParameterManager.getInventoryCheckParams(r)}popAdData(){const{ad:e}=this,t=this.required_information||[],i=this.ui_components;return this.clearAdAndStopAdAvailableTimer(),{ad:e,requiredInfo:t,uiComponents:i}}clearAdAndStopAdAvailableTimer(e=!1){console.debug(`[FSP] FullScreenPlacement.clearAdAndStopTimer(${this.name})`),void 0!==this.request&&(this.networkRequestController.abortRequest(this.request),this.request=void 0),this.stopAdAvailableTimer(),this.clearAd(),this.placementListener&&!this.refreshInProgress()&&e&&this.placementListener.onPlacementAdExpired(this)}adImpression(){this.placementListener.adImpression?.(this)}handleRefresh(e,t){if(this.adAvailableTimerIsActive())if(void 0===this.ad?.bidResponse){if(this.refreshInProgress()){console.debug(`[FSP] Refresh in progress for placement ${this.name} with request ${this.refreshRequest}`);const t=void 0!==this.secondaryRefreshInfo?this.secondaryRefreshInfo.context:"",i={};return i.context=t===yi?yi:e,void(this.secondaryRefreshInfo=i)}console.debug(`[FSP] Refresh started for placement: ${this} with context: ${e}`),e===yi&&this.clearAd(),this.clearRequiredInfo(),this.refreshAd(t)}else console.debug(`[FSP] Skipping refresh for placement ${this.name} because it was loaded from a bid`)}async refreshAd(e){this.ad?.appendRequestData(e,!0),console.debug(`[FSP] refresh params are: ${JSON.stringify(e)}`);const t=new Ye(this.id,this.inventoryCheckURL);this.refreshRequest=t;const{ad:i,required_information:r,ui_components:s,error:n}=await t.loadAd(e,this.requestParameterManager,this.networkRequestController);this.refreshAdCallback(i,r,s,n)}refreshAdCallback(e,t,i,r){r?console.debug(`[FSP] Refresh failed for placement: ${this} with error ${r}`):console.debug(`[FSP] Refresh completed for placement: ${this}`),this.refreshRequest=void 0;const s=void 0!==e,n=s?e.getAdIdentifier():"";if(s)this.ad=e,this.required_information=t||[],void 0!==i&&(this.ui_components=i),console.debug(`[FSP] ${n.length>0?`Refreshed with ad id: ${n}. `:"Refreshed with new RequiredInfo. "}Restarted timer.`),this.stopAdAvailableTimer(),this.startAdAvailableTimer();else{if(!this.adAvailableTimerIsActive())return console.debug("[FSP] Refreshed completed with no ad and timer has ended"),void this.placementListener.onPlacementAdExpired(this);console.debug(`[FSP] Refreshed completed with no new ad. Current ad id is: ${n}.  Timer still active.`)}this.placementListener.onRefreshAdResponse(this,r),this.secondaryRefreshInfo&&(console.debug(`[FSP] Starting secondary refresh for Context: ${this.secondaryRefreshInfo.context}`),this.placementListener.startAdRefresh(this.secondaryRefreshInfo.context,this.name),this.secondaryRefreshInfo=void 0)}refreshInProgress(){return void 0!==this.refreshRequest}adAvailableTimerIsActive(){return void 0!==this.adAvailableTimer}hasRequiredInformation(){return void 0!==this.required_information&&this.required_information.length>0}hasUIComponents(){return void 0!==this.ui_components&&Object.keys(this.ui_components).length>0}getAdType(){return void 0!==this.ad?this.ad?.getAdType():super.getAdType()}getOfferName(){return void 0!==this.ad?this.ad?.getOfferName():super.getOfferName()}getRequiredInfoString(){return this.hasRequiredInformation()?JSON.stringify(this.required_information):"[]"}getUIComponentsString(){const e=this.hasUIComponents()?this.ui_components:globalThis.defaultUIComponents;return void 0===e.no_ad&&(e.no_ad=Pi.noAdDefaultUI()),JSON.stringify(e)}static noAdDefaultUI(){const e={title:"This ad is not available based on your response. Close to continue.",title_color:"282828",title_size:16};return e}toString(){return`${super.toString()}, isAdAvailable=${this.isAdAvailable()}, canShowFor=${this.canShowFor}]`}}const Ai="3.7.7",Si=Ai,Ti="function"==typeof Buffer,Ci="function"==typeof TextDecoder?new TextDecoder:void 0,Ei="function"==typeof TextEncoder?new TextEncoder:void 0,Ri=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),ki=(e=>{let t={};return e.forEach(((e,i)=>t[e]=i)),t})(Ri),xi=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,Ni=String.fromCharCode.bind(String),Oi="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),Ii=e=>e.replace(/=/g,"").replace(/[+\/]/g,(e=>"+"==e?"-":"_")),Mi=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),Vi=e=>{let t,i,r,s,n="";const o=e.length%3;for(let o=0;o<e.length;){if((i=e.charCodeAt(o++))>255||(r=e.charCodeAt(o++))>255||(s=e.charCodeAt(o++))>255)throw new TypeError("invalid character found");t=i<<16|r<<8|s,n+=Ri[t>>18&63]+Ri[t>>12&63]+Ri[t>>6&63]+Ri[63&t]}return o?n.slice(0,o-3)+"===".substring(o):n},Di="function"==typeof btoa?e=>btoa(e):Ti?e=>Buffer.from(e,"binary").toString("base64"):Vi,Fi=Ti?e=>Buffer.from(e).toString("base64"):e=>{let t=[];for(let i=0,r=e.length;i<r;i+=4096)t.push(Ni.apply(null,e.subarray(i,i+4096)));return Di(t.join(""))},Bi=(e,t=!1)=>t?Ii(Fi(e)):Fi(e),Ui=e=>{if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?Ni(192|t>>>6)+Ni(128|63&t):Ni(224|t>>>12&15)+Ni(128|t>>>6&63)+Ni(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return Ni(240|t>>>18&7)+Ni(128|t>>>12&63)+Ni(128|t>>>6&63)+Ni(128|63&t)},$i=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,Li=e=>e.replace($i,Ui),Wi=Ti?e=>Buffer.from(e,"utf8").toString("base64"):Ei?e=>Fi(Ei.encode(e)):e=>Di(Li(e)),qi=(e,t=!1)=>t?Ii(Wi(e)):Wi(e),zi=e=>qi(e,!0),Hi=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,Ji=e=>{switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return Ni(55296+(t>>>10))+Ni(56320+(1023&t));case 3:return Ni((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return Ni((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},Yi=e=>e.replace(Hi,Ji),ji=e=>{if(e=e.replace(/\s+/g,""),!xi.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));let t,i,r,s="";for(let n=0;n<e.length;)t=ki[e.charAt(n++)]<<18|ki[e.charAt(n++)]<<12|(i=ki[e.charAt(n++)])<<6|(r=ki[e.charAt(n++)]),s+=64===i?Ni(t>>16&255):64===r?Ni(t>>16&255,t>>8&255):Ni(t>>16&255,t>>8&255,255&t);return s},Ki="function"==typeof atob?e=>atob(Mi(e)):Ti?e=>Buffer.from(e,"base64").toString("binary"):ji,Zi=Ti?e=>Oi(Buffer.from(e,"base64")):e=>Oi(Ki(e).split("").map((e=>e.charCodeAt(0)))),Gi=e=>Zi(Qi(e)),Xi=Ti?e=>Buffer.from(e,"base64").toString("utf8"):Ci?e=>Ci.decode(Zi(e)):e=>Yi(Ki(e)),Qi=e=>Mi(e.replace(/[-_]/g,(e=>"-"==e?"+":"/"))),er=e=>Xi(Qi(e)),tr=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),ir=function(){const e=(e,t)=>Object.defineProperty(String.prototype,e,tr(t));e("fromBase64",(function(){return er(this)})),e("toBase64",(function(e){return qi(this,e)})),e("toBase64URI",(function(){return qi(this,!0)})),e("toBase64URL",(function(){return qi(this,!0)})),e("toUint8Array",(function(){return Gi(this)}))},rr=function(){const e=(e,t)=>Object.defineProperty(Uint8Array.prototype,e,tr(t));e("toBase64",(function(e){return Bi(this,e)})),e("toBase64URI",(function(){return Bi(this,!0)})),e("toBase64URL",(function(){return Bi(this,!0)}))},sr={version:Ai,VERSION:Si,atob:Ki,atobPolyfill:ji,btoa:Di,btoaPolyfill:Vi,fromBase64:er,toBase64:qi,encode:qi,encodeURI:zi,encodeURL:zi,utob:Li,btou:Yi,decode:er,isValid:e=>{if("string"!=typeof e)return!1;const t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},fromUint8Array:Bi,toUint8Array:Gi,extendString:ir,extendUint8Array:rr,extendBuiltins:()=>{ir(),rr()}};function nr(e){let t=e.length;for(;--t>=0;)e[t]=0}const or=256,ar=286,lr=30,dr=15,cr=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),hr=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),ur=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),pr=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),gr=new Array(576);nr(gr);const mr=new Array(60);nr(mr);const fr=new Array(512);nr(fr);const br=new Array(256);nr(br);const _r=new Array(29);nr(_r);const vr=new Array(lr);function wr(e,t,i,r,s){this.static_tree=e,this.extra_bits=t,this.extra_base=i,this.elems=r,this.max_length=s,this.has_stree=e&&e.length}let yr,Pr,Ar;function Sr(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}nr(vr);const Tr=e=>e<256?fr[e]:fr[256+(e>>>7)],Cr=(e,t)=>{e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255},Er=(e,t,i)=>{e.bi_valid>16-i?(e.bi_buf|=t<<e.bi_valid&65535,Cr(e,e.bi_buf),e.bi_buf=t>>16-e.bi_valid,e.bi_valid+=i-16):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=i)},Rr=(e,t,i)=>{Er(e,i[2*t],i[2*t+1])},kr=(e,t)=>{let i=0;do{i|=1&e,e>>>=1,i<<=1}while(--t>0);return i>>>1},xr=(e,t,i)=>{const r=new Array(16);let s,n,o=0;for(s=1;s<=dr;s++)o=o+i[s-1]<<1,r[s]=o;for(n=0;n<=t;n++){let t=e[2*n+1];0!==t&&(e[2*n]=kr(r[t]++,t))}},Nr=e=>{let t;for(t=0;t<ar;t++)e.dyn_ltree[2*t]=0;for(t=0;t<lr;t++)e.dyn_dtree[2*t]=0;for(t=0;t<19;t++)e.bl_tree[2*t]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.sym_next=e.matches=0},Or=e=>{e.bi_valid>8?Cr(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0},Ir=(e,t,i,r)=>{const s=2*t,n=2*i;return e[s]<e[n]||e[s]===e[n]&&r[t]<=r[i]},Mr=(e,t,i)=>{const r=e.heap[i];let s=i<<1;for(;s<=e.heap_len&&(s<e.heap_len&&Ir(t,e.heap[s+1],e.heap[s],e.depth)&&s++,!Ir(t,r,e.heap[s],e.depth));)e.heap[i]=e.heap[s],i=s,s<<=1;e.heap[i]=r},Vr=(e,t,i)=>{let r,s,n,o,a=0;if(0!==e.sym_next)do{r=255&e.pending_buf[e.sym_buf+a++],r+=(255&e.pending_buf[e.sym_buf+a++])<<8,s=e.pending_buf[e.sym_buf+a++],0===r?Rr(e,s,t):(n=br[s],Rr(e,n+or+1,t),o=cr[n],0!==o&&(s-=_r[n],Er(e,s,o)),r--,n=Tr(r),Rr(e,n,i),o=hr[n],0!==o&&(r-=vr[n],Er(e,r,o)))}while(a<e.sym_next);Rr(e,256,t)},Dr=(e,t)=>{const i=t.dyn_tree,r=t.stat_desc.static_tree,s=t.stat_desc.has_stree,n=t.stat_desc.elems;let o,a,l,d=-1;for(e.heap_len=0,e.heap_max=573,o=0;o<n;o++)0!==i[2*o]?(e.heap[++e.heap_len]=d=o,e.depth[o]=0):i[2*o+1]=0;for(;e.heap_len<2;)l=e.heap[++e.heap_len]=d<2?++d:0,i[2*l]=1,e.depth[l]=0,e.opt_len--,s&&(e.static_len-=r[2*l+1]);for(t.max_code=d,o=e.heap_len>>1;o>=1;o--)Mr(e,i,o);l=n;do{o=e.heap[1],e.heap[1]=e.heap[e.heap_len--],Mr(e,i,1),a=e.heap[1],e.heap[--e.heap_max]=o,e.heap[--e.heap_max]=a,i[2*l]=i[2*o]+i[2*a],e.depth[l]=(e.depth[o]>=e.depth[a]?e.depth[o]:e.depth[a])+1,i[2*o+1]=i[2*a+1]=l,e.heap[1]=l++,Mr(e,i,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],((e,t)=>{const i=t.dyn_tree,r=t.max_code,s=t.stat_desc.static_tree,n=t.stat_desc.has_stree,o=t.stat_desc.extra_bits,a=t.stat_desc.extra_base,l=t.stat_desc.max_length;let d,c,h,u,p,g,m=0;for(u=0;u<=dr;u++)e.bl_count[u]=0;for(i[2*e.heap[e.heap_max]+1]=0,d=e.heap_max+1;d<573;d++)c=e.heap[d],u=i[2*i[2*c+1]+1]+1,u>l&&(u=l,m++),i[2*c+1]=u,c>r||(e.bl_count[u]++,p=0,c>=a&&(p=o[c-a]),g=i[2*c],e.opt_len+=g*(u+p),n&&(e.static_len+=g*(s[2*c+1]+p)));if(0!==m){do{for(u=l-1;0===e.bl_count[u];)u--;e.bl_count[u]--,e.bl_count[u+1]+=2,e.bl_count[l]--,m-=2}while(m>0);for(u=l;0!==u;u--)for(c=e.bl_count[u];0!==c;)h=e.heap[--d],h>r||(i[2*h+1]!==u&&(e.opt_len+=(u-i[2*h+1])*i[2*h],i[2*h+1]=u),c--)}})(e,t),xr(i,d,e.bl_count)},Fr=(e,t,i)=>{let r,s,n=-1,o=t[1],a=0,l=7,d=4;for(0===o&&(l=138,d=3),t[2*(i+1)+1]=65535,r=0;r<=i;r++)s=o,o=t[2*(r+1)+1],++a<l&&s===o||(a<d?e.bl_tree[2*s]+=a:0!==s?(s!==n&&e.bl_tree[2*s]++,e.bl_tree[32]++):a<=10?e.bl_tree[34]++:e.bl_tree[36]++,a=0,n=s,0===o?(l=138,d=3):s===o?(l=6,d=3):(l=7,d=4))},Br=(e,t,i)=>{let r,s,n=-1,o=t[1],a=0,l=7,d=4;for(0===o&&(l=138,d=3),r=0;r<=i;r++)if(s=o,o=t[2*(r+1)+1],!(++a<l&&s===o)){if(a<d)do{Rr(e,s,e.bl_tree)}while(0!=--a);else 0!==s?(s!==n&&(Rr(e,s,e.bl_tree),a--),Rr(e,16,e.bl_tree),Er(e,a-3,2)):a<=10?(Rr(e,17,e.bl_tree),Er(e,a-3,3)):(Rr(e,18,e.bl_tree),Er(e,a-11,7));a=0,n=s,0===o?(l=138,d=3):s===o?(l=6,d=3):(l=7,d=4)}};let Ur=!1;const $r=(e,t,i,r)=>{Er(e,0+(r?1:0),3),Or(e),Cr(e,i),Cr(e,~i),i&&e.pending_buf.set(e.window.subarray(t,t+i),e.pending),e.pending+=i};var Lr=(e,t,i,r)=>{let s,n,o=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=(e=>{let t,i=4093624447;for(t=0;t<=31;t++,i>>>=1)if(1&i&&0!==e.dyn_ltree[2*t])return 0;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return 1;for(t=32;t<or;t++)if(0!==e.dyn_ltree[2*t])return 1;return 0})(e)),Dr(e,e.l_desc),Dr(e,e.d_desc),o=(e=>{let t;for(Fr(e,e.dyn_ltree,e.l_desc.max_code),Fr(e,e.dyn_dtree,e.d_desc.max_code),Dr(e,e.bl_desc),t=18;t>=3&&0===e.bl_tree[2*pr[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t})(e),s=e.opt_len+3+7>>>3,n=e.static_len+3+7>>>3,n<=s&&(s=n)):s=n=i+5,i+4<=s&&-1!==t?$r(e,t,i,r):4===e.strategy||n===s?(Er(e,2+(r?1:0),3),Vr(e,gr,mr)):(Er(e,4+(r?1:0),3),((e,t,i,r)=>{let s;for(Er(e,t-257,5),Er(e,i-1,5),Er(e,r-4,4),s=0;s<r;s++)Er(e,e.bl_tree[2*pr[s]+1],3);Br(e,e.dyn_ltree,t-1),Br(e,e.dyn_dtree,i-1)})(e,e.l_desc.max_code+1,e.d_desc.max_code+1,o+1),Vr(e,e.dyn_ltree,e.dyn_dtree)),Nr(e),r&&Or(e)},Wr={_tr_init:e=>{Ur||((()=>{let e,t,i,r,s;const n=new Array(16);for(i=0,r=0;r<28;r++)for(_r[r]=i,e=0;e<1<<cr[r];e++)br[i++]=r;for(br[i-1]=r,s=0,r=0;r<16;r++)for(vr[r]=s,e=0;e<1<<hr[r];e++)fr[s++]=r;for(s>>=7;r<lr;r++)for(vr[r]=s<<7,e=0;e<1<<hr[r]-7;e++)fr[256+s++]=r;for(t=0;t<=dr;t++)n[t]=0;for(e=0;e<=143;)gr[2*e+1]=8,e++,n[8]++;for(;e<=255;)gr[2*e+1]=9,e++,n[9]++;for(;e<=279;)gr[2*e+1]=7,e++,n[7]++;for(;e<=287;)gr[2*e+1]=8,e++,n[8]++;for(xr(gr,287,n),e=0;e<lr;e++)mr[2*e+1]=5,mr[2*e]=kr(e,5);yr=new wr(gr,cr,257,ar,dr),Pr=new wr(mr,hr,0,lr,dr),Ar=new wr(new Array(0),ur,0,19,7)})(),Ur=!0),e.l_desc=new Sr(e.dyn_ltree,yr),e.d_desc=new Sr(e.dyn_dtree,Pr),e.bl_desc=new Sr(e.bl_tree,Ar),e.bi_buf=0,e.bi_valid=0,Nr(e)},_tr_stored_block:$r,_tr_flush_block:Lr,_tr_tally:(e,t,i)=>(e.pending_buf[e.sym_buf+e.sym_next++]=t,e.pending_buf[e.sym_buf+e.sym_next++]=t>>8,e.pending_buf[e.sym_buf+e.sym_next++]=i,0===t?e.dyn_ltree[2*i]++:(e.matches++,t--,e.dyn_ltree[2*(br[i]+or+1)]++,e.dyn_dtree[2*Tr(t)]++),e.sym_next===e.sym_end),_tr_align:e=>{Er(e,2,3),Rr(e,256,gr),(e=>{16===e.bi_valid?(Cr(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)})(e)}};var qr=(e,t,i,r)=>{let s=65535&e|0,n=e>>>16&65535|0,o=0;for(;0!==i;){o=i>2e3?2e3:i,i-=o;do{s=s+t[r++]|0,n=n+s|0}while(--o);s%=65521,n%=65521}return s|n<<16|0};const zr=new Uint32Array((()=>{let e,t=[];for(var i=0;i<256;i++){e=i;for(var r=0;r<8;r++)e=1&e?3988292384^e>>>1:e>>>1;t[i]=e}return t})());var Hr=(e,t,i,r)=>{const s=zr,n=r+i;e^=-1;for(let i=r;i<n;i++)e=e>>>8^s[255&(e^t[i])];return-1^e},Jr={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},Yr={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};const{_tr_init:jr,_tr_stored_block:Kr,_tr_flush_block:Zr,_tr_tally:Gr,_tr_align:Xr}=Wr,{Z_NO_FLUSH:Qr,Z_PARTIAL_FLUSH:es,Z_FULL_FLUSH:ts,Z_FINISH:is,Z_BLOCK:rs,Z_OK:ss,Z_STREAM_END:ns,Z_STREAM_ERROR:os,Z_DATA_ERROR:as,Z_BUF_ERROR:ls,Z_DEFAULT_COMPRESSION:ds,Z_FILTERED:cs,Z_HUFFMAN_ONLY:hs,Z_RLE:us,Z_FIXED:ps,Z_DEFAULT_STRATEGY:gs,Z_UNKNOWN:ms,Z_DEFLATED:fs}=Yr,bs=258,_s=262,vs=42,ws=113,ys=666,Ps=(e,t)=>(e.msg=Jr[t],t),As=e=>2*e-(e>4?9:0),Ss=e=>{let t=e.length;for(;--t>=0;)e[t]=0},Ts=e=>{let t,i,r,s=e.w_size;t=e.hash_size,r=t;do{i=e.head[--r],e.head[r]=i>=s?i-s:0}while(--t);t=s,r=t;do{i=e.prev[--r],e.prev[r]=i>=s?i-s:0}while(--t)};let Cs=(e,t,i)=>(t<<e.hash_shift^i)&e.hash_mask;const Es=e=>{const t=e.state;let i=t.pending;i>e.avail_out&&(i=e.avail_out),0!==i&&(e.output.set(t.pending_buf.subarray(t.pending_out,t.pending_out+i),e.next_out),e.next_out+=i,t.pending_out+=i,e.total_out+=i,e.avail_out-=i,t.pending-=i,0===t.pending&&(t.pending_out=0))},Rs=(e,t)=>{Zr(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,Es(e.strm)},ks=(e,t)=>{e.pending_buf[e.pending++]=t},xs=(e,t)=>{e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t},Ns=(e,t,i,r)=>{let s=e.avail_in;return s>r&&(s=r),0===s?0:(e.avail_in-=s,t.set(e.input.subarray(e.next_in,e.next_in+s),i),1===e.state.wrap?e.adler=qr(e.adler,t,s,i):2===e.state.wrap&&(e.adler=Hr(e.adler,t,s,i)),e.next_in+=s,e.total_in+=s,s)},Os=(e,t)=>{let i,r,s=e.max_chain_length,n=e.strstart,o=e.prev_length,a=e.nice_match;const l=e.strstart>e.w_size-_s?e.strstart-(e.w_size-_s):0,d=e.window,c=e.w_mask,h=e.prev,u=e.strstart+bs;let p=d[n+o-1],g=d[n+o];e.prev_length>=e.good_match&&(s>>=2),a>e.lookahead&&(a=e.lookahead);do{if(i=t,d[i+o]===g&&d[i+o-1]===p&&d[i]===d[n]&&d[++i]===d[n+1]){n+=2,i++;do{}while(d[++n]===d[++i]&&d[++n]===d[++i]&&d[++n]===d[++i]&&d[++n]===d[++i]&&d[++n]===d[++i]&&d[++n]===d[++i]&&d[++n]===d[++i]&&d[++n]===d[++i]&&n<u);if(r=bs-(u-n),n=u-bs,r>o){if(e.match_start=t,o=r,r>=a)break;p=d[n+o-1],g=d[n+o]}}}while((t=h[t&c])>l&&0!=--s);return o<=e.lookahead?o:e.lookahead},Is=e=>{const t=e.w_size;let i,r,s;do{if(r=e.window_size-e.lookahead-e.strstart,e.strstart>=t+(t-_s)&&(e.window.set(e.window.subarray(t,t+t-r),0),e.match_start-=t,e.strstart-=t,e.block_start-=t,e.insert>e.strstart&&(e.insert=e.strstart),Ts(e),r+=t),0===e.strm.avail_in)break;if(i=Ns(e.strm,e.window,e.strstart+e.lookahead,r),e.lookahead+=i,e.lookahead+e.insert>=3)for(s=e.strstart-e.insert,e.ins_h=e.window[s],e.ins_h=Cs(e,e.ins_h,e.window[s+1]);e.insert&&(e.ins_h=Cs(e,e.ins_h,e.window[s+3-1]),e.prev[s&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=s,s++,e.insert--,!(e.lookahead+e.insert<3)););}while(e.lookahead<_s&&0!==e.strm.avail_in)},Ms=(e,t)=>{let i,r,s,n=e.pending_buf_size-5>e.w_size?e.w_size:e.pending_buf_size-5,o=0,a=e.strm.avail_in;do{if(i=65535,s=e.bi_valid+42>>3,e.strm.avail_out<s)break;if(s=e.strm.avail_out-s,r=e.strstart-e.block_start,i>r+e.strm.avail_in&&(i=r+e.strm.avail_in),i>s&&(i=s),i<n&&(0===i&&t!==is||t===Qr||i!==r+e.strm.avail_in))break;o=t===is&&i===r+e.strm.avail_in?1:0,Kr(e,0,0,o),e.pending_buf[e.pending-4]=i,e.pending_buf[e.pending-3]=i>>8,e.pending_buf[e.pending-2]=~i,e.pending_buf[e.pending-1]=~i>>8,Es(e.strm),r&&(r>i&&(r=i),e.strm.output.set(e.window.subarray(e.block_start,e.block_start+r),e.strm.next_out),e.strm.next_out+=r,e.strm.avail_out-=r,e.strm.total_out+=r,e.block_start+=r,i-=r),i&&(Ns(e.strm,e.strm.output,e.strm.next_out,i),e.strm.next_out+=i,e.strm.avail_out-=i,e.strm.total_out+=i)}while(0===o);return a-=e.strm.avail_in,a&&(a>=e.w_size?(e.matches=2,e.window.set(e.strm.input.subarray(e.strm.next_in-e.w_size,e.strm.next_in),0),e.strstart=e.w_size,e.insert=e.strstart):(e.window_size-e.strstart<=a&&(e.strstart-=e.w_size,e.window.set(e.window.subarray(e.w_size,e.w_size+e.strstart),0),e.matches<2&&e.matches++,e.insert>e.strstart&&(e.insert=e.strstart)),e.window.set(e.strm.input.subarray(e.strm.next_in-a,e.strm.next_in),e.strstart),e.strstart+=a,e.insert+=a>e.w_size-e.insert?e.w_size-e.insert:a),e.block_start=e.strstart),e.high_water<e.strstart&&(e.high_water=e.strstart),o?4:t!==Qr&&t!==is&&0===e.strm.avail_in&&e.strstart===e.block_start?2:(s=e.window_size-e.strstart,e.strm.avail_in>s&&e.block_start>=e.w_size&&(e.block_start-=e.w_size,e.strstart-=e.w_size,e.window.set(e.window.subarray(e.w_size,e.w_size+e.strstart),0),e.matches<2&&e.matches++,s+=e.w_size,e.insert>e.strstart&&(e.insert=e.strstart)),s>e.strm.avail_in&&(s=e.strm.avail_in),s&&(Ns(e.strm,e.window,e.strstart,s),e.strstart+=s,e.insert+=s>e.w_size-e.insert?e.w_size-e.insert:s),e.high_water<e.strstart&&(e.high_water=e.strstart),s=e.bi_valid+42>>3,s=e.pending_buf_size-s>65535?65535:e.pending_buf_size-s,n=s>e.w_size?e.w_size:s,r=e.strstart-e.block_start,(r>=n||(r||t===is)&&t!==Qr&&0===e.strm.avail_in&&r<=s)&&(i=r>s?s:r,o=t===is&&0===e.strm.avail_in&&i===r?1:0,Kr(e,e.block_start,i,o),e.block_start+=i,Es(e.strm)),o?3:1)},Vs=(e,t)=>{let i,r;for(;;){if(e.lookahead<_s){if(Is(e),e.lookahead<_s&&t===Qr)return 1;if(0===e.lookahead)break}if(i=0,e.lookahead>=3&&(e.ins_h=Cs(e,e.ins_h,e.window[e.strstart+3-1]),i=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==i&&e.strstart-i<=e.w_size-_s&&(e.match_length=Os(e,i)),e.match_length>=3)if(r=Gr(e,e.strstart-e.match_start,e.match_length-3),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=3){e.match_length--;do{e.strstart++,e.ins_h=Cs(e,e.ins_h,e.window[e.strstart+3-1]),i=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=Cs(e,e.ins_h,e.window[e.strstart+1]);else r=Gr(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(r&&(Rs(e,!1),0===e.strm.avail_out))return 1}return e.insert=e.strstart<2?e.strstart:2,t===is?(Rs(e,!0),0===e.strm.avail_out?3:4):e.sym_next&&(Rs(e,!1),0===e.strm.avail_out)?1:2},Ds=(e,t)=>{let i,r,s;for(;;){if(e.lookahead<_s){if(Is(e),e.lookahead<_s&&t===Qr)return 1;if(0===e.lookahead)break}if(i=0,e.lookahead>=3&&(e.ins_h=Cs(e,e.ins_h,e.window[e.strstart+3-1]),i=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=2,0!==i&&e.prev_length<e.max_lazy_match&&e.strstart-i<=e.w_size-_s&&(e.match_length=Os(e,i),e.match_length<=5&&(e.strategy===cs||3===e.match_length&&e.strstart-e.match_start>4096)&&(e.match_length=2)),e.prev_length>=3&&e.match_length<=e.prev_length){s=e.strstart+e.lookahead-3,r=Gr(e,e.strstart-1-e.prev_match,e.prev_length-3),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=s&&(e.ins_h=Cs(e,e.ins_h,e.window[e.strstart+3-1]),i=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=2,e.strstart++,r&&(Rs(e,!1),0===e.strm.avail_out))return 1}else if(e.match_available){if(r=Gr(e,0,e.window[e.strstart-1]),r&&Rs(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(r=Gr(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<2?e.strstart:2,t===is?(Rs(e,!0),0===e.strm.avail_out?3:4):e.sym_next&&(Rs(e,!1),0===e.strm.avail_out)?1:2};function Fs(e,t,i,r,s){this.good_length=e,this.max_lazy=t,this.nice_length=i,this.max_chain=r,this.func=s}const Bs=[new Fs(0,0,0,0,Ms),new Fs(4,4,8,4,Vs),new Fs(4,5,16,8,Vs),new Fs(4,6,32,32,Vs),new Fs(4,4,16,16,Ds),new Fs(8,16,32,32,Ds),new Fs(8,16,128,128,Ds),new Fs(8,32,128,256,Ds),new Fs(32,128,258,1024,Ds),new Fs(32,258,258,4096,Ds)];function Us(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=fs,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),Ss(this.dyn_ltree),Ss(this.dyn_dtree),Ss(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),Ss(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),Ss(this.depth),this.sym_buf=0,this.lit_bufsize=0,this.sym_next=0,this.sym_end=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}const $s=e=>{if(!e)return 1;const t=e.state;return!t||t.strm!==e||t.status!==vs&&57!==t.status&&69!==t.status&&73!==t.status&&91!==t.status&&103!==t.status&&t.status!==ws&&t.status!==ys?1:0},Ls=e=>{if($s(e))return Ps(e,os);e.total_in=e.total_out=0,e.data_type=ms;const t=e.state;return t.pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=2===t.wrap?57:t.wrap?vs:ws,e.adler=2===t.wrap?0:1,t.last_flush=-2,jr(t),ss},Ws=e=>{const t=Ls(e);var i;return t===ss&&((i=e.state).window_size=2*i.w_size,Ss(i.head),i.max_lazy_match=Bs[i.level].max_lazy,i.good_match=Bs[i.level].good_length,i.nice_match=Bs[i.level].nice_length,i.max_chain_length=Bs[i.level].max_chain,i.strstart=0,i.block_start=0,i.lookahead=0,i.insert=0,i.match_length=i.prev_length=2,i.match_available=0,i.ins_h=0),t},qs=(e,t,i,r,s,n)=>{if(!e)return os;let o=1;if(t===ds&&(t=6),r<0?(o=0,r=-r):r>15&&(o=2,r-=16),s<1||s>9||i!==fs||r<8||r>15||t<0||t>9||n<0||n>ps||8===r&&1!==o)return Ps(e,os);8===r&&(r=9);const a=new Us;return e.state=a,a.strm=e,a.status=vs,a.wrap=o,a.gzhead=null,a.w_bits=r,a.w_size=1<<a.w_bits,a.w_mask=a.w_size-1,a.hash_bits=s+7,a.hash_size=1<<a.hash_bits,a.hash_mask=a.hash_size-1,a.hash_shift=~~((a.hash_bits+3-1)/3),a.window=new Uint8Array(2*a.w_size),a.head=new Uint16Array(a.hash_size),a.prev=new Uint16Array(a.w_size),a.lit_bufsize=1<<s+6,a.pending_buf_size=4*a.lit_bufsize,a.pending_buf=new Uint8Array(a.pending_buf_size),a.sym_buf=a.lit_bufsize,a.sym_end=3*(a.lit_bufsize-1),a.level=t,a.strategy=n,a.method=i,Ws(e)};var zs={deflateInit:(e,t)=>qs(e,t,fs,15,8,gs),deflateInit2:qs,deflateReset:Ws,deflateResetKeep:Ls,deflateSetHeader:(e,t)=>$s(e)||2!==e.state.wrap?os:(e.state.gzhead=t,ss),deflate:(e,t)=>{if($s(e)||t>rs||t<0)return e?Ps(e,os):os;const i=e.state;if(!e.output||0!==e.avail_in&&!e.input||i.status===ys&&t!==is)return Ps(e,0===e.avail_out?ls:os);const r=i.last_flush;if(i.last_flush=t,0!==i.pending){if(Es(e),0===e.avail_out)return i.last_flush=-1,ss}else if(0===e.avail_in&&As(t)<=As(r)&&t!==is)return Ps(e,ls);if(i.status===ys&&0!==e.avail_in)return Ps(e,ls);if(i.status===vs&&0===i.wrap&&(i.status=ws),i.status===vs){let t=fs+(i.w_bits-8<<4)<<8,r=-1;if(r=i.strategy>=hs||i.level<2?0:i.level<6?1:6===i.level?2:3,t|=r<<6,0!==i.strstart&&(t|=32),t+=31-t%31,xs(i,t),0!==i.strstart&&(xs(i,e.adler>>>16),xs(i,65535&e.adler)),e.adler=1,i.status=ws,Es(e),0!==i.pending)return i.last_flush=-1,ss}if(57===i.status)if(e.adler=0,ks(i,31),ks(i,139),ks(i,8),i.gzhead)ks(i,(i.gzhead.text?1:0)+(i.gzhead.hcrc?2:0)+(i.gzhead.extra?4:0)+(i.gzhead.name?8:0)+(i.gzhead.comment?16:0)),ks(i,255&i.gzhead.time),ks(i,i.gzhead.time>>8&255),ks(i,i.gzhead.time>>16&255),ks(i,i.gzhead.time>>24&255),ks(i,9===i.level?2:i.strategy>=hs||i.level<2?4:0),ks(i,255&i.gzhead.os),i.gzhead.extra&&i.gzhead.extra.length&&(ks(i,255&i.gzhead.extra.length),ks(i,i.gzhead.extra.length>>8&255)),i.gzhead.hcrc&&(e.adler=Hr(e.adler,i.pending_buf,i.pending,0)),i.gzindex=0,i.status=69;else if(ks(i,0),ks(i,0),ks(i,0),ks(i,0),ks(i,0),ks(i,9===i.level?2:i.strategy>=hs||i.level<2?4:0),ks(i,3),i.status=ws,Es(e),0!==i.pending)return i.last_flush=-1,ss;if(69===i.status){if(i.gzhead.extra){let t=i.pending,r=(65535&i.gzhead.extra.length)-i.gzindex;for(;i.pending+r>i.pending_buf_size;){let s=i.pending_buf_size-i.pending;if(i.pending_buf.set(i.gzhead.extra.subarray(i.gzindex,i.gzindex+s),i.pending),i.pending=i.pending_buf_size,i.gzhead.hcrc&&i.pending>t&&(e.adler=Hr(e.adler,i.pending_buf,i.pending-t,t)),i.gzindex+=s,Es(e),0!==i.pending)return i.last_flush=-1,ss;t=0,r-=s}let s=new Uint8Array(i.gzhead.extra);i.pending_buf.set(s.subarray(i.gzindex,i.gzindex+r),i.pending),i.pending+=r,i.gzhead.hcrc&&i.pending>t&&(e.adler=Hr(e.adler,i.pending_buf,i.pending-t,t)),i.gzindex=0}i.status=73}if(73===i.status){if(i.gzhead.name){let t,r=i.pending;do{if(i.pending===i.pending_buf_size){if(i.gzhead.hcrc&&i.pending>r&&(e.adler=Hr(e.adler,i.pending_buf,i.pending-r,r)),Es(e),0!==i.pending)return i.last_flush=-1,ss;r=0}t=i.gzindex<i.gzhead.name.length?255&i.gzhead.name.charCodeAt(i.gzindex++):0,ks(i,t)}while(0!==t);i.gzhead.hcrc&&i.pending>r&&(e.adler=Hr(e.adler,i.pending_buf,i.pending-r,r)),i.gzindex=0}i.status=91}if(91===i.status){if(i.gzhead.comment){let t,r=i.pending;do{if(i.pending===i.pending_buf_size){if(i.gzhead.hcrc&&i.pending>r&&(e.adler=Hr(e.adler,i.pending_buf,i.pending-r,r)),Es(e),0!==i.pending)return i.last_flush=-1,ss;r=0}t=i.gzindex<i.gzhead.comment.length?255&i.gzhead.comment.charCodeAt(i.gzindex++):0,ks(i,t)}while(0!==t);i.gzhead.hcrc&&i.pending>r&&(e.adler=Hr(e.adler,i.pending_buf,i.pending-r,r))}i.status=103}if(103===i.status){if(i.gzhead.hcrc){if(i.pending+2>i.pending_buf_size&&(Es(e),0!==i.pending))return i.last_flush=-1,ss;ks(i,255&e.adler),ks(i,e.adler>>8&255),e.adler=0}if(i.status=ws,Es(e),0!==i.pending)return i.last_flush=-1,ss}if(0!==e.avail_in||0!==i.lookahead||t!==Qr&&i.status!==ys){let r=0===i.level?Ms(i,t):i.strategy===hs?((e,t)=>{let i;for(;;){if(0===e.lookahead&&(Is(e),0===e.lookahead)){if(t===Qr)return 1;break}if(e.match_length=0,i=Gr(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,i&&(Rs(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===is?(Rs(e,!0),0===e.strm.avail_out?3:4):e.sym_next&&(Rs(e,!1),0===e.strm.avail_out)?1:2})(i,t):i.strategy===us?((e,t)=>{let i,r,s,n;const o=e.window;for(;;){if(e.lookahead<=bs){if(Is(e),e.lookahead<=bs&&t===Qr)return 1;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=3&&e.strstart>0&&(s=e.strstart-1,r=o[s],r===o[++s]&&r===o[++s]&&r===o[++s])){n=e.strstart+bs;do{}while(r===o[++s]&&r===o[++s]&&r===o[++s]&&r===o[++s]&&r===o[++s]&&r===o[++s]&&r===o[++s]&&r===o[++s]&&s<n);e.match_length=bs-(n-s),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=3?(i=Gr(e,1,e.match_length-3),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(i=Gr(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),i&&(Rs(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===is?(Rs(e,!0),0===e.strm.avail_out?3:4):e.sym_next&&(Rs(e,!1),0===e.strm.avail_out)?1:2})(i,t):Bs[i.level].func(i,t);if(3!==r&&4!==r||(i.status=ys),1===r||3===r)return 0===e.avail_out&&(i.last_flush=-1),ss;if(2===r&&(t===es?Xr(i):t!==rs&&(Kr(i,0,0,!1),t===ts&&(Ss(i.head),0===i.lookahead&&(i.strstart=0,i.block_start=0,i.insert=0))),Es(e),0===e.avail_out))return i.last_flush=-1,ss}return t!==is?ss:i.wrap<=0?ns:(2===i.wrap?(ks(i,255&e.adler),ks(i,e.adler>>8&255),ks(i,e.adler>>16&255),ks(i,e.adler>>24&255),ks(i,255&e.total_in),ks(i,e.total_in>>8&255),ks(i,e.total_in>>16&255),ks(i,e.total_in>>24&255)):(xs(i,e.adler>>>16),xs(i,65535&e.adler)),Es(e),i.wrap>0&&(i.wrap=-i.wrap),0!==i.pending?ss:ns)},deflateEnd:e=>{if($s(e))return os;const t=e.state.status;return e.state=null,t===ws?Ps(e,as):ss},deflateSetDictionary:(e,t)=>{let i=t.length;if($s(e))return os;const r=e.state,s=r.wrap;if(2===s||1===s&&r.status!==vs||r.lookahead)return os;if(1===s&&(e.adler=qr(e.adler,t,i,0)),r.wrap=0,i>=r.w_size){0===s&&(Ss(r.head),r.strstart=0,r.block_start=0,r.insert=0);let e=new Uint8Array(r.w_size);e.set(t.subarray(i-r.w_size,i),0),t=e,i=r.w_size}const n=e.avail_in,o=e.next_in,a=e.input;for(e.avail_in=i,e.next_in=0,e.input=t,Is(r);r.lookahead>=3;){let e=r.strstart,t=r.lookahead-2;do{r.ins_h=Cs(r,r.ins_h,r.window[e+3-1]),r.prev[e&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=e,e++}while(--t);r.strstart=e,r.lookahead=2,Is(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=2,r.match_available=0,e.next_in=o,e.input=a,e.avail_in=n,r.wrap=s,ss},deflateInfo:"pako deflate (from Nodeca project)"};const Hs=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var Js={assign:function(e){const t=Array.prototype.slice.call(arguments,1);for(;t.length;){const i=t.shift();if(i){if("object"!=typeof i)throw new TypeError(i+"must be non-object");for(const t in i)Hs(i,t)&&(e[t]=i[t])}}return e},flattenChunks:e=>{let t=0;for(let i=0,r=e.length;i<r;i++)t+=e[i].length;const i=new Uint8Array(t);for(let t=0,r=0,s=e.length;t<s;t++){let s=e[t];i.set(s,r),r+=s.length}return i}};let Ys=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){Ys=!1}const js=new Uint8Array(256);for(let e=0;e<256;e++)js[e]=e>=252?6:e>=248?5:e>=240?4:e>=224?3:e>=192?2:1;js[254]=js[254]=1;var Ks={string2buf:e=>{if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(e);let t,i,r,s,n,o=e.length,a=0;for(s=0;s<o;s++)i=e.charCodeAt(s),55296==(64512&i)&&s+1<o&&(r=e.charCodeAt(s+1),56320==(64512&r)&&(i=65536+(i-55296<<10)+(r-56320),s++)),a+=i<128?1:i<2048?2:i<65536?3:4;for(t=new Uint8Array(a),n=0,s=0;n<a;s++)i=e.charCodeAt(s),55296==(64512&i)&&s+1<o&&(r=e.charCodeAt(s+1),56320==(64512&r)&&(i=65536+(i-55296<<10)+(r-56320),s++)),i<128?t[n++]=i:i<2048?(t[n++]=192|i>>>6,t[n++]=128|63&i):i<65536?(t[n++]=224|i>>>12,t[n++]=128|i>>>6&63,t[n++]=128|63&i):(t[n++]=240|i>>>18,t[n++]=128|i>>>12&63,t[n++]=128|i>>>6&63,t[n++]=128|63&i);return t},buf2string:(e,t)=>{const i=t||e.length;if("function"==typeof TextDecoder&&TextDecoder.prototype.decode)return(new TextDecoder).decode(e.subarray(0,t));let r,s;const n=new Array(2*i);for(s=0,r=0;r<i;){let t=e[r++];if(t<128){n[s++]=t;continue}let o=js[t];if(o>4)n[s++]=65533,r+=o-1;else{for(t&=2===o?31:3===o?15:7;o>1&&r<i;)t=t<<6|63&e[r++],o--;o>1?n[s++]=65533:t<65536?n[s++]=t:(t-=65536,n[s++]=55296|t>>10&1023,n[s++]=56320|1023&t)}}return((e,t)=>{if(t<65534&&e.subarray&&Ys)return String.fromCharCode.apply(null,e.length===t?e:e.subarray(0,t));let i="";for(let r=0;r<t;r++)i+=String.fromCharCode(e[r]);return i})(n,s)},utf8border:(e,t)=>{(t=t||e.length)>e.length&&(t=e.length);let i=t-1;for(;i>=0&&128==(192&e[i]);)i--;return i<0||0===i?t:i+js[e[i]]>t?i:t}};var Zs=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0};const Gs=Object.prototype.toString,{Z_NO_FLUSH:Xs,Z_SYNC_FLUSH:Qs,Z_FULL_FLUSH:en,Z_FINISH:tn,Z_OK:rn,Z_STREAM_END:sn,Z_DEFAULT_COMPRESSION:nn,Z_DEFAULT_STRATEGY:on,Z_DEFLATED:an}=Yr;function ln(e){this.options=Js.assign({level:nn,method:an,chunkSize:16384,windowBits:15,memLevel:8,strategy:on},e||{});let t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Zs,this.strm.avail_out=0;let i=zs.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(i!==rn)throw new Error(Jr[i]);if(t.header&&zs.deflateSetHeader(this.strm,t.header),t.dictionary){let e;if(e="string"==typeof t.dictionary?Ks.string2buf(t.dictionary):"[object ArrayBuffer]"===Gs.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,i=zs.deflateSetDictionary(this.strm,e),i!==rn)throw new Error(Jr[i]);this._dict_set=!0}}function dn(e,t){const i=new ln(t);if(i.push(e,!0),i.err)throw i.msg||Jr[i.err];return i.result}ln.prototype.push=function(e,t){const i=this.strm,r=this.options.chunkSize;let s,n;if(this.ended)return!1;for(n=t===~~t?t:!0===t?tn:Xs,"string"==typeof e?i.input=Ks.string2buf(e):"[object ArrayBuffer]"===Gs.call(e)?i.input=new Uint8Array(e):i.input=e,i.next_in=0,i.avail_in=i.input.length;;)if(0===i.avail_out&&(i.output=new Uint8Array(r),i.next_out=0,i.avail_out=r),(n===Qs||n===en)&&i.avail_out<=6)this.onData(i.output.subarray(0,i.next_out)),i.avail_out=0;else{if(s=zs.deflate(i,n),s===sn)return i.next_out>0&&this.onData(i.output.subarray(0,i.next_out)),s=zs.deflateEnd(this.strm),this.onEnd(s),this.ended=!0,s===rn;if(0!==i.avail_out){if(n>0&&i.next_out>0)this.onData(i.output.subarray(0,i.next_out)),i.avail_out=0;else if(0===i.avail_in)break}else this.onData(i.output)}return!0},ln.prototype.onData=function(e){this.chunks.push(e)},ln.prototype.onEnd=function(e){e===rn&&(this.result=Js.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var cn={Deflate:ln,deflate:dn,deflateRaw:function(e,t){return(t=t||{}).raw=!0,dn(e,t)},gzip:function(e,t){return(t=t||{}).gzip=!0,dn(e,t)},constants:Yr};const hn=16209;var un=function(e,t){let i,r,s,n,o,a,l,d,c,h,u,p,g,m,f,b,_,v,w,y,P,A,S,T;const C=e.state;i=e.next_in,S=e.input,r=i+(e.avail_in-5),s=e.next_out,T=e.output,n=s-(t-e.avail_out),o=s+(e.avail_out-257),a=C.dmax,l=C.wsize,d=C.whave,c=C.wnext,h=C.window,u=C.hold,p=C.bits,g=C.lencode,m=C.distcode,f=(1<<C.lenbits)-1,b=(1<<C.distbits)-1;e:do{p<15&&(u+=S[i++]<<p,p+=8,u+=S[i++]<<p,p+=8),_=g[u&f];t:for(;;){if(v=_>>>24,u>>>=v,p-=v,v=_>>>16&255,0===v)T[s++]=65535&_;else{if(!(16&v)){if(0==(64&v)){_=g[(65535&_)+(u&(1<<v)-1)];continue t}if(32&v){C.mode=16191;break e}e.msg="invalid literal/length code",C.mode=hn;break e}w=65535&_,v&=15,v&&(p<v&&(u+=S[i++]<<p,p+=8),w+=u&(1<<v)-1,u>>>=v,p-=v),p<15&&(u+=S[i++]<<p,p+=8,u+=S[i++]<<p,p+=8),_=m[u&b];i:for(;;){if(v=_>>>24,u>>>=v,p-=v,v=_>>>16&255,!(16&v)){if(0==(64&v)){_=m[(65535&_)+(u&(1<<v)-1)];continue i}e.msg="invalid distance code",C.mode=hn;break e}if(y=65535&_,v&=15,p<v&&(u+=S[i++]<<p,p+=8,p<v&&(u+=S[i++]<<p,p+=8)),y+=u&(1<<v)-1,y>a){e.msg="invalid distance too far back",C.mode=hn;break e}if(u>>>=v,p-=v,v=s-n,y>v){if(v=y-v,v>d&&C.sane){e.msg="invalid distance too far back",C.mode=hn;break e}if(P=0,A=h,0===c){if(P+=l-v,v<w){w-=v;do{T[s++]=h[P++]}while(--v);P=s-y,A=T}}else if(c<v){if(P+=l+c-v,v-=c,v<w){w-=v;do{T[s++]=h[P++]}while(--v);if(P=0,c<w){v=c,w-=v;do{T[s++]=h[P++]}while(--v);P=s-y,A=T}}}else if(P+=c-v,v<w){w-=v;do{T[s++]=h[P++]}while(--v);P=s-y,A=T}for(;w>2;)T[s++]=A[P++],T[s++]=A[P++],T[s++]=A[P++],w-=3;w&&(T[s++]=A[P++],w>1&&(T[s++]=A[P++]))}else{P=s-y;do{T[s++]=T[P++],T[s++]=T[P++],T[s++]=T[P++],w-=3}while(w>2);w&&(T[s++]=T[P++],w>1&&(T[s++]=T[P++]))}break}}break}}while(i<r&&s<o);w=p>>3,i-=w,p-=w<<3,u&=(1<<p)-1,e.next_in=i,e.next_out=s,e.avail_in=i<r?r-i+5:5-(i-r),e.avail_out=s<o?o-s+257:257-(s-o),C.hold=u,C.bits=p};const pn=15,gn=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),mn=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),fn=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),bn=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]);var _n=(e,t,i,r,s,n,o,a)=>{const l=a.bits;let d,c,h,u,p,g,m=0,f=0,b=0,_=0,v=0,w=0,y=0,P=0,A=0,S=0,T=null;const C=new Uint16Array(16),E=new Uint16Array(16);let R,k,x,N=null;for(m=0;m<=pn;m++)C[m]=0;for(f=0;f<r;f++)C[t[i+f]]++;for(v=l,_=pn;_>=1&&0===C[_];_--);if(v>_&&(v=_),0===_)return s[n++]=20971520,s[n++]=20971520,a.bits=1,0;for(b=1;b<_&&0===C[b];b++);for(v<b&&(v=b),P=1,m=1;m<=pn;m++)if(P<<=1,P-=C[m],P<0)return-1;if(P>0&&(0===e||1!==_))return-1;for(E[1]=0,m=1;m<pn;m++)E[m+1]=E[m]+C[m];for(f=0;f<r;f++)0!==t[i+f]&&(o[E[t[i+f]]++]=f);if(0===e?(T=N=o,g=20):1===e?(T=gn,N=mn,g=257):(T=fn,N=bn,g=0),S=0,f=0,m=b,p=n,w=v,y=0,h=-1,A=1<<v,u=A-1,1===e&&A>852||2===e&&A>592)return 1;for(;;){R=m-y,o[f]+1<g?(k=0,x=o[f]):o[f]>=g?(k=N[o[f]-g],x=T[o[f]-g]):(k=96,x=0),d=1<<m-y,c=1<<w,b=c;do{c-=d,s[p+(S>>y)+c]=R<<24|k<<16|x|0}while(0!==c);for(d=1<<m-1;S&d;)d>>=1;if(0!==d?(S&=d-1,S+=d):S=0,f++,0==--C[m]){if(m===_)break;m=t[i+o[f]]}if(m>v&&(S&u)!==h){for(0===y&&(y=v),p+=b,w=m-y,P=1<<w;w+y<_&&(P-=C[w+y],!(P<=0));)w++,P<<=1;if(A+=1<<w,1===e&&A>852||2===e&&A>592)return 1;h=S&u,s[h]=v<<24|w<<16|p-n|0}}return 0!==S&&(s[p+S]=m-y<<24|64<<16|0),a.bits=v,0};const{Z_FINISH:vn,Z_BLOCK:wn,Z_TREES:yn,Z_OK:Pn,Z_STREAM_END:An,Z_NEED_DICT:Sn,Z_STREAM_ERROR:Tn,Z_DATA_ERROR:Cn,Z_MEM_ERROR:En,Z_BUF_ERROR:Rn,Z_DEFLATED:kn}=Yr,xn=16180,Nn=16190,On=16191,In=16192,Mn=16194,Vn=16199,Dn=16200,Fn=16206,Bn=16209,Un=e=>(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24);function $n(){this.strm=null,this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Uint16Array(320),this.work=new Uint16Array(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}const Ln=e=>{if(!e)return 1;const t=e.state;return!t||t.strm!==e||t.mode<xn||t.mode>16211?1:0},Wn=e=>{if(Ln(e))return Tn;const t=e.state;return e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=xn,t.last=0,t.havedict=0,t.flags=-1,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new Int32Array(852),t.distcode=t.distdyn=new Int32Array(592),t.sane=1,t.back=-1,Pn},qn=e=>{if(Ln(e))return Tn;const t=e.state;return t.wsize=0,t.whave=0,t.wnext=0,Wn(e)},zn=(e,t)=>{let i;if(Ln(e))return Tn;const r=e.state;return t<0?(i=0,t=-t):(i=5+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?Tn:(null!==r.window&&r.wbits!==t&&(r.window=null),r.wrap=i,r.wbits=t,qn(e))},Hn=(e,t)=>{if(!e)return Tn;const i=new $n;e.state=i,i.strm=e,i.window=null,i.mode=xn;const r=zn(e,t);return r!==Pn&&(e.state=null),r};let Jn,Yn,jn=!0;const Kn=e=>{if(jn){Jn=new Int32Array(512),Yn=new Int32Array(32);let t=0;for(;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(_n(1,e.lens,0,288,Jn,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;_n(2,e.lens,0,32,Yn,0,e.work,{bits:5}),jn=!1}e.lencode=Jn,e.lenbits=9,e.distcode=Yn,e.distbits=5},Zn=(e,t,i,r)=>{let s;const n=e.state;return null===n.window&&(n.wsize=1<<n.wbits,n.wnext=0,n.whave=0,n.window=new Uint8Array(n.wsize)),r>=n.wsize?(n.window.set(t.subarray(i-n.wsize,i),0),n.wnext=0,n.whave=n.wsize):(s=n.wsize-n.wnext,s>r&&(s=r),n.window.set(t.subarray(i-r,i-r+s),n.wnext),(r-=s)?(n.window.set(t.subarray(i-r,i),0),n.wnext=r,n.whave=n.wsize):(n.wnext+=s,n.wnext===n.wsize&&(n.wnext=0),n.whave<n.wsize&&(n.whave+=s))),0};var Gn={inflateReset:qn,inflateReset2:zn,inflateResetKeep:Wn,inflateInit:e=>Hn(e,15),inflateInit2:Hn,inflate:(e,t)=>{let i,r,s,n,o,a,l,d,c,h,u,p,g,m,f,b,_,v,w,y,P,A,S=0;const T=new Uint8Array(4);let C,E;const R=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(Ln(e)||!e.output||!e.input&&0!==e.avail_in)return Tn;i=e.state,i.mode===On&&(i.mode=In),o=e.next_out,s=e.output,l=e.avail_out,n=e.next_in,r=e.input,a=e.avail_in,d=i.hold,c=i.bits,h=a,u=l,A=Pn;e:for(;;)switch(i.mode){case xn:if(0===i.wrap){i.mode=In;break}for(;c<16;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}if(2&i.wrap&&35615===d){0===i.wbits&&(i.wbits=15),i.check=0,T[0]=255&d,T[1]=d>>>8&255,i.check=Hr(i.check,T,2,0),d=0,c=0,i.mode=16181;break}if(i.head&&(i.head.done=!1),!(1&i.wrap)||(((255&d)<<8)+(d>>8))%31){e.msg="incorrect header check",i.mode=Bn;break}if((15&d)!==kn){e.msg="unknown compression method",i.mode=Bn;break}if(d>>>=4,c-=4,P=8+(15&d),0===i.wbits&&(i.wbits=P),P>15||P>i.wbits){e.msg="invalid window size",i.mode=Bn;break}i.dmax=1<<i.wbits,i.flags=0,e.adler=i.check=1,i.mode=512&d?16189:On,d=0,c=0;break;case 16181:for(;c<16;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}if(i.flags=d,(255&i.flags)!==kn){e.msg="unknown compression method",i.mode=Bn;break}if(57344&i.flags){e.msg="unknown header flags set",i.mode=Bn;break}i.head&&(i.head.text=d>>8&1),512&i.flags&&4&i.wrap&&(T[0]=255&d,T[1]=d>>>8&255,i.check=Hr(i.check,T,2,0)),d=0,c=0,i.mode=16182;case 16182:for(;c<32;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}i.head&&(i.head.time=d),512&i.flags&&4&i.wrap&&(T[0]=255&d,T[1]=d>>>8&255,T[2]=d>>>16&255,T[3]=d>>>24&255,i.check=Hr(i.check,T,4,0)),d=0,c=0,i.mode=16183;case 16183:for(;c<16;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}i.head&&(i.head.xflags=255&d,i.head.os=d>>8),512&i.flags&&4&i.wrap&&(T[0]=255&d,T[1]=d>>>8&255,i.check=Hr(i.check,T,2,0)),d=0,c=0,i.mode=16184;case 16184:if(1024&i.flags){for(;c<16;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}i.length=d,i.head&&(i.head.extra_len=d),512&i.flags&&4&i.wrap&&(T[0]=255&d,T[1]=d>>>8&255,i.check=Hr(i.check,T,2,0)),d=0,c=0}else i.head&&(i.head.extra=null);i.mode=16185;case 16185:if(1024&i.flags&&(p=i.length,p>a&&(p=a),p&&(i.head&&(P=i.head.extra_len-i.length,i.head.extra||(i.head.extra=new Uint8Array(i.head.extra_len)),i.head.extra.set(r.subarray(n,n+p),P)),512&i.flags&&4&i.wrap&&(i.check=Hr(i.check,r,p,n)),a-=p,n+=p,i.length-=p),i.length))break e;i.length=0,i.mode=16186;case 16186:if(2048&i.flags){if(0===a)break e;p=0;do{P=r[n+p++],i.head&&P&&i.length<65536&&(i.head.name+=String.fromCharCode(P))}while(P&&p<a);if(512&i.flags&&4&i.wrap&&(i.check=Hr(i.check,r,p,n)),a-=p,n+=p,P)break e}else i.head&&(i.head.name=null);i.length=0,i.mode=16187;case 16187:if(4096&i.flags){if(0===a)break e;p=0;do{P=r[n+p++],i.head&&P&&i.length<65536&&(i.head.comment+=String.fromCharCode(P))}while(P&&p<a);if(512&i.flags&&4&i.wrap&&(i.check=Hr(i.check,r,p,n)),a-=p,n+=p,P)break e}else i.head&&(i.head.comment=null);i.mode=16188;case 16188:if(512&i.flags){for(;c<16;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}if(4&i.wrap&&d!==(65535&i.check)){e.msg="header crc mismatch",i.mode=Bn;break}d=0,c=0}i.head&&(i.head.hcrc=i.flags>>9&1,i.head.done=!0),e.adler=i.check=0,i.mode=On;break;case 16189:for(;c<32;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}e.adler=i.check=Un(d),d=0,c=0,i.mode=Nn;case Nn:if(0===i.havedict)return e.next_out=o,e.avail_out=l,e.next_in=n,e.avail_in=a,i.hold=d,i.bits=c,Sn;e.adler=i.check=1,i.mode=On;case On:if(t===wn||t===yn)break e;case In:if(i.last){d>>>=7&c,c-=7&c,i.mode=Fn;break}for(;c<3;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}switch(i.last=1&d,d>>>=1,c-=1,3&d){case 0:i.mode=16193;break;case 1:if(Kn(i),i.mode=Vn,t===yn){d>>>=2,c-=2;break e}break;case 2:i.mode=16196;break;case 3:e.msg="invalid block type",i.mode=Bn}d>>>=2,c-=2;break;case 16193:for(d>>>=7&c,c-=7&c;c<32;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}if((65535&d)!=(d>>>16^65535)){e.msg="invalid stored block lengths",i.mode=Bn;break}if(i.length=65535&d,d=0,c=0,i.mode=Mn,t===yn)break e;case Mn:i.mode=16195;case 16195:if(p=i.length,p){if(p>a&&(p=a),p>l&&(p=l),0===p)break e;s.set(r.subarray(n,n+p),o),a-=p,n+=p,l-=p,o+=p,i.length-=p;break}i.mode=On;break;case 16196:for(;c<14;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}if(i.nlen=257+(31&d),d>>>=5,c-=5,i.ndist=1+(31&d),d>>>=5,c-=5,i.ncode=4+(15&d),d>>>=4,c-=4,i.nlen>286||i.ndist>30){e.msg="too many length or distance symbols",i.mode=Bn;break}i.have=0,i.mode=16197;case 16197:for(;i.have<i.ncode;){for(;c<3;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}i.lens[R[i.have++]]=7&d,d>>>=3,c-=3}for(;i.have<19;)i.lens[R[i.have++]]=0;if(i.lencode=i.lendyn,i.lenbits=7,C={bits:i.lenbits},A=_n(0,i.lens,0,19,i.lencode,0,i.work,C),i.lenbits=C.bits,A){e.msg="invalid code lengths set",i.mode=Bn;break}i.have=0,i.mode=16198;case 16198:for(;i.have<i.nlen+i.ndist;){for(;S=i.lencode[d&(1<<i.lenbits)-1],f=S>>>24,b=S>>>16&255,_=65535&S,!(f<=c);){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}if(_<16)d>>>=f,c-=f,i.lens[i.have++]=_;else{if(16===_){for(E=f+2;c<E;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}if(d>>>=f,c-=f,0===i.have){e.msg="invalid bit length repeat",i.mode=Bn;break}P=i.lens[i.have-1],p=3+(3&d),d>>>=2,c-=2}else if(17===_){for(E=f+3;c<E;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}d>>>=f,c-=f,P=0,p=3+(7&d),d>>>=3,c-=3}else{for(E=f+7;c<E;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}d>>>=f,c-=f,P=0,p=11+(127&d),d>>>=7,c-=7}if(i.have+p>i.nlen+i.ndist){e.msg="invalid bit length repeat",i.mode=Bn;break}for(;p--;)i.lens[i.have++]=P}}if(i.mode===Bn)break;if(0===i.lens[256]){e.msg="invalid code -- missing end-of-block",i.mode=Bn;break}if(i.lenbits=9,C={bits:i.lenbits},A=_n(1,i.lens,0,i.nlen,i.lencode,0,i.work,C),i.lenbits=C.bits,A){e.msg="invalid literal/lengths set",i.mode=Bn;break}if(i.distbits=6,i.distcode=i.distdyn,C={bits:i.distbits},A=_n(2,i.lens,i.nlen,i.ndist,i.distcode,0,i.work,C),i.distbits=C.bits,A){e.msg="invalid distances set",i.mode=Bn;break}if(i.mode=Vn,t===yn)break e;case Vn:i.mode=Dn;case Dn:if(a>=6&&l>=258){e.next_out=o,e.avail_out=l,e.next_in=n,e.avail_in=a,i.hold=d,i.bits=c,un(e,u),o=e.next_out,s=e.output,l=e.avail_out,n=e.next_in,r=e.input,a=e.avail_in,d=i.hold,c=i.bits,i.mode===On&&(i.back=-1);break}for(i.back=0;S=i.lencode[d&(1<<i.lenbits)-1],f=S>>>24,b=S>>>16&255,_=65535&S,!(f<=c);){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}if(b&&0==(240&b)){for(v=f,w=b,y=_;S=i.lencode[y+((d&(1<<v+w)-1)>>v)],f=S>>>24,b=S>>>16&255,_=65535&S,!(v+f<=c);){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}d>>>=v,c-=v,i.back+=v}if(d>>>=f,c-=f,i.back+=f,i.length=_,0===b){i.mode=16205;break}if(32&b){i.back=-1,i.mode=On;break}if(64&b){e.msg="invalid literal/length code",i.mode=Bn;break}i.extra=15&b,i.mode=16201;case 16201:if(i.extra){for(E=i.extra;c<E;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}i.length+=d&(1<<i.extra)-1,d>>>=i.extra,c-=i.extra,i.back+=i.extra}i.was=i.length,i.mode=16202;case 16202:for(;S=i.distcode[d&(1<<i.distbits)-1],f=S>>>24,b=S>>>16&255,_=65535&S,!(f<=c);){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}if(0==(240&b)){for(v=f,w=b,y=_;S=i.distcode[y+((d&(1<<v+w)-1)>>v)],f=S>>>24,b=S>>>16&255,_=65535&S,!(v+f<=c);){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}d>>>=v,c-=v,i.back+=v}if(d>>>=f,c-=f,i.back+=f,64&b){e.msg="invalid distance code",i.mode=Bn;break}i.offset=_,i.extra=15&b,i.mode=16203;case 16203:if(i.extra){for(E=i.extra;c<E;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}i.offset+=d&(1<<i.extra)-1,d>>>=i.extra,c-=i.extra,i.back+=i.extra}if(i.offset>i.dmax){e.msg="invalid distance too far back",i.mode=Bn;break}i.mode=16204;case 16204:if(0===l)break e;if(p=u-l,i.offset>p){if(p=i.offset-p,p>i.whave&&i.sane){e.msg="invalid distance too far back",i.mode=Bn;break}p>i.wnext?(p-=i.wnext,g=i.wsize-p):g=i.wnext-p,p>i.length&&(p=i.length),m=i.window}else m=s,g=o-i.offset,p=i.length;p>l&&(p=l),l-=p,i.length-=p;do{s[o++]=m[g++]}while(--p);0===i.length&&(i.mode=Dn);break;case 16205:if(0===l)break e;s[o++]=i.length,l--,i.mode=Dn;break;case Fn:if(i.wrap){for(;c<32;){if(0===a)break e;a--,d|=r[n++]<<c,c+=8}if(u-=l,e.total_out+=u,i.total+=u,4&i.wrap&&u&&(e.adler=i.check=i.flags?Hr(i.check,s,u,o-u):qr(i.check,s,u,o-u)),u=l,4&i.wrap&&(i.flags?d:Un(d))!==i.check){e.msg="incorrect data check",i.mode=Bn;break}d=0,c=0}i.mode=16207;case 16207:if(i.wrap&&i.flags){for(;c<32;){if(0===a)break e;a--,d+=r[n++]<<c,c+=8}if(4&i.wrap&&d!==(4294967295&i.total)){e.msg="incorrect length check",i.mode=Bn;break}d=0,c=0}i.mode=16208;case 16208:A=An;break e;case Bn:A=Cn;break e;case 16210:return En;default:return Tn}return e.next_out=o,e.avail_out=l,e.next_in=n,e.avail_in=a,i.hold=d,i.bits=c,(i.wsize||u!==e.avail_out&&i.mode<Bn&&(i.mode<Fn||t!==vn))&&Zn(e,e.output,e.next_out,u-e.avail_out),h-=e.avail_in,u-=e.avail_out,e.total_in+=h,e.total_out+=u,i.total+=u,4&i.wrap&&u&&(e.adler=i.check=i.flags?Hr(i.check,s,u,e.next_out-u):qr(i.check,s,u,e.next_out-u)),e.data_type=i.bits+(i.last?64:0)+(i.mode===On?128:0)+(i.mode===Vn||i.mode===Mn?256:0),(0===h&&0===u||t===vn)&&A===Pn&&(A=Rn),A},inflateEnd:e=>{if(Ln(e))return Tn;let t=e.state;return t.window&&(t.window=null),e.state=null,Pn},inflateGetHeader:(e,t)=>{if(Ln(e))return Tn;const i=e.state;return 0==(2&i.wrap)?Tn:(i.head=t,t.done=!1,Pn)},inflateSetDictionary:(e,t)=>{const i=t.length;let r,s,n;return Ln(e)?Tn:(r=e.state,0!==r.wrap&&r.mode!==Nn?Tn:r.mode===Nn&&(s=1,s=qr(s,t,i,0),s!==r.check)?Cn:(n=Zn(e,t,i,i),n?(r.mode=16210,En):(r.havedict=1,Pn)))},inflateInfo:"pako inflate (from Nodeca project)"};var Xn=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1};const Qn=Object.prototype.toString,{Z_NO_FLUSH:eo,Z_FINISH:to,Z_OK:io,Z_STREAM_END:ro,Z_NEED_DICT:so,Z_STREAM_ERROR:no,Z_DATA_ERROR:oo,Z_MEM_ERROR:ao}=Yr;function lo(e){this.options=Js.assign({chunkSize:65536,windowBits:15,to:""},e||{});const t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&0==(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Zs,this.strm.avail_out=0;let i=Gn.inflateInit2(this.strm,t.windowBits);if(i!==io)throw new Error(Jr[i]);if(this.header=new Xn,Gn.inflateGetHeader(this.strm,this.header),t.dictionary&&("string"==typeof t.dictionary?t.dictionary=Ks.string2buf(t.dictionary):"[object ArrayBuffer]"===Qn.call(t.dictionary)&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw&&(i=Gn.inflateSetDictionary(this.strm,t.dictionary),i!==io)))throw new Error(Jr[i])}function co(e,t){const i=new lo(t);if(i.push(e),i.err)throw i.msg||Jr[i.err];return i.result}lo.prototype.push=function(e,t){const i=this.strm,r=this.options.chunkSize,s=this.options.dictionary;let n,o,a;if(this.ended)return!1;for(o=t===~~t?t:!0===t?to:eo,"[object ArrayBuffer]"===Qn.call(e)?i.input=new Uint8Array(e):i.input=e,i.next_in=0,i.avail_in=i.input.length;;){for(0===i.avail_out&&(i.output=new Uint8Array(r),i.next_out=0,i.avail_out=r),n=Gn.inflate(i,o),n===so&&s&&(n=Gn.inflateSetDictionary(i,s),n===io?n=Gn.inflate(i,o):n===oo&&(n=so));i.avail_in>0&&n===ro&&i.state.wrap>0&&0!==e[i.next_in];)Gn.inflateReset(i),n=Gn.inflate(i,o);switch(n){case no:case oo:case so:case ao:return this.onEnd(n),this.ended=!0,!1}if(a=i.avail_out,i.next_out&&(0===i.avail_out||n===ro))if("string"===this.options.to){let e=Ks.utf8border(i.output,i.next_out),t=i.next_out-e,s=Ks.buf2string(i.output,e);i.next_out=t,i.avail_out=r-t,t&&i.output.set(i.output.subarray(e,e+t),0),this.onData(s)}else this.onData(i.output.length===i.next_out?i.output:i.output.subarray(0,i.next_out));if(n!==io||0!==a){if(n===ro)return n=Gn.inflateEnd(this.strm),this.onEnd(n),this.ended=!0,!0;if(0===i.avail_in)break}}return!0},lo.prototype.onData=function(e){this.chunks.push(e)},lo.prototype.onEnd=function(e){e===io&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=Js.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var ho={Inflate:lo,inflate:co,inflateRaw:function(e,t){return(t=t||{}).raw=!0,co(e,t)},ungzip:co,constants:Yr};const{Deflate:uo,deflate:po,deflateRaw:go,gzip:mo}=cn,{Inflate:fo,inflate:bo,inflateRaw:_o,ungzip:vo}=ho;var wo={Deflate:uo,deflate:po,deflateRaw:go,gzip:mo,Inflate:fo,inflate:bo,inflateRaw:_o,ungzip:vo,constants:Yr};class yo{constructor(e=globalThis.HYPRPlacementController,t=HYPRRequestParameterManager){this.placementController=e,this.requestParameterManager=t}getSessionToken(){const e=JSON.stringify(this.requestParameterManager.getBiddingSessionParams()),t=wo.deflate(e),i=sr.fromUint8Array(t);return`2/${encodeURI(i)}`}async loadBid(e,t){console.debug(`[BC] loadBid started for placement: ${e}`);const i=this.placementController.getPlacement(e),{success:r,error:s}=await i.loadBidResponse(t);return console.debug(`[BC] loadBid completed with status: ${r}, error: ${s}`),r}}const Po="webview",Ao="native";class So extends ie{constructor(e,t,i,r,s,n,o=globalThis.HYPRNetworkController,a=globalThis.HYPRTimerController,l=globalThis.consentController,d=HYPRRequestParameterManager){super(e,t,i,r,s,o,a,l,d),this.rtbParameterBuilder=n,this.rtbClickMode=Po}getAdType(){return m}}function To(){let e=(new Date).getTime(),t="undefined"!=typeof performance&&performance.now&&1e3*performance.now()||0;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(i=>{let r=16*Math.random();return e>0?(r=(e+r)%16|0,e=Math.floor(e/16)):(r=(t+r)%16|0,t=Math.floor(t/16)),("x"===i?r:3&r|8).toString(16)}))}function Co(e){let t;switch(e.getConnectionType()){case"Ethernet":t=1;break;case"WIFI":t=2;break;case"NR":t=3;break;case"CDMA":case"CDMA - EvDo rev. 0":case"CDMA - EvDo rev. A":case"CDMA - EvDo rev. B":case"CDMA - 1xRTT":case"CDMA - eHRPD":case"EDGE":t=4;break;case"GPRS":case"UMTS":case"HSPA":case"HSDPA":case"HSPA+":case"HSUPA":case"GSM":case"3G":t=5;break;case"LTE_CA":case"LTE":t=6;break;default:t=0}const i={connectiontype:t};if(void 0===e.getCarriers())return i;const r=JSON.parse(e.getCarriers()),s=Object.keys(r).sort();if(s.length>0){const e=r[s[0]];i.carrier=e.carrier_name,i.mccmnc=`${e.mobile_country_code},${e.mobile_network_code}`}return i}class Eo{static addStatus(e={}){if("object"==typeof HYPRTransparencyTrackingManager){const t=HYPRTransparencyTrackingManager.getTrackingPermissionStatus();console.debug(`TransparencyTracking received status of ${t}`),e.privacy_tracking_status=t}return e}static canPrompt(e={}){const t="object"==typeof HYPRTransparencyTrackingManager&&void 0!==e.enable_tracking_prompt&&e.enable_tracking_prompt&&"not_determined"===HYPRTransparencyTrackingManager.getTrackingPermissionStatus();return console.debug(`TransparencyTracking.canPrompt result: ${t}`),t}static prompt(e){if(void 0===e||e.length<1)throw new Error("No callback supplied to Transparency Tracking prompt");console.debug("TransparencyTracking will present prompt to user."),HYPRTransparencyTrackingManager.promptForTrackingPermissionWithCallback(e)}}const Ro={context:"context",distributor_id:"distributor_id",uid:"uid",bundle_id:"bundle_id",bundle_version:"bundle_version",ats_settings:"ats_settings",supported_interface_settings:"supported_interface_settings",xcode_version:"xcode_version",mobile_js_version:"mobile_js_version",identifier_for_vendor:"identifier_for_vendor",permissions:"permissions",msdkv:"msdkv",sdk_version:"sdk_version",device_type:"device_type",device_model:"device_model",device_os_version:"device_os_version",connection_type:"connection_type",carrier_data:"carrier_data",persistent_id:"persistent_id",preloaded_mraid_ads:"preloaded_mraid_ads",low_power_enabled:"low_power_enabled",privacy_tracking_status:"privacy_tracking_status",screen_traits:"screen_traits",supports_multiple_scenes:"supports_multiple_scenes",maccatalyst:"maccatalyst",hypr_modules:"hypr_modules",HYPRPermissions:"HYPRPermissions",app_bound_domains:"app_bound_domains",ios_app_on_mac:"ios_app_on_mac",device_width:"device_width",device_height:"device_height",pxratio:"pxratio",age_restricted_user:"age_restricted_user",ad_id_opted_out:"ad_id_opted_out",preloaded_ads:"preloaded_ads",cleartext_traffic_permitted:"cleartext_traffic_permitted",SKAdNetworkItems:"SKAdNetworkItems",platform:"platform",device_fingerprint:"device_fingerprint",device_product:"device_product",device_manufacturer:"device_manufacturer",device_brand:"device_brand",device:"device",supports_multiwindow:"supports_multiwindow",consent:"consent",android_id:"android_id",gaid:"gaid",target_sdk_version:"target_sdk_version",placement_id:"placement_id",test:"test"},ko="hypr_uuid";class xo{constructor(e,t=globalThis.consentController,i=HYPRRequestParamListener,r=hyprMXLocalStorage){this.consentController=t,this.uid=void 0,this.distributorID=void 0,this.nativeParams=i,this.preloadController=void 0,this.localStorage=r,console.debug(`[RPM] User Extras: ${JSON.stringify(this.getUserExtras())}`),console.debug(`[RPM] User Extras(UID2): ${this.getUserExtra("UID2")}`)}static APP_USER_TOKEN="";setPreloadController(e){this.preloadController=e}getInitializationParams(){let e=this.baseParameters();return e=Object.assign(e,JSON.parse(this.nativeParams.getUnityParams())),e=Object.assign(e,JSON.parse(this.nativeParams.getMediationParams())),delete e.persistent_id,delete e.ad_id_opted_out,delete e.gaid,delete e.age_restricted_user,U.compact(e)}getErrorParams(){return U.compact(this.baseParameters())}getTrackingParams(){return U.compact(this.baseParameters())}getCatalogFrameParams(){return U.compact(this.baseParameters())}getInventoryCheckParams(e={}){const t=this.baseParameters();return t[Ro.preloaded_mraid_ads]=this.preloadController?.getPreloadedMraidAds(),void 0===e.context&&(t.context="inventoryCheck"),U.compact(Object.assign(t,e))}getBaseRtbParams(){return U.compact({test:this.nativeParams.isTestModeEnabled?.()?1:0,app:this.buildApp(),device:this.buildDevice(),user:this.buildUser()})}baseParameters(){let e={};return e=Object.assign(e,this.buildSegment()),e=Object.assign(e,this.deviceParams()),e=Object.assign(e,this.cachedAppParams()),Eo.addStatus(e),e[Ro.test]=this.nativeParams.isTestModeEnabled?.()?1:0,e[Ro.uid]=this.getUserId(),e}getUserId(){return void 0!==this.uid||(this.uid=this.localStorage.getItem(ko),void 0!==this.uid&&null!==this.uid&&"null"!==this.uid.toLowerCase()||(this.uid=To(),this.localStorage.setItem(ko,this.uid))),this.uid}getUserExtras(){return"function"==typeof this.nativeParams.getUserExtras&&console.debug("[RPM] getUserExtras exists"),JSON.parse(this.nativeParams.getUserExtras?.()||"{}")}getUserExtra(e){return this.nativeParams.getUserExtra?.(e)||void 0}getDistributorID(){return void 0!==this.distributorID||(this.distributorID=this.nativeParams.getDistributorID()),this.distributorID}getSDKVersion(){return void 0!==this.sdkVersion||(this.sdkVersion=this.nativeParams.getSDKVersion()),this.sdkVersion}buildUser(){return{id:this.getUserId(),data:[{segment:[this.buildSegment(!0)]}]}}buildApp(){const e=l(this.cachedAppParams());return{ver:e[Ro.bundle_version],bundle:e[Ro.bundle_id]}}buildSegment(e=!1){const t=l(this.cachedSegmentParams());t[Ro.consent]=this.consentController.getConsentParameters(),t[Ro.cleartext_traffic_permitted]=this.nativeParams.getClearTextPermitted(),t[Ro.age_restricted_user]=this.nativeParams.getIsAgeRestrictedUser(),t[Ro.low_power_enabled]=this.nativeParams.getIsLowPowerEnabled(),t[Ro.privacy_tracking_status]=this.nativeParams.getPrivacyTrackingStatus(),t[Ro.connection_type]=this.nativeParams.getConnectionType(),t[Ro.android_id]=this.nativeParams.getAndroidId();const i=parseFloat(this.nativeParams.getTargetSDKVersion().toFixed(1));return i>0&&(t[Ro.target_sdk_version]=i),this.nativeParams.getIsAgeRestrictedUser()||"function"!=typeof this.nativeParams.getGAID||(t[Ro.gaid]=this.nativeParams.getGAID()),e&&(t[Ro.carrier_data]=JSON.parse(this.nativeParams.getCarriers())),t[Ro.screen_traits]=this.nativeParams.getScreenTraits(),t}cachedSegmentParams(){if(void 0!==this.cachedSegment)return this.cachedSegment;const e={};return e[Ro.sdk_version]=this.nativeParams.getSDKVersion(),e[Ro.supported_interface_settings]=this.nativeParams.getSupportedInterfaceSettings(),e[Ro.msdkv]=this.nativeParams.getMSDKV(),e[Ro.ats_settings]=this.nativeParams.getATSSettings(),e[Ro.maccatalyst]=this.nativeParams.getMacCatalyst(),e[Ro.ios_app_on_mac]=this.nativeParams.getIOSAppOnMac(),e[Ro.identifier_for_vendor]=this.nativeParams.getIdentifierForVendor(),e[Ro.xcode_version]=this.nativeParams.getXcodeVersion(),e[Ro.supports_multiple_scenes]=this.nativeParams.getSupportsMultipleScenes(),e[Ro.supports_multiwindow]=this.nativeParams.getSupportsMultipleWindow(),e[Ro.permissions]=JSON.parse(this.nativeParams.getPermissions()),e[Ro.distributor_id]=this.nativeParams.getDistributorID(),e[Ro.mobile_js_version]=globalThis.version(),e[Ro.device_type]=this.nativeParams.getDeviceType(),e[Ro.SKAdNetworkItems]=this.nativeParams.getSKAdNetworkItems(),this.cachedSegment=e,this.cachedSegment}buildDevice(){const e=l(this.cachedDeviceParams()),t={device_fingerprint:e[Ro.device_fingerprint],device_product:e[Ro.device_product]};Object.assign(t,Pe());const i={model:e[Ro.device_model],os:e[Ro.platform],osv:e[Ro.device_os_version],h:e[Ro.device_height],w:e[Ro.device_width],pxratio:e[Ro.pxratio],make:e[Ro.device_brand],ext:t};return this.nativeParams.getIsAgeRestrictedUser()||(i.lmt=this.formatLimitAdTracking(this.nativeParams.getAdIdOptedOut()),i.ifa=this.nativeParams.getPersistentID()),Object.assign(i,this.rtbDeviceCarrierData())}deviceParams(){const e=l(this.cachedDeviceParams());return this.nativeParams.getIsAgeRestrictedUser()||(e[Ro.persistent_id]=this.nativeParams.getPersistentID(),"function"==typeof this.nativeParams.getGAID&&(e[Ro.gaid]=this.nativeParams.getGAID()),e[Ro.ad_id_opted_out]=this.nativeParams.getAdIdOptedOut()),e[Ro.carrier_data]=JSON.parse(this.nativeParams.getCarriers()),e}cachedDeviceParams(){if(void 0!==this.cachedDevice)return this.cachedDevice;const e={};return e[Ro.device_model]=this.nativeParams.getDeviceModel(),e[Ro.device_os_version]=this.nativeParams.getOSVersion(),e[Ro.device_height]=this.nativeParams.getDeviceHeight(),e[Ro.device_width]=this.nativeParams.getDeviceWidth(),e[Ro.pxratio]=this.nativeParams.getPxRatio(),e[Ro.device_product]=this.nativeParams.getDeviceProduct(),e[Ro.device_fingerprint]=this.nativeParams.getDeviceFingerPrint(),e[Ro.device_brand]=this.nativeParams.getDeviceBrand(),e[Ro.device]=this.nativeParams.getDevice(),e[Ro.platform]=this.nativeParams.getPlatform(),e[Ro.device_manufacturer]=this.nativeParams.getDeviceManufacturer(),this.cachedDevice=e,this.cachedDevice}cachedAppParams(){if(void 0!==this.cachedApp)return this.cachedApp;const e={};return e[Ro.bundle_id]=this.nativeParams.getBundleID(),e[Ro.bundle_version]=this.nativeParams.getBundleVersion(),this.cachedApp=e,this.cachedApp}getBiddingSessionParams(){return{ad_id_opted_out:this.nativeParams.getAdIdOptedOut(),age_restricted_user:this.nativeParams.getIsAgeRestrictedUser(),carrier_data:JSON.parse(this.nativeParams.getCarriers()),connection_type:this.nativeParams.getConnectionType(),consent:this.consentController.getConsentParameters(),low_power_enabled:this.nativeParams.getIsLowPowerEnabled(),privacy_tracking_status:this.nativeParams.getPrivacyTrackingStatus(),screen_traits:this.nativeParams.getScreenTraits(),app_user_token:xo.APP_USER_TOKEN,test:this.nativeParams.isTestModeEnabled?.()?1:0}}formatLimitAdTracking(e){return e?1:0}rtbDeviceCarrierData(){let e;switch(this.nativeParams.getConnectionType()){case"Ethernet":e=1;break;case"WIFI":e=2;break;case"NR":e=3;break;case"CDMA":case"CDMA - EvDo rev. 0":case"CDMA - EvDo rev. A":case"CDMA - EvDo rev. B":case"CDMA - 1xRTT":case"CDMA - eHRPD":case"EDGE":e=4;break;case"GPRS":case"UMTS":case"HSPA":case"HSDPA":case"HSPA+":case"HSUPA":case"GSM":case"3G":e=5;break;case"LTE_CA":case"LTE":e=6;break;default:e=0}const t={connectiontype:e};if(void 0===this.nativeParams.getCarriers())return t;const i=JSON.parse(this.nativeParams.getCarriers()),r=Object.keys(i).sort();if(r.length>0){const e=i[r[0]];t.carrier=e.carrier_name,t.mccmnc=`${e.mobile_country_code},${e.mobile_network_code}`}return t}}globalThis.RequestParameterManager=xo;const No={OPTED_OUT:"1YY-",NOT_OPTED_OUT:"1YN-"};function Oo(e={},t={}){const i=c(l(e),l(t)),r=function(e={},t=HYPRRequestParamListener){const i={...l(e),ver:t.getBundleVersion()};return()=>i}(i.app),s=function(e={},t=HYPRRequestParamListener,i=Pe){const r={...l(e),model:t.getDeviceModel(),os:t.getPlatform(),osv:t.getOSVersion(),h:t.getDeviceHeight(),w:t.getDeviceWidth(),pxratio:t.getPxRatio(),make:t.getDeviceBrand()};r.ext=r.ext||{},r.ext.hyprmx={...r.ext.hyprmx,device_fingerprint:t.getDeviceFingerPrint(),device_product:t.getDeviceProduct()};let s=JSON.stringify(r);return()=>{const e={...JSON.parse(s),...Co(t)};return t.getIsAgeRestrictedUser()?(delete e.lmt,delete e.ifa):(e.lmt=t.getAdIdOptedOut()?1:0,e.ifa=t.getPersistentID()),e.ext.hyprmx={...e.ext.hyprmx,...i()},s=JSON.stringify(e),e}}(i.device),n=function(e={},t=HYPRRequestParamListener,i=globalThis.consentController,r=HYPRRequestParameterManager){const s={};s[Ro.sdk_version]=t.getSDKVersion(),s[Ro.supported_interface_settings]=t.getSupportedInterfaceSettings(),s[Ro.msdkv]=t.getMSDKV(),s[Ro.ats_settings]=t.getATSSettings(),s[Ro.maccatalyst]=t.getMacCatalyst(),s[Ro.ios_app_on_mac]=t.getIOSAppOnMac(),s[Ro.identifier_for_vendor]=t.getIdentifierForVendor(),s[Ro.xcode_version]=t.getXcodeVersion(),s[Ro.supports_multiple_scenes]=t.getSupportsMultipleScenes(),s[Ro.supports_multiwindow]=t.getSupportsMultipleWindow(),s[Ro.permissions]=JSON.parse(t.getPermissions()),s[Ro.distributor_id]=t.getDistributorID(),s[Ro.mobile_js_version]=globalThis.version(),s[Ro.device_type]=t.getDeviceType(),s[Ro.SKAdNetworkItems]=t.getSKAdNetworkItems(),s[Ro.age_restricted_user]=t.getIsAgeRestrictedUser(),s[Ro.android_id]=t.getAndroidId();const n=parseFloat(t.getTargetSDKVersion().toFixed(1));n>0&&(s[Ro.target_sdk_version]=n);const o=JSON.stringify(s),a=r.getUserId(),d=l(e);return e=>{const r=JSON.parse(o);return r[Ro.consent]=i.getConsentParameters(),r[Ro.low_power_enabled]=t.getIsLowPowerEnabled(),r[Ro.privacy_tracking_status]=t.getPrivacyTrackingStatus(),r[Ro.connection_type]=t.getConnectionType(),r[Ro.carrier_data]=JSON.parse(t.getCarriers()),r[Ro.screen_traits]=t.getScreenTraits(),r[Ro.age_restricted_user]=t.getIsAgeRestrictedUser(),r[Ro.placement_id]=e,t.getIsAgeRestrictedUser()||"function"!=typeof t.getGAID?delete r[Ro.gaid]:r[Ro.gaid]=t.getGAID(),{...d,id:a,data:[{segment:[{id:"hyprmx",ext:r}]}]}}}(i.user),o=function(e=[{}],t=HYPRRequestParamListener){const i=[{...e?.[0]}];return i[0].banner={...i[0].banner},(e,r)=>{const s=l(i);return s[0].id=e,s[0].banner.h=r.definedSize.height,s[0].banner.w=r.definedSize.width,s[0].secure=!1===t.getClearTextPermitted()?1:0,s}}(t.imp),a=function(e={},t=globalThis.consentController,i=HYPRRequestParamListener){return()=>{const r=t.consentStatus===S.CONSENT_DECLINED?No.OPTED_OUT:No.NOT_OPTED_OUT,s=e.coppa|(i.getIsAgeRestrictedUser()?1:0),n=l(e);return n.us_privacy=r,n.coppa=s,n.ext=n.ext?n.ext:{},n.ext.us_privacy=r,n.ext.coppa=s,n}}(i.regs),d=function(e={},t=globalThis.OMIDPN,i=globalThis.OMIDPV){return()=>{const r=l(e);return console.debug(`[RS] OMID source is ${JSON.stringify(r)} with ${t} and ${i}`),void 0===t||void 0===i?(console.debug("[RS] OMID not defined.  Skipping OMID source."),r):(r.ext=r.ext?r.ext:{},r.ext.omidpn=t,r.ext.omidpv=i,console.debug(`[RS] OMID source set to ${JSON.stringify(r)}`),r)}}(i.source);return delete i.app,delete i.device,delete i.user,delete i.imp,delete i.regs,delete i.source,function(e,t,i,r,s,n,o){return(a,l)=>{const d=To();return{id:d,...e,app:t(),device:i(),user:r(l),imp:s(d,a),regs:n(),source:o()}}}(i,r,s,n,o,a,d)}globalThis.BASE_RTB_REQUEST={};class Io{static fromObject(e,t,i=globalThis.HYPRNetworkController,r=globalThis.HYPRTimerController){return o(te.BANNER,e.type)?new So(e.id,e.name,e.type,e.inventory_check_url,t,Oo(globalThis.BASE_RTB_REQUEST,e.placement_rtb_request),i,r):o(te.REWARDED,e.type)||o(te.INTERSTITIAL,e.type)?new Pi(e.id,e.name,e.type,e.inventory_check_url,t,e.cancellation_dialog,i,r):(console.error(`PlacementFactory received unknown placement type ${e.type}`),new ie(e.id,e.name,e.type,e.inventory_check_url,t,i,r))}static deepCopy(e){const t=new e.constructor;for(const i in e)t[i]=e[i];return t}}function Mo(e=(()=>{}),t=globalThis.initializationController?.deviceConfiguration){if(void 0===t)return void console.error("unable to detect device configuration");const{msdkv:i}=t;(t.isAndroid()&&i<=406||t.isIos&&i<=284)&&e()}class Vo{constructor(e,t=globalThis.consentController,i=globalThis.preloadController){if(this.placementMap=new Map,this.preloadController=i,console.debug(`[PC] Placement Controller initialized with ${JSON.stringify(e)}`),Array.isArray(e))for(const t of e)this.placementMap.set(t.name,Io.fromObject(t,this));else void 0!==e&&"object"==typeof e&&this.placementMap.set(e.name,Io.fromObject(e,this));t.setConsentChangedListener(this),globalThis.HYPRBiddingController=new yo(this)}getPlacements(){return this.placementMap}getBannerPlacements(){return[...this.placementMap.values()].filter((e=>e.isBannerAdType()))}getPlacement(e){const t=this.placementMap.get(e);return void 0!==t&&void 0===t.placementListener&&(t.placementListener=this),this.placementMap.get(e)}isAdAvailable(e){const t=this.getPlacement(e);return void 0===t?(console.debug(`[PC] PlacementController.isAdAvailable(${e}) reports undefined placement. Returning false.`),!1):t.isAdAvailable()}clearAllPlacementsAdStateAndTimers(){this.placementMap.forEach(((e,t,i)=>{e.clearAdAndStopAdAvailableTimer()}))}startAdRefresh(e,t=void 0){console.debug(`[PC] PlacementController - Start Ad Refresh for context: ${e} placementName: ${t}`);const i={context:e};void 0===t?this.placementMap.forEach((t=>{t.handleRefresh(e,i)})):this.getPlacement(t).handleRefresh(e,i)}async loadAd(e,t={}){const i=this.getPlacement(e);if(void 0===i)return console.debug("[PC] PlacementController loadAd reports undefined placement."),!1;const{success:r,error:s}=await i.loadAd(t);return this.onLoadAdResponse(i,s),r}handlePreloading(e){this.preloadController.handleMraidAdToPreload(e),this.preloadController.handleUIComponentsToPreloadResponse(e.ui_components)}onLoadAdResponse(e,t){void 0!==t?(console.debug(`PlacementController loadAd failure ${t}`),Mo((()=>{HYPRPlacementListener.onLoadAdFailure(e.name,t)}))):(Mo((()=>{HYPRPlacementListener.onLoadAdSuccess(e.name,e.isAdAvailable())})),this.handlePreloading(e))}onRefreshAdResponse(e,t){void 0===t&&this.handlePreloading(e)}onPlacementAdExpired(e){console.debug(`[PC] Ad expired for ${e}`),HYPRPlacementListener.onAdExpired(e.name)}adImpression(e){console.debug(`[PC] adImpression for ${e}`),HYPRPlacementListener.adImpression?.(e.name)}onConsentChanged(){console.debug("[PC] Consent status changed.  Clearing all ad state."),this.startAdRefresh(yi)}}globalThis.PlacementController=Vo;class Do{constructor(e=Date.now){this.timeReporter=e,this.duration=0,this.isActive=!1,this.startTime=0}start(){this.isActive||(this.isActive=!0,this.startTime=this.timeReporter())}pause(){this.isActive&&(this.isActive=!1,this.duration+=this.timeReporter()-this.startTime)}}class Fo{constructor(e,t=new Do,i=new Do,r=new Do,s=new Do){this.trackingUrl=e,this.startTimeStamp=void 0,this.timeDuration=0,this.loadingRecorder=t,this.loadingInBackgroundRecorder=i,this.onPageRecorder=r,this.onPageBackgroundRecorder=s,this.isLoading=!1,this.isOnPage=!1,this.inBackground=!1,this.reason=""}startPageLoadingTimer(e){this.isLoading=!0,this.inBackground=e,this.notifyTimers(this.inBackground,this.loadingRecorder,this.loadingInBackgroundRecorder)}stopPageLoadingTimer(e){this.isLoading&&(this.isLoading=!1,this.reason=e,this.loadingInBackgroundRecorder.pause(),this.loadingRecorder.pause())}startOnPageTimer(e){this.isOnPage=!0,this.inBackground=e,this.notifyTimers(e,this.onPageRecorder,this.onPageBackgroundRecorder)}stopOnPageTimer(){this.isOnPage&&(this.isOnPage=!1,this.onPageRecorder.pause(),this.onPageBackgroundRecorder.pause())}setBackGroundState(e){this.inBackground=e,this.isLoading&&this.notifyTimers(e,this.loadingRecorder,this.loadingInBackgroundRecorder),this.isOnPage&&this.notifyTimers(e,this.onPageRecorder,this.onPageBackgroundRecorder)}notifyTimers(e,t,i){e?(t.pause(),i.start()):(t.start(),i.pause())}onForegrounded(){this.setBackGroundState(!1)}onBackgrounded(){this.setBackGroundState(!0)}stopTracking(){this.loadingRecorder.pause(),this.loadingInBackgroundRecorder.pause(),this.onPageRecorder.pause(),this.onPageBackgroundRecorder.pause()}}const Bo="CLICK_REPORTING_URL";class Uo extends Fo{constructor(e,t=globalThis.HYPRNetworkController){super(e),this.networkRequestController=t}onBrowserClosed(){if(this.stopTracking(),console.debug(`onBrowserClosed with time ${this.onPageRecorder.duration}`),!a(this.trackingUrl))return void console.error("Invalid BrowserTrackingDurationUrl");const e=this.trackingUrl.replace("TIME_ON_SITE",this.onPageRecorder.duration);this.networkRequestController.get(e)}}const $o="NONE",Lo="STARTED",Wo="COMPLETED",qo={_audioAdState:$o,setAudioAdState(e){if(e!==this._audioAdState){if(e===Lo)this.audioEventPublisher?.audioStarted();else if(this._audioAdState===Lo)this.audioEventPublisher?.audioEnded();else if(e===Wo)return void console.error(`[BVM] Audio state cannot be set to complete.  Currently set to ${this._audioAdState}`);this._audioAdState=e}},audioEvent(e){if("complete"===e)console.debug("[BVM] Ending audio on complete audio event"),this.setAudioAdState(Wo);else if("start"===e){console.debug("[BVM] starting audio on start audio event"),this.setAudioAdState(Lo);const{outputVolume:e,isMuted:t}=Pe();this.audioStatusManager?.onVolumeChange(e),this.audioStatusManager?.onMuteChange(t)}else console.error(`[BVM] Unknown audio event received: ${e}`)}},zo={_omSessionActive:!1,startOMSession(e=(()=>{})){console.debug("[BOMM] startOMSession."),this._omSessionActive?console.debug("[BOMM] OM Session already active, ignoring."):(this._omSessionActive=!0,e())},finishOMSession(e=(()=>{})){this._omSessionActive?(console.debug("[BOMM] finishOMSession."),this._omSessionActive=!1,e()):console.debug("[BOMM] OM Session not active, cannot finish. Ignoring.")},clearOMSession(e=(()=>{})){console.debug("[BOMM] clearOMSession."),this.finishOMSession(e)}},Ho=4;let Jo,Yo=3;function jo({message:e,severity:t,type:i}={},r=globalThis.HYPRRequestParameterManager,s=globalThis.HYPRNetworkController){const n={errorMessage:e||"",errorTypeKey:i||"shared_js_error"},o=t||Ho;if(console.debug(`Client Error Capture [level: ${o}] error: ${JSON.stringify(n)}`),void 0===Jo||o<=Yo)return;const a={...r.getErrorParams(),...n};"function"==typeof console.messages&&(a.value=console.messages()),s.post(Jo,a)}globalThis.HYPRErrorController={sendClientErrorCapture(e,t,i){jo({message:e,severity:i,type:t})}};const Ko="\x3c!--PROPS_TEMPLATE--\x3e";function Zo(e,t,i,r,s,n=null,o=null){console.debug("[DPF] setting up props");let a="";if(function(e){try{return"string"==typeof JSON.parse(e)?.steps?.[0]?.adm}catch(e){return!1}}(t)){console.debug("[DPF] props using provided JSON Props");const i=JSON.parse(t);i.placementType=e,a=JSON.stringify(i)}else{console.debug(`[DPF] props using HTML template. Placement type is ${e}`);const l=sr.encode(t);console.log(`[DPF] base64AdMarkup is ${l}`);const d={placement_type:e,steps:[{adm:`${l}`}],os:i,os_version:r,msdkv:s,shared_version:globalThis.version};null!==n&&null!==o&&(d.w=n,d.h=o),a=JSON.stringify(d)}return console.debug(`[DPF] props set with ${a}`),a}globalThis.PLAYER_AD_DISPLAY_TEMPLATE="";class Go{constructor(e={},t=Po){if(0===Object.keys(e).length)return;const i=e.seatbid||[];if(i.length<1)return;const r=i[0]||[];if(r.length<1)return;const s=r.bid[0]||{};this.id=s.id,this.impid=s.impid,n(s.adm)?this.ad_html="":this.ad_html=s.adm,this.hyprAd=void 0!==s.ext?.hyprmx;const o=s.ext?.hyprmx||{};this.audioAd=o?.audio_ad||!1,this.offerId=o?.offer_id||"",this.clickMode=o?.click?.mode||t,this.onPageJS=o?.click?.on_page_load_js||"",this.clickReportingUrl=o?.click_reporting_url,this.timeOnSiteUrl=o?.time_on_site_url,this.acceptableAdSizeVariance=o.acceptable_ad_size_variance||1,this.isHyprAd()?(console.debug("[BA] Hypr ad detected."),this.impressionUrl=s.burl):(console.debug("[BA] Prebid ad detected."),this.impressionUrl=s.ext?.prebid?.events?.imp,this.winUrl=s.ext?.prebid?.events?.win,console.debug(`[BA] Prebid ad.  imp url is ${this.impressionUrl}`),console.debug(`[BA] Prebid ad.  win url is ${this.winUrl}`))}updateAdConfiguration({click:e={},time_on_site_url:t,click_reporting_url:i}={}){const{mode:r,on_page_load_js:s}=e;this.clickMode=r||this.clickMode,this.onPageJS=s||this.onPageJS,this.timeOnSiteUrl=t||this.timeOnSiteUrl,this.clickReportingUrl=i||this.clickReportingUrl,console.debug(`[BA] Updated ad configuration.\n      click mode is ${this.clickMode}\n      on page js is ${this.onPageJS}\n      time on site url is ${this.timeOnSiteUrl}\n      click reporting url is ${this.clickReportingUrl}`)}isHyprAd(){return this.hyprAd}hasImpressionId(){return"string"==typeof this.impid&&this.impid.length>0}getImpressionUrl(){return this.impressionUrl}getWinUrl(){return this.winUrl}getClickReportingUrl(){return this.clickReportingUrl}getTimeOnSiteUrl(){return this.timeOnSiteUrl}hasAd(){return"string"==typeof this.ad_html&&this.ad_html.length>0}shouldAppStoreUseOverlay(){return!1}openClickInNativeBrowser(){return"native"===this.clickMode}getOfferName(){return this.offerId}getClickMode(){return this.clickMode||"webview"}isAudioAd(){return this.audioAd}}class Xo extends Je{constructor(e,t){super(),this.inventoryCheckURL=e,this.rtbClickMode=t}async loadAd(e,t=globalThis.HYPRNetworkController){const{imp:i}=e;try{console.debug(`[RAL] rtb request started for: ${i.id}`);const{promise:r,canceller:s}=t.cancellableAsyncPost(this.inventoryCheckURL,e);this.canceller=s;const{code:n,body:o,error:a}=await r;return this.canceller=void 0,{...this.processResponse(n,o,a),imp:i}}catch(e){return console.debug(`[RAL] rtb request aborted for: ${i.id} with error: ${e}`),{error:e}}}processResponse(e,t,i){let r=i;void 0!==r?console.debug(`[BP] Loading ads received error for placement: ${this} with status code ${e} and error: ${r}`):j.validResponseStatus(e)?console.debug(`[BP] Loading ads completed for placement: ${this}`):(console.debug(`[BP] Loading ads received invalid status code for placement: ${this} with status code ${e}`),r=`Invalid status code ${e}`);const s=Q.parseJSON(t);let n;return j.validResponseStatus(e)&&void 0===r&&void 0!==s&&(n=new Go(s,this.rtbClickMode)),{ad:n,error:r}}}const Qo="</head>",ea="PLACEMENT_DOES_NOT_EXIST",ta="PLAYER_AD_DISPLAY_TEMPLATE_DOES_NOT_EXIST";class ia extends Ue{constructor({audioStatusManager:e,audioEventPublisher:t},i,r,s,n,...o){super(...o),console.debug(`[BVM] ${this.instanceId}`),this.ad=new Go,this.hasFiredImpression=!1,this.hasFiredWin=!1,this.userInteracted=!1,this.visibilityRateCheck=200,this.opacityThresholdPercent=100,this.currentAdData=void 0,this.visibilityData=void 0,this.audioEventPublisher=t,this.audioStatusManager=e,this.audioStatusManager?.addObserver(this),this.impressionTracker=i(this.networkRequestController),this.winTracker=n(this.networkRequestController),this.clickReporting=r(this.networkRequestController),this.timeOnSiteFactory=s(this.networkRequestController)}get audioAdState(){return this._audioAdState}set audioAdState(e){this.setAudioAdState?.apply(this,[e])}async loadAd({definedSize:e,actualSize:t,bidResponse:i}){if(this.audioAdState=$o,this.hasFiredImpression=!1,this.hasFiredWin=!1,this.clearOMSession((()=>this.presenter.finishOMSession?.())),!this.hasPlacement())return this.presenter.loadAdFailed(ea),this.audioAdState=$o,{success:!1,error:ea};if(0===globalThis.PLAYER_AD_DISPLAY_TEMPLATE.length)return console.error("[BVM] Player Ad Display Template is empty"),this.presenter.loadAdFailed(ta),this.audioAdState=$o,{success:!1,error:ta};if(void 0!==i){console.debug(`[BVM] Loading adm with ${i}`);const{success:t,error:r}=this.internalLoadBidResponse(i);return this.onBannerLoadResult(this.placement,void 0,e),{success:t,error:r}}const r={definedSize:e,actualSize:t};console.debug(`[BVM] Loading ads with ${JSON.stringify(r)}`);const{success:s,error:n}=await this.internalLoadAd({definedSize:e,actualSize:t});return this.onBannerLoadResult(this.placement,n,e),{success:s,error:n}}async internalLoadAd(e){console.debug("[BP] loadAd for banner"),this.imp=void 0,console.debug(`[BP] Loading ads for ${this}`);const t=this.placement.rtbParameterBuilder(e,this.placement.id);this.placement.rtbClickMode=1===t.imp[0]?.clickbrowser?Ao:Po,this.adLoadRequest?.cancel(),this.adLoadRequest=new Xo(this.placement.inventoryCheckURL,this.placement.rtbClickMode);const{ad:i,error:r,imp:s}=await this.adLoadRequest.loadAd(t,this.networkRequestController);return r===$?(console.debug("[BP] Request aborted"),{success:!1,error:r}):(this.adLoadRequest=void 0,this.ad=i,this.imp=s,{success:this.isAdAvailable(),error:r})}async internalLoadBidResponse(e){return console.debug("[BP] load bid response for banner"),this.imp=void 0,this.ad=function(e,t){const i=new vi(e);i.lastError&&console.log(`[BA] Ad VAST parse produced error: ${JSON.stringify(i.lastError)}`);const{bannerExtensions:r,clickMode:s}=i.getBannerExtension();console.log(`[BA] Banner adJson is ${JSON.stringify(r)}`);const n={seatbid:[{bid:[{id:t,impid:To(),adm:e,ext:{hyprmx:{time_on_site_url:r.time_on_site_url,click_reporting_url:r.click_reporting_url}}}]}]};return s?new Go(n,s):new Go(n)}(e,this.id),{success:this.isAdAvailable()}}onBannerLoadResult(e,t,i){console.debug(`[BVM] Ad load complete for Banner Placement ${e}, isAdAvailable: ${this.isAdAvailable()}, error: ${t}`),void 0===t&&this.isAdAvailable()?(this.presenter.loadAdSuccess(),this.currentAdData=function(e,t,i,r,s=HYPRRequestParameterManager){const n=Zo(te.BANNER,e,r,s.nativeParams.getOSVersion(),s.nativeParams.getMSDKV(),t,i),o=globalThis.PLAYER_AD_DISPLAY_TEMPLATE.replace(Ko,`window.props = ${n}`);return console.debug(`[DPF] generated html ${o}`),o}(this.ad.ad_html,i.width,i.height,this.configuration.platform,this.requestParameterManager),this.isViewable()||this.setCurrentUrl("about:blank"),this.attemptToShowAd()):(this.audioAdState=$o,this.currentAdData=void 0,this.setCurrentUrl("about:blank"),this.presenter.loadAdFailed("NO_FILL"))}hasPlacement(){return void 0!==this.placement}getMraidEnabledAdHTML(){return this.ad.ad_html.replace(Qo,ve.getMraidJSTag()+Qo)}onParentViewChangeEvent(e){super.onParentViewChangeEvent(e),this.hasParentView||(this.audioAdState=Wo,this.finishOMSession((()=>this.presenter.finishOMSession?.()))),this.updateVisibilityTracking()}containerVisibleChange(e){super.containerVisibleChange(e),this.updateVisibilityTracking()}onWebViewLoadData(){}onModalClosed(){super.onModalClosed()}fireBannerImpressionAttemptIfReady(){if(this.hasFiredImpression||!this.hasParentView||!1===this.visible)return!1;const e={width:0,height:0,...this.currentSizeData};if(e.width<=0&&e.height<=0)return console.debug(`[BVM] Container size not set for ${this.instanceId} when attempting impression`),!1;this.hasFiredImpression=!0,console.debug(`[BVM] Firing Impression attempt for placement: ${this.placement.name} with id ${this.instanceId}`);const t=this.getBannerImpressionSize();return this.presenter.adImpression?.(),this.validateContainerSize(t,e),this.impressionTracker(this.getAdModel().getImpressionUrl(),this.placement.name),!0}getBannerImpressionSize(){return void 0!==this.imp&&this.imp[0]?this.imp[0].banner:{}}isAdAvailable(){return void 0!==this.ad&&this.ad.hasImpressionId()&&this.ad.hasAd()}fireBannerWinAttemptIfReady(){return!(this.hasFiredWin||!this.hasParentView||!1===this.visible)&&(this.hasFiredWin=!0,console.debug(`[BVM] Firing Win attempt for placement: ${this.placement.name} with id ${this.instanceId}`),this.winTracker(this.getAdModel().getWinUrl(),this.placement.name),!0)}addContainerToSegment(e={}){if(!(e.user?.data&&e.user.data[0]&&e.user.data[0].segment&&e.user.data[0].segment[0]))return;const t=e.user?.data[0]?.segment[0],i={width:0,height:0,...this.currentSizeData};t.banner_container_width=i.width,t.banner_container_height=i.height}validateContainerSize(e,t){const i=this.ad.acceptableAdSizeVariance;(i<e.w-t.width||i<e.h-t.height)&&jo(_i.getBannerContainerSizeMismatchedError(`Mismatched banner size on impression(${this.instanceId}).  Expected container size (${e.w}x${e.h}) found (${t.width}x${t.height})`))}javaScriptAlertAttempt(e){return this.userInteracted}permissionRequest(e){this.denyPermissionRequest(e)}shouldBootstrapMraid(){return!0}shouldAllowWindowOpenEvent(e){return this.onBannerClicked(),!0}nativeBrowserWillPresent(e){super.nativeBrowserWillPresent(e),this.fireClickTraffic(e.initialUrl)}fireClickTraffic(e){this.clickReporting(this.ad?.getClickReportingUrl(),e)}onBannerClicked(){this.presenter.onAdClicked()}captureImage(){return!1}generateTimeOnSiteTracking(){return new Uo(this.ad?.getTimeOnSiteUrl())}scrollingEnabled(){return!1}shouldScrollBounce(){return!1}allowsWebViewLinkPreview(){return!1}webViewAlpha(){return"00"}isPinchEnabled(){return!1}setUserInteracted(e){void 0!==e.interacted&&(this.userInteracted=e.interacted)}onVisibleEvent(e){const t=this.isViewable();this.visibilityData=e,this.attemptToShowAd(),t!==this.isViewable()&&this.reportVisibilityChanged()}reportVisibilityChanged(){super.reportVisibilityChanged(),this.publishCustomEvent(ke.EVENT_NAME,this.isViewable()?ke.EVENT_VIEWABLE:ke.EVENT_NOT_VIEWABLE)}updateVisibilityTracking(){console.debug(`[BVM] updateVisibilityTracking ${this.visible} ${this.hasParentView}`),this.visible&&this.hasParentView?this.presenter.startVisibilityTracking(this.visibilityRateCheck,this.opacityThresholdPercent):(this.presenter.stopVisibilityTracking(),this.visibilityData=void 0,this.reportVisibilityChanged())}attemptToShowAd(){this.isViewable()&&void 0!==this.currentAdData&&(console.debug("[BVM] Banner ad will be shown."),this.setCurrentData(this.currentAdData),this.fireBannerImpressionAttemptIfReady(),this.fireBannerWinAttemptIfReady(),this.currentAdData=void 0,void 0===this.ad?console.debug("[BVM] Ad Undefined"):console.debug("[BVM] Ad Defined"))}isViewable(){return!(!this.visible||!this.hasParentView||this.backgrounded||!1!==this.visibilityData?.fullyOffscreen||!0!==this.visibilityData?.parentAlphaPassesThreshold||!0!==this.visibilityData?.isShown)}storePicture(e){super.storePicture(e),this.fireClickTraffic(e)}createCalendarEvent(e){this.fireClickTraffic(null),super.createCalendarEvent(e)}destroy(){this.audioAdState=Wo,this.audioStatusManager?.removeObserver(this)}handlePageFinishFromWebView(e){super.handlePageFinishFromWebView(e),console.debug(`[BVM] handlePageFinishFromWebView: ${e}`)}setNativeAdConfiguration(e){console.debug(`[BVM] setNativeAdConfiguration: ${JSON.stringify(e)}`),this.ad?.updateAdConfiguration(e)}setTrampoline(e){console.debug(`[BVM] setTrampoline: ${JSON.stringify(e)}`),void 0!==e?.nativeInterface&&this.setNativeAdConfiguration({nativeInterface:e.nativeInterface})}handleJSMessageFromWebView(e){const{name:t}=e;if("pageReady"===t)this.startOMSession((()=>this.presenter.startOMSession?.()));else super.handleJSMessageFromWebView(e)}}Object.assign(ia.prototype,qo),Object.assign(ia.prototype,zo);class ra{constructor(e,t,i,r=globalThis.HYPRNetworkController,s=HYPRRequestParameterManager){this.catalogFrameUrl=e,this.recoveryParams=void 0,this.placement=t,this.ad=i,this.requestParameterManager=s,this.networkRequestController=r,this.clickReportingUrl=void 0,this.thankYouURL=void 0,this.trampoline=void 0,this.htmlBody=void 0,this.catalogFrameError=void 0,this.preloading=!1}async getWebAd(e=!1){this.preloading=e;const t=await this.startCatalogFrameRequest();let i;i=void 0!==this.htmlBody?this.catalogFrameUrl:this.trampoline?.redirection_url;return{error:t,pageUrl:i,html:this.htmlBody}}async getTrampolineResponse(){const e=await this.startCatalogFrameRequest();console.debug("[CatalogFramePromise] completed trampoline request");const{trampoline:t}=this;return{error:e,trampoline:t}}mergeTrampoline(e){void 0===this.trampoline?(console.debug("[CatalogFrame] setting trampoline from app.js"),this.trampoline=e):void 0!==this.trampoline.viewing_id&&""!==this.trampoline.viewing_id||""===e.viewing_id?n(this.trampoline.click_reporting_url)&&""!==e.click_reporting_url&&(console.debug("[CatalogFrame] Merging click reporting url from app.js"),this.trampoline.click_reporting_url=`${e.click_reporting_url}`):(console.debug("[CatalogFrame] Merging viewing ID from app.js for preloaded mraid"),this.trampoline.viewing_id=`${e.viewing_id}`)}storeRecoveryParams(e){this.recoveryParams=e}shouldReloadCatalogFrame(e){const{url:t}=e;return t.startsWith(this.catalogFrameUrl)&&void 0!==this.recoveryParams}webtrafficSdkConfiguration(){return void 0===this.trampoline||Object.keys(this.trampoline).length<1?{}:{completion_url:this.completionUrl(),maximum_page_load_wait_time_in_seconds:this.trampoline.maximum_page_load_wait_time_in_seconds,viewing_id:this.trampoline.viewing_id,minimum_visit_time_in_seconds:this.trampoline.visit_length,urls:this.webTrafficPagesArray()}}pageUrl(){return this.trampoline?.redirection_url}completionUrl(){return void 0!==this.trampoline.redirection_url?`${this.trampoline.redirection_url}&do_completion=1&phase=thank_you&recovery=1`:""}async startCatalogFrameRequest(){this.updateCatalogFrameUrl();const{response:e,body:t,error:i,headers:r}=await this.networkRequestController.postAsync(this.catalogFrameUrl,this.requestParams(),ra.requestConfig());let s;if(void 0===i&&j.validResponseStatus(e)&&void 0!==r){if(j.validHtmlType(r)&&void 0!==t)return void(this.htmlBody=t);j.validJSONType(r)||(s="Unparsable catalog frame response")}else console.error(`catalogFrame error received: ${i}`),s=`CatalogFrame error: ${i}, status Code: ${e}`;return void 0!==s?(this.catalogFrameError=s,s):(s=this.handleTrampolineBody(t),void 0!==s&&(this.catalogFrameError=s),s)}requestParams(e=this.placement,t=this.ad){const i=this.requestParameterManager.getCatalogFrameParams(),r=e.compileRequestParamsFromParams(i,!1,t);return U.compact(r)}updateCatalogFrameUrl(){void 0!==this.recoveryParams&&(this.catalogFrameUrl=`${this.catalogFrameUrl.split("?")[0]}?${this.recoveryParams}`,this.recoveryParams=void 0)}handleTrampolineBody(e){console.debug("CatalogFrame.handleTrampolineBody()");const t=Q.parseJSON(e);return"object"==typeof t&&(0===Object.keys(t).length?"No trampoline keys":!1===this.preloading&&void 0===t.token?"No token parameter":void 0===t.reward_token?"No reward_token parameter":!1===this.preloading&&void 0===t.viewing_id?"No viewing_id":void(this.trampoline=t))}webTrafficDynamicPageArray(e){const t=this.webTrafficPagesArray();return 1!==t.length?(console.error(`Unexpected webtraffic dynamic page length: ${t.length}`),t):"placeholder"!==t[0].url?(console.error(`webtraffic dynamic page missing expected token, found url: ${t[0].url}`),t):(t[0].url=e,t)}webTrafficPagesArray(){const e="object"==typeof this.trampoline.page_load_js?this.trampoline.page_load_js:{},t=e.map,i=e.js,r=Array.isArray(this.trampoline.wtu_ids)&&this.trampoline.urls.length===this.trampoline.wtu_ids.length?this.trampoline.wtu_ids:void 0,s=[];for(const e in this.trampoline.urls)if(this.trampoline.urls.hasOwnProperty(e)){const n=this.trampoline.urls[e];let o;void 0!==t&&void 0!==i&&(o=i[t[e]]),void 0===o&&(o="");const a={url:n,on_page_load_js:[o]};void 0!==r&&(a.wtu_id=r[e]),s.push(a)}return s}impressionURLs(){const e=[];if(this.trampoline.tracking_impression_html.length>0){const t=this.trampoline.tracking_impression_html.replace(/"/g,"'").split("'");for(const i of t)Q.urlValidation(i)&&e.push(i)}return e}static requestConfig(){return new k(!0,O,I,{"Content-Type":N,Accept:x})}}const sa="HYPR_AD_PROGRESS_STORAGE_KEY";class na{constructor(e,t,i,r){const s=[e,t,i,r];for(const n of s)if("string"!=typeof n||0===n.length){return jo(_i.getAdProgressInvalidSetUpError(e,t,i,r)),void(this.data=void 0)}this.data={},this.data.ad_progress_state=T.IN_PROGRESS,this.data.ad_closed_action=C.UNKNOWN,this.data.viewing_id=e,this.data.distributor_id=t,this.data.user_id=i,this.data.offer_type=r,this.send(),this.store(),this.adClosedActionSent=!1}send(){if(void 0!==this.data){this.data.ad_progress_timestamp=(new Date).toISOString();const e=JSON.stringify(this.data),t=na.generateEventCallbackFunction(e);globalThis.HYPREventController.sendAdProgressTracking(e,t)}}store(){let e="";if(void 0!==this.data){const t=JSON.parse(JSON.stringify(this.data));t.ad_closed_action=C.CRASHED,e=JSON.stringify(t),na.updateStorage(e)}return e}static updateStorage(e=""){hyprMXLocalStorage.setItem(sa,e)}setProgressState(e){if(this.adClosedActionSent)return void console.debug(`[APT] Ad progress state change (${e}) received after ad finished. Ignoring.`);if(void 0===this.data)return void console.debug("Cannot update progress state.  Data undefined");if(this.dataUpdateError(e))return;let t=e;void 0!==this.completionState&&e===T.IN_PROGRESS&&(t=this.completionState),this.data.ad_progress_state=t,this.send(),e===T.BACKGROUNDED?na.updateStorage():this.store(),e!==T.COMPLETED&&e!==T.PAYOUT_COMPLETE||(this.completionState=e)}dataUpdateError(e,t=!1){const i=t?"closed action":"progress state",r=` (value: ${e}, viewing ID: ${this.data?.viewing_id})`,s=t?"InvalidCloseContext":"InvalidProgressContext",n=t?"InvalidCloseValue":"InvalidProgressValue";let o=`${i} error`,a=0;if(void 0===this.data?(o+=" -- session data undefined",a=gi[s]):t&&void 0!==this.data&&this.data.ad_closed_action!==C.UNKNOWN&&(o+=` -- ad_closed_action already set: ${this.data.ad_closed_action}`,a=gi[s]),(!t&&void 0===T[e]||t&&(void 0===C[e]||e===C.UNKNOWN))&&(o+=" -- invalid value",a=gi[n]),a>0){o+=r;return jo(_i.getAdProgressUpdateError(o,a)),!0}return!1}setClosedAction(e){this.adClosedActionSent?console.debug("[APT] Ad closed tracking already sent"):(this.adClosedActionSent=!0,this.dataUpdateError(e,!0)||(this.data.ad_closed_action=e,this.send(),na.updateStorage()))}static checkStoredData(){const e=hyprMXLocalStorage.getItem(sa);if("string"==typeof e&&e.length>0){console.debug("[APT] checkStoredData");const t=na.generateEventCallbackFunction(e);globalThis.HYPREventController.sendAdProgressTracking(e,t),na.updateStorage()}}static generateEventCallbackFunction(e){return(t,i)=>{const r=`sent ad progress tracking: ${e}, response: ${JSON.stringify(t)}, error: ${i}`;return console.debug(`[APT] ${r}`),r}}}class oa extends Ue{constructor(e,t,i,r={},...s){super(...s),this.pageReadyController=t,this.pageReadyController.pageReadyListener=this,this.presentationListener=e,this.customData=r,this.clickReporting=i(this.networkRequestController),void 0!==this.placement&&(r.placementId=this.placement.id),this.catalogFrame=void 0,this.impressionFired=!1,this.recoveryParams=void 0,this.presenter=void 0,this.adCompleted=!1,void 0!==this.placement&&(this.ad=this.placement.ad),this.thankYouUrl=void 0,this.viewingId=void 0,this.queryParams=void 0,this.payoutComplete=!1,this.storedProgressStates=[],this.closable=this.ad?.show_close_button||!1,this.backButtonEnabled=!1,this.presenterSubscriptions.push("closable"),this.presenterSubscriptions.push("backButtonEnabled"),this.closableWithoutDialog=!1}get closable(){return this._closable}set closable(e){this._closable=e,this.presenter?.setClosable(e)}get backButtonEnabled(){return this._backButtonEnabled}set backButtonEnabled(e){this._backButtonEnabled=e,this.presenter?.setBackButtonEnabled(e)}beginFullScreenPresentation(e){if(this.ad=e,void 0!==this.ad){if(this.userAgent=e.user_agent||"",void 0!==this.ad.bidResponse){const t=this.ad.bidResponse,i=this.ad.type,{platform:r}=this.configuration.platform,s=function(e,t,i,r=HYPRRequestParameterManager){const s=Zo(t,e,i,r.nativeParams.getOSVersion(),r.nativeParams.getMSDKV()),n=globalThis.PLAYER_AD_DISPLAY_TEMPLATE.replace(Ko,`window.props = ${s}`);return console.debug(`[DPF] generated html ${n}`),n}(t,i,r,this.requestParameterManager);this.setCurrentData(s),this.closable=!1,this.presentationListener.onDisplayAd(this.instanceId,e.getAdType(),e.getAdString())}else this.catalogFrame=this.getCatalogFrame(),this.presentAdForPlacement(this.placement,this.ad),this.closable=void 0!==this?.ad?.show_close_button&&this.ad.show_close_button;this.pageReadyController.beginPageReadyTimer(this.computePageReadyTime())}else this.presentNoAdForPlacement(this.placement)}presentNoAdForPlacement(e){console.debug(`[FSVM] PresentationController.showFullscreenAd(${e.name}) has no ad to show`),this.pageReadyController.stopPageReadyTimer(),this.eventController.sendNoAdTracking(e),this.presentationListener.showNoAd(e.getUIComponentsString())}presentAdForPlacement(e,t){if(void 0===t)return console.debug(`[FSVM] PresentationController.presentAdForPlacement(${e.name}) has no ad to show`),void this.presentNoAdForPlacement(e);console.debug(`[FSVM] PresentationController.presentAdForPlacement(${e.name}) to show ${t.getAdType()} offer`),t.hasAd()?(this.presentationListener.onDisplayAd(this.instanceId,t.getAdType(),t.getAdString()),this.loadPageFromCatalogFrame()):(console.debug(`[FSVM] PresentationController.presentAdForPlacement(${e.name}) has no ad to show`),this.presentNoAdForPlacement(e))}handleJSMessageFromWebView(e){const{name:t,body:i}=e;switch(t){case"pageReady":this.pageReadyController.onPageReadyFinished(),this.presenter.reportPowerState();break;case"setRecoveryPostParameters":this.catalogFrame?.storeRecoveryParams(i);break;case"payoutComplete":this.payoutComplete=!0,this.updateAdProgressState(T.PAYOUT_COMPLETE);break;case"setClosable":this.closable=!0;break;case"close":case"closeAd":this.updateAdClosedAction("close"===t?C.MRAID_CLOSE:C.CLOSE_AD),this.adCompleted||void 0===this.placement.cancellationDialog?this.presenter.closeAdExperience():this.presenter.showCancelDialog(this.placement.cancellationDialog.message,this.placement.cancellationDialog.exit,this.placement.cancellationDialog.continue);break;case"adDidComplete":this.updateAdProgressState(T.COMPLETED),this.closable=!0,this.adCompleted=!0,this.presenter.dismissOfferCancellationDialog();break;case"presentDialog":this.presenter.showDialog(i);break;case"abort":"presentDialog"===i?this.updateAdClosedAction(C.PRESENT_DIALOG_OK):this.updateAdClosedAction(C.ABORT),this.presenter.closeAdExperience();break;case"startOMSession":this.presenter.startOMSession?.(i);break;case"endOMSession":this.presenter.endOMSession();break;case"useCustomClose":this.adCompleted?console.error("[FSVM] useCustomClose invoked after ad completed"):this.presenter.useCustomClose("true"===i);break;default:super.handleJSMessageFromWebView(e)}}setTrampoline(e){console.debug(`[FSVM] setTrampoline ${JSON.stringify(e)}`),this.getCatalogFrame().mergeTrampoline(e),this.onCatalogFrameSuccess()}getCatalogFrame(){return void 0===this.catalogFrame&&(this.catalogFrame=new ra(this.ad?.catalog_frame_url,this.placement,this.ad)),this.catalogFrame}async loadPageFromCatalogFrame(){console.debug("[FSVM] loadPageFromCatalogFrame() started");const{error:e,pageUrl:t,html:i}=await this.catalogFrame.getWebAd();void 0===e?(void 0!==i?this.setCurrentData(i,t):this.setCurrentUrl(t),this.onCatalogFrameSuccess()):this.onShowError(e)}urlNavigationAttempt(e){const{url:t}=e;return this.catalogFrame?.shouldReloadCatalogFrame(e)?(console.debug(`[FSVM] CatalogFrame Reload for placement: ${this.placement.name} params: ${this.catalogFrame?.recoveryParams}`),this.loadPageFromCatalogFrame(),JSON.stringify(new re(ne,t))):super.urlNavigationAttempt(e)}shouldAllowWindowOpenEvent(e){return!1===this.hasBecomeVisible?(console.error("[FSVM] rejecting Window.open attempt before Ad was displayed"),!1):super.shouldAllowWindowOpenEvent(e)}nativeBrowserWillPresent(e){super.nativeBrowserWillPresent(e),this.fireClickTraffic(e.initialUrl)}fireClickTraffic(e){void 0!==this.clickReporting?this.clickReporting(this.clickReportingUrl,e):console.debug(`[FSVM] Click reporting will be ignore for ${this.placement.name}.`)}javaScriptAlertAttempt(e){return!1===this.hasBecomeVisible?(console.error("[FSVM] rejecting javaScriptAlertAttempt before Ad was displayed"),!1):super.javaScriptAlertAttempt(e)}openMeasurementCustomData(){return JSON.stringify(this.customData)}shouldBootstrapMraid(){return!1}shouldScrollBounce(){return!0}webViewBackground(){return this.ad.getAdType()===g?"ffffff":"000000"}playbackRequiresUserAction(){return void 0===this.ad?super.playbackRequiresUserAction():this.ad.video_requires_user_action||super.playbackRequiresUserAction()}onShowError(e){console.debug(`[FSVM] onShowError: ${e}`),this.presenter.showErrorDialog("There was an error displaying the ad.")}onCatalogFrameSuccess(){if(void 0===this.catalogFrame.trampoline)return void console.debug("[FSVM] onCatalogFrameSuccess cannot proceed.  No trampoline");console.debug(`[FSVM] Attempting Catalog Frame response from onCatalogFrameSuccess callback${JSON.stringify(this.catalogFrame.trampoline)}`);const e={viewingID:this.catalogFrame.trampoline.viewing_id,distributorID:this.catalogFrame.trampoline.distributor_id,userID:this.catalogFrame.trampoline.uid,offerType:this.ad.getAdType()};this.viewingId=this.catalogFrame.trampoline.viewing_id,this.thankYouUrl=this.catalogFrame.trampoline.redirection_url,this.clickReportingUrl=this.catalogFrame.trampoline.click_reporting_url,this.startAdProgressTracking(e)}computePageReadyTime(){return void 0===this.ad?8:void 0!==this.ad.preloaded_mraid_page_ready_timeout&&void 0!==this.preloadInstanceId?this.ad.preloaded_mraid_page_ready_timeout:this.ad.offer_initiation_timeout_in_seconds||8}onClose(){this.adWillClose()}onWebViewTermination(){const e=void 0!==this.ad?.enable_webview_reload?this.ad.enable_webview_reload:super.onWebViewTermination();return!1===e&&(console.error("[FSVM] Webview crashed. Closing ad abruptly."),this.updateAdClosedAction(C.WEBVIEW_CRASH),this.presenter.closeAdExperience()),e}permissionRequest(e){if(!1===this.hasBecomeVisible)return console.error("[FSVM] rejecting permissionRequest attempt before Ad was displayed"),void this.denyPermissionRequest(e);super.permissionRequest(e)}createCalendarEvent(e){!1!==this.hasBecomeVisible?super.createCalendarEvent(e):console.error("[FSVM] rejecting createCalendarEvent attempt before Ad was displayed")}storePicture(e){!1!==this.hasBecomeVisible?super.storePicture(e):console.error("[FSVM] rejecting storePicture attempt before Ad was displayed")}captureImage(){return!1===this.hasBecomeVisible?(console.error("[FSVM] rejecting captureImage attempt before Ad was displayed"),!1):super.captureImage()}isPageReadyActiveOrFinished(){return this.pageReadyController.isPageReadyActiveOrFinished()}isPageReady(){return this.pageReadyController.pageReady}onPageReadyFinished(){}onPageReadyExpired(){console.debug("[FSVM] onPageReadyExpired. Setting ad closable"),this.closable=!0,this.closableWithoutDialog=!0}shouldScaleViewport(){return void 0!==this.getAdModel()?.scale_viewport?this.getAdModel().scale_viewport:super.shouldScaleViewport()}startAdProgressTracking(e){let t="";const i=Object.keys(e);for(const r of["viewingID","distributorID","userID","offerType"])i.includes(r)&&"string"==typeof e[r]||(t+=`Valid ${r} missing from parameters. `);if(t.length>0)return console.debug(`[FSVM] error with startAdProgressTracking() - ${t}`),t;const{viewingID:r,distributorID:s,userID:n,offerType:o}=e;return void 0!==this.adProgress?"Ad Progress already in progress.":(this.adProgress=new na(r,s,n,o),this.storedProgressStates.forEach((e=>this.updateAdProgressState(e))),null)}updateAdProgressState(e){if(console.debug(`[FSVM] updateAdProgressState to ${e}`),void 0!==this.adProgress||e!==T.IN_PROGRESS)return void 0===this.adProgress?(console.debug(`[FSVM] updateAdProgressState before adProgress set ${e}`),void this.storedProgressStates.push(e)):void this.adProgress.setProgressState(e);console.debug("[FSVM] Ad Progress set to IN_PROGRESS before ad started.  Ignoring.")}updateAdClosedAction(e){console.debug(`[FSVM] updateAdClosedAction to ${JSON.stringify(e)}`),void 0!==this.adProgress?this.adProgress.setClosedAction(e):console.debug(`[FSVM] updateAdClosedAction before adProgress set ${e}`)}getPresenterPromise(){return new Promise((e=>{this.resolvePresenter=()=>{console.debug("[FSVM] [CatalogFramePromise] resolving presenter"),e(void 0!==this.presenter)}}))}async waitForTrampolineAndPresenter(){this.catalogFrame=this.getCatalogFrame();const e=this.catalogFrame.getTrampolineResponse(),t=this.getPresenterPromise();console.debug("[FSVM] [CatalogFramePromise] will start promise");const[{error:i,trampoline:r},s]=await Promise.all([e,t]);return console.debug("[FSVM] [CatalogFramePromise] completed promise"),s&&void 0===i?(console.debug("[FSVM] [CatalogFramePromise] success"),r):(console.debug("[FSVM] [CatalogFramePromise] error"),void this.onShowError(i))}setPresenter(e){super.setPresenter(e),"function"==typeof this.resolvePresenter&&(console.debug("[FSVM] [CatalogFramePromise] will resolve presenter promise"),this.resolvePresenter(),this.resolvePresenter=void 0)}onForegroundTransition(){super.onForegroundTransition(),this.updateAdProgressState(T.IN_PROGRESS)}onBackgroundTransition(){super.onBackgroundTransition(),this.updateAdProgressState(T.BACKGROUNDED)}nativeClosePressed(){this.exitAdExperience(C.NATIVE_CLOSE_BUTTON)}cancelDialogExitPressed(){this.updateAdClosedAction(C.CANCELLATION_DIALOG_OK),this.presenter.closeAdExperience()}onErrorDialogOKPressed(){this.updateAdClosedAction(C.ERROR_DIALOG_OK),this.presenter.closeAdExperience()}unknownErrorOccurred(e){console.error(`[FSVM] Unknown error occurred:  ${e}. Closing Ad.`),this.updateAdClosedAction(C.UNKNOWN),this.onShowError(e)}onBackButtonPressed(){if(console.debug("[FSVM] onBackButtonPressed"),this.webViewHistory?.canNavigateBack)return console.debug("[FSVM] Navigating Back"),void this.navigateBack();this.closable||this.adCompleted?this.exitAdExperience(C.BACK_PRESS):console.debug("[FSVM] This ad is non-closable.")}exitAdExperience(e){console.debug(`[FSVM] exitAdExperience ${e}`),this.adCompleted||this.closableWithoutDialog||void 0===this.placement.cancellationDialog?(console.debug("[FSVM] closeAdExperience"),this.updateAdClosedAction(e),this.presenter.closeAdExperience()):(console.debug("[FSVM] showCancelDialog"),this.presenter.showCancelDialog(this.placement.cancellationDialog.message,this.placement.cancellationDialog.exit,this.placement.cancellationDialog.continue))}internetLossDetected(){this.closable=!0}onWebViewHistoryChanged(e){super.onWebViewHistoryChanged(e),this.backButtonEnabled=e.canNavigateBack}}const aa={viewModelMap:new Map,baseAdIdentifierIndex:(new Date).valueOf(),removeViewModel(e){if(console.debug(`[VMC] removeViewModel ${e}`),this.viewModelMap.has(e)){console.debug(`[VMC] ViewModel found for ${e} and will be destroyed`);const t=this.viewModelMap.get(e);t.webViewPresenter=null,t.presenter=null,this.viewModelMap.delete(e),t.destroy()}},getViewModel(e){if(this.viewModelMap.has(e))return this.viewModelMap.get(e);throw new Error(`No matching viewModel for id: ${e}`)},getFullScreenViewModelForPlacementName(e){console.debug(this.toContentString());for(const[,t]of this.viewModelMap.entries())if(t.placement.name===e&&t instanceof oa)return console.debug(`[VMC] ViewModel found for placement: ${e}`),t;console.debug(`[VMC] ViewModel not found for placement: ${e}`)},storeViewModel(e,t){this.viewModelMap.has(e)&&console.debug(`[VMC] ViewModel already found for identifier: ${e}.  Replacing VM in store`),this.viewModelMap.set(e,t)},generateUniqueId(){const e=`ViewModel-${this.baseAdIdentifierIndex}`;return this.baseAdIdentifierIndex+=1,e},publishEvent(e,t,i){try{const r=this.getViewModel(e);return r[t].apply(r,i)}catch(e){return`publishEventError: ${e.toString()}`}},reset(){this.viewModelMap=new Map,this.baseAdIdentifierIndex=0},toContentString(){const e=[];for(const[t,i]of this.viewModelMap.entries()){let r="unknown";i instanceof oa?r="FullscreenWebAdViewModel":i instanceof Le?r="HyprMXBrowserViewModel":i instanceof ia&&(r="BannerViewModel"),e.push(`${t}: type ${r}, placement: ${i?.placement?.name}`)}return JSON.stringify(e)}};globalThis.ViewModelController=aa;class la{constructor(e){this.timerController=e,this.pageReadyListener=void 0,this.pageReady=!1,this.pageReadyTimer=void 0}beginPageReadyTimer(e){if(void 0===this.pageReadyListener)throw new Error("PageReadyController.beginPageReadyTimer called with no pageReadyListener set");return this.pageReady?(console.log("[pageReady] already complete.  Not starting timer."),!1):this.isPageReadyTimerActive()?(console.log("[pageReady] already active.  Not starting timer."),!1):e<=0?(console.log(`[pageReady] Invalid time ${e}.  Not starting timer.`),!1):(console.debug(`[pageReady] initiating timer for ${e}s`),this.pageReadyTimer=this.timerController.startTimer(1e3*e,(()=>{this.onPageReadyExpired()})),!0)}onPageReadyExpired(){console.debug("[pageReady] timer expired"),this.pageReadyTimer=void 0,this.pageReadyListener.onPageReadyExpired()}onPageReadyFinished(){console.debug("[pageReady] onPageReadyFinished"),this.pageReady=!0,this.stopPageReadyTimer(),this.pageReadyListener.onPageReadyFinished()}stopPageReadyTimer(){console.debug("[pageReady] stopPageReadyTimer"),this.isPageReadyTimerActive()&&(console.debug("[pageReady] stopping timer"),this.timerController?.stopTimer(this.pageReadyTimer),console.debug("[pageReady] stopped timer"),this.pageReadyTimer=void 0)}isPageReadyTimerActive(){return void 0!==this.pageReadyTimer}isPageReady(){return this.pageReady}isPageReadyActiveOrFinished(){return this.isPageReady()||this.isPageReadyTimerActive()}}class da extends Fo{constructor(e,t=globalThis.HYPREventController){super(e),this.eventController=t}sendTracking(){void 0!==this.trackingUrl&&(this.stopTracking(),this.eventController.sendWebTrafficTimeSpent(this.trackingUrl,JSON.stringify(this.getTimeSpent())),this.trackingUrl=void 0)}getTimeSpent(){return{reason:this.reason,page_load_time:{foreground:this.loadingRecorder.duration,background:this.loadingInBackgroundRecorder.duration},time_on_page:{foreground:this.onPageRecorder.duration,background:this.onPageBackgroundRecorder.duration}}}}const ca="not_started",ha="waiting_on_catalog",ua="proscenium",pa="wt_start_experience",ga="wt_loading",ma="wt_counting",fa="wt_waiting_on_next",ba="thank_you",_a="offer_completion";class va extends oa{constructor(...e){super(...e),this.currentStepIndex=0,this.webtrafficConfiguration=void 0,this.currentTrackingSession=void 0,this.state=ca,this.hasStartedWTExperience=!1,this.webTrafficDynamicUrl=void 0,this.maxWaitTimeTimer=void 0,this.delayedWT=!1,this._forwardButtonEnabled=!1,this.presenterSubscriptions.push("forwardButtonEnabled")}get forwardButtonEnabled(){return this._forwardButtonEnabled}set forwardButtonEnabled(e){this._forwardButtonEnabled=e,this.presenter?.setForwardButtonEnabled(e)}async presentAdForPlacement(e,t){if(console.debug(`[WTVM] WebTrafficViewModel.presentAdForPlacement(${e.name}) to show ${t.getAdType()} offer`),this.presentationListener.onDisplayAd(this.instanceId,t.getAdType(),t.getAdString()),this.updateWebTrafficHeader(),!0!==t.skip_proscenium)return;this.state=ha;void 0!==await this.waitForTrampolineAndPresenter()&&(this.pageReadyController.onPageReadyFinished(),this.startWebTraffic(),this.onCatalogFrameSuccess())}setPresenter(e){super.setPresenter(e),!1===this.ad.skip_proscenium&&this.showProscenium()}showProscenium(){this.state===ca&&(this.state=ua,this.loadPageFromCatalogFrame())}startWebTraffic(){if(this.backgrounded)return this.delayedWT=!0,void console.debug("[WTVM] delaying startWebTraffic until foregrounded");console.debug("[WTVM] startWebTraffic"),this.hasStartedWTExperience?console.debug("[WTVM] Webtraffic Experience has already started."):void 0!==this.presenter?(void 0===this.webtrafficConfiguration&&(this.webtrafficConfiguration=this.catalogFrame.webtrafficSdkConfiguration()),void 0!==this.webtrafficConfiguration?(this.url=void 0,this.hasStartedWTExperience=!0,this.state=pa,this.updateWebTrafficHeader(),this.sendDurationUpdateTracking(),this.fireImpressionUrls(),this.loadNextPageOfWebTraffic()):console.debug("[WTVM] webtrafficConfiguration not set.  Cannot start.")):console.debug("[WTVM] WTVM presenter not attached.  Cannot start.")}updateWebTrafficHeader(){this.state===pa&&this.presenter.showWebTrafficHeader(this.webtrafficConfiguration.urls.length)}sendDurationUpdateTracking(){this.state===pa&&this.ad.skip_proscenium&&this.eventController.sendDurationUpdateTracking(this.catalogFrame.trampoline.token,"0",this.webtrafficConfiguration.viewing_id)}fireImpressionUrls(){this.state===pa&&this.ad.skip_proscenium&&this.eventController.sendPixelTrackingEvents(this.catalogFrame.impressionURLs())}loadNextPageOfWebTraffic(){const{url:e,wtu_id:t}=this.webtrafficConfiguration.urls[this.currentStepIndex];this.maxWaitTimeTimer=this.timerController.startTimer(1e3*this.webtrafficConfiguration.maximum_page_load_wait_time_in_seconds,(()=>{this.webTrafficPageLoadTimeout()})),this.state=ga,this.backButtonEnabled=!1,this.forwardButtonEnabled=!1,this.presenter.loadWebTrafficPage(e,this.currentStepIndex,this.userAgent),this.eventController.sendWebTrafficVisitEvent(e,this.webtrafficConfiguration?.viewing_id,t),this.currentTrackingSession=new da(e),this.currentTrackingSession.startPageLoadingTimer(this.backgrounded)}executeJS(){if(this.state===ma||this.state===ga||this.state===fa)for(const e of this.webtrafficConfiguration.urls[this.currentStepIndex].on_page_load_js)console.debug("[WTVM] Attaching onPage JS"),this.publishASyncScriptEvent(e);else console.debug("[WTVM] Cannot insert JS.  Not on WT Page")}didTapNext(){this.currentTrackingSession.sendTracking(),this.currentStepIndex+=1,this.loadNextPageOfWebTraffic()}didTapFinish(){this.currentTrackingSession.sendTracking(),this.finishAd()}didTapClose(){console.debug("[WTVM] didTapClose"),(this.adCompleted||this.closableWithoutDialog||void 0===this.placement.cancellationDialog)&&void 0!==this.maxWaitTimeTimer&&this.timerController.stopTimer(this.maxWaitTimeTimer),this.exitAdExperience(C.NATIVE_CLOSE_BUTTON)}finishAd(){this.ad.skip_thank_you_screen?this.state===_a?console.debug("[WTVM] Debounced finish Ad Call"):(this.state=_a,console.debug("[WTVM] Skipping thank you page"),this.offerCompletionRequest()):(console.debug("[WTVM] Loading thank you"),this.state=ba,this.presenter.loadThankYouPage(this.webtrafficConfiguration.completion_url))}handleJSMessageFromWebView(e){const{name:t,body:i}=e;if("startWebtraffic"===t)this.webtrafficConfiguration=JSON.parse(i),void 0!==this.webtrafficConfiguration.user_agent&&(this.userAgent=this.webtrafficConfiguration.user_agent),this.adHtml=void 0,this.startWebTraffic();else super.handleJSMessageFromWebView(e)}handlePageFinishFromWebView(e){super.handlePageFinishFromWebView(e),this.timerController.stopTimer(this.maxWaitTimeTimer),this.transitionToCountdown(R.LOADED)}onWebViewHistoryChanged(e){this.backButtonEnabled=e.canNavigateBack,this.forwardButtonEnabled=e.canNavigateForward}webTrafficPageLoadTimeout(){console.debug("[WTVM] webview maximum page load wait time hit"),this.transitionToCountdown(R.TIMED_OUT)}transitionToCountdown(e){this.executeJS(),this.state===ga&&(this.state=ma,this.presenter.startCountDownTimer(this.webtrafficConfiguration.minimum_visit_time_in_seconds),this.currentTrackingSession.stopPageLoadingTimer(e),this.currentTrackingSession.startOnPageTimer(),this.backgrounded&&this.presenter.pauseCountDownTimer())}webTrafficPageMinTimeComplete(){this.state=fa,this.currentStepIndex<this.webtrafficConfiguration.urls.length-1?this.presenter.showNextButton():(this.presenter.showFinishButton(),this.closable=!1)}async offerCompletionRequest(){console.debug("[WTVM] offerCompletionRequest");let e=!0;try{const{code:t,body:i,error:r}=await this.networkRequestController.postAsync(globalThis.COMPLETION_URL,this.getCompletionParams(),D);console.debug("[WTVM] offerCompletionRequest response received"),void 0===r&&j.validResponseStatus(t)||(console.error(`[WTVM] offerCompletionRequest error received: ${r}`),e=!1)}catch(t){console.error(`[WTVM] offerCompletionRequest error: ${t}`),e=!1}this.skipThankYouPage(e)}skipThankYouPage(e){this.payoutComplete=e,this.adCompleted=!0,e&&(this.updateAdProgressState(T.COMPLETED),this.updateAdProgressState(T.PAYOUT_COMPLETE)),this.updateAdClosedAction(C.COMPLETE_NO_THANK_YOU),this.presenter.closeAdExperience()}getCompletionParams(){const e={};return e[E.USER_ID]=HYPRRequestParameterManager?.getUserId(),e[E.TOKEN]=this.catalogFrame?.trampoline?.token,e[E.VIEWING_ID]=this.webtrafficConfiguration?.viewing_id,e[E.REWARD_TOKEN]=this.catalogFrame?.trampoline?.reward_token,e[E.DISTRIBUTOR_ID]=HYPRRequestParameterManager?.getDistributorID(),e}restoreState(){this.state===_a||this.state===ba?this.finishAd():(this.updateAdClosedAction(C.UNKNOWN),this.presenter.closeAdExperience())}onForegroundTransition(){super.onForegroundTransition(),this.state===ma&&void 0===this.browserInstance&&this.presenter.resumeCountDownTimer(),console.debug("[WTVM] TimeOnSite resuming from foreground event"),this.currentTrackingSession?.setBackGroundState(!1),this.delayedWT&&(this.delayedWT=!1,this.startWebTraffic())}onBackgroundTransition(){super.onBackgroundTransition(),this.state===ma&&this.presenter.pauseCountDownTimer(),console.debug("[WTVM] TimeOnSite pausing from background event"),this.currentTrackingSession?.setBackGroundState(!0)}onModalClosed(){super.onModalClosed(),console.debug("[WTVM] Resuming TimeOnSite from Presentation"),this.currentTrackingSession?.setBackGroundState(!1)}nativeBrowserWillPresent(e){super.nativeBrowserWillPresent(e),console.debug("[WTVM] Pausing TimeOnSite for Presentation"),this.currentTrackingSession?.setBackGroundState(!0)}shouldAllowWindowOpenEvent(e){const{url:t}=e;return this.state!==ua||!this.ad.isNavigationRuleMatch(t)||(this.notifyWindowOpenURLBlock(t),!1)}onNavBarButtonTapped(e){console.debug(`[WTVM] onNavBarButtonTapped: ${JSON.stringify(e)}`);const t=this.webtrafficConfiguration?.daa_url||this.ad.footer.images[e.index].image.link;this.windowOpenAttempt({url:t})}onWebViewTermination(){return console.error("[WTVM] Webview crashed!"),this.ad&&void 0===this.ad.enable_webview_reload&&(this.ad.enable_webview_reload=!1),super.onWebViewTermination()}}class wa extends oa{presentAdForPlacement(e,t){console.debug(`[MRVM] PresentationController.presentAdForPlacement(${e.name}) to show ${t.getAdType()} offer`),this.presentationListener.onDisplayAd(this.instanceId,t.getAdType(),t.getAdString())}setWebViewPresenter(e){console.debug("[MRVM] mraid setWebViewPresenter."),super.setWebViewPresenter(e),this.lazyLoadCatalogFrame()}setPresenter(e){console.debug("[MRVM] mraid setPresenter."),super.setPresenter(e),void 0===this.catalogFrame?.catalogFrameError?this.pageReadyController.beginPageReadyTimer(this.computePageReadyTime()):this.onShowError(this.catalogFrame?.catalogFrameError)}getCatalogFrame(){let e;return console.debug("[MRVM] presenter status: "+typeof this.presenter),void 0===this.presenter&&void 0!==this.ad?.preload_player_url?(console.debug("[MRVM] selected preload_player_url"),e=this.ad.preload_player_url):(console.debug("[MRVM] selected catalog_frame_url"),e=this.ad?.catalog_frame_url),void 0===this.catalogFrame&&(this.catalogFrame=new ra(e,this.placement,this.ad)),this.catalogFrame}async lazyLoadCatalogFrame(){if(void 0!==this.catalogFrame&&void 0!==this.catalogFrame?.trampoline||(console.debug("[MRVM] setting catalogFrame."),this.catalogFrame=void 0,this.catalogFrame=this.getCatalogFrame()),console.debug("[MRVM] lazyLoadCatalogFrame."),void 0===this.catalogFrame.trampoline){console.debug(`[MRVM] starting catalog frame load to url: ${this.catalogFrame?.catalogFrameUrl}`);const{pageUrl:e}=await this.catalogFrame.getWebAd(void 0===this.presenter);console.debug("[MRVM] finished catalog frame load."),void 0!==e?(console.debug("[MRVM] setPageUrl()."),this.url=e):console.debug("[MRVM] lazyLoadCatalogFrame - undefined pageUrl.")}void 0!==this.presenter&&void 0!==this.catalogFrame?.catalogFrameError&&this.onShowError(this.catalogFrame?.catalogFrameError)}onCheckMraidReady(){console.debug("[MRVM] onCheckMraidReady()")}handleJSMessageFromWebView(e){const{name:t}=e;if("pageReady"===t)console.debug("[MRVM] pageReady()."),this.pageReadyController.onPageReadyFinished(),this.presenter?.reportPowerState(),this.onCheckMraidReady();else super.handleJSMessageFromWebView(e)}shouldBootstrapMraid(){return!0}shouldScrollBounce(){return!1}}function ya(e,t,i,r){j.validResponseStatus(t)&&void 0===r?console.debug(`[BVM] ${e} Attempt completed with body: ${i}`):console.debug(`[BVM] ${e} Attempt completed with status code ${t} and with error: ${r}`)}function Pa(e,t,i){i.get(t,((t,i,r)=>{ya(e,t,i,r)}))}function Aa(e){return(t,i)=>{if(n(t))return void ya("ClickThrough",400,void 0,"No ClickThrough URL found.");if(!t.includes(Bo))return void ya("ClickThrough",400,void 0,"No replacement token found on ClickThrough URL.");const r=t.replace(Bo,encodeURIComponent(i));e.get(r,((e,t,i)=>{ya("ClickReporting",e,t,i)}))}}function Sa(e){return Aa(e)}class Ta{static viewModel(e,t,i,r,s,n,o=Sa){const a=new la(globalThis.HYPRTimerController);switch(t){case g:return new va(r,a,o,s,e,n,i);case p:return new wa(r,a,o,s,e,n,i);default:return new oa(r,a,o,s,e,n,i)}}}class Ca{constructor(e=globalThis.HYPRAudioEventPublisherNative){this.listener=e,this.currentAudioStreams=0}audioStarted(){this.currentAudioStreams+=1,console.debug(`[AEP] audioStarted with ${this.currentAudioStreams} streams`),1===this.currentAudioStreams&&(console.debug("[AEP] calling onAudioStart native"),this.listener.onAudioStart())}audioEnded(){this.currentAudioStreams-=1,console.debug(`[AEP] audioEnded with ${this.currentAudioStreams} streams`),0===this.currentAudioStreams&&(console.debug("[AEP] calling onAudioEnd native"),this.listener.onAudioEnd())}}class Ea{constructor(e,t=globalThis.HYPRNativeAudioListener){this.isActive=!1,this.muteCheckRate=e,this.listener=t,this.observers=[]}activate(e){this.isActive=e,this.listener.setActive(this.isActive,this.muteCheckRate)}addObserver(e){this.observers.push(e)}removeObserver(e){this.observers=this.observers.filter((t=>t!==e))}onVolumeChange(e,t=new Se){console.debug(`[ASM] New volume value: ${e}`),this.observers.forEach((i=>t.execute(e,i)))}onMuteChange(e,t=new Te){this.observers.forEach((i=>t.execute(e,i)))}onAudioCategoryChange(e,t=new Ce){console.debug(`[ASM] New audio category: ${e}`),this.observers.forEach((i=>t.execute(e,i)))}}function Ra(){return void 0===globalThis.HYPRAudioStatusManager&&(globalThis.HYPRAudioStatusManager=new Ea(1,globalThis.HYPRNativeAudioListener)),void 0===globalThis.HYPRAudioEventPublisher&&(globalThis.HYPRAudioEventPublisher=new Ca(globalThis.HYPRAudioEventPublisherNative)),{audioStatusManager:globalThis.HYPRAudioStatusManager,audioEventPublisher:globalThis.HYPRAudioEventPublisher}}class ka{constructor(e,t,i,r,s=globalThis.HYPREventController){this.placement=e,this.nativeListener=r,this.eventController=s,this.setInfo(t),this.uiComponents=globalThis.defaultUIComponents||{},this.setUiComponents(i)}setInfo(e=[]){this.info=e}setUiComponents(e){void 0!==e&&(this.uiComponents=e)}hasQuestions(){return this.info.length>0}count(){return this.info.length}presentRequiredInfo(){const e=JSON.stringify(this.info);this.setInfo(),console.debug(`PresentationController.showFullscreenAd(${this.placement.name}) showing ReqInfo`),this.eventController.sendUserInfoTrackingEvent(this.placement,e,this.placement.ad),this.nativeListener.showRequiredInfo(e,JSON.stringify(this.uiComponents))}async reloadPlacement(e){console.debug("[Presentation][RIVM] start placement reload");const t=Io.deepCopy(this.placement);console.debug("[Presentation][RIVM] copied placement");const{success:i,error:r}=await t.loadAd(e);if(console.debug(`[Presentation][RIVM] reloaded with success = ${i}`),console.debug("[Presentation][RIVM] reload network request completed"),void 0!==r)return void console.debug(`[Presentation][RIVM] Error received on loading Ad: ${r}`);const{ad:s,requiredInfo:n,uiComponents:o}=t.popAdData();return this.setInfo(n),this.setUiComponents(o),console.debug("[Presentation][RIVM] prequal state updated"),s}}class xa{constructor(e,t,i=globalThis.HYPREventController){this.placement=e,this.impressionFired=!1,this.eventController=i;const{ad:r,requiredInfo:s,uiComponents:n}=e?.popAdData?.apply(e)||{};this.ad=r,this.requiredInfoViewModel=new ka(e,s,n,t)}questionCount(){return this.requiredInfoViewModel.count()}isSession(e){return this.getPlacementName()===e}isValid(){return void 0!==this.placement&&this.placement instanceof Pi}getPlacementName(){return this.placement?.name}getAdType(){return this.ad?.getAdType()||""}getAdRewardText(){return this.ad?.getAdRewardText()||""}getAdRewardQuantity(){return this.ad?.getAdRewardQuantity()||0}isRewarded(){return"rewarded"===this.placement?.type}fireImpressionOnce(){!this.impressionFired&&(void 0!==this.ad||this.questionCount()>0)&&(this.placement.adImpression(),this.eventController.sendAdImpressionTrackingEvent(this.placement,this.ad),this.impressionFired=!0)}async performReload(e){this.ad=await this.requiredInfoViewModel.reloadPlacement(e)}presentRequiredInfo(){this.requiredInfoViewModel.presentRequiredInfo()}showViewModel(e){this.instanceId=e.instanceId,e.beginFullScreenPresentation(this.ad)}}function Na(e){return(t,i)=>{console.debug(`[BE] Impression Attempt for placement: ${i} with url: ${t}`),n(t)?ya("Impression",400,void 0,"No impression URL found."):Pa("Impression",t,e)}}function Oa(e){return(t,i)=>{console.debug(`[BE] Win Attempt for placement: ${i} with url: ${t}`),n(t)?ya("Win",400,void 0,"No Win URL found."):Pa("Win",t,e)}}function Ia(e){return Aa(e)}function Ma(e){return t=>{if(!n(t))return new Uo(t,e)}}const Va="PresentationController already locked by";class Da{constructor(e={},t=globalThis.HYPREventController,i=HYPRPresentationListener,r=globalThis.preloadController,s=globalThis.HYPRPlacementController,n=aa){this.args=e,this.listener=i,this.eventController=t,this.preloadController=r,this.placementController=s,this.initConfiguration=globalThis.initializationController?.getConfiguration(),this.vmController=n,this.presentedSession=void 0}getFullscreenViewModel(e,t=void 0){let i,r;try{i=t||this.presentedSession.getAdType(),console.debug(`[Presentation] retreiving viewModel for placement: ${e}, ad type: ${i}`),r=this.vmController.getFullScreenViewModelForPlacementName(e)}catch(e){const t=`Error accessing viewModelMap: ${e}\nCurrent Contents: ${this.vmController.toContentString()}`,i="shared_js.PresentationController.getFullscreenViewModel";console.error(t),jo({message:t,type:i})}if(void 0!==r)return r;const s=this.placementController.getPlacement(e);if(void 0===s)return;const n=this.vmController.generateUniqueId();return r=Ta.viewModel(s,i,n,this.listener,this.args,this),this.vmController.storeViewModel(n,r),r}getPreloadedViewModel(e){try{return this.vmController.getViewModel(e)}catch(t){return void console.error(`Error getting preloaded ViewModel: ${e} error: ${t}`)}}removePreloadedViewModel(e){e!==this.presentedSession?.instanceId?(console.debug(`[PresentationControler] removing instance from map: ${e}`),this.vmController.removeViewModel(e)):console.error("Error - trying to remove a ViewModel being presented")}showFullscreenAd(e){if(console.debug(`[Presentation] request to show for ${e}`),Eo.canPrompt(this.initConfiguration)){console.debug("PresentationController will prompt for TransparencyTracking approval");try{return void Eo.prompt(`HYPRPresentationController.showFullscreenAd('${e}')`)}catch(t){console.error(`PresentationController.showFullscreenAd(${e}) error displaying prompt.`)}}const t=this.placementController.getPlacement(e),i=new xa(t,this.listener);i.isValid()?this.isPresentationLocked()?this.listener.adDisplayError(e,`PresentationController already showing ad for placement ${e}.`):this.beginPresentation(i):this.adFailedToDisplay(e,"Placement not a valid fullscreen Ad.")}beginPresentation(e){console.debug(`[Presentation] transition to beginPresentation to lock display ${e.getPlacementName()}`);try{this.lockPresentationWithSession(e)}catch(t){return this.adFailedToDisplay(e.getPlacementName(),`SDK reported presentation error: ${t.message}`),void(t.message.includes(Va)||this.endPresentation(!1))}this.presentedSession.fireImpressionOnce(),this.onDisplayQuestions()}onDisplayQuestions(){if(!this.presentedSession)return console.error("[Presentation] Error presenting questions, no session"),void this.endPresentation(!1);console.debug(`[Presentation] transition to onDisplayQuestions with ${this.presentedSession.questionCount()} questions to show`),this.presentedSession.questionCount()>0?this.presentedSession.presentRequiredInfo():this.onDisplayAd()}onDisplayAd(){if(!this.presentedSession)return console.error("[Presentation] Error presenting ad, no session"),void this.endPresentation(!1);console.debug(`[Presentation] transition to onDisplayAd for placement ${this.presentedSession.getPlacementName()}`);const e=this.getFullscreenViewModel(this.presentedSession.getPlacementName());this.presentedSession.showViewModel(e)}endPresentation(e){if("object"!=typeof this.presentedSession)return void console.debug("[Presentation] endPresentation called without a presentedSession (see PLAYER-15335)");const t=this.presentedSession.getPlacementName();console.debug(`[Presentation] transition endPresentation for placement ${t}`),this.placementController.startAdRefresh("refresh"),console.debug(`[Presentation] will remove viewModel instance: ${this.presentedSession.instanceId}`),this.clearFullscreenViewModel(this.presentedSession.instanceId),this.presentedSession=void 0,e?this.listener.adFinished(t):this.listener.adCanceled(t)}clearFullscreenViewModel(e){this.vmController.removeViewModel(e)}lockPresentationWithSession(e){const t=e.getPlacementName();if(this.isPresentationLockedWithAnotherPlacement(t))throw new Error(`${Va} ${this.presentedSession.getPlacementName()}`);this.placementController.getPlacement(t).stopAdAvailableTimer(),this.isPresentationLocked()||(console.debug(`[Presentation] PresentationController.lockPresentationWithPlacement(${t})`),this.listener.adStarted(t),this.presentedSession=e)}adFailedToDisplay(e,t){console.debug(t),this.presentedSession=void 0,this.listener.adStarted(e),this.listener.adDisplayError(e,t),this.listener.adFinished(e)}isPresentationLockedWithAnotherPlacement(e){return void 0!==this.presentedSession&&!this.presentedSession.isSession(e)}isPresentationLocked(){return void 0!==this.presentedSession}bindBannerViewModel(e){let t=this.placementController.getPlacement(e);"object"==typeof t&&t.isBannerAdType()||(console.error(`error binding Banner for placement: ${e}`),t=void 0);const i=this.vmController.generateUniqueId(),r=new ia(Ra(),Na,Ia,Ma,Oa,t,this,i);return this.vmController.storeViewModel(i,r),i}initializeBrowserViewModel(e,t,i,r){let s,n;const o=i.getAdModel();void 0!==o&&(s=o.getClickMode(),n=o.onPageJS);const a=this.vmController.generateUniqueId();console.debug(`Initialize BrowserViewModel for requester ${e} creating vm ${a}`);const l=i.generateTimeOnSiteTracking(),d=new Le(t.initialUrl,s,n,i,l,r,this,a);return this.vmController.storeViewModel(a,d),i.browserInstance=d,a}destroyBrowserViewModel(e){this.vmController.removeViewModel(e)}async requiredInfoPresentationCompletedWithParams(e){console.debug("[Presentation] Prequal form finished with questions answered"),e.context="userInfoSubmission",await this.presentedSession.performReload(e),console.debug("[Presentation] Reload of placement completed"),this.onDisplayQuestions()}requiredInfoPresentationCancelled(){console.debug("[Presentation] Prequal form was cancelled"),this.onDisplayQuestions()}adDismissed(e){console.debug(`[Presentation] adDismissed(${e})`),this.endPresentation(e)}adRewarded(){this.presentedSession.isRewarded()&&(console.debug(`Ad Rewarded for ${this.presentedSession.getPlacementName()}`),this.listener.adRewarded(this.presentedSession.getPlacementName(),this.presentedSession.getAdRewardText(),this.presentedSession.getAdRewardQuantity()))}presentationSceneBackgrounded(){this.isPresentationLocked()&&console.debug(`Ad Window backgrounded for ${this.presentedSession.getPlacementName()}`)}presentationSceneForegrounded(){this.isPresentationLocked()&&console.debug(`Ad Window foregrounded for ${this.presentedSession.getPlacementName()}`)}preloadedWebViewPageReadyExpired(e){this.preloadController.clearMraidPreload(e)}static attributesForWebtrafficText(e){return e===b||e===_?JSON.stringify({Color:"FFFFFFFF",Font:{fontName:"Helvetica Neue",size:13.33},NSKern:0,NSParagraphStyle:{alignment:"left"},NSStrokeWidth:0}):e===v?JSON.stringify({Color:"3A3A3AFF",Font:{fontName:"Helvetica Neue",size:13.33},NSKern:0,NSParagraphStyle:{alignment:"left"},NSStrokeWidth:0}):e===w?JSON.stringify({Color:"5D5D5DFF",Font:{fontName:"HelveticaNeue-Bold",size:18.67},NSKern:0,NSParagraphStyle:{alignment:"left",minimumLineHeight:17},NSStrokeWidth:0}):"{}"}}globalThis.PresentationController=Da;class Fa{constructor(e){this.version=Fa.osVersion(e.device_os_version),this.platform=Fa.devicePlatform(e.device_type),this.orientations=Fa.supportedInterfaceSettings(e.supported_interface_settings),this.sdkVersion=e.sdk_version||"0",this.msdkv=e.msdkv}isIos(){return"iOS"===this.platform}isAndroid(){return"Android"===this.platform}isPortraitSupported(){return this.version>=14||this.orientations.indexOf("portrait")>-1}static osVersion(e){return void 0===e||-1===e.indexOf(".")?0:parseFloat(e.match(/[0-9]*.[0-9]*/)[0])}static devicePlatform(e){return void 0===e?"unknown":e.includes("iP")||e.toLowerCase().includes("mac")?"iOS":e.toLowerCase().includes("android")?"Android":"unknown"}static supportedInterfaceSettings(e){const t=[];return void 0===e||e.forEach((e=>{const i=e.split("UIInterfaceOrientation")[1];void 0!==i&&t.push(i.toLowerCase())})),t}}class Ba{constructor(e){return console.debug("[IC] Returning pre-initialized InitController"),void 0!==e&&(globalThis.initializationController.host=e),globalThis.initializationController}}globalThis.InitializationController=Ba;class Ua{constructor(e){console.debug("[IC] Initializing"),this._host=e,this.initialized=!1,this.configuration=void 0,this.deviceConfiguration=void 0,this.failureCount=0}get host(){const e=HYPRRequestParamListener.isTestModeEnabled?.()??!1?"test-":"";if(this._host)return`${e}${this._host}`;const t=this.deviceConfiguration?.platform?.toLowerCase(),i=this.deviceConfiguration?.msdkv;return`${e}${`marketplace-${t}-b${i}.hyprmx.com`}`}set host(e){this._host=e}get initializationURL(){return`https://${this.host}/v1/initialization`}initFromNative(e,t){console.debug(`[IC] Initializing from native ${t}`),void 0!==e&&(this.host=e),this.failureCount=t,this.initialize()}initialize(e={},t=globalThis.HYPRNetworkController,i=globalThis.preloadController,r=HYPRRequestParameterManager){!function(e,t,i,r=500,s=globalThis.HYPRTimerController){const n=function(e,t){return 0===e?0:t*2**(e-1)}(i,r);console.warn(`[BM] ${e} has been delayed ${n} millis.`),0!==n?s.startTimer(n,(async()=>{t()})):t()}("networkRequestController.initialize",(()=>{console.debug("[IC] Attempting to initialize"),this.init(e,t,i,r)}),this.failureCount)}init(e={},t=globalThis.HYPRNetworkController,i=globalThis.preloadController,r=HYPRRequestParameterManager){this.preloadController=i,this.networkRequestController=t,this.requestParameterManager=r,this.initialized=!1,this.configuration=void 0,this.requestParameterManager.setPreloadController(this.preloadController);const s=this.requestParameterManager.getInitializationParams();this.deviceConfiguration=new Fa(s),globalThis.IS_IOS=this.deviceConfiguration.isIos();const n=JSON.parse(hyprMXLocalStorage.getItem(u)),o=hyprMXLocalStorage.getItem(h);console.debug(`oldPersistentId: ${o}`),console.debug(`oldOptedOut: ${n}`),(void 0!==s[u]&&null!=n&&n!==s[u]||void 0!==s[h]&&null!=o&&o!==s[h])&&(console.debug("Persistent Id reset detected"),s.reset_user_data=!0),this.networkRequestController.post(this.initializationURL,s,((e,t,i)=>{this.response(e,t,i,(()=>{void 0!==s[u]&&hyprMXLocalStorage.setItem(u,s[u].toString()),void 0!==s[h]&&hyprMXLocalStorage.setItem(h,s[h].toString())}))}))}response(e,t,i,r=(()=>{})){const s=Q.parseJSON(t);if(void 0!==HYPRInitListener.setEnableAllLogs&&HYPRInitListener.setEnableAllLogs(void 0!==s&&void 0!==s.enable_all_logs&&s.enable_all_logs),j.validResponseStatus(e)&&void 0===i&&this.validateResponse(s)){if(r(),this.configuration=s,n=s.error_reporting_endpoint,o=s.error_severity_level,Jo=n,Yo=o,this.updateDefaultUIComponents(s.ui_components),function(e){Ee=new Re(e)}(s),void 0!==s.new_mobile_js?this.updateJavascript(s):(this.initializeNativeControllers(s),na.checkStoredData()),void 0!==s.open_measurement){const e=s.open_measurement;HYPRInitListener.initializeOMSDK(e.omsdk_url,e.partner_name,e.api_version)}void 0!==s.app_user_token&&(xo.APP_USER_TOKEN=s.app_user_token)}else this.initialized=!1,console.error(`Initialization Failed Response: ${t} code ${e} error: ${i}`),HYPRInitListener.initializationFailed(`${e}`);var n,o}validateResponse(e){return void 0!==e&&Q.hasRequiredKeys(e,["sharing_endpoint","completion_endpoint","ad_impression_tracking_endpoint","user_info_tracking_endpoint","duration_update_endpoint","error_reporting_endpoint","webtraffic_visit_endpoint","no_ad_tracking_endpoint","ad_progress_endpoint"],"url")&&Q.hasRequiredKeys(e,["placements"],"object")}javascriptUpgradeFailed(e){this.initializeNativeControllers(this.configuration)}initializeNativeControllers(e){this.initialized=!0;const t=JSON.stringify(e.placements);var i;globalThis.COMPLETION_URL=e.completion_endpoint,globalThis.BASE_RTB_REQUEST=e.base_rtb_request,void 0!==e.available_for&&(globalThis.AVAILABLE_FOR=e.available_for),globalThis.OMIDPN=e.open_measurement?.partner_name,globalThis.OMIDPV=e.open_measurement?.api_version,Mo((()=>{globalThis.OMIDPN=void 0,globalThis.OMIDPV=void 0})),(i=this.networkRequestController,e=>{0!==e.length?i.get(e,((t,i,r,s)=>{console.debug(`[DPF] responded with ${t} and response: ${i}`),j.validResponseStatus(t)&&void 0===r?(console.debug(`[DPF] Requesting ad display for ${e} completed with response.`),globalThis.PLAYER_AD_DISPLAY_TEMPLATE=i):console.debug(`[DPF] Requesting ad display for ${e} failed with error: ${r}`)})):console.debug("[DPF] Requesting ad display url is blank.  Template not downloaded.")})(Ee.playerAdDisplayIndex),Io.BASE_RTB_REQUEST=e.base_rtb_request,void 0!==e.banner_html_wrapper_tag&&(Go.wrapperHtml=e.banner_html_wrapper_tag),this.createEventController(e),globalThis.HYPRStorePresentationController=new ue(this.deviceConfiguration,e.skadnetwork_signature_endpoint),globalThis.HYPRPlacementController=new Vo(e.placements),globalThis.HYPRPresentationController=new Da({userId:this.requestParameterManager.getUserId()}),globalThis.preloadController.setPresentationController(globalThis.HYPRPresentationController);const{audioStatusManager:r}=Ra();r?.activate(!0),HYPRInitListener.initializationSuccessWithPlacements(t,globalThis.version()),this.preloadController.handleUIComponentsToPreloadResponse(e.ui_components)}createEventController(e){globalThis.HYPREventController=new ee(e.ad_impression_tracking_endpoint,e.user_info_tracking_endpoint,e.webtraffic_visit_endpoint,e.error_reporting_endpoint,e.error_severity_level,e.no_ad_tracking_endpoint,e.ad_progress_endpoint,e.duration_update_endpoint)}updateJavascript(e){let t=8;void 0!==e.new_mobile_js.timeout&&(t=e.new_mobile_js.timeout),HYPRInitListener.updateJavascript(e.new_mobile_js.url,e.new_mobile_js.version,t)}isInitialized(){return this.initialized}allowsInitialization(){return"object"!=typeof HYPRPresentationController||!HYPRPresentationController.isPresentationLocked()}requestInitializationAllowed(e={}){const t=this.allowsInitialization();return HYPRInitListener.initializationIsAllowed?.(this.allowsInitialization(),JSON.stringify(e)),t}getConfiguration(){return this.configuration}updateDefaultUIComponents(e){void 0!==e&&"object"==typeof JSON.parse(JSON.stringify(e))&&(globalThis.defaultUIComponents=e)}}const $a="IABConsent_CMPPresent",La="IABConsent_SubjectToGDPR",Wa="IABConsent_ConsentString",qa="IABConsent_ParsedPurposeConsents",za="IABConsent_ParsedVendorConsents",Ha=[$a,La,Wa,qa,za];class Ja{constructor(e,t){if(void 0!==globalThis.consentController)return globalThis.consentController;this._sharedDataController=t,this._nativeConsentController=e}get sharedDataController(){return void 0===this._sharedDataController&&void 0!==globalThis.HYPRSharedDataController&&(this._sharedDataController=HYPRSharedDataController),this._sharedDataController}get nativeConsentController(){return void 0===this._nativeConsentController&&void 0!==globalThis.HYPRNativeConsentController&&(this._nativeConsentController=HYPRNativeConsentController),this._nativeConsentController}setConsentChangedListener(e){this.consentListener=e,this.consentStatus=this.nativeConsentController?.getConsentStatus?.();for(const e of Ha)this.sharedDataController?.monitorSharedValue?.("HYPRConsentController",e)}consentStatusChanged(e){this.consentStatus!==e?(this.consentStatus=e,console.debug(`[CC] Consent status changed to ${Object.keys(S)[e]}`),void 0!==this.consentListener&&this.consentListener.onConsentChanged()):console.debug("[CC] Consent status has not changed.  Ignoring.")}onValueChanged(e){console.debug(`[CC] Monitored consent string ${e.key} changed to ${e.value}`),void 0!==this.consentListener&&this.consentListener.onConsentChanged()}getConsentParameters(){const e={},t=this.nativeConsentController?.getConsentStatus?.();t!==S.CONSENT_STATUS_UNKNOWN&&(e.is_consent_given=t===S.CONSENT_GIVEN);const i=JSON.parse(this.sharedDataController?.getSharedValue?.($a)).value;void 0!==i&&(e.iab_consent_cmppresent=i);const r=JSON.parse(this.sharedDataController?.getSharedValue?.(La)).value;void 0!==r&&(e.iab_consent_subject_to_gdpr=r);const s=JSON.parse(this.sharedDataController?.getSharedValue?.(Wa)).value;void 0!==s&&(e.iab_consent_string=s);const n=JSON.parse(this.sharedDataController?.getSharedValue?.(qa)).value;void 0!==n&&(e.iab_consent_parsed_purpose_consents=n);const o=JSON.parse(this.sharedDataController?.getSharedValue?.(za)).value;return void 0!==o&&(e.iab_consent_parsed_vendor_consents=o),console.debug(`[CC] ${JSON.stringify(e)}`),e}}globalThis.ConsentController=Ja;class Ya{constructor(e,t,i){this.timerId=t,this.callback=i,this.setExpirationDate(e)}setExpirationDate(e){const t=1e3*e;this.expirationDate=new Date((new Date).getTime()+t)}timeLeft(){const e=this.expirationDate-new Date;return e<=0?0:e/1e3}}class ja{constructor(){this.timerMap=new Map,this.requestId=0}startTimer(e,t){const i=this.requestId.toString();this.requestId+=1,HYPRNativeTimer.startNativeTimer(i,e,`HYPRTimerController.timeoutComplete('${i}')`);const r=new Ya(e,i,t);return this.timerMap.set(i,r),i}updateTimeRemaining(e,t){HYPRNativeTimer.updateTimer(e,t);this.timerMap.get(e).setExpirationDate(t)}getTimeLeft(e){if(!this.timerMap.has(e))return;return this.timerMap.get(e).timeLeft()}stopTimer(e){HYPRNativeTimer.stopTimer(e),this.timerMap.delete(e)}timeoutComplete(e){console.debug(`Timeout complete for id ${e}`);const t=this.timerMap.get(e),i=t?.callback;this.timerMap.delete(e),"function"==typeof i&&i()}}globalThis.TimerController=ja;const Ka={mraidPreloadMap:new Map,receivedPreloadRequest(e){console.debug(`[PreloadController] receivedPreloadRequest: ${e.name}`);const t=e.ad?.webview_timeout,i=e.name;let r;try{let t=this.presentationController.getFullscreenViewModel(i,p);console.debug(`[PreloadController] found viewModel: ${t?.instanceId}`),t.getAdModel()?.reward_token!==e.ad?.reward_token&&(console.debug(`[PreloadController] viewModel offer id's do not match: ${t?.instanceId}`),this.removePreload(r),t=this.presentationController.getFullscreenViewModel(i,p)),r=t?.instanceId}catch(e){return void console.error(`[PreloadController] caught error getting viewModel instance: ${e}`)}console.debug(`[PreloadController] setting up preload for instance: ${r}`);const s=this.setupTimer(r,t);console.debug(`[PreloadController] storing timerId: ${s} for instance:${r} `),this.mraidPreloadMap.set(r,s),this.storeNativePreload(r)},setupTimer(e,t){const i=1e3*t;if(this.mraidPreloadMap.has(e)){const t=this.mraidPreloadMap.get(e);return console.debug(`[PreloadController] storing timerId: ${t}`),this.timerController.updateTimeRemaining(t,i),this.mraidPreloadMap.get(e)}const r=this.timerController.startTimer(i,(()=>{console.debug(`[PreloadController] preload ttl timer ended for instance:${e} `),this.removeNativePreload(e)}));return console.debug(`[PreloadController] created new timerId: ${r}`),r},removePreload(e){console.debug(`[PreloadController] will remove preload for instance: ${e} `);try{this.presentationController.removePreloadedViewModel(e)}catch(e){console.error(`[PreloadController] caught error releasing viewModel instance: ${e}`)}if(this.mraidPreloadMap.has(e)){console.debug(`[PreloadController] removing instance:${e} from timer map`);const t=this.mraidPreloadMap.get(e);void 0!==t?this.timerController.stopTimer(t):console.error("[PreloadController] Could not find timer id in preloaded map."),this.mraidPreloadMap.delete(e)}},getPreloadedMraidAds(){const e=[];return this.mraidPreloadMap.forEach(((t,i)=>{const r=this.timerController.getTimeLeft(t),s=this.presentationController.getPreloadedViewModel(i),n={ad_id:s?.getAdModel()?.id,placement_id:s?.placement.id,time_to_expiration:r};e.push(n)})),e}};class Za{constructor(e=0,t=0,i=globalThis.HYPRCacheListener,r=globalThis.HYPRTimerController,s=globalThis.HYPRRequestParamListener){if(void 0!==globalThis.preloadController)return globalThis.preloadController;this._nativeCacheListener=i,this.presentationController=void 0,this.timerController=r,this.mraidPreloadMap=new Map,this._nativeParams=s}get nativeParams(){return void 0===this._nativeParams&&(this._nativeParams=globalThis.HYPRRequestParamListener),this._nativeParams}get nativeCacheListener(){return void 0===this._nativeCacheListener&&(this._nativeCacheListener=globalThis.HYPRCacheListener),this._nativeCacheListener}get width(){return this.nativeParams?.getDeviceWidth()}get height(){return this.nativeParams?.getDeviceHeight()}setPresentationController(e){this.presentationController=e}handleUIComponentsToPreloadResponse(e){void 0!==e&&void 0!==this.nativeCacheListener&&this.preloadImages(e)}preloadImages(e){if(void 0===e||void 0===this.nativeCacheListener)return;for(const t in e)if(e.hasOwnProperty(t))if("image"!==t&&"submit_button_image"!==t&&"close_button_image"!==t||e[t]instanceof Array)"object"==typeof e[t]&&this.preloadImages(e[t]);else{const i=e[t];i.hasOwnProperty("portrait_url")?(console.debug(`Requesting image to preload for (url = ${i.portrait_url}, height=${i.height}, width=${i.width}, fill_screen_width=${i.fill_screen_width})`),this.nativeCacheListener.preloadPortraitImage(i.portrait_url,i.height,i.width,i.fill_screen_width)):i.hasOwnProperty("url")&&(console.debug(`Requesting image to preload for (url = ${i.url}, height=${i.height}, width=${i.width}, scale=${i.scale}, tiled=${i.tiled}, x=${i.x}, y=${i.y})`),this.nativeCacheListener.preloadUIImage(i.url,i.height,i.width,i.scale,i.tiled,i.x,i.y))}}handleMraidAdToPreload(e){void 0!==e&&void 0!==this.nativeCacheListener&&"mraid"===e.getAdType()&&void 0!==e.ad.preload_player_url&&"number"==typeof e.ad.webview_timeout&&(e.ad.webview_timeout<1?this.removeNativePreload(e):this.receivedPreloadRequest(e))}storeNativePreload(e){console.debug(`[PreloadController] requesting native storage for: ${e}`),this.nativeCacheListener.storeInstance(e)}removeNativePreload(e){console.debug(`[PreloadController] requesting native to remove storage for: ${e}`),this.nativeCacheListener.removeInstance(e)}nativePreloadDidRemove(e){console.debug(`[PreloadController] notified: ${e} was removed by native`),this.removePreload(e)}}Object.assign(Za.prototype,Ka),globalThis.PreloadController=Za,globalThis.HYPRNetworkController=new j,globalThis.HYPRTimerController=new ja,globalThis.dispatchCEC=jo,globalThis.consentController=new Ja,globalThis.HYPRConsentController=globalThis.consentController,globalThis.callAsync=function(e,t,i=globalThis,r=[],s=((...e)=>{globalThis.NativeContinuationController.resume(...e)}),n=((...e)=>{globalThis.NativeContinuationController.resumeWithError(...e)})){console.debug(`[CC] starting async task ${e}...`),(async()=>i[t].apply(i,r))().then((t=>{console.debug(`[CC] successfully completed task ${e}`),s(e,"object"!=typeof t?t:JSON.stringify(t))})).catch((t=>{console.debug(`[CC] async task ${e} error: ${t}`),n(e,t)}))},globalThis.initializationController=new Ua,globalThis.HYPRInitializationController=globalThis.initializationController,globalThis.preloadController=new Za,globalThis.HYPRPreloadController=globalThis.preloadController,e.AD_CLOSED_ACTION=C,e.AD_CLOSED_ACTION_KEY="action",e.AD_PROGRESS_STATE=T,e.APPSTORE_URL_FALLBACK_PRESENT_BROWSER=!0,e.BANNER_OFFER=m,e.CONSENT_STATUS=S,e.ConsentController=Ja,e.DISPLAY_OFFER=f,e.HYPR_BANNER_ENCODING=A,e.HYPR_BANNER_MIME_TYPE=P,e.HYPR_COUNTDOWN_TEXT_KEY=w,e.HYPR_DEFAULT_ACCEPTABLE_BANNER_SIZE_VARIANCE=1,e.HYPR_FINISH_BUTTON_TEXT_KEY=_,e.HYPR_NEXT_BUTTON_TEXT_KEY=b,e.HYPR_TITLE_TEXT_KEY=v,e.HYPR_WEB_VIEW_PROTOCOLS=y,e.InitController=Ua,e.InitializationController=Ba,e.KEYS=E,e.MRAID_OFFER=p,e.OptedOutKey=u,e.PAGE_LOADING_STATE=R,e.PLACEMENT_TYPE=te,e.PersistentIdKey=h,e.Placement=ie,e.PlacementController=Vo,e.PreloadController=Za,e.PresentationController=Da,e.RequestParameterManager=xo,e.SKAdNetworkItemsKey="SKAdNetworkItems",e.TimerController=ja,e.VIEW_MODEL_PREFIX={BASE:"HyprMXAd",WEBVIEW:"HyprMXWebView",BROWSER:"HyprMXBrowser"},e.WEB_TRAFFIC_OFFER=g,e.addToArray=function(e,t){void 0!==t&&(Array.isArray(t)?e.push(...t):e.push(t))},e.cdnDomain=i,e.deepClone=l,e.defaultAdAvailableFor=s,e.defaultWebViewReleaseSeconds=0,e.dispatch=function(e,t,i){t.apply(e,i)},e.equalsIgnoreCase=o,e.initializationPath=r,e.isNullOrEmpty=n,e.isObject=d,e.isValidUrl=a,e.mergeDeep=c,e.numberOrZero=function(e){const t=parseFloat(e);return Number.isNaN(t)?0:t},Object.defineProperty(e,"__esModule",{value:!0})}));
