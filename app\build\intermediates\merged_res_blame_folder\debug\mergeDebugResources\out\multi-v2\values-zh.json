{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-zh/values-zh.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\25197d2b38fc8d21bd942743667033fe\\transformed\\jetified-mbridge_android_sdk-16.9.71\\res\\values-zh\\values-zh.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10974,11034,11104,11175,11249,11328,11409,11483,11564,11647,11728,11805,11871,11931,11993,12068,12125,12215,12276,12339,12420,12500,12587,12654,12723,12796", "endColumns": "59,69,70,73,78,80,73,80,82,80,76,65,59,61,74,56,89,60,62,80,79,86,66,68,72,66", "endOffsets": "11029,11099,11170,11244,11323,11404,11478,11559,11642,11723,11800,11866,11926,11988,12063,12120,12210,12271,12334,12415,12495,12582,12649,12718,12791,12858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-zh\\values-zh.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16987,17037,17093,17143,17189,17248,17300,17356,17415,17466,17504,17577,17627,17678,17741,17791,17848,17906,17956,17995,18061,18114,18182,18261,18324,18400,18455,18509,18565,18640,18714,18771,18831,18898,18974,19035,19096,19165,19214,19282,19354,19422,19481,19534,19594,19652,19691,19760,19813,19880,19962,20004,20046,20099,20165,20219,20265,20313,20355,20407,20461,20503,20552,20599,20650,20709,20755,20805,20870,20937,21002,21075,21134,21192,21258,21326,21387,21444,21510,21560,21621,21676,21732,21804,21855,21912,21960,22029,22095,22159,22222,22285,22354,22423,22483,22546,22609,22669,22745,22797,22844,22904,22966,23027,23071,23120,23173,23228,23287,23347,23416,23467", "endColumns": "49,55,49,45,58,51,55,58,50,37,72,49,50,62,49,56,57,49,38,65,52,67,78,62,75,54,53,55,74,73,56,59,66,75,60,60,68,48,67,71,67,58,52,59,57,38,68,52,66,81,41,41,52,65,53,45,47,41,51,53,41,48,46,50,58,45,49,64,66,64,72,58,57,65,67,60,56,65,49,60,54,55,71,50,56,47,68,65,63,62,62,68,68,59,62,62,59,75,51,46,59,61,60,43,48,52,54,58,59,68,50,51", "endOffsets": "17032,17088,17138,17184,17243,17295,17351,17410,17461,17499,17572,17622,17673,17736,17786,17843,17901,17951,17990,18056,18109,18177,18256,18319,18395,18450,18504,18560,18635,18709,18766,18826,18893,18969,19030,19091,19160,19209,19277,19349,19417,19476,19529,19589,19647,19686,19755,19808,19875,19957,19999,20041,20094,20160,20214,20260,20308,20350,20402,20456,20498,20547,20594,20645,20704,20750,20800,20865,20932,20997,21070,21129,21187,21253,21321,21382,21439,21505,21555,21616,21671,21727,21799,21850,21907,21955,22024,22090,22154,22217,22280,22349,22418,22478,22541,22604,22664,22740,22792,22839,22899,22961,23022,23066,23115,23168,23223,23282,23342,23411,23462,23514"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,267,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,268,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,269,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,15542,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,15590,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,15644,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,46,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,52,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,65,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,15584,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,15638,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,15705,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,106,149,193,236,280,328,372,416,465,513,561,605,651,697,743,818,888,940,987,1033,1081,1147,1212,1279,1345,1409,1461,1506,1558,1593,1662,1710,1772,1814,1867,1928,1969,2064,2106,2218,2255,2301,2889,2926,2977,3022,3058,3113,3160,3216,3362,3511,3548,3607,3666,3921,3990,4074,4129,4182,4268,4386,4444,4481,4518,4566,4621,4661,4714,4766,4817,4855,4908,4966,5004,5052,5126,5179,5242,5304,5354,5413,5464,5516,5562,5608,5657,5702,5741,5796,5848,5890,6011,6059,6108,6302,6506,6564,6606,6672,6717,6845,6889,7051,7100,7180,7261,7343,7393,7432,7473,7529,7595,7660,7704,7771,7839,7887,7944,8002,8118,8235,8324,8403,8466,8515,8568,8616,8664,8701,8762,8812,8870,8954,8997,9050,9100,9141,9221,9265,9338,9429,9465,9512,9550,9588,9624,9660,9715,9753,9794,9832,9882,9938,9991,10048,10091,10138,10178,10215,10278,10429,10473,10525,10606,10661,10719,10755,10789,10829,10871,10923,12863,12919,12965,13023,13091,13124,13183,13230,13274,13335,13399,13464,13536,13599,13646,13681,13735,13848,13917,14003,14064,14137,14177,14216,14271,14321,14372,14429,14466,14519,14580,14621,14658,14698,14765,14814,14867,14934,15006,15045,15094,15185,15242,15286,15334,15389,15433,15480,15605,15662,15730,15785,15825,15878,15933,15987,16028,16079,16119,16205,16337,16403,16453,16495,16542,16590,16634,16677,16714,16762,16815,16928,23519,23570,23625,23671,23728,23775,23813,23856,23902,23945,23984,24025,24068,24114,24166,24208,24242,24277", "endColumns": "50,42,43,42,43,47,43,43,48,47,47,43,45,45,45,74,69,51,46,45,47,65,64,66,65,63,51,44,51,34,68,47,61,41,52,60,40,94,41,111,36,45,39,36,50,44,35,54,46,55,145,148,36,58,58,254,68,83,54,52,85,117,57,36,36,47,54,39,52,51,50,37,52,57,37,47,73,52,62,61,49,58,50,51,45,45,48,44,38,54,51,41,120,47,48,193,203,57,41,65,44,127,43,161,48,79,80,81,49,38,40,55,65,64,43,66,67,47,56,57,115,116,88,78,62,48,52,47,47,36,60,49,57,83,42,52,49,40,79,43,72,90,35,46,37,37,35,35,54,37,40,37,49,55,52,56,42,46,39,36,62,150,43,51,80,54,57,35,33,39,41,51,50,55,45,57,67,32,58,46,43,60,63,64,71,62,46,34,53,112,68,85,60,72,39,38,54,49,50,56,36,52,60,40,36,39,66,48,52,66,71,38,48,90,56,43,47,54,43,46,124,56,67,54,39,52,54,53,40,50,39,85,131,65,49,41,46,47,43,42,36,47,52,112,58,50,54,45,56,46,37,42,45,42,38,40,42,45,51,41,33,34,43", "endOffsets": "101,144,188,231,275,323,367,411,460,508,556,600,646,692,738,813,883,935,982,1028,1076,1142,1207,1274,1340,1404,1456,1501,1553,1588,1657,1705,1767,1809,1862,1923,1964,2059,2101,2213,2250,2296,2336,2921,2972,3017,3053,3108,3155,3211,3357,3506,3543,3602,3661,3916,3985,4069,4124,4177,4263,4381,4439,4476,4513,4561,4616,4656,4709,4761,4812,4850,4903,4961,4999,5047,5121,5174,5237,5299,5349,5408,5459,5511,5557,5603,5652,5697,5736,5791,5843,5885,6006,6054,6103,6297,6501,6559,6601,6667,6712,6840,6884,7046,7095,7175,7256,7338,7388,7427,7468,7524,7590,7655,7699,7766,7834,7882,7939,7997,8113,8230,8319,8398,8461,8510,8563,8611,8659,8696,8757,8807,8865,8949,8992,9045,9095,9136,9216,9260,9333,9424,9460,9507,9545,9583,9619,9655,9710,9748,9789,9827,9877,9933,9986,10043,10086,10133,10173,10210,10273,10424,10468,10520,10601,10656,10714,10750,10784,10824,10866,10918,10969,12914,12960,13018,13086,13119,13178,13225,13269,13330,13394,13459,13531,13594,13641,13676,13730,13843,13912,13998,14059,14132,14172,14211,14266,14316,14367,14424,14461,14514,14575,14616,14653,14693,14760,14809,14862,14929,15001,15040,15089,15180,15237,15281,15329,15384,15428,15475,15600,15657,15725,15780,15820,15873,15928,15982,16023,16074,16114,16200,16332,16398,16448,16490,16537,16585,16629,16672,16709,16757,16810,16923,16982,23565,23620,23666,23723,23770,23808,23851,23897,23940,23979,24020,24063,24109,24161,24203,24237,24272,24316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d148dadd36a9aeec86630cf6fa0b2687\\transformed\\jetified-applovin-sdk-13.3.1\\res\\values-zh\\values-zh.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "45,46,47,48,49,50,51,52", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2341,2417,2471,2532,2627,2703,2765,2829", "endColumns": "75,53,60,94,75,61,63,59", "endOffsets": "2412,2466,2527,2622,2698,2760,2824,2884"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tqhit.battery.one.app-mergeDebugResources-132:\\values-zh\\values-zh.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\25197d2b38fc8d21bd942743667033fe\\transformed\\jetified-mbridge_android_sdk-16.9.71\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,185,256,330,409,490,564,645,728,809,886,952,1012,1074,1149,1206,1296,1357,1420,1501,1581,1668,1735,1804,1877", "endColumns": "59,69,70,73,78,80,73,80,82,80,76,65,59,61,74,56,89,60,62,80,79,86,66,68,72,66", "endOffsets": "110,180,251,325,404,485,559,640,723,804,881,947,1007,1069,1144,1201,1291,1352,1415,1496,1576,1663,1730,1799,1872,1939"}, "to": {"startLines": "182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10927,10987,11057,11128,11202,11281,11362,11436,11517,11600,11681,11758,11824,11884,11946,12021,12078,12168,12229,12292,12373,12453,12540,12607,12676,12749", "endColumns": "59,69,70,73,78,80,73,80,82,80,76,65,59,61,74,56,89,60,62,80,79,86,66,68,72,66", "endOffsets": "10982,11052,11123,11197,11276,11357,11431,11512,11595,11676,11753,11819,11879,11941,12016,12073,12163,12224,12287,12368,12448,12535,12602,12671,12744,12811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,105,161,211,257,316,368,424,483,534,572,645,695,746,809,859,916,974,1024,1063,1129,1182,1250,1329,1392,1468,1523,1577,1633,1708,1782,1839,1899,1966,2042,2103,2164,2233,2282,2350,2422,2490,2549,2602,2662,2720,2759,2828,2881,2948,3030,3072,3114,3167,3233,3287,3333,3381,3423,3475,3529,3571,3620,3667,3718,3777,3823,3873,3938,4005,4070,4143,4202,4260,4326,4394,4455,4512,4578,4628,4689,4744,4800,4872,4923,4980,5028,5097,5163,5227,5290,5353,5422,5491,5551,5614,5677,5737,5813,5865,5912,5972,6034,6095,6139,6188,6241,6296,6355,6415,6484,6535", "endColumns": "49,55,49,45,58,51,55,58,50,37,72,49,50,62,49,56,57,49,38,65,52,67,78,62,75,54,53,55,74,73,56,59,66,75,60,60,68,48,67,71,67,58,52,59,57,38,68,52,66,81,41,41,52,65,53,45,47,41,51,53,41,48,46,50,58,45,49,64,66,64,72,58,57,65,67,60,56,65,49,60,54,55,71,50,56,47,68,65,63,62,62,68,68,59,62,62,59,75,51,46,59,61,60,43,48,52,54,58,59,68,50,51", "endOffsets": "100,156,206,252,311,363,419,478,529,567,640,690,741,804,854,911,969,1019,1058,1124,1177,1245,1324,1387,1463,1518,1572,1628,1703,1777,1834,1894,1961,2037,2098,2159,2228,2277,2345,2417,2485,2544,2597,2657,2715,2754,2823,2876,2943,3025,3067,3109,3162,3228,3282,3328,3376,3418,3470,3524,3566,3615,3662,3713,3772,3818,3868,3933,4000,4065,4138,4197,4255,4321,4389,4450,4507,4573,4623,4684,4739,4795,4867,4918,4975,5023,5092,5158,5222,5285,5348,5417,5486,5546,5609,5672,5732,5808,5860,5907,5967,6029,6090,6134,6183,6236,6291,6350,6410,6479,6530,6582"}, "to": {"startLines": "278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16821,16871,16927,16977,17023,17082,17134,17190,17249,17300,17338,17411,17461,17512,17575,17625,17682,17740,17790,17829,17895,17948,18016,18095,18158,18234,18289,18343,18399,18474,18548,18605,18665,18732,18808,18869,18930,18999,19048,19116,19188,19256,19315,19368,19428,19486,19525,19594,19647,19714,19796,19838,19880,19933,19999,20053,20099,20147,20189,20241,20295,20337,20386,20433,20484,20543,20589,20639,20704,20771,20836,20909,20968,21026,21092,21160,21221,21278,21344,21394,21455,21510,21566,21638,21689,21746,21794,21863,21929,21993,22056,22119,22188,22257,22317,22380,22443,22503,22579,22631,22678,22738,22800,22861,22905,22954,23007,23062,23121,23181,23250,23301", "endColumns": "49,55,49,45,58,51,55,58,50,37,72,49,50,62,49,56,57,49,38,65,52,67,78,62,75,54,53,55,74,73,56,59,66,75,60,60,68,48,67,71,67,58,52,59,57,38,68,52,66,81,41,41,52,65,53,45,47,41,51,53,41,48,46,50,58,45,49,64,66,64,72,58,57,65,67,60,56,65,49,60,54,55,71,50,56,47,68,65,63,62,62,68,68,59,62,62,59,75,51,46,59,61,60,43,48,52,54,58,59,68,50,51", "endOffsets": "16866,16922,16972,17018,17077,17129,17185,17244,17295,17333,17406,17456,17507,17570,17620,17677,17735,17785,17824,17890,17943,18011,18090,18153,18229,18284,18338,18394,18469,18543,18600,18660,18727,18803,18864,18925,18994,19043,19111,19183,19251,19310,19363,19423,19481,19520,19589,19642,19709,19791,19833,19875,19928,19994,20048,20094,20142,20184,20236,20290,20332,20381,20428,20479,20538,20584,20634,20699,20766,20831,20904,20963,21021,21087,21155,21216,21273,21339,21389,21450,21505,21561,21633,21684,21741,21789,21858,21924,21988,22051,22114,22183,22252,22312,22375,22438,22498,22574,22626,22673,22733,22795,22856,22900,22949,23002,23057,23116,23176,23245,23296,23348"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "104,156,200,245,289,334,383,428,473,523,572,621,666,713,760,13805,807,878,931,979,1026,1075,1142,1208,1276,1343,1408,1461,15442,1507,1543,1613,1662,1725,1768,1822,13975,14544,14770,14867,1884,57,15401,14131,15300,1922,1968,2005,2061,2109,2166,2313,2463,7221,7281,6965,6895,7341,2501,2557,2611,2698,2817,2876,2914,2952,3001,3057,3098,13881,3152,3204,14077,3243,3302,3341,3390,3465,3519,3583,3646,3697,3757,14444,3809,3856,3903,3953,3999,4039,4095,4148,4191,4313,4362,4412,4607,4812,4871,4914,4981,5027,5156,5201,5364,5414,5495,5577,5660,5711,5751,5793,5850,5917,5983,6028,6096,6165,6214,6272,6331,6448,6566,6656,6736,6800,7428,7482,15147,7531,7569,7631,7682,7741,7826,7870,7924,7975,8017,8098,8143,8217,8309,8346,8394,8433,8472,8509,8546,8602,8641,8683,8722,8773,15196,8830,8888,8932,8973,9011,9075,9227,9272,9325,9407,9463,9522,9559,9594,9635,9678,9731,9783,9840,9887,9946,10015,14017,10049,10097,10142,10204,10269,10335,10408,10472,10520,10556,10611,14318,14231,14169,14696,14980,10725,10765,10821,10872,10924,10982,14813,11020,11082,11124,13934,11162,15250,11230,11298,11371,11411,11461,11553,11611,11656,14388,11705,11750,15021,11798,11856,11925,11981,12022,14640,12076,12131,12173,12225,12266,12353,12486,12537,12580,12628,12677,12722,12766,15352,12804,12858,12972,13032,13084,13140,13187,13245,13293,13332,14497,13376,13420,13460,13502,13546,13593,13646,13689,13724,13760", "endColumns": "50,42,43,42,43,47,43,43,48,47,47,43,45,45,45,74,69,51,46,45,47,65,64,66,65,63,51,44,51,34,68,47,61,41,52,60,40,94,41,111,36,45,39,36,50,44,35,54,46,55,145,148,36,58,58,254,68,83,54,52,85,117,57,36,36,47,54,39,52,51,50,37,52,57,37,47,73,52,62,61,49,58,50,51,45,45,48,44,38,54,51,41,120,47,48,193,203,57,41,65,44,127,43,161,48,79,80,81,49,38,40,55,65,64,43,66,67,47,56,57,115,116,88,78,62,48,52,47,47,36,60,49,57,83,42,52,49,40,79,43,72,90,35,46,37,37,35,35,54,37,40,37,49,55,52,56,42,39,36,62,150,43,51,80,54,57,35,33,39,41,51,50,55,45,57,67,32,58,46,43,60,63,64,71,62,46,34,53,112,68,85,60,72,39,38,54,49,50,56,36,52,60,40,36,39,66,48,66,71,38,48,90,56,43,47,54,43,46,124,56,67,54,39,52,54,53,40,50,39,85,131,49,41,46,47,43,42,36,47,52,112,58,50,54,45,56,46,37,42,45,42,38,40,42,45,51,41,33,34,43", "endOffsets": "150,194,239,283,328,377,422,467,517,566,615,660,707,754,801,13875,872,925,973,1020,1069,1136,1202,1270,1337,1402,1455,1501,15489,1537,1607,1656,1719,1762,1816,1878,14011,14634,14807,14974,1916,98,15436,14163,15346,1962,1999,2055,2103,2160,2307,2457,2495,7275,7335,7215,6959,7420,2551,2605,2692,2811,2870,2908,2946,2995,3051,3092,3146,13928,3198,3237,14125,3296,3335,3384,3459,3513,3577,3640,3691,3751,3803,14491,3850,3897,3947,3993,4033,4089,4142,4185,4307,4356,4406,4601,4806,4865,4908,4975,5021,5150,5195,5358,5408,5489,5571,5654,5705,5745,5787,5844,5911,5977,6022,6090,6159,6208,6266,6325,6442,6560,6650,6730,6794,6844,7476,7525,15190,7563,7625,7676,7735,7820,7864,7918,7969,8011,8092,8137,8211,8303,8340,8388,8427,8466,8503,8540,8596,8635,8677,8716,8767,8824,15244,8882,8926,8967,9005,9069,9221,9266,9319,9401,9457,9516,9553,9588,9629,9672,9725,9777,9834,9881,9940,10009,10043,14071,10091,10136,10198,10263,10329,10402,10466,10514,10550,10605,10719,14382,14312,14225,14764,15015,10759,10815,10866,10918,10976,11014,14861,11076,11118,11156,13969,11224,15294,11292,11365,11405,11455,11547,11605,11650,11699,14438,11744,11792,15141,11850,11919,11975,12016,12070,14690,12125,12167,12219,12260,12347,12480,12531,12574,12622,12671,12716,12760,12798,15395,12852,12966,13026,13078,13134,13181,13239,13287,13326,13370,14538,13414,13454,13496,13540,13587,13640,13683,13718,13754,13799"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,106,149,193,236,280,328,372,416,465,513,561,605,651,697,743,818,888,940,987,1033,1081,1147,1212,1279,1345,1409,1461,1506,1558,1593,1662,1710,1772,1814,1867,1928,1969,2064,2106,2218,2255,2301,2889,2926,2977,3022,3058,3113,3160,3216,3362,3511,3548,3607,3666,3921,3990,4074,4129,4182,4268,4386,4444,4481,4518,4566,4621,4661,4714,4766,4817,4855,4908,4966,5004,5052,5126,5179,5242,5304,5354,5413,5464,5516,5562,5608,5657,5702,5741,5796,5848,5890,6011,6059,6108,6302,6506,6564,6606,6672,6717,6845,6889,7051,7100,7180,7261,7343,7393,7432,7473,7529,7595,7660,7704,7771,7839,7887,7944,8002,8118,8235,8324,8403,8466,8515,8568,8616,8664,8701,8762,8812,8870,8954,8997,9050,9100,9141,9221,9265,9338,9429,9465,9512,9550,9588,9624,9660,9715,9753,9794,9832,9882,9938,9991,10048,10091,10131,10168,10231,10382,10426,10478,10559,10614,10672,10708,10742,10782,10824,10876,12816,12872,12918,12976,13044,13077,13136,13183,13227,13288,13352,13417,13489,13552,13599,13634,13688,13801,13870,13956,14017,14090,14130,14169,14224,14274,14325,14382,14419,14472,14533,14574,14611,14651,14718,14767,14834,14906,14945,14994,15085,15142,15186,15234,15289,15333,15380,15505,15562,15630,15685,15725,15778,15833,15887,15928,15979,16019,16105,16237,16287,16329,16376,16424,16468,16511,16548,16596,16649,16762,23353,23404,23459,23505,23562,23609,23647,23690,23736,23779,23818,23859,23902,23948,24000,24042,24076,24111", "endColumns": "50,42,43,42,43,47,43,43,48,47,47,43,45,45,45,74,69,51,46,45,47,65,64,66,65,63,51,44,51,34,68,47,61,41,52,60,40,94,41,111,36,45,39,36,50,44,35,54,46,55,145,148,36,58,58,254,68,83,54,52,85,117,57,36,36,47,54,39,52,51,50,37,52,57,37,47,73,52,62,61,49,58,50,51,45,45,48,44,38,54,51,41,120,47,48,193,203,57,41,65,44,127,43,161,48,79,80,81,49,38,40,55,65,64,43,66,67,47,56,57,115,116,88,78,62,48,52,47,47,36,60,49,57,83,42,52,49,40,79,43,72,90,35,46,37,37,35,35,54,37,40,37,49,55,52,56,42,39,36,62,150,43,51,80,54,57,35,33,39,41,51,50,55,45,57,67,32,58,46,43,60,63,64,71,62,46,34,53,112,68,85,60,72,39,38,54,49,50,56,36,52,60,40,36,39,66,48,66,71,38,48,90,56,43,47,54,43,46,124,56,67,54,39,52,54,53,40,50,39,85,131,49,41,46,47,43,42,36,47,52,112,58,50,54,45,56,46,37,42,45,42,38,40,42,45,51,41,33,34,43", "endOffsets": "101,144,188,231,275,323,367,411,460,508,556,600,646,692,738,813,883,935,982,1028,1076,1142,1207,1274,1340,1404,1456,1501,1553,1588,1657,1705,1767,1809,1862,1923,1964,2059,2101,2213,2250,2296,2336,2921,2972,3017,3053,3108,3155,3211,3357,3506,3543,3602,3661,3916,3985,4069,4124,4177,4263,4381,4439,4476,4513,4561,4616,4656,4709,4761,4812,4850,4903,4961,4999,5047,5121,5174,5237,5299,5349,5408,5459,5511,5557,5603,5652,5697,5736,5791,5843,5885,6006,6054,6103,6297,6501,6559,6601,6667,6712,6840,6884,7046,7095,7175,7256,7338,7388,7427,7468,7524,7590,7655,7699,7766,7834,7882,7939,7997,8113,8230,8319,8398,8461,8510,8563,8611,8659,8696,8757,8807,8865,8949,8992,9045,9095,9136,9216,9260,9333,9424,9460,9507,9545,9583,9619,9655,9710,9748,9789,9827,9877,9933,9986,10043,10086,10126,10163,10226,10377,10421,10473,10554,10609,10667,10703,10737,10777,10819,10871,10922,12867,12913,12971,13039,13072,13131,13178,13222,13283,13347,13412,13484,13547,13594,13629,13683,13796,13865,13951,14012,14085,14125,14164,14219,14269,14320,14377,14414,14467,14528,14569,14606,14646,14713,14762,14829,14901,14940,14989,15080,15137,15181,15229,15284,15328,15375,15500,15557,15625,15680,15720,15773,15828,15882,15923,15974,16014,16100,16232,16282,16324,16371,16419,16463,16506,16543,16591,16644,16757,16816,23399,23454,23500,23557,23604,23642,23685,23731,23774,23813,23854,23897,23943,23995,24037,24071,24106,24150"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d148dadd36a9aeec86630cf6fa0b2687\\transformed\\jetified-applovin-sdk-13.3.1\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,131,185,246,341,417,479,543", "endColumns": "75,53,60,94,75,61,63,59", "endOffsets": "126,180,241,336,412,474,538,598"}, "to": {"startLines": "45,46,47,48,49,50,51,52", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2341,2417,2471,2532,2627,2703,2765,2829", "endColumns": "75,53,60,94,75,61,63,59", "endOffsets": "2412,2466,2527,2622,2698,2760,2824,2884"}}]}]}